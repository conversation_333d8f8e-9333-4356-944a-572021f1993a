package com.moego.server.message.api;

import com.moego.server.message.dto.BatchDailyReportSendParams;
import com.moego.server.message.dto.DailyReportSendEmailParams;
import com.moego.server.message.dto.DailyReportSendResultDTO;
import com.moego.server.message.dto.FulfillmentReportSendResultDTO;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IDailyReportSendService {
    @PostMapping("/service/message/sendDailyReportCardEmail")
    DailyReportSendResultDTO sendDailyReportEmail(@Valid @RequestBody DailyReportSendEmailParams params);

    @PostMapping("/service/message/batch/sendDailyReportCard")
    List<DailyReportSendResultDTO> batchSendDailyReport(@Valid @RequestBody BatchDailyReportSendParams params);

    @PostMapping("/service/message/sendFulfillmentDailyReportCardEmail")
    FulfillmentReportSendResultDTO sendFulfillmentDailyReportEmail(@Valid @RequestBody DailyReportSendEmailParams params);
}
