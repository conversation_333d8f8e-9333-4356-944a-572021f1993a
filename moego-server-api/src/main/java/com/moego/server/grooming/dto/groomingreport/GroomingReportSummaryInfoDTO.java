package com.moego.server.grooming.dto.groomingreport;

import com.moego.common.enums.groomingreport.GroomingReportConst;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * Grooming report 汇总信息，包括 business, pet, appointment 以及填写的 grooming report
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class GroomingReportSummaryInfoDTO {

    private static final List<String> DEFAULT_PRESET_TAGS = GroomingReportConst.PRESET_TAGS;

    @Schema(description = "business name, logo")
    private BusinessInfo businessInfo;

    @Schema(description = "pet name, breed, avatar path, gender, weight")
    private PetInfo petInfo;

    @Schema(description = "current appointment info, appointment date, start time, service name, staff name")
    private GroomingInfo groomingInfo;

    @Schema(description = "next appointment info")
    private GroomingInfo nextGroomingInfo;

    @Schema(description = "grooming report fill in content")
    private GroomingReportInfoDTO reportInfo;

    private ReviewBoosterConfig reviewBoosterConfig;

    @Schema(description = "review booster record")
    private ReviewBoosterRecord reviewBoosterRecord;

    @Schema(description = "grooming report theme config")
    private GroomingReportThemeConfigDTO themeConfig;

    @Schema(description = "grooming report related resource list")
    private List<GroomingReportResourceDTO> resourceList;

    private List<String> presetTags = DEFAULT_PRESET_TAGS;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessInfo {

        private String businessName;
        private String avatarPath;

        @Schema(description = "Business mode, 0-Mobile, 1-Salon")
        private Byte businessMode;

        @Schema(description = "Address 1")
        private String address1;

        @Schema(description = "Address 2")
        private String address2;

        @Schema(description = "Address city")
        private String addressCity;

        @Schema(description = "Address state")
        private String addressState;

        @Schema(description = "Address zipcode")
        private String addressZipcode;

        @Schema(description = "Address country")
        private String addressCountry;

        @Schema(description = "Address latitude")
        private String addressLat;

        @Schema(description = "Address longitude")
        private String addressLng;

        @Schema(description = "business setting phone number")
        private String phoneNumber;

        @Schema(description = "business date format")
        private String dateFormat;

        @Schema(description = "business time format: 1-24h, 2-12h")
        private Byte timeFormatType;

        @Schema(description = "business book online name")
        private String bookOnlineName;

        @Schema(description = "business book online enable")
        private Boolean bookOnlineEnable;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PetInfo {

        private Integer petId;
        private String petName;
        private Integer petTypeId;
        private String petBreed;
        private String avatarPath;
        private Integer gender;
        private String genderText;
        private String weight;
        private String weightWithUnit;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroomingInfo {

        private Integer appointmentId;
        private Byte status;
        private String appointmentDate;
        private Integer appointmentStartTime;
        private Integer appointmentEndTime;

        // arrival window before
        private Integer arrivalBeforeStartTime;
        // arrival window after
        private Integer arrivalAfterStartTime;

        @Deprecated // 废弃，由调用方自行格式化
        private String appointmentDateTimeText;

        private List<PetServiceDetailInfo> petServiceDetails;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PetServiceDetailInfo {

        // pet info
        private Integer petId;
        private String petName;
        private Integer petTypeId;
        private String petAvatarPath;
        // service, staff info list
        List<ServiceDetailInfo> serviceDetails;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceDetailInfo {
        private Integer serviceId;
        private String serviceName;
        private Integer serviceType;
        private Integer startTime;
        private Integer serviceDuration;
        private Integer petId;
        private Integer staffId;
        private String staffFirstName;
        private String staffLastName;
        private String staffAvatarPath;
    }

    @Data
    public static class ReviewBoosterConfig {

        private Integer positiveScore;
        private String positiveYelp;
        private String positiveFacebook;
        private String positiveGoogle;
    }

    @Data
    public static class ReviewBoosterRecord {

        private Integer positiveScore;
        private String reviewContent;
        private Integer reviewTime;
    }
}
