package com.moego.server.message.service;

import static com.moego.common.enums.groomingreport.GroomingReportConst.DEFAULT_CAT_AVATAR;
import static com.moego.common.enums.groomingreport.GroomingReportConst.DEFAULT_DOG_AVATAR;
import static com.moego.lib.featureflag.features.FeatureFlags.ALLOW_GROOMING_REPORT_MULTI_PHOTO;

import com.microtripit.mandrillapp.lutung.view.MandrillMessage;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo;
import com.moego.common.enums.PetTypeEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.groomingreport.GroomingReportCategoryEnum;
import com.moego.common.enums.groomingreport.GroomingReportConst;
import com.moego.common.enums.groomingreport.GroomingReportQuestionTypeEnum;
import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerPrimaryDto;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.grooming.client.IGroomingGroomingReportClient;
import com.moego.server.grooming.dto.appointment.history.SendNotificationLogDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingRecommendation;
import com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.NotificationTypeEnum;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportSummaryInfoParams;
import com.moego.server.grooming.params.groomingreport.GroomingIdListParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams;
import com.moego.server.grooming.params.groomingreport.UpdateGroomingReportStatusParams;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import com.moego.server.message.dto.GroomingReportSendParams;
import com.moego.server.message.dto.GroomingReportSendPreviewParams;
import com.moego.server.message.enums.MessageDetailEnum;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import com.moego.server.message.enums.MessageTargetTypeEnums;
import com.moego.server.message.mapper.MoeGroomingReportSendLogMapper;
import com.moego.server.message.mapperbean.MoeBusinessMessageDetail;
import com.moego.server.message.mapperbean.MoeGroomingReportSendLog;
import com.moego.server.message.params.MailSendParams;
import com.moego.server.message.params.SendMessageCustomerParams;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.server.message.service.dto.model.GroomingReportEmailModel;
import com.moego.server.message.service.sendmessage.MessageSendRouterService;
import com.moego.server.message.service.third.MandrillService;
import com.moego.server.message.service.util.BusinessInfoHelper;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import freemarker.template.Template;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

@Service
@Slf4j
public class GroomingReportSendService {

    @Autowired
    private MoeGroomingReportSendLogMapper moeGroomingReportSendLogMapper;

    @Autowired
    private MessageSendRouterService messageSendRouterService;

    @Autowired
    private MandrillService mandrillService;

    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Autowired
    private IGroomingGroomingReportClient iGroomingReportClient;

    // 模板引擎
    @Autowired
    private FreeMarkerConfigurer configurer;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @Value("${moego.grooming-report.book-again-url}")
    private String bookAgainUrl;

    @Value("${moego.grooming-report.client-url}")
    private String groomingReportClientUrl;

    public static final String GROOMING_REPORT_SMS_TEMPLATE =
            "{PetName}’s {Title} at {BusinessName} {DirectAccessLink}";

    public static final String GROOMING_REPORT_SMS_RESEND_TEMPLATE =
            "[Updated] {PetName}’s {Title} at {BusinessName} {DirectAccessLink}";

    public static final String GROOMING_REPORT_EMAIL_SUBJECT = "{PetName}’s Grooming Report at {BusinessName}";

    public static final String GROOMING_REPORT_EMAIL_RESEND_SUBJECT = "[Updated] {PetName}’s {Title} at {BusinessName}";

    @Autowired
    private FeatureFlagApi featureFlagApi;

    /**
     * 查询预约的 Grooming report 每种 sending method 的最近一条发送记录
     *
     * @param businessId businessId
     * @param groomingId 预约id
     * @return 发送记录
     */
    public List<GroomingReportSendLogDTO> getGroomingLastReportSendLogs(Integer businessId, Integer groomingId) {
        return moeGroomingReportSendLogMapper.selectLastSendRecordsByGroomingId(businessId, groomingId).stream()
                .map(log -> {
                    GroomingReportSendLogDTO dto = new GroomingReportSendLogDTO();
                    BeanUtils.copyProperties(log, dto);
                    return dto;
                })
                .toList();
    }

    public Map<Integer, List<GroomingReportSendLogDTO>> getGroomingLastReportSendLogsMap(GroomingIdListParams params) {
        if (CollectionUtils.isEmpty(params.groomingIdList())) {
            return Map.of();
        }
        List<MoeGroomingReportSendLog> sendLogList =
                moeGroomingReportSendLogMapper.selectLastSendRecordsByGroomingIdList(
                        params.businessId(), params.groomingIdList());
        return sendLogList.stream()
                .map(log -> {
                    GroomingReportSendLogDTO dto = new GroomingReportSendLogDTO();
                    BeanUtils.copyProperties(log, dto);
                    return dto;
                })
                .collect(Collectors.groupingBy(GroomingReportSendLogDTO::getGroomingId));
    }

    /**
     * 前端获取 GR SMS/Email 发送内容
     *
     * @param businessId businessId
     * @param reportId reportId
     * @param sendingMethod sendingMethod: 1-sms, 2-email
     * @return 发送内容
     */
    public String getGroomingReportSendContent(Integer businessId, Integer reportId, Byte sendingMethod) {
        List<GroomingReportSummaryInfoDTO> reportInfos = iGroomingReportClient.getGroomingReportSummaryInfoList(
                new GetGroomingReportSummaryInfoParams(businessId, List.of(reportId)));
        if (CollectionUtils.isEmpty(reportInfos)) {
            throw ExceptionUtil.bizException(Code.CODE_GROOMING_REPORT_NOT_AVAILABLE);
        }

        GroomingReportSummaryInfoDTO reportInfo = reportInfos.get(0);
        return switch (sendingMethod) {
            case GroomingReportConst.SEND_BY_EMAIL -> buildEmailSubject(null, reportInfo);
            case GroomingReportConst.SEND_BY_SMS -> buildSmsSendContent(reportInfo);
            default -> null;
        };
    }

    /**
     * 发送 Grooming Report preview email
     *
     * @param businessId
     * @param params
     */
    public void sendGroomingReportPreviewEmail(Integer businessId, GroomingReportSendPreviewParams params) {
        GroomingReportPreviewDataDTO previewDataDTO = iGroomingReportClient.getGroomingReportPreviewData(
                businessId,
                params.getPreviewParams() != null ? params.getPreviewParams() : new GroomingReportPreviewParams());
        GroomingReportSummaryInfoDTO reportSummaryInfo = previewDataDTO.getReportSummary();

        CustomerPrimaryDto customerInfo = new CustomerPrimaryDto();
        customerInfo.setEmail(params.getRecipientEmail());
        customerInfo.setFirstName("Demo");
        customerInfo.setLastName("Customer");

        sendGroomingReportByEmail(businessId, params.getEmailSubject(), customerInfo, reportSummaryInfo);
    }

    /**
     * 批量发送 Grooming report
     *
     * @param businessId businessId
     * @param staffId    发送人 staffId
     * @param params     发送参数
     */
    public void sendGroomingReports(Integer businessId, Integer staffId, GroomingReportSendParams params) {
        // 查询 report summary info
        List<GroomingReportSummaryInfoDTO> reportSummaryInfoList =
                iGroomingReportClient.getGroomingReportSummaryInfoList(
                        new GetGroomingReportSummaryInfoParams(businessId, params.getReportIdList()));
        if (CollectionUtils.isEmpty(reportSummaryInfoList)) {
            return;
        }
        // 前端指定发送方式，如无指定则查 setting 的发送方式
        List<Byte> sendingMethodList;
        if (CollectionUtils.isEmpty(params.getSendingMethodList())) {
            GroomingReportSettingDTO settingDTO = iGroomingReportClient.getGroomingReportSetting(businessId);
            sendingMethodList = settingDTO.getSendingMethodList();
            if (CollectionUtils.isEmpty(sendingMethodList)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "no sending method selected");
            }
        } else {
            sendingMethodList = params.getSendingMethodList();
        }

        List<MoeGroomingReportSendLog> sendLogs = reportSummaryInfoList.stream()
                .map(report -> sendOneGroomingReport(businessId, staffId, report, sendingMethodList))
                .flatMap(Collection::stream)
                .toList();

        List<Integer> sentReportIds = sendLogs.stream()
                .map(MoeGroomingReportSendLog::getReportId)
                .distinct()
                .toList();
        // 存 send log
        if (!CollectionUtils.isEmpty(sendLogs)) {
            // 设置成相同的发送时间
            Date now = new Date();
            Long companyId = businessInfoHelper.getCompanyIdByBusinessId(businessId);
            sendLogs.forEach(log -> {
                log.setSentTime(now);
                log.setCompanyId(companyId);
            });
            moeGroomingReportSendLogMapper.batchInsert(sendLogs);
        }
        // 更新 GR 状态为 Sent
        if (!CollectionUtils.isEmpty(sentReportIds)) {
            iGroomingReportClient.updateGroomingReportSentStatus(
                    new UpdateGroomingReportStatusParams(businessId, staffId, sentReportIds));
        }

        int failedCount = 0;
        for (MoeGroomingReportSendLog sendLog : sendLogs) {
            // 如有失败的发送记录，抛出异常
            if (sendLog.getStatus() != 0) {
                failedCount++;
            }
        }
        if (failedCount > 0) {
            String errorMessage = failedCount == 1 ? "Report send failed" : failedCount + " reports send failed";
            if (reportSummaryInfoList.stream()
                    .anyMatch(report ->
                            Objects.equals(report.getReportInfo().getStatus(), GroomingReportStatusEnum.sent.name()))) {
                errorMessage = errorMessage.replace("send", "resend");
            }
            throw ExceptionUtil.bizException(Code.CODE_GROOMING_REPORT_SEND_FAILED, errorMessage);
        }
    }

    /**
     * 发送单个 Grooming report
     *
     * @param businessId         businessId
     * @param staffId            发送人 staffId
     * @param reportSummaryInfo  report summary info
     * @param sendingMethodList  发送方式列表，可以多种方式发送
     * @return 发送记录
     */
    private List<MoeGroomingReportSendLog> sendOneGroomingReport(
            Integer businessId,
            Integer staffId,
            GroomingReportSummaryInfoDTO reportSummaryInfo,
            List<Byte> sendingMethodList) {
        GroomingReportInfoDTO reportInfo = reportSummaryInfo.getReportInfo();
        if (reportInfo == null || (Objects.equals(reportInfo.getStatus(), GroomingReportStatusEnum.created.name()))) {
            log.warn("grooming report not draft or submitted: " + JsonUtil.toJson(reportSummaryInfo));
            return List.of();
        }

        return sendingMethodList.stream()
                .map(sendingMethod -> switch (sendingMethod) {
                    case GroomingReportConst.SEND_BY_SMS -> sendGroomingReportBySms(
                            businessId, staffId, reportSummaryInfo);
                    case GroomingReportConst.SEND_BY_EMAIL -> sendGroomingReportByEmail(
                            businessId, staffId, reportSummaryInfo);
                    default -> null;
                })
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 通过 sms 发送
     *
     * @param businessId        businessId
     * @param staffId           发送人 staffId
     * @param reportSummaryInfo report summary info
     */
    private MoeGroomingReportSendLog sendGroomingReportBySms(
            Integer businessId, Integer staffId, GroomingReportSummaryInfoDTO reportSummaryInfo) {
        MoeGroomingReportSendLog sendLog = buildSendLog(businessId, staffId, reportSummaryInfo.getReportInfo());
        sendLog.setSendingMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue().byteValue());
        try {
            SendMessagesParams sendMessagesParams = new SendMessagesParams();
            sendMessagesParams.setBusinessId(businessId);
            sendMessagesParams.setStaffId(staffId);
            SendMessageCustomerParams customerParams = new SendMessageCustomerParams();
            customerParams.setCustomerId(reportSummaryInfo.getReportInfo().getCustomerId());
            sendMessagesParams.setCustomer(customerParams);
            sendMessagesParams.setMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue());
            sendMessagesParams.setTargetType(MessageTargetTypeEnums.GROOMING_REPORT.getValue());
            sendMessagesParams.setTargetId(reportSummaryInfo.getReportInfo().getGroomingId());

            String messageBody = buildSmsSendContent(reportSummaryInfo);
            sendMessagesParams.setMessageBody(messageBody);
            // 复用 auto message 发送代码
            MoeBusinessMessageDetail messageDetail =
                    messageSendRouterService.sendServicesMessageToCustomer(sendMessagesParams);

            sendLog.setMsgId(messageDetail.getId());

            ActivityLogRecorder.record(
                    businessId,
                    staffId,
                    AppointmentAction.SEND_NOTIFICATION,
                    ResourceType.APPOINTMENT,
                    reportSummaryInfo.getGroomingInfo().getAppointmentId(),
                    new SendNotificationLogDTO(
                            MessageMethodTypeEnum.MESSAGE_METHOD_MSG, true, "", NotificationTypeEnum.GROOMING_REPORT));
        } catch (CommonException exception) {
            // 捕获异常后只记录到 sendLog, 在外层统一处理
            String failedReason = messageSendRouterService.processErrorMsg(exception.getCode());
            sendLog.setStatus((byte) 1);
            sendLog.setErrorCode(exception.getCode());
            sendLog.setErrorMsg(failedReason);
            ActivityLogRecorder.record(
                    businessId,
                    staffId,
                    AppointmentAction.SEND_NOTIFICATION,
                    ResourceType.APPOINTMENT,
                    reportSummaryInfo.getGroomingInfo().getAppointmentId(),
                    new SendNotificationLogDTO(
                            MessageMethodTypeEnum.MESSAGE_METHOD_MSG,
                            false,
                            failedReason,
                            NotificationTypeEnum.GROOMING_REPORT));
        }
        return sendLog;
    }

    private MoeGroomingReportSendLog sendGroomingReportByEmail(
            Integer businessId, Integer staffId, GroomingReportSummaryInfoDTO reportSummaryInfo) {
        MoeGroomingReportSendLog sendLog = buildSendLog(businessId, staffId, reportSummaryInfo.getReportInfo());
        sendLog.setSendingMethod(
                MessageDetailEnum.MESSAGE_METHOD_EMAIL.getValue().byteValue());
        try {
            // 这里不挪到外层原因：循环次数不会很多，并且发送 msg 内部也有查询，挪到外面就重复了，后续再优化
            CustomerInfoIdParams customerInfoIdParams = new CustomerInfoIdParams();
            customerInfoIdParams.setBusinessId(businessId);
            customerInfoIdParams.setCustomerId(reportSummaryInfo.getReportInfo().getCustomerId());
            CustomerPrimaryDto customerDetailWithPrimary =
                    iCustomerCustomerClient.getCustomerDetailWithPrimary(customerInfoIdParams);

            if (!StringUtils.hasText(customerDetailWithPrimary.getEmail())) {
                throw new CommonException(ResponseCodeEnum.MESSAGE_SEND_EMAIL_IS_NULL);
            }

            sendGroomingReportByEmail(businessId, null, customerDetailWithPrimary, reportSummaryInfo);
            ActivityLogRecorder.record(
                    businessId,
                    staffId,
                    AppointmentAction.SEND_NOTIFICATION,
                    ResourceType.APPOINTMENT,
                    reportSummaryInfo.getGroomingInfo().getAppointmentId(),
                    new SendNotificationLogDTO(
                            MessageMethodTypeEnum.MESSAGE_METHOD_EMAIL,
                            true,
                            "",
                            NotificationTypeEnum.GROOMING_REPORT));
        } catch (CommonException exception) {
            String failedReason = messageSendRouterService.processErrorMsg(exception.getCode());
            sendLog.setStatus((byte) 1);
            sendLog.setErrorCode(exception.getCode());
            sendLog.setErrorMsg(failedReason);
            ActivityLogRecorder.record(
                    businessId,
                    staffId,
                    AppointmentAction.SEND_NOTIFICATION,
                    ResourceType.APPOINTMENT,
                    reportSummaryInfo.getGroomingInfo().getAppointmentId(),
                    new SendNotificationLogDTO(
                            MessageMethodTypeEnum.MESSAGE_METHOD_APP,
                            false,
                            failedReason,
                            NotificationTypeEnum.GROOMING_REPORT));
        }
        return sendLog;
    }

    /**
     * 通过 email 发送
     *
     * @param businessId        businessId
     * @param emailSubject      email subject
     * @param customerInfo      顾客信息
     * @param reportSummaryInfo report summary info
     */
    private void sendGroomingReportByEmail(
            Integer businessId,
            String emailSubject,
            CustomerPrimaryDto customerInfo,
            GroomingReportSummaryInfoDTO reportSummaryInfo) {
        Template template;
        String html;
        boolean isMultiPhoto = false;
        Long companyId = businessInfoHelper.getCompanyIdByBusinessIdV2(businessId);
        if (featureFlagApi.isOn(
                ALLOW_GROOMING_REPORT_MULTI_PHOTO,
                FeatureFlagContext.builder().company(companyId).build())) {
            isMultiPhoto = true;
        }
        try {
            template = configurer.getConfiguration().getTemplate("groomingReport.ftl");
            GroomingReportEmailModel model = buildGroomingReportModel(reportSummaryInfo);
            //            log.info("send email model: {}", JsonUtil.toJson(model));
            model.setIsMultiPhoto(isMultiPhoto);
            html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
        } catch (Exception e) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, e.getMessage());
        }

        // 发送邮件
        MailSendParams mailSendParams = new MailSendParams();
        mailSendParams.setSubject(buildEmailSubject(emailSubject, reportSummaryInfo));
        String businessName = reportSummaryInfo.getBusinessInfo().getBusinessName();
        mailSendParams.setFrom_name(businessName);
        mailSendParams.setHtml(html);

        // 设置发送人
        List<MandrillMessage.Recipient> to = new ArrayList<>();
        MandrillMessage.Recipient recipient = new MandrillMessage.Recipient();

        log.info("receiver email {}", customerInfo.getEmail());
        recipient.setEmail(customerInfo.getEmail());
        recipient.setName(customerInfo.getFirstName() + " " + customerInfo.getLastName());
        recipient.setType(MandrillMessage.Recipient.Type.TO);

        to.add(recipient);
        mailSendParams.setTo(to);
        mailSendParams.setBusinessId(businessId);

        mandrillService.sendEmail(mailSendParams);
    }

    public void updateGroomingReportSendLogStatus(MoeBusinessMessageDetail messageDetail) {
        MoeGroomingReportSendLog sendLog = moeGroomingReportSendLogMapper.selectByBusinessIdAndMsgId(
                messageDetail.getBusinessId(), messageDetail.getId());
        if (sendLog == null) {
            return;
        }

        MoeGroomingReportSendLog update = new MoeGroomingReportSendLog();
        update.setId(sendLog.getId());
        update.setStatus((byte) 1);
        update.setErrorCode(messageDetail.getErrorCode());
        update.setErrorMsg(messageSendRouterService.processErrorMsg(messageDetail.getErrorCode()));

        moeGroomingReportSendLogMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 构建 SMS 发送内容
     *
     * @param reportSummaryInfo report summary info
     * @return sms send content
     */
    private String buildSmsSendContent(GroomingReportSummaryInfoDTO reportSummaryInfo) {
        GroomingReportSummaryInfoDTO.BusinessInfo businessInfo = reportSummaryInfo.getBusinessInfo();
        GroomingReportSummaryInfoDTO.PetInfo petInfo = reportSummaryInfo.getPetInfo();
        GroomingReportSummaryInfoDTO.GroomingInfo groomingInfo = reportSummaryInfo.getGroomingInfo();

        String directLink = String.format(
                groomingReportClientUrl, reportSummaryInfo.getReportInfo().getUuid());
        boolean isResend =
                Objects.equals(reportSummaryInfo.getReportInfo().getStatus(), GroomingReportStatusEnum.sent.name());
        String title = reportSummaryInfo.getReportInfo().getTemplate().getTitle();
        if (!StringUtils.hasText(title)) {
            title = GroomingReportConst.DEFAULT_TITLE;
        }
        String sendContent = isResend ? GROOMING_REPORT_SMS_RESEND_TEMPLATE : GROOMING_REPORT_SMS_TEMPLATE;
        sendContent = sendContent.replace("{BusinessName}", businessInfo.getBusinessName());
        sendContent = sendContent.replace("{PetName}", petInfo.getPetName());
        sendContent = sendContent.replace("{Title}", title);
        sendContent = sendContent.replace(
                "{MainStaff}",
                groomingInfo.getPetServiceDetails().stream()
                        .flatMap(detail -> detail.getServiceDetails().stream())
                        .map(GroomingReportSummaryInfoDTO.ServiceDetailInfo::getStaffFirstName)
                        .collect(Collectors.joining(", ")));
        sendContent = sendContent.replace("{DirectAccessLink}", directLink);
        return sendContent;
    }

    /**
     * 组装 email template 所需字段
     *
     * @param reportSummaryInfo report summary info
     * @return grooming report email model
     */
    private GroomingReportEmailModel buildGroomingReportModel(GroomingReportSummaryInfoDTO reportSummaryInfo) {
        GroomingReportSummaryInfoDTO.BusinessInfo businessInfo = reportSummaryInfo.getBusinessInfo();
        GroomingReportSummaryInfoDTO.GroomingInfo groomingInfo = reportSummaryInfo.getGroomingInfo();
        GroomingReportSummaryInfoDTO.GroomingInfo nextGroomingInfo = reportSummaryInfo.getNextGroomingInfo();
        GroomingReportSummaryInfoDTO.PetInfo petInfo = reportSummaryInfo.getPetInfo();
        GroomingReportInfoDTO reportInfo = reportSummaryInfo.getReportInfo();

        GroomingReportEmailModel model = new GroomingReportEmailModel();
        BeanUtils.copyProperties(reportSummaryInfo, model);

        // theme config
        if (reportSummaryInfo.getThemeConfig() != null
                && StringUtils.hasText(reportSummaryInfo.getThemeConfig().getColor())
                && StringUtils.hasText(reportSummaryInfo.getThemeConfig().getLightColor())) {
            reportInfo
                    .getTemplate()
                    .setThemeColor(reportSummaryInfo.getThemeConfig().getColor());
            reportInfo
                    .getTemplate()
                    .setLightThemeColor(reportSummaryInfo.getThemeConfig().getLightColor());
        }

        // curGroomingInfo
        GroomingReportEmailModel.GroomingInfoModel curGroomingInfoModel =
                new GroomingReportEmailModel.GroomingInfoModel();
        curGroomingInfoModel.setPetServiceDetails(groomingInfo.getPetServiceDetails());
        curGroomingInfoModel.setAppointmentDateTimeText(
                buildAppointmentDateTimeText(groomingInfo, businessInfo, false));
        model.setCurGroomingInfo(curGroomingInfoModel);

        if (petInfo != null && !StringUtils.hasText(petInfo.getAvatarPath())) {
            petInfo.setAvatarPath(
                    !PetTypeEnum.DOG.getType().equals(petInfo.getPetTypeId())
                            ? DEFAULT_CAT_AVATAR
                            : DEFAULT_DOG_AVATAR);
        }

        List<GroomingReportEmailModel.QuestionModel> customizedFeedbacks = new ArrayList<>();

        // report info content 需要额外处理，单独设置 key-value
        if (reportInfo.getContent() != null && reportInfo.getContent().getFeedbacks() != null) {
            for (GroomingReportInfoDTO.GroomingReportQuestion question :
                    reportInfo.getContent().getFeedbacks()) {
                switch (question.getKey()) {
                    case GroomingReportConst.QUESTION_KEY_OVERALL_FEEDBACK -> model.setFeedback(
                            convertQuestionModel(question));
                    case GroomingReportConst.QUESTION_KEY_COMMENT -> model.setComment(convertQuestionModel(question));
                    case GroomingReportConst.QUESTION_KEY_MOOD -> model.setMood(convertQuestionModel(question));
                    default -> {}
                }

                if (question.getCategory().equals(GroomingReportCategoryEnum.CATEGORY_CUSTOMIZED_FEEDBACK.getType())) {
                    GroomingReportEmailModel.QuestionModel questionModel = convertQuestionModel(question);
                    // answer 为空的 question 不展示
                    if (!questionModel.getAnswer().isEmpty()) {
                        customizedFeedbacks.add(questionModel);
                    }
                }
            }
        }

        if (!CollectionUtils.isEmpty(customizedFeedbacks)) {
            model.setCustomizedFeedbacks(customizedFeedbacks);
        }

        boolean hasShownPetConditions = false;
        if (reportInfo.getContent() != null && reportInfo.getContent().getPetConditions() != null) {
            List<GroomingReportEmailModel.QuestionModel> questionsExceptBodyView = new ArrayList<>();
            GroomingReportEmailModel.QuestionModel bodyViewQuestion = null;
            // 是否有需要展示的 pet condition
            for (GroomingReportInfoDTO.GroomingReportQuestion question :
                    reportInfo.getContent().getPetConditions()) {
                // show = false 不展示
                if (Boolean.FALSE.equals(question.getShow())) {
                    continue;
                }
                GroomingReportEmailModel.QuestionModel questionModel = convertQuestionModel(question);
                if (!GroomingReportQuestionTypeEnum.isBodyViewQuestion(question.getType())) {
                    questionsExceptBodyView.add(questionModel);
                } else {
                    bodyViewQuestion = questionModel;
                }
                if (Boolean.TRUE.equals(question.getShow())) {
                    hasShownPetConditions = true;
                }
            }
            model.setPetConditions(questionsExceptBodyView);
            if (bodyViewQuestion != null) {
                model.setBodyView(bodyViewQuestion);
            }
        }
        model.setHasShownPetConditions(hasShownPetConditions);

        boolean showDateOnly = Objects.equals(
                reportInfo.getTemplate().getNextAppointmentDateFormatType(),
                GroomingReportConst.APPOINTMENT_SHOW_ONLY_DATE);

        GroomingReportEmailModel.RecommendationModel recommendationModel =
                new GroomingReportEmailModel.RecommendationModel();
        if (reportInfo.getContent() != null && reportInfo.getContent().getRecommendation() != null) {
            GroomingRecommendation recommendation = reportInfo.getContent().getRecommendation();
            recommendationModel.setNextAppointmentDate(
                    buildAppointmentDateTimeText(nextGroomingInfo, businessInfo, showDateOnly));
            recommendationModel.setFrequencyText(recommendation.getFrequencyText());

            // next appointment date 过期，发送时隐藏 next appointment date，book again button
            String nextAppointmentDate = recommendation.getNextAppointmentDate();
            if (isBeforeToday(nextAppointmentDate)) {
                reportInfo.getTemplate().setShowNextAppointment(false);
            }

            if (Boolean.TRUE.equals(businessInfo.getBookOnlineEnable())) {
                recommendationModel.setBookAgainURL(
                        String.format(bookAgainUrl, businessInfo.getBookOnlineName(), reportInfo.getId()));
                model.setIsOBEnable(true);
            } else {
                recommendationModel.setBookAgainURL("");
                model.setIsOBEnable(false);
            }
        } else {
            // 默认值
            recommendationModel.setFrequencyText(null);
            recommendationModel.setNextAppointmentDate(null);
            recommendationModel.setBookAgainURL(null);
            model.setIsOBEnable(false);
        }
        model.setRecommendation(recommendationModel);
        return model;
    }

    private GroomingReportEmailModel.QuestionModel convertQuestionModel(
            GroomingReportInfoDTO.GroomingReportQuestion question) {
        GroomingReportEmailModel.QuestionModel questionModel = new GroomingReportEmailModel.QuestionModel();
        BeanUtils.copyProperties(question, questionModel);
        if (StringUtils.hasText(question.getText())) {
            questionModel.setTextList(List.of(question.getText().split("\n")));
        } else {
            questionModel.setTextList(List.of(""));
        }

        List<String> allChoices = new ArrayList<>();
        if (question.getChoices() != null) {
            allChoices.addAll(question.getChoices());
        }
        if (question.getCustomOptions() != null) {
            allChoices.addAll(question.getCustomOptions());
        }
        questionModel.setAllChoices(allChoices.stream().distinct().toList());

        questionModel.setQuestion(question.getTitle());

        // 新增 answer 字段处理
        String answer = "";
        switch (GroomingReportQuestionTypeEnum.valueOf(question.getType())) {
            case single_choice, multi_choice, tag_choice: {
                if (!CollectionUtils.isEmpty(question.getChoices())) {
                    answer = String.join("\n", question.getChoices());
                }
                break;
            }
            case text_input, short_text_input: {
                answer = question.getText() != null ? question.getText() : "";
                break;
            }
            default:
                answer = "";
        }
        questionModel.setAnswer(answer);

        return questionModel;
    }

    /**
     * 构建 email subject
     *
     * @param subject           前端指定 email subject
     * @param reportSummaryInfo report summary info
     * @return email subject
     */
    private String buildEmailSubject(String subject, GroomingReportSummaryInfoDTO reportSummaryInfo) {
        boolean isResend =
                Objects.equals(reportSummaryInfo.getReportInfo().getStatus(), GroomingReportStatusEnum.sent.name());
        if (!StringUtils.hasText(subject)) {
            subject = isResend ? GROOMING_REPORT_EMAIL_RESEND_SUBJECT : GROOMING_REPORT_EMAIL_SUBJECT;
        }
        String businessName = reportSummaryInfo.getBusinessInfo().getBusinessName();
        String petName = reportSummaryInfo.getPetInfo().getPetName();
        String title = reportSummaryInfo.getReportInfo().getTemplate().getTitle();
        if (!StringUtils.hasText(title)) {
            title = GroomingReportConst.DEFAULT_TITLE;
        }
        subject = subject.replace("{BusinessName}", businessName);
        subject = subject.replace("{PetName}", petName);
        subject = subject.replace("{Title}", title);
        subject = subject.replace(
                "{MainStaff}",
                reportSummaryInfo.getGroomingInfo().getPetServiceDetails().stream()
                        .flatMap(petServiceDetail -> petServiceDetail.getServiceDetails().stream())
                        .map(GroomingReportSummaryInfoDTO.ServiceDetailInfo::getStaffFirstName)
                        .collect(Collectors.joining(", ")));
        return subject;
    }

    private String buildEmailSubject(String subject, FulfillmentReportCardSummaryInfo reportSummaryInfo) {
        boolean isResend =
                GroomingReportStatusEnum.sent.name().equalsIgnoreCase(
                        reportSummaryInfo.getFulfillmentReport().getStatus().toString());
        if (!StringUtils.hasText(subject)) {
            subject = isResend ? GROOMING_REPORT_EMAIL_RESEND_SUBJECT : GROOMING_REPORT_EMAIL_SUBJECT;
        }
        String businessName = reportSummaryInfo.getBusinessInfo().getBusinessName();
        String petName = reportSummaryInfo.getPetInfo().getPetName();
        String title = reportSummaryInfo.getFulfillmentReport().getTemplate().getTitle();
        if (!StringUtils.hasText(title)) {
            title = GroomingReportConst.DEFAULT_TITLE;
        }
        subject = subject.replace("{BusinessName}", businessName);
        subject = subject.replace("{PetName}", petName);
        subject = subject.replace("{Title}", title);
        subject = subject.replace(
                "{MainStaff}",
                reportSummaryInfo.getAppointmentInfo().getPetServiceDetails().stream()
                        .flatMap(petServiceDetail -> petServiceDetail.getServiceDetails().stream())
                        .map(GroomingReportSummaryInfoDTO.ServiceDetailInfo::getStaffFirstName)
                        .collect(Collectors.joining(", ")));
        return subject;
    }

    /**
     * 构建 send log
     *
     * @param businessId businessId
     * @param staffId    staffId
     * @param reportInfo reportInfo
     * @return MoeGroomingReportSendLog
     */
    private MoeGroomingReportSendLog buildSendLog(
            Integer businessId, Integer staffId, GroomingReportInfoDTO reportInfo) {
        MoeGroomingReportSendLog sendLog = new MoeGroomingReportSendLog();
        sendLog.setBusinessId(businessId);
        sendLog.setReportId(reportInfo.getId());
        sendLog.setGroomingId(reportInfo.getGroomingId());
        sendLog.setPetId(reportInfo.getPetId());
        sendLog.setSendingType(GroomingReportConst.SENDING_MANUALLY);
        sendLog.setSentBy(staffId);
        sendLog.setStatus((byte) 0);
        sendLog.setMsgId(0);
        return sendLog;
    }

    /**
     * 判断日期是否在今天之前，因为是 yyyy-MM-dd，所以和今天的0点对比
     *
     * @param dateString date string
     * @return true if before today
     */
    private boolean isBeforeToday(String dateString) {
        Date date = DateUtil.parseDate(dateString, DateUtil.STANDARD_DATE);
        if (date != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date today = calendar.getTime();
            return date.before(today);
        }
        return false;
    }

    private static String buildAppointmentDateTimeText(
            GroomingReportSummaryInfoDTO.GroomingInfo groomingInfo,
            GroomingReportSummaryInfoDTO.BusinessInfo businessInfo,
            boolean showDateOnly) {
        if (groomingInfo == null || businessInfo == null) {
            return null;
        }
        String appointmentDateText =
                DateUtil.dateToBusinessFormat(groomingInfo.getAppointmentDate(), businessInfo.getDateFormat());
        String appointmentDateTimeText;
        if (showDateOnly) {
            // 只展示 date
            appointmentDateTimeText = appointmentDateText;
        } else {
            // Arrival window
            if (Objects.nonNull(groomingInfo.getArrivalBeforeStartTime())
                    && Objects.nonNull(groomingInfo.getArrivalAfterStartTime())
                    && !groomingInfo.getArrivalBeforeStartTime().equals(groomingInfo.getArrivalAfterStartTime())) {
                appointmentDateTimeText = appointmentDateText + ", arrive between: "
                        + DateUtil.formatArrivalWindowTime(
                                groomingInfo.getArrivalBeforeStartTime(),
                                groomingInfo.getArrivalAfterStartTime(),
                                businessInfo.getTimeFormatType());
            } else {
                // date + time
                appointmentDateTimeText = DateUtil.getApptDateAndTimeStr(
                        groomingInfo.getAppointmentDate(),
                        groomingInfo.getAppointmentStartTime(),
                        businessInfo.getDateFormat(),
                        businessInfo.getTimeFormatType());
            }
        }
        return appointmentDateTimeText;
    }
}
