package com.moego.server.message.service;

import static com.moego.idl.models.errors.v1.Code.CODE_MESSAGE_SEND_EMAIL_IS_NULL;

import com.microtripit.mandrillapp.lutung.view.MandrillMessage;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportContent;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportQuestion;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.GetFulfillmentReportSummaryInfoRequest;
import com.moego.common.exception.CommonException;
import com.moego.idl.models.appointment.v1.ContentDef;
import com.moego.idl.models.appointment.v1.DailyReportConfigDef;
import com.moego.idl.models.appointment.v1.ListDailyReportConfigFilter;
import com.moego.idl.models.appointment.v1.QuestionDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.DailyReportServiceGrpc;
import com.moego.idl.service.appointment.v1.GenerateMessageContentByIdRequest;
import com.moego.idl.service.appointment.v1.GenerateMessageContentByIdResponse;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByFilterRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.GetPetInfoRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.dto.appointment.history.SendNotificationLogDTO;
import com.moego.server.grooming.dto.dailyreport.DailyReportSummaryInfoDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.NotificationTypeEnum;
import com.moego.server.message.dto.DailyReportSendResultDTO;
import com.moego.server.message.dto.FulfillmentReportSendResultDTO;
import com.moego.server.message.enums.MessageDetailEnum;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import com.moego.server.message.enums.MessageTargetTypeEnums;
import com.moego.server.message.params.MailSendParams;
import com.moego.server.message.params.SendMessageCustomerParams;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.server.message.service.dto.model.DailyReportEmailModel;
import com.moego.server.message.service.sendmessage.MessageSendRouterService;
import com.moego.server.message.service.third.MandrillService;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import freemarker.template.Template;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

@Service
@Slf4j
@RequiredArgsConstructor
public class DailyReportSendService {

    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub
            businessCustomerPetServiceBlockingStub;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerServiceGrpc;
    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final FreeMarkerConfigurer configurer;
    private final MandrillService mandrillService;
    private final MessageSendRouterService messageSendRouterService;
    private final DailyReportServiceGrpc.DailyReportServiceBlockingStub dailyReportServiceBlockingStub;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub
            fulfillmentReportServiceBlockingStub;

    public static final String DAILY_REPORT_EMAIL_SUBJECT = "{PetName}’s Daily Report at {BusinessName}";
    public static final String DEFAULT_TITLE = "Daily Report";
    public static final String DEFAULT_LIGHT_THEME_COLOR = "#FEF0E8";

    public List<DailyReportSendResultDTO> sendDailyReportSMSs(
            Long companyId, Long businessId, Long staffId, List<Long> ids) {
        Map<Long, String> dailyReportMessageMap = dailyReportServiceBlockingStub
                .generateMessageContentById(GenerateMessageContentByIdRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllDailyReportIds(ids)
                        .build())
                .getDailyReportMessagesList()
                .stream()
                .collect(Collectors.toMap(
                        GenerateMessageContentByIdResponse.DailyReportMessage::getId,
                        GenerateMessageContentByIdResponse.DailyReportMessage::getMessage,
                        (a, b) -> a));
        List<DailyReportConfigDef> dailyReportConfigs = listDailyReportConfigs(companyId, businessId, ids);

        List<DailyReportSendResultDTO> dailyReportSendResultDTOList = new ArrayList<>();

        dailyReportConfigs.forEach(dailyReportConfig -> {
            var id = dailyReportConfig.getId();
            String message = dailyReportMessageMap.get(dailyReportConfig.getId());
            if (!StringUtils.hasText(message)) {
                return;
            }

            try {
                SendMessagesParams sendMessagesParams =
                        buildSmsSendContent(businessId, staffId, dailyReportConfig, message);
                messageSendRouterService.sendServicesMessageToCustomer(sendMessagesParams);
                recordSendDailyReportActivityLog(
                        businessId, staffId, dailyReportConfig, MessageMethodTypeEnum.MESSAGE_METHOD_MSG, true, "");
                dailyReportSendResultDTOList.add(new DailyReportSendResultDTO(id, true, ""));
            } catch (CommonException ex) {
                var failedReason = messageSendRouterService.processErrorMsg(ex.getCode());
                recordSendDailyReportActivityLog(
                        businessId,
                        staffId,
                        dailyReportConfig,
                        MessageMethodTypeEnum.MESSAGE_METHOD_MSG,
                        false,
                        failedReason);

                dailyReportSendResultDTOList.add(new DailyReportSendResultDTO(id, false, failedReason));
            }
        });

        return dailyReportSendResultDTOList;
    }

    private SendMessagesParams buildSmsSendContent(
            Long businessId, Long staffId, DailyReportConfigDef config, String messageBody) {
        SendMessagesParams sendMessagesParams = new SendMessagesParams();
        sendMessagesParams.setBusinessId(Math.toIntExact(businessId));
        sendMessagesParams.setStaffId(Math.toIntExact(staffId));
        SendMessageCustomerParams customerParams = new SendMessageCustomerParams();
        customerParams.setCustomerId(Math.toIntExact(config.getCustomerId()));
        sendMessagesParams.setCustomer(customerParams);
        sendMessagesParams.setMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue());
        sendMessagesParams.setTargetType(MessageTargetTypeEnums.DAILY_REPORT.getValue());
        sendMessagesParams.setTargetId(Math.toIntExact(config.getAppointmentId()));
        sendMessagesParams.setMessageBody(messageBody);
        return sendMessagesParams;
    }

    public List<DailyReportSendResultDTO> sendDailyReportEmails(
            Long companyId, Long businessId, Long staffId, List<Long> ids) {

        List<DailyReportConfigDef> dailyReportConfigs = listDailyReportConfigs(companyId, businessId, ids);

        MoeBusinessDto business = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(businessId.intValue()).build());

        var petIds = dailyReportConfigs.stream()
                .map(DailyReportConfigDef::getPetId)
                .distinct()
                .toList();
        var customerIds = dailyReportConfigs.stream()
                .map(DailyReportConfigDef::getCustomerId)
                .distinct()
                .toList();
        Map<Long, BusinessCustomerPetInfoModel> petInfoModelMap = businessCustomerPetServiceBlockingStub
                .batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                        .addAllIds(petIds)
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build())
                .getPetsList()
                .stream()
                .collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity(), (a, b) -> a));
        Map<Long, BusinessCustomerInfoModel> customerInfoModelMap = businessCustomerServiceGrpc
                .batchGetCustomerInfo(BatchGetCustomerInfoRequest.newBuilder()
                        .addAllIds(customerIds)
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build())
                .getCustomersList()
                .stream()
                .collect(Collectors.toMap(BusinessCustomerInfoModel::getId, Function.identity(), (a, b) -> a));

        List<DailyReportSummaryInfoDTO> dailyReportSummaryInfoDTOList = dailyReportConfigs.stream()
                .filter(e -> petInfoModelMap.containsKey(e.getPetId()))
                .filter(e -> customerInfoModelMap.containsKey(e.getCustomerId()))
                .map(e -> buildDailyReportSummary(
                        e, business, petInfoModelMap.get(e.getPetId()), customerInfoModelMap.get(e.getCustomerId())))
                .toList();

        List<DailyReportSendResultDTO> sendResultDTOS = new ArrayList<>(List.of());
        dailyReportSummaryInfoDTOList.forEach(dailyReportSummaryInfoDTO ->
                sendResultDTOS.add(sendOneDailyReportEmail(staffId, dailyReportSummaryInfoDTO)));

        return sendResultDTOS;
    }

    private DailyReportSendResultDTO sendOneDailyReportEmail(
            Long staffId, DailyReportSummaryInfoDTO dailyReportSummaryInfoDTO) {
        var businessId = dailyReportSummaryInfoDTO.getBusinessInfo().getBusinessId();
        long dailyReportId = dailyReportSummaryInfoDTO.getReportInfo().getId();
        try {
            if (CollectionUtils.isEmpty(dailyReportSummaryInfoDTO.getRecipientEmailList())) {
                throw ExceptionUtil.bizException(CODE_MESSAGE_SEND_EMAIL_IS_NULL);
            }

            sendDailyReportEmail(businessId, dailyReportSummaryInfoDTO);
            recordSendDailyReportActivityLog(
                    businessId,
                    staffId,
                    dailyReportSummaryInfoDTO.getReportInfo(),
                    MessageMethodTypeEnum.MESSAGE_METHOD_EMAIL,
                    true,
                    "");
            return new DailyReportSendResultDTO(dailyReportId, true, "");
        } catch (CommonException exception) {
            String failedReason = messageSendRouterService.processErrorMsg(exception.getCode());
            recordSendDailyReportActivityLog(
                    businessId,
                    staffId,
                    dailyReportSummaryInfoDTO.getReportInfo(),
                    MessageMethodTypeEnum.MESSAGE_METHOD_EMAIL,
                    false,
                    failedReason);
            return new DailyReportSendResultDTO(dailyReportId, false, failedReason);
        }
    }

    public FulfillmentReportSendResultDTO sendFulfillmentDailyReportEmail(
            Long reportId, Long businessId, Long companyId,
            Long staffId, List<String> recipientEmailList, String subject) {
        FulfillmentReportCardSummaryInfo summaryInfo = fulfillmentReportServiceBlockingStub
                .getFulfillmentReportSummaryInfo(GetFulfillmentReportSummaryInfoRequest.newBuilder()
                        .setFulfillmentReportId(reportId)
                        .setBusinessId(businessId)
                        .setCompanyId(companyId)
                        .build())
                .getSummaryInfo();
        try {
            if (CollectionUtils.isEmpty(recipientEmailList)) {
                throw ExceptionUtil.bizException(CODE_MESSAGE_SEND_EMAIL_IS_NULL);
            }

            sendFulfillmentDailyReportEmail(businessId, companyId, summaryInfo, recipientEmailList,  subject);
            recordSendDailyReportActivityLog(
                    businessId,
                    staffId,
                    summaryInfo.getFulfillmentReport().getAppointmentId(),
                    MessageMethodTypeEnum.MESSAGE_METHOD_EMAIL,
                    true,
                    "");
            return new FulfillmentReportSendResultDTO(reportId, true, "");
        } catch (CommonException exception) {
            String failedReason = messageSendRouterService.processErrorMsg(exception.getCode());
            recordSendDailyReportActivityLog(
                    businessId,
                    staffId,
                    summaryInfo.getFulfillmentReport().getAppointmentId(),
                    MessageMethodTypeEnum.MESSAGE_METHOD_EMAIL,
                    false,
                    failedReason);
            return new FulfillmentReportSendResultDTO(reportId, false, failedReason);
        }
    }

    public DailyReportSendResultDTO sendDailyReportEmail(
            Long companyId,
            Long businessId,
            Long staffId,
            DailyReportConfigDef dailyReportConfig,
            List<String> recipientEmailList) {

        MoeBusinessDto business = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(businessId.intValue()).build());
        BusinessCustomerPetInfoModel pet = businessCustomerPetServiceBlockingStub
                .getPetInfo(GetPetInfoRequest.newBuilder()
                        .setId(dailyReportConfig.getPetId())
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build())
                .getPet();
        BusinessCustomerInfoModel customer = businessCustomerServiceGrpc
                .getCustomerInfo(GetCustomerInfoRequest.newBuilder()
                        .setId(dailyReportConfig.getCustomerId())
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build())
                .getCustomer();
        DailyReportSummaryInfoDTO dailyReportSummaryInfoDTO =
                buildDailyReportSummary(dailyReportConfig, business, pet, customer);

        if (!CollectionUtils.isEmpty(recipientEmailList)) {
            dailyReportSummaryInfoDTO.setRecipientEmailList(recipientEmailList);
        }

        return sendOneDailyReportEmail(staffId, dailyReportSummaryInfoDTO);
    }

    private void sendDailyReportEmail(Long businessId, DailyReportSummaryInfoDTO dailyReportSummaryInfoDTO) {
        Template template;
        String html;
        try {
            template = configurer.getConfiguration().getTemplate("dailyReport.ftl");
            DailyReportEmailModel dailyReportEmailModel = buildDailyReportEmailModel(dailyReportSummaryInfoDTO);
            //            log.info("send email model: {}", JsonUtil.toJson(dailyReportEmailModel));
            html = FreeMarkerTemplateUtils.processTemplateIntoString(template, dailyReportEmailModel);
        } catch (Exception e) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, e.getMessage());
        }

        // 发送邮件
        MailSendParams mailSendParams = new MailSendParams();
        mailSendParams.setSubject(buildEmailSubject(
                dailyReportSummaryInfoDTO.getBusinessInfo().getBusinessName(),
                dailyReportSummaryInfoDTO.getPetInfo().getPetName(),
                null));
        String businessName = dailyReportSummaryInfoDTO.getBusinessInfo().getBusinessName();
        mailSendParams.setFrom_name(businessName);
        mailSendParams.setHtml(html);

        // 设置发送人
        List<MandrillMessage.Recipient> to = new ArrayList<>();
        MandrillMessage.Recipient recipient = new MandrillMessage.Recipient();

        var customer = dailyReportSummaryInfoDTO.getCustomerInfo();
        recipient.setName(customer.getFirstName() + " " + customer.getLastName());
        recipient.setType(MandrillMessage.Recipient.Type.TO);

        to.add(recipient);
        mailSendParams.setTo(to);
        mailSendParams.setBusinessId(businessId.intValue());

        dailyReportSummaryInfoDTO.getRecipientEmailList().forEach(email -> {
            log.info("receiver email {}", email);
            recipient.setEmail(email);
            sendDailyReportEmail(mailSendParams);
        });
    }

    private void sendFulfillmentDailyReportEmail(
            Long businessId,
            Long companyId,
            FulfillmentReportCardSummaryInfo summaryInfo,
            List<String> recipientEmailList,
            String subject) {
        Template template;
        String html;
        try {
            template = configurer.getConfiguration().getTemplate("dailyReport.ftl");
            DailyReportEmailModel dailyReportEmailModel = buildDailyReportEmailModel(summaryInfo);
            //            log.info("send email model: {}", JsonUtil.toJson(dailyReportEmailModel));
            html = FreeMarkerTemplateUtils.processTemplateIntoString(template, dailyReportEmailModel);
        } catch (Exception e) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, e.getMessage());
        }

        // 发送邮件
        MailSendParams mailSendParams = new MailSendParams();
        if (StringUtils.hasText(subject)) {
            mailSendParams.setSubject(subject);
        } else {
            mailSendParams.setSubject(buildEmailSubject(
                    summaryInfo.getBusinessInfo().getBusinessName(),
                    summaryInfo.getPetInfo().getPetName(),
                    null));
        }
        String businessName = summaryInfo.getBusinessInfo().getBusinessName();
        mailSendParams.setFrom_name(businessName);
        mailSendParams.setHtml(html);

        // 设置发送人
        List<MandrillMessage.Recipient> to = new ArrayList<>();
        MandrillMessage.Recipient recipient = new MandrillMessage.Recipient();

        long customerId = summaryInfo.getFulfillmentReport().getCustomerId();
        // 获取客户信息
        var customerInfo = businessCustomerServiceGrpc
                .getCustomerInfo(GetCustomerInfoRequest.newBuilder()
                        .setId(customerId)
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build())
                .getCustomer();
        recipient.setName(customerInfo.getFirstName() + " " + customerInfo.getLastName());
        recipient.setType(MandrillMessage.Recipient.Type.TO);

        to.add(recipient);
        mailSendParams.setTo(to);
        mailSendParams.setBusinessId(businessId.intValue());

        recipientEmailList.forEach(email -> {
            log.info("receiver email {}", email);
            recipient.setEmail(email);
            sendDailyReportEmail(mailSendParams);
        });
    }

    private void sendDailyReportEmail(MailSendParams mailSendParams) {
        mandrillService.sendEmail(mailSendParams);
    }

    /**
     * 构建 email subject
     * @return email subject
     */
    private String buildEmailSubject(String businessName, String petName, String title) {
        var subject = DAILY_REPORT_EMAIL_SUBJECT;
        if (!StringUtils.hasText(title)) {
            title = DEFAULT_TITLE;
        }
        subject = subject.replace("{BusinessName}", businessName);
        subject = subject.replace("{PetName}", petName);
        subject = subject.replace("{Title}", title);

        return subject;
    }

    public DailyReportSummaryInfoDTO buildDailyReportSummary(
            DailyReportConfigDef dailyReportConfig,
            MoeBusinessDto business,
            BusinessCustomerPetInfoModel pet,
            BusinessCustomerInfoModel customer) {
        DailyReportSummaryInfoDTO dailyReportSummaryInfoDTO = new DailyReportSummaryInfoDTO();
        dailyReportSummaryInfoDTO.setReportInfo(dailyReportConfig);

        DailyReportSummaryInfoDTO.PetInfo petInfo = new DailyReportSummaryInfoDTO.PetInfo();
        petInfo.setPetId(pet.getId());
        petInfo.setPetName(pet.getPetName());
        dailyReportSummaryInfoDTO.setPetInfo(petInfo);

        DailyReportSummaryInfoDTO.BusinessInfo businessInfo = new DailyReportSummaryInfoDTO.BusinessInfo();
        businessInfo.setBusinessId(business.getId().longValue());
        businessInfo.setBusinessName(business.getBusinessName());
        businessInfo.setAvatarPath(business.getAvatarPath());
        dailyReportSummaryInfoDTO.setBusinessInfo(businessInfo);

        DailyReportSummaryInfoDTO.CustomerInfo customerInfo = new DailyReportSummaryInfoDTO.CustomerInfo();
        customerInfo.setCustomerId(customer.getId());
        customerInfo.setEmail(customer.getEmail());
        customerInfo.setFirstName(customer.getFirstName());
        customerInfo.setLastName(customer.getLastName());
        dailyReportSummaryInfoDTO.setCustomerInfo(customerInfo);

        // 设置默认邮箱
        dailyReportSummaryInfoDTO.setRecipientEmailList(List.of(customer.getEmail()));

        return dailyReportSummaryInfoDTO;
    }

    public DailyReportEmailModel buildDailyReportEmailModel(DailyReportSummaryInfoDTO dailyReportSummaryInfoDTO) {
        var pet = dailyReportSummaryInfoDTO.getPetInfo();
        var business = dailyReportSummaryInfoDTO.getBusinessInfo();

        DailyReportEmailModel dailyReportEmailModel = new DailyReportEmailModel();
        dailyReportEmailModel.setReportTitle("Daily Report");

        DailyReportEmailModel.PetInfo petInfo = new DailyReportEmailModel.PetInfo();
        petInfo.setPetName(pet.getPetName());
        dailyReportEmailModel.setPetInfo(petInfo);

        DailyReportEmailModel.BusinessInfo businessInfo = new DailyReportEmailModel.BusinessInfo();
        businessInfo.setBusinessName(business.getBusinessName());
        businessInfo.setAvatarPath(business.getAvatarPath());
        dailyReportEmailModel.setBusinessInfo(businessInfo);

        ContentDef content =
                dailyReportSummaryInfoDTO.getReportInfo().getReport().getContent();

        String s = CollectionUtils.isEmpty(content.getVideosList())
                ? null
                : content.getVideosList().get(0);
        dailyReportEmailModel.setShowcase(new DailyReportEmailModel.Showcase(content.getPhotosList(), s));

        var overallFeedbacks = new ArrayList<DailyReportEmailModel.FeedbackItem>();
        var customizedFeedbacks = new ArrayList<DailyReportEmailModel.FeedbackItem>();
        content.getFeedbacksList().forEach(feedback -> {
            DailyReportEmailModel.FeedbackItem feedbackItem = buildFeedbackItem(feedback);
            // answer 为空的问题不展示
            if (feedbackItem.getAnswer().isEmpty()) {
                return;
            }
            switch (feedback.getCategory()) {
                case FEEDBACK -> overallFeedbacks.add(feedbackItem);
                case CUSTOMIZE_FEEDBACK -> customizedFeedbacks.add(feedbackItem);
                default -> {}
            }
        });

        dailyReportEmailModel.setOverallFeedbacks(overallFeedbacks);
        dailyReportEmailModel.setCustomizedFeedbacks(customizedFeedbacks);

        dailyReportEmailModel.setThemeColor(content.getThemeColor());
        dailyReportEmailModel.setLightThemeColor(
                content.getLightThemeColor().isEmpty() ? DEFAULT_LIGHT_THEME_COLOR : content.getLightThemeColor());

        return dailyReportEmailModel;
    }

    public DailyReportEmailModel buildDailyReportEmailModel(FulfillmentReportCardSummaryInfo summaryInfo) {
        var pet = summaryInfo.getPetInfo();
        var business = summaryInfo.getBusinessInfo();

        DailyReportEmailModel dailyReportEmailModel = new DailyReportEmailModel();
        dailyReportEmailModel.setReportTitle("Daily Report");

        DailyReportEmailModel.PetInfo petInfo = new DailyReportEmailModel.PetInfo();
        petInfo.setPetName(pet.getPetName());
        dailyReportEmailModel.setPetInfo(petInfo);

        DailyReportEmailModel.BusinessInfo businessInfo = new DailyReportEmailModel.BusinessInfo();
        businessInfo.setBusinessName(business.getBusinessName());
        businessInfo.setAvatarPath(business.getAvatarPath());
        dailyReportEmailModel.setBusinessInfo(businessInfo);

        FulfillmentReportContent content = summaryInfo.getFulfillmentReport().getContent();

        String s = CollectionUtils.isEmpty(content.getVideosList())
                ? null
                : content.getVideosList().get(0);
        dailyReportEmailModel.setShowcase(new DailyReportEmailModel.Showcase(content.getPhotosList(), s));

        var overallFeedbacks = new ArrayList<DailyReportEmailModel.FeedbackItem>();
        var customizedFeedbacks = new ArrayList<DailyReportEmailModel.FeedbackItem>();
        content.getFeedbacksList().forEach(feedback -> {
            DailyReportEmailModel.FeedbackItem feedbackItem = buildFeedbackItem(feedback);
            // answer 为空的问题不展示
            if (feedbackItem.getAnswer().isEmpty()) {
                return;
            }
            switch (feedback.getCategory()) {
                case FEEDBACK -> overallFeedbacks.add(feedbackItem);
                case CUSTOMIZE_FEEDBACK -> customizedFeedbacks.add(feedbackItem);
                default -> {}
            }
        });

        dailyReportEmailModel.setOverallFeedbacks(overallFeedbacks);
        dailyReportEmailModel.setCustomizedFeedbacks(customizedFeedbacks);

        dailyReportEmailModel.setThemeColor(content.getThemeColor());
        dailyReportEmailModel.setLightThemeColor(
                content.getLightThemeColor().isEmpty() ? DEFAULT_LIGHT_THEME_COLOR : content.getLightThemeColor());

        return dailyReportEmailModel;
    }

    public DailyReportEmailModel.FeedbackItem buildFeedbackItem(QuestionDef question) {
        DailyReportEmailModel.FeedbackItem feedbackItem = new DailyReportEmailModel.FeedbackItem();
        feedbackItem.setQuestion(question.getTitle());

        String answer = "";
        switch (question.getType()) {
            case SINGLE_CHOICE, MULTI_CHOICE, TAG_CHOICE: {
                List<String> allChoices = Stream.concat(
                                CollectionUtils.isEmpty(question.getChoicesList())
                                        ? Stream.empty()
                                        : question.getChoicesList().stream(),
                                CollectionUtils.isEmpty(question.getCustomOptionsList())
                                        ? Stream.empty()
                                        : question.getCustomOptionsList().stream())
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(allChoices)) {
                    // mood question 特殊拼接方式
                    if (question.getKey().equals("mood")) {
                        answer = allChoices.stream()
                                .distinct()
                                .map(choice -> "#" + choice)
                                .collect(Collectors.joining(" "));
                    } else {
                        answer = String.join("\n", allChoices);
                    }
                }
                break;
            }
            case TEXT_INPUT, SHORT_TEXT_INPUT: {
                answer = question.getInputText();
                break;
            }
            default:
                answer = "";
        }
        feedbackItem.setAnswer(answer);

        return feedbackItem;
    }

    public DailyReportEmailModel.FeedbackItem buildFeedbackItem(FulfillmentReportQuestion question) {
        DailyReportEmailModel.FeedbackItem feedbackItem = new DailyReportEmailModel.FeedbackItem();
        feedbackItem.setQuestion(question.getTitle());

        String answer = "";
        switch (question.getType()) {
            case SINGLE_CHOICE, MULTI_CHOICE, TAG_CHOICE: {
                List<String> allChoices = Stream.concat(
                                CollectionUtils.isEmpty(question.getChoicesList())
                                        ? Stream.empty()
                                        : question.getChoicesList().stream(),
                                CollectionUtils.isEmpty(question.getCustomOptionsList())
                                        ? Stream.empty()
                                        : question.getCustomOptionsList().stream())
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(allChoices)) {
                    // mood question 特殊拼接方式
                    if (question.getKey().equals("mood")) {
                        answer = allChoices.stream()
                                .distinct()
                                .map(choice -> "#" + choice)
                                .collect(Collectors.joining(" "));
                    } else {
                        answer = String.join("\n", allChoices);
                    }
                }
                break;
            }
            case TEXT_INPUT, SHORT_TEXT_INPUT: {
                answer = question.getInputText();
                break;
            }
            default:
                answer = "";
        }
        feedbackItem.setAnswer(answer);

        return feedbackItem;
    }

    private List<DailyReportConfigDef> listDailyReportConfigs(Long companyId, Long businessId, List<Long> ids) {
        return dailyReportServiceBlockingStub
                .listDailyReportConfigByFilter(ListDailyReportConfigByFilterRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setFilter(ListDailyReportConfigFilter.newBuilder().addAllDailyReportIds(ids))
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .build())
                .getReportConfigsList();
    }

    private void recordSendDailyReportActivityLog(
            long businessId,
            long staffId,
            DailyReportConfigDef dailyReportConfig,
            MessageMethodTypeEnum messageMethodTypeEnum,
            boolean sendSuccess,
            String failedReason) {
        ActivityLogRecorder.record(
                businessId,
                staffId,
                AppointmentAction.SEND_NOTIFICATION,
                ResourceType.APPOINTMENT,
                dailyReportConfig.getAppointmentId(),
                new SendNotificationLogDTO(
                        messageMethodTypeEnum, sendSuccess, failedReason, NotificationTypeEnum.DAILY_REPORT));
    }

    private void recordSendDailyReportActivityLog(
            long businessId,
            long staffId,
            long appointmentId,
            MessageMethodTypeEnum messageMethodTypeEnum,
            boolean sendSuccess,
            String failedReason) {
        ActivityLogRecorder.record(
                businessId,
                staffId,
                AppointmentAction.SEND_NOTIFICATION,
                ResourceType.APPOINTMENT,
                appointmentId,
                new SendNotificationLogDTO(
                        messageMethodTypeEnum, sendSuccess, failedReason, NotificationTypeEnum.DAILY_REPORT));
    }
}
