package com.moego.server.message.server;

import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.idl.models.appointment.v1.DailyReportConfigDef;
import com.moego.idl.models.appointment.v1.ListDailyReportConfigFilter;
import com.moego.idl.service.appointment.v1.DailyReportServiceGrpc;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByFilterRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.server.message.api.IDailyReportSendServiceBase;
import com.moego.server.message.dto.BatchDailyReportSendParams;
import com.moego.server.message.dto.DailyReportSendEmailParams;
import com.moego.server.message.dto.DailyReportSendResultDTO;
import com.moego.server.message.dto.FulfillmentReportSendResultDTO;
import com.moego.server.message.service.DailyReportSendService;
import io.jsonwebtoken.lang.Collections;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class DailyReportSendServer extends IDailyReportSendServiceBase {

    private final DailyReportServiceGrpc.DailyReportServiceBlockingStub dailyReportServiceBlockingStub;
    private final DailyReportSendService dailyReportSendService;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub
            fulfillmentReportServiceBlockingStub;

    @Override
    public DailyReportSendResultDTO sendDailyReportEmail(DailyReportSendEmailParams params) {
        DailyReportConfigDef dailyReportConfig = dailyReportServiceBlockingStub
                .listDailyReportConfigByFilter(ListDailyReportConfigByFilterRequest.newBuilder()
                        .setCompanyId(params.getCompanyId())
                        .setBusinessId(params.getBusinessId())
                        .setFilter(ListDailyReportConfigFilter.newBuilder().addDailyReportIds(params.getId()))
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .build())
                .getReportConfigsList()
                .stream()
                .findFirst()
                .orElse(null);

        if (Objects.isNull(dailyReportConfig)) {
            return new DailyReportSendResultDTO(params.getId(), false, "send error");
        }
        return dailyReportSendService.sendDailyReportEmail(
                params.getCompanyId(),
                params.getBusinessId(),
                params.getStaffId(),
                dailyReportConfig,
                params.getRecipientEmailList());
    }

    @Override
    public List<DailyReportSendResultDTO> batchSendDailyReport(BatchDailyReportSendParams params) {
        if (Collections.isEmpty(params.getIds())) {
            return List.of();
        }
        return switch (params.getSendMethod()) {
            case SEND_METHOD_EMAIL -> dailyReportSendService.sendDailyReportEmails(
                    params.getCompanyId(), params.getBusinessId(), params.getStaffId(), params.getIds());
            case SEND_METHOD_SMS -> dailyReportSendService.sendDailyReportSMSs(
                    params.getCompanyId(), params.getBusinessId(), params.getStaffId(), params.getIds());
            default -> List.of();
        };
    }

    @Override
    public FulfillmentReportSendResultDTO sendFulfillmentDailyReportEmail(DailyReportSendEmailParams params) {
        return dailyReportSendService.sendFulfillmentDailyReportEmail(
                params.getId(),
                params.getBusinessId(),
                params.getCompanyId(),
                params.getStaffId(),
                params.getRecipientEmailList(),
                params.getSubject());
    }
}
