package com.moego.server.message.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.server.message.mapperbean.MoeBusinessMessageBatch;
import com.moego.server.message.vo.DescribeMassTextsVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBusinessMessageBatchMapper extends DynamicDataSource<MoeBusinessMessageBatchMapper> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_message_batch
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_message_batch
     *
     * @mbg.generated
     */
    int insert(MoeBusinessMessageBatch record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_message_batch
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessMessageBatch record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_message_batch
     *
     * @mbg.generated
     */
    MoeBusinessMessageBatch selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_message_batch
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessMessageBatch record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_message_batch
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessMessageBatch record);

    List<MoeBusinessMessageBatch> selectBatchMessages(MoeBusinessMessageBatch query);

    MoeBusinessMessageBatch selectByPrimaryKeyAndBusinessId(
            @Param("id") Integer id, @Param("businessId") Integer businessId);

    List<MoeBusinessMessageBatch> selectByNotFinishBatch();

    List<MoeBusinessMessageBatch> describeMassTexts(DescribeMassTextsVO vo);
}
