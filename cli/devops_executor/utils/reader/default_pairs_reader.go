package reader

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"os"
	"strings"

	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
)

type DefaultPairsReader struct {
	File     string
	Prefix   string
	Optional bool
}

func newDefaultPairsReader(file string, prefix string, optional bool) PairsReader {
	return &DefaultPairsReader{File: file, Prefix: prefix, Optional: optional}
}

func (reader *DefaultPairsReader) Type() string {
	return "DefaultPairsReader"
}

func (reader *DefaultPairsReader) Read(_ context.Context) (map[string]string, error) {
	file, err := os.Open(reader.File)
	if err != nil {
		if errors.Is(err, os.ErrNotExist) {
			fmt.Println("NOT found properties file: ", reader.File)
			if reader.Optional {
				return make(map[string]string), nil
			}
			return nil, err
		}

		return nil, fmt.Errorf("read properties file %s failed: %v", reader.File, err)
	}
	defer file.Close()

	result := make(map[string]string)
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}

		k := strings.TrimSpace(parts[0])
		v := strings.TrimSpace(parts[1])

		if utils.IsBlank(reader.Prefix) {
			result[k] = v
		} else {
			result[reader.Prefix+k] = v
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return result, nil
}
