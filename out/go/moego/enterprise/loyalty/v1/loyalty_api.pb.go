// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/loyalty/v1/loyalty_api.proto

package loyaltyapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CreateMembershipParams
type CreateMembershipParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// membership
	Membership *v1.CreateMembershipDef `protobuf:"bytes,1,opt,name=membership,proto3" json:"membership,omitempty"`
}

func (x *CreateMembershipParams) Reset() {
	*x = CreateMembershipParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMembershipParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMembershipParams) ProtoMessage() {}

func (x *CreateMembershipParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMembershipParams.ProtoReflect.Descriptor instead.
func (*CreateMembershipParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateMembershipParams) GetMembership() *v1.CreateMembershipDef {
	if x != nil {
		return x.Membership
	}
	return nil
}

// CreateMembershipResult
type CreateMembershipResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// membership
	Membership *v1.Membership `protobuf:"bytes,1,opt,name=membership,proto3" json:"membership,omitempty"`
}

func (x *CreateMembershipResult) Reset() {
	*x = CreateMembershipResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMembershipResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMembershipResult) ProtoMessage() {}

func (x *CreateMembershipResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMembershipResult.ProtoReflect.Descriptor instead.
func (*CreateMembershipResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateMembershipResult) GetMembership() *v1.Membership {
	if x != nil {
		return x.Membership
	}
	return nil
}

// ListMembershipsParams
type ListMembershipsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v11.ListMembershipsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListMembershipsParams) Reset() {
	*x = ListMembershipsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMembershipsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMembershipsParams) ProtoMessage() {}

func (x *ListMembershipsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMembershipsParams.ProtoReflect.Descriptor instead.
func (*ListMembershipsParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{2}
}

func (x *ListMembershipsParams) GetFilter() *v11.ListMembershipsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListMembershipsResult
type ListMembershipsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// memberships
	Memberships []*v1.Membership `protobuf:"bytes,1,rep,name=memberships,proto3" json:"memberships,omitempty"`
}

func (x *ListMembershipsResult) Reset() {
	*x = ListMembershipsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMembershipsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMembershipsResult) ProtoMessage() {}

func (x *ListMembershipsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMembershipsResult.ProtoReflect.Descriptor instead.
func (*ListMembershipsResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListMembershipsResult) GetMemberships() []*v1.Membership {
	if x != nil {
		return x.Memberships
	}
	return nil
}

// UpdateMembershipParams
type UpdateMembershipParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// membership
	Membership *v1.UpdateMembershipDef `protobuf:"bytes,2,opt,name=membership,proto3" json:"membership,omitempty"`
}

func (x *UpdateMembershipParams) Reset() {
	*x = UpdateMembershipParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMembershipParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMembershipParams) ProtoMessage() {}

func (x *UpdateMembershipParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMembershipParams.ProtoReflect.Descriptor instead.
func (*UpdateMembershipParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateMembershipParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateMembershipParams) GetMembership() *v1.UpdateMembershipDef {
	if x != nil {
		return x.Membership
	}
	return nil
}

// UpdateMembershipResult
type UpdateMembershipResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// membership
	Membership *v1.Membership `protobuf:"bytes,1,opt,name=membership,proto3" json:"membership,omitempty"`
}

func (x *UpdateMembershipResult) Reset() {
	*x = UpdateMembershipResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMembershipResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMembershipResult) ProtoMessage() {}

func (x *UpdateMembershipResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMembershipResult.ProtoReflect.Descriptor instead.
func (*UpdateMembershipResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateMembershipResult) GetMembership() *v1.Membership {
	if x != nil {
		return x.Membership
	}
	return nil
}

// DeleteMembershipParams
type DeleteMembershipParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteMembershipParams) Reset() {
	*x = DeleteMembershipParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMembershipParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMembershipParams) ProtoMessage() {}

func (x *DeleteMembershipParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMembershipParams.ProtoReflect.Descriptor instead.
func (*DeleteMembershipParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteMembershipParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteMembershipResult
type DeleteMembershipResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteMembershipResult) Reset() {
	*x = DeleteMembershipResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMembershipResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMembershipResult) ProtoMessage() {}

func (x *DeleteMembershipResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMembershipResult.ProtoReflect.Descriptor instead.
func (*DeleteMembershipResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{7}
}

// PushMembershipChangesParams
type PushMembershipChangesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// membership ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushMembershipChangesParams) Reset() {
	*x = PushMembershipChangesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushMembershipChangesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushMembershipChangesParams) ProtoMessage() {}

func (x *PushMembershipChangesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushMembershipChangesParams.ProtoReflect.Descriptor instead.
func (*PushMembershipChangesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{8}
}

func (x *PushMembershipChangesParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *PushMembershipChangesParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushMembershipChangesResult
type PushMembershipChangesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushMembershipChangesResult) Reset() {
	*x = PushMembershipChangesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushMembershipChangesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushMembershipChangesResult) ProtoMessage() {}

func (x *PushMembershipChangesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushMembershipChangesResult.ProtoReflect.Descriptor instead.
func (*PushMembershipChangesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{9}
}

func (x *PushMembershipChangesResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushMembershipChangesResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// CreatePackageParams
type CreatePackageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// package
	Package *v1.CreatePackageDef `protobuf:"bytes,1,opt,name=package,proto3" json:"package,omitempty"`
}

func (x *CreatePackageParams) Reset() {
	*x = CreatePackageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePackageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePackageParams) ProtoMessage() {}

func (x *CreatePackageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePackageParams.ProtoReflect.Descriptor instead.
func (*CreatePackageParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{10}
}

func (x *CreatePackageParams) GetPackage() *v1.CreatePackageDef {
	if x != nil {
		return x.Package
	}
	return nil
}

// CreatePackageResult
type CreatePackageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// package
	Package *v1.Package `protobuf:"bytes,1,opt,name=package,proto3" json:"package,omitempty"`
}

func (x *CreatePackageResult) Reset() {
	*x = CreatePackageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePackageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePackageResult) ProtoMessage() {}

func (x *CreatePackageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePackageResult.ProtoReflect.Descriptor instead.
func (*CreatePackageResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{11}
}

func (x *CreatePackageResult) GetPackage() *v1.Package {
	if x != nil {
		return x.Package
	}
	return nil
}

// ListPackagesParams
type ListPackagesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v11.ListPackagesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPackagesParams) Reset() {
	*x = ListPackagesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPackagesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPackagesParams) ProtoMessage() {}

func (x *ListPackagesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPackagesParams.ProtoReflect.Descriptor instead.
func (*ListPackagesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{12}
}

func (x *ListPackagesParams) GetFilter() *v11.ListPackagesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListPackagesResult
type ListPackagesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// packages
	Packages []*v1.Package `protobuf:"bytes,1,rep,name=packages,proto3" json:"packages,omitempty"`
}

func (x *ListPackagesResult) Reset() {
	*x = ListPackagesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPackagesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPackagesResult) ProtoMessage() {}

func (x *ListPackagesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPackagesResult.ProtoReflect.Descriptor instead.
func (*ListPackagesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{13}
}

func (x *ListPackagesResult) GetPackages() []*v1.Package {
	if x != nil {
		return x.Packages
	}
	return nil
}

// UpdatePackageParams
type UpdatePackageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// package
	Package *v1.UpdatePackageDef `protobuf:"bytes,2,opt,name=package,proto3" json:"package,omitempty"`
}

func (x *UpdatePackageParams) Reset() {
	*x = UpdatePackageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePackageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePackageParams) ProtoMessage() {}

func (x *UpdatePackageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePackageParams.ProtoReflect.Descriptor instead.
func (*UpdatePackageParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{14}
}

func (x *UpdatePackageParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePackageParams) GetPackage() *v1.UpdatePackageDef {
	if x != nil {
		return x.Package
	}
	return nil
}

// UpdatePackageResult
type UpdatePackageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// package
	Package *v1.Package `protobuf:"bytes,1,opt,name=package,proto3" json:"package,omitempty"`
}

func (x *UpdatePackageResult) Reset() {
	*x = UpdatePackageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePackageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePackageResult) ProtoMessage() {}

func (x *UpdatePackageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePackageResult.ProtoReflect.Descriptor instead.
func (*UpdatePackageResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{15}
}

func (x *UpdatePackageResult) GetPackage() *v1.Package {
	if x != nil {
		return x.Package
	}
	return nil
}

// DeletePackageParams
type DeletePackageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePackageParams) Reset() {
	*x = DeletePackageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePackageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePackageParams) ProtoMessage() {}

func (x *DeletePackageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePackageParams.ProtoReflect.Descriptor instead.
func (*DeletePackageParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{16}
}

func (x *DeletePackageParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeletePackageResult
type DeletePackageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePackageResult) Reset() {
	*x = DeletePackageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePackageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePackageResult) ProtoMessage() {}

func (x *DeletePackageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePackageResult.ProtoReflect.Descriptor instead.
func (*DeletePackageResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{17}
}

// PushPackageChangesParams
type PushPackageChangesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// package ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushPackageChangesParams) Reset() {
	*x = PushPackageChangesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPackageChangesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPackageChangesParams) ProtoMessage() {}

func (x *PushPackageChangesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPackageChangesParams.ProtoReflect.Descriptor instead.
func (*PushPackageChangesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{18}
}

func (x *PushPackageChangesParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *PushPackageChangesParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushPackageChangesResult
type PushPackageChangesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushPackageChangesResult) Reset() {
	*x = PushPackageChangesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPackageChangesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPackageChangesResult) ProtoMessage() {}

func (x *PushPackageChangesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPackageChangesResult.ProtoReflect.Descriptor instead.
func (*PushPackageChangesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{19}
}

func (x *PushPackageChangesResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushPackageChangesResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// CreateDiscountParams
type CreateDiscountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount
	Discount *v1.CreateDiscountDef `protobuf:"bytes,1,opt,name=discount,proto3" json:"discount,omitempty"`
}

func (x *CreateDiscountParams) Reset() {
	*x = CreateDiscountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDiscountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDiscountParams) ProtoMessage() {}

func (x *CreateDiscountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDiscountParams.ProtoReflect.Descriptor instead.
func (*CreateDiscountParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{20}
}

func (x *CreateDiscountParams) GetDiscount() *v1.CreateDiscountDef {
	if x != nil {
		return x.Discount
	}
	return nil
}

// CreateDiscountResult
type CreateDiscountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount
	Discount *v1.Discount `protobuf:"bytes,1,opt,name=discount,proto3" json:"discount,omitempty"`
}

func (x *CreateDiscountResult) Reset() {
	*x = CreateDiscountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDiscountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDiscountResult) ProtoMessage() {}

func (x *CreateDiscountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDiscountResult.ProtoReflect.Descriptor instead.
func (*CreateDiscountResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{21}
}

func (x *CreateDiscountResult) GetDiscount() *v1.Discount {
	if x != nil {
		return x.Discount
	}
	return nil
}

// ListDiscountsParams
type ListDiscountsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v11.ListDiscountsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListDiscountsParams) Reset() {
	*x = ListDiscountsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDiscountsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDiscountsParams) ProtoMessage() {}

func (x *ListDiscountsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDiscountsParams.ProtoReflect.Descriptor instead.
func (*ListDiscountsParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{22}
}

func (x *ListDiscountsParams) GetFilter() *v11.ListDiscountsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListDiscountsResult
type ListDiscountsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discounts
	Discounts []*v1.Discount `protobuf:"bytes,1,rep,name=discounts,proto3" json:"discounts,omitempty"`
}

func (x *ListDiscountsResult) Reset() {
	*x = ListDiscountsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDiscountsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDiscountsResult) ProtoMessage() {}

func (x *ListDiscountsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDiscountsResult.ProtoReflect.Descriptor instead.
func (*ListDiscountsResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{23}
}

func (x *ListDiscountsResult) GetDiscounts() []*v1.Discount {
	if x != nil {
		return x.Discounts
	}
	return nil
}

// UpdateDiscountParams
type UpdateDiscountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// discount
	Discount *v1.UpdateDiscountDef `protobuf:"bytes,2,opt,name=discount,proto3" json:"discount,omitempty"`
}

func (x *UpdateDiscountParams) Reset() {
	*x = UpdateDiscountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDiscountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDiscountParams) ProtoMessage() {}

func (x *UpdateDiscountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDiscountParams.ProtoReflect.Descriptor instead.
func (*UpdateDiscountParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateDiscountParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDiscountParams) GetDiscount() *v1.UpdateDiscountDef {
	if x != nil {
		return x.Discount
	}
	return nil
}

// UpdateDiscountResult
type UpdateDiscountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount
	Discount *v1.Discount `protobuf:"bytes,1,opt,name=discount,proto3" json:"discount,omitempty"`
}

func (x *UpdateDiscountResult) Reset() {
	*x = UpdateDiscountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDiscountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDiscountResult) ProtoMessage() {}

func (x *UpdateDiscountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDiscountResult.ProtoReflect.Descriptor instead.
func (*UpdateDiscountResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateDiscountResult) GetDiscount() *v1.Discount {
	if x != nil {
		return x.Discount
	}
	return nil
}

// DeleteDiscountParams
type DeleteDiscountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteDiscountParams) Reset() {
	*x = DeleteDiscountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDiscountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDiscountParams) ProtoMessage() {}

func (x *DeleteDiscountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDiscountParams.ProtoReflect.Descriptor instead.
func (*DeleteDiscountParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{26}
}

func (x *DeleteDiscountParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteDiscountResult
type DeleteDiscountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteDiscountResult) Reset() {
	*x = DeleteDiscountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDiscountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDiscountResult) ProtoMessage() {}

func (x *DeleteDiscountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDiscountResult.ProtoReflect.Descriptor instead.
func (*DeleteDiscountResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{27}
}

// PushDiscountChangesParams
type PushDiscountChangesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushDiscountChangesParams) Reset() {
	*x = PushDiscountChangesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushDiscountChangesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushDiscountChangesParams) ProtoMessage() {}

func (x *PushDiscountChangesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushDiscountChangesParams.ProtoReflect.Descriptor instead.
func (*PushDiscountChangesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{28}
}

func (x *PushDiscountChangesParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *PushDiscountChangesParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushDiscountChangesResult
type PushDiscountChangesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushDiscountChangesResult) Reset() {
	*x = PushDiscountChangesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushDiscountChangesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushDiscountChangesResult) ProtoMessage() {}

func (x *PushDiscountChangesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushDiscountChangesResult.ProtoReflect.Descriptor instead.
func (*PushDiscountChangesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{29}
}

func (x *PushDiscountChangesResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushDiscountChangesResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// GenerateDiscountCodeParams
type GenerateDiscountCodeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GenerateDiscountCodeParams) Reset() {
	*x = GenerateDiscountCodeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateDiscountCodeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateDiscountCodeParams) ProtoMessage() {}

func (x *GenerateDiscountCodeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateDiscountCodeParams.ProtoReflect.Descriptor instead.
func (*GenerateDiscountCodeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{30}
}

// GenerateDiscountCodeResult
type GenerateDiscountCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount code
	DiscountCode string `protobuf:"bytes,1,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
}

func (x *GenerateDiscountCodeResult) Reset() {
	*x = GenerateDiscountCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateDiscountCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateDiscountCodeResult) ProtoMessage() {}

func (x *GenerateDiscountCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateDiscountCodeResult.ProtoReflect.Descriptor instead.
func (*GenerateDiscountCodeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP(), []int{31}
}

func (x *GenerateDiscountCodeResult) GetDiscountCode() string {
	if x != nil {
		return x.DiscountCode
	}
	return ""
}

var File_moego_enterprise_loyalty_v1_loyalty_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f,
	0x79, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x1a, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x79, 0x61, 0x6c,
	0x74, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x69, 0x0a, 0x16, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x4f, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x22, 0x60, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46,
	0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x22, 0x6b, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x52, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x22, 0x61, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x0b,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4f, 0x0a, 0x0a, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x65, 0x66, 0x52,
	0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x22, 0x60, 0x0a, 0x16, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x22, 0x31, 0x0a,
	0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x73, 0x0a, 0x1b, 0x50, 0x75,
	0x73, 0x68, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22,
	0x7b, 0x0a, 0x1b, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2e,
	0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c,
	0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x5d, 0x0a, 0x13,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x46, 0x0a, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44,
	0x65, 0x66, 0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x22, 0x54, 0x0a, 0x13, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x22, 0x65, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4f, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x55, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3f,
	0x0a, 0x08, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x08, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x22,
	0x76, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x46, 0x0a, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x07,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x22, 0x54, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3d,
	0x0a, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x22, 0x2e, 0x0a,
	0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x15, 0x0a,
	0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x70, 0x0a, 0x18, 0x50, 0x75, 0x73, 0x68, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69,
	0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0x78, 0x0a, 0x18, 0x50, 0x75, 0x73, 0x68, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73,
	0x22, 0x61, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x49, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x58, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x08, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x67, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x50, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x59, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a,
	0x09, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x09, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x22, 0x7a, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x49, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x65, 0x66, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x58, 0x0a,
	0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x2f, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x71, 0x0a, 0x19, 0x50, 0x75, 0x73, 0x68, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12,
	0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x22, 0x79, 0x0a, 0x19, 0x50, 0x75, 0x73, 0x68, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73,
	0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x1c,
	0x0a, 0x1a, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x41, 0x0a, 0x1a,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x32,
	0xd9, 0x0f, 0x0a, 0x0e, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x7c, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c,
	0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x79, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c,
	0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x10, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x10, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x15, 0x50, 0x75, 0x73, 0x68,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x73, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x75, 0x73, 0x68, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c,
	0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x73, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61,
	0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x0c, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f,
	0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c,
	0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x73, 0x0a, 0x0d,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x73, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x12, 0x50, 0x75, 0x73, 0x68, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x0e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x73, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f,
	0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x76, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f,
	0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x13, 0x50, 0x75, 0x73, 0x68, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c,
	0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x86, 0x01, 0x0a, 0x23,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x6c, 0x6f, 0x79,
	0x61, 0x6c, 0x74, 0x79, 0x2f, 0x76, 0x31, 0x3b, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescData = file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDesc
)

func file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescData)
	})
	return file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDescData
}

var file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_moego_enterprise_loyalty_v1_loyalty_api_proto_goTypes = []interface{}{
	(*CreateMembershipParams)(nil),            // 0: moego.enterprise.loyalty.v1.CreateMembershipParams
	(*CreateMembershipResult)(nil),            // 1: moego.enterprise.loyalty.v1.CreateMembershipResult
	(*ListMembershipsParams)(nil),             // 2: moego.enterprise.loyalty.v1.ListMembershipsParams
	(*ListMembershipsResult)(nil),             // 3: moego.enterprise.loyalty.v1.ListMembershipsResult
	(*UpdateMembershipParams)(nil),            // 4: moego.enterprise.loyalty.v1.UpdateMembershipParams
	(*UpdateMembershipResult)(nil),            // 5: moego.enterprise.loyalty.v1.UpdateMembershipResult
	(*DeleteMembershipParams)(nil),            // 6: moego.enterprise.loyalty.v1.DeleteMembershipParams
	(*DeleteMembershipResult)(nil),            // 7: moego.enterprise.loyalty.v1.DeleteMembershipResult
	(*PushMembershipChangesParams)(nil),       // 8: moego.enterprise.loyalty.v1.PushMembershipChangesParams
	(*PushMembershipChangesResult)(nil),       // 9: moego.enterprise.loyalty.v1.PushMembershipChangesResult
	(*CreatePackageParams)(nil),               // 10: moego.enterprise.loyalty.v1.CreatePackageParams
	(*CreatePackageResult)(nil),               // 11: moego.enterprise.loyalty.v1.CreatePackageResult
	(*ListPackagesParams)(nil),                // 12: moego.enterprise.loyalty.v1.ListPackagesParams
	(*ListPackagesResult)(nil),                // 13: moego.enterprise.loyalty.v1.ListPackagesResult
	(*UpdatePackageParams)(nil),               // 14: moego.enterprise.loyalty.v1.UpdatePackageParams
	(*UpdatePackageResult)(nil),               // 15: moego.enterprise.loyalty.v1.UpdatePackageResult
	(*DeletePackageParams)(nil),               // 16: moego.enterprise.loyalty.v1.DeletePackageParams
	(*DeletePackageResult)(nil),               // 17: moego.enterprise.loyalty.v1.DeletePackageResult
	(*PushPackageChangesParams)(nil),          // 18: moego.enterprise.loyalty.v1.PushPackageChangesParams
	(*PushPackageChangesResult)(nil),          // 19: moego.enterprise.loyalty.v1.PushPackageChangesResult
	(*CreateDiscountParams)(nil),              // 20: moego.enterprise.loyalty.v1.CreateDiscountParams
	(*CreateDiscountResult)(nil),              // 21: moego.enterprise.loyalty.v1.CreateDiscountResult
	(*ListDiscountsParams)(nil),               // 22: moego.enterprise.loyalty.v1.ListDiscountsParams
	(*ListDiscountsResult)(nil),               // 23: moego.enterprise.loyalty.v1.ListDiscountsResult
	(*UpdateDiscountParams)(nil),              // 24: moego.enterprise.loyalty.v1.UpdateDiscountParams
	(*UpdateDiscountResult)(nil),              // 25: moego.enterprise.loyalty.v1.UpdateDiscountResult
	(*DeleteDiscountParams)(nil),              // 26: moego.enterprise.loyalty.v1.DeleteDiscountParams
	(*DeleteDiscountResult)(nil),              // 27: moego.enterprise.loyalty.v1.DeleteDiscountResult
	(*PushDiscountChangesParams)(nil),         // 28: moego.enterprise.loyalty.v1.PushDiscountChangesParams
	(*PushDiscountChangesResult)(nil),         // 29: moego.enterprise.loyalty.v1.PushDiscountChangesResult
	(*GenerateDiscountCodeParams)(nil),        // 30: moego.enterprise.loyalty.v1.GenerateDiscountCodeParams
	(*GenerateDiscountCodeResult)(nil),        // 31: moego.enterprise.loyalty.v1.GenerateDiscountCodeResult
	(*v1.CreateMembershipDef)(nil),            // 32: moego.models.enterprise.v1.CreateMembershipDef
	(*v1.Membership)(nil),                     // 33: moego.models.enterprise.v1.Membership
	(*v11.ListMembershipsRequest_Filter)(nil), // 34: moego.service.enterprise.v1.ListMembershipsRequest.Filter
	(*v1.UpdateMembershipDef)(nil),            // 35: moego.models.enterprise.v1.UpdateMembershipDef
	(*v1.TenantObject)(nil),                   // 36: moego.models.enterprise.v1.TenantObject
	(*v1.CreatePackageDef)(nil),               // 37: moego.models.enterprise.v1.CreatePackageDef
	(*v1.Package)(nil),                        // 38: moego.models.enterprise.v1.Package
	(*v11.ListPackagesRequest_Filter)(nil),    // 39: moego.service.enterprise.v1.ListPackagesRequest.Filter
	(*v1.UpdatePackageDef)(nil),               // 40: moego.models.enterprise.v1.UpdatePackageDef
	(*v1.CreateDiscountDef)(nil),              // 41: moego.models.enterprise.v1.CreateDiscountDef
	(*v1.Discount)(nil),                       // 42: moego.models.enterprise.v1.Discount
	(*v11.ListDiscountsRequest_Filter)(nil),   // 43: moego.service.enterprise.v1.ListDiscountsRequest.Filter
	(*v1.UpdateDiscountDef)(nil),              // 44: moego.models.enterprise.v1.UpdateDiscountDef
}
var file_moego_enterprise_loyalty_v1_loyalty_api_proto_depIdxs = []int32{
	32, // 0: moego.enterprise.loyalty.v1.CreateMembershipParams.membership:type_name -> moego.models.enterprise.v1.CreateMembershipDef
	33, // 1: moego.enterprise.loyalty.v1.CreateMembershipResult.membership:type_name -> moego.models.enterprise.v1.Membership
	34, // 2: moego.enterprise.loyalty.v1.ListMembershipsParams.filter:type_name -> moego.service.enterprise.v1.ListMembershipsRequest.Filter
	33, // 3: moego.enterprise.loyalty.v1.ListMembershipsResult.memberships:type_name -> moego.models.enterprise.v1.Membership
	35, // 4: moego.enterprise.loyalty.v1.UpdateMembershipParams.membership:type_name -> moego.models.enterprise.v1.UpdateMembershipDef
	33, // 5: moego.enterprise.loyalty.v1.UpdateMembershipResult.membership:type_name -> moego.models.enterprise.v1.Membership
	36, // 6: moego.enterprise.loyalty.v1.PushMembershipChangesParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	37, // 7: moego.enterprise.loyalty.v1.CreatePackageParams.package:type_name -> moego.models.enterprise.v1.CreatePackageDef
	38, // 8: moego.enterprise.loyalty.v1.CreatePackageResult.package:type_name -> moego.models.enterprise.v1.Package
	39, // 9: moego.enterprise.loyalty.v1.ListPackagesParams.filter:type_name -> moego.service.enterprise.v1.ListPackagesRequest.Filter
	38, // 10: moego.enterprise.loyalty.v1.ListPackagesResult.packages:type_name -> moego.models.enterprise.v1.Package
	40, // 11: moego.enterprise.loyalty.v1.UpdatePackageParams.package:type_name -> moego.models.enterprise.v1.UpdatePackageDef
	38, // 12: moego.enterprise.loyalty.v1.UpdatePackageResult.package:type_name -> moego.models.enterprise.v1.Package
	36, // 13: moego.enterprise.loyalty.v1.PushPackageChangesParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	41, // 14: moego.enterprise.loyalty.v1.CreateDiscountParams.discount:type_name -> moego.models.enterprise.v1.CreateDiscountDef
	42, // 15: moego.enterprise.loyalty.v1.CreateDiscountResult.discount:type_name -> moego.models.enterprise.v1.Discount
	43, // 16: moego.enterprise.loyalty.v1.ListDiscountsParams.filter:type_name -> moego.service.enterprise.v1.ListDiscountsRequest.Filter
	42, // 17: moego.enterprise.loyalty.v1.ListDiscountsResult.discounts:type_name -> moego.models.enterprise.v1.Discount
	44, // 18: moego.enterprise.loyalty.v1.UpdateDiscountParams.discount:type_name -> moego.models.enterprise.v1.UpdateDiscountDef
	42, // 19: moego.enterprise.loyalty.v1.UpdateDiscountResult.discount:type_name -> moego.models.enterprise.v1.Discount
	36, // 20: moego.enterprise.loyalty.v1.PushDiscountChangesParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	0,  // 21: moego.enterprise.loyalty.v1.LoyaltyService.CreateMembership:input_type -> moego.enterprise.loyalty.v1.CreateMembershipParams
	2,  // 22: moego.enterprise.loyalty.v1.LoyaltyService.ListMemberships:input_type -> moego.enterprise.loyalty.v1.ListMembershipsParams
	4,  // 23: moego.enterprise.loyalty.v1.LoyaltyService.UpdateMembership:input_type -> moego.enterprise.loyalty.v1.UpdateMembershipParams
	6,  // 24: moego.enterprise.loyalty.v1.LoyaltyService.DeleteMembership:input_type -> moego.enterprise.loyalty.v1.DeleteMembershipParams
	8,  // 25: moego.enterprise.loyalty.v1.LoyaltyService.PushMembershipChanges:input_type -> moego.enterprise.loyalty.v1.PushMembershipChangesParams
	10, // 26: moego.enterprise.loyalty.v1.LoyaltyService.CreatePackage:input_type -> moego.enterprise.loyalty.v1.CreatePackageParams
	12, // 27: moego.enterprise.loyalty.v1.LoyaltyService.ListPackages:input_type -> moego.enterprise.loyalty.v1.ListPackagesParams
	14, // 28: moego.enterprise.loyalty.v1.LoyaltyService.UpdatePackage:input_type -> moego.enterprise.loyalty.v1.UpdatePackageParams
	16, // 29: moego.enterprise.loyalty.v1.LoyaltyService.DeletePackage:input_type -> moego.enterprise.loyalty.v1.DeletePackageParams
	18, // 30: moego.enterprise.loyalty.v1.LoyaltyService.PushPackageChanges:input_type -> moego.enterprise.loyalty.v1.PushPackageChangesParams
	20, // 31: moego.enterprise.loyalty.v1.LoyaltyService.CreateDiscount:input_type -> moego.enterprise.loyalty.v1.CreateDiscountParams
	22, // 32: moego.enterprise.loyalty.v1.LoyaltyService.ListDiscounts:input_type -> moego.enterprise.loyalty.v1.ListDiscountsParams
	24, // 33: moego.enterprise.loyalty.v1.LoyaltyService.UpdateDiscount:input_type -> moego.enterprise.loyalty.v1.UpdateDiscountParams
	26, // 34: moego.enterprise.loyalty.v1.LoyaltyService.DeleteDiscount:input_type -> moego.enterprise.loyalty.v1.DeleteDiscountParams
	30, // 35: moego.enterprise.loyalty.v1.LoyaltyService.GenerateDiscountCode:input_type -> moego.enterprise.loyalty.v1.GenerateDiscountCodeParams
	28, // 36: moego.enterprise.loyalty.v1.LoyaltyService.PushDiscountChanges:input_type -> moego.enterprise.loyalty.v1.PushDiscountChangesParams
	1,  // 37: moego.enterprise.loyalty.v1.LoyaltyService.CreateMembership:output_type -> moego.enterprise.loyalty.v1.CreateMembershipResult
	3,  // 38: moego.enterprise.loyalty.v1.LoyaltyService.ListMemberships:output_type -> moego.enterprise.loyalty.v1.ListMembershipsResult
	5,  // 39: moego.enterprise.loyalty.v1.LoyaltyService.UpdateMembership:output_type -> moego.enterprise.loyalty.v1.UpdateMembershipResult
	7,  // 40: moego.enterprise.loyalty.v1.LoyaltyService.DeleteMembership:output_type -> moego.enterprise.loyalty.v1.DeleteMembershipResult
	9,  // 41: moego.enterprise.loyalty.v1.LoyaltyService.PushMembershipChanges:output_type -> moego.enterprise.loyalty.v1.PushMembershipChangesResult
	11, // 42: moego.enterprise.loyalty.v1.LoyaltyService.CreatePackage:output_type -> moego.enterprise.loyalty.v1.CreatePackageResult
	13, // 43: moego.enterprise.loyalty.v1.LoyaltyService.ListPackages:output_type -> moego.enterprise.loyalty.v1.ListPackagesResult
	15, // 44: moego.enterprise.loyalty.v1.LoyaltyService.UpdatePackage:output_type -> moego.enterprise.loyalty.v1.UpdatePackageResult
	17, // 45: moego.enterprise.loyalty.v1.LoyaltyService.DeletePackage:output_type -> moego.enterprise.loyalty.v1.DeletePackageResult
	19, // 46: moego.enterprise.loyalty.v1.LoyaltyService.PushPackageChanges:output_type -> moego.enterprise.loyalty.v1.PushPackageChangesResult
	21, // 47: moego.enterprise.loyalty.v1.LoyaltyService.CreateDiscount:output_type -> moego.enterprise.loyalty.v1.CreateDiscountResult
	23, // 48: moego.enterprise.loyalty.v1.LoyaltyService.ListDiscounts:output_type -> moego.enterprise.loyalty.v1.ListDiscountsResult
	25, // 49: moego.enterprise.loyalty.v1.LoyaltyService.UpdateDiscount:output_type -> moego.enterprise.loyalty.v1.UpdateDiscountResult
	27, // 50: moego.enterprise.loyalty.v1.LoyaltyService.DeleteDiscount:output_type -> moego.enterprise.loyalty.v1.DeleteDiscountResult
	31, // 51: moego.enterprise.loyalty.v1.LoyaltyService.GenerateDiscountCode:output_type -> moego.enterprise.loyalty.v1.GenerateDiscountCodeResult
	29, // 52: moego.enterprise.loyalty.v1.LoyaltyService.PushDiscountChanges:output_type -> moego.enterprise.loyalty.v1.PushDiscountChangesResult
	37, // [37:53] is the sub-list for method output_type
	21, // [21:37] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_moego_enterprise_loyalty_v1_loyalty_api_proto_init() }
func file_moego_enterprise_loyalty_v1_loyalty_api_proto_init() {
	if File_moego_enterprise_loyalty_v1_loyalty_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMembershipParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMembershipResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMembershipsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMembershipsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMembershipParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMembershipResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteMembershipParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteMembershipResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushMembershipChangesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushMembershipChangesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePackageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePackageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPackagesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPackagesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePackageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePackageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePackageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePackageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPackageChangesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPackageChangesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDiscountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDiscountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDiscountsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDiscountsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDiscountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDiscountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDiscountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDiscountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushDiscountChangesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushDiscountChangesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateDiscountCodeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateDiscountCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_loyalty_v1_loyalty_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_loyalty_v1_loyalty_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_loyalty_v1_loyalty_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_loyalty_v1_loyalty_api_proto = out.File
	file_moego_enterprise_loyalty_v1_loyalty_api_proto_rawDesc = nil
	file_moego_enterprise_loyalty_v1_loyalty_api_proto_goTypes = nil
	file_moego_enterprise_loyalty_v1_loyalty_api_proto_depIdxs = nil
}
