// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/fulfillment/v1/fulfillment_service.proto

package fulfillmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// FulfillmentServiceClient is the client API for FulfillmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FulfillmentServiceClient interface {
	// Enroll a pet into a group class instance
	EnrollPet(ctx context.Context, in *EnrollPetRequest, opts ...grpc.CallOption) (*EnrollPetResponse, error)
	// Remove a pet from a group class instance
	RemovePet(ctx context.Context, in *RemovePetRequest, opts ...grpc.CallOption) (*RemovePetResponse, error)
	// List fulfillment by filter
	ListFulfillments(ctx context.Context, in *ListFulfillmentsRequest, opts ...grpc.CallOption) (*ListFulfillmentsResponse, error)
	// Create fulfillment
	CreateFulfillment(ctx context.Context, in *CreateFulfillmentRequest, opts ...grpc.CallOption) (*CreateFulfillmentResponse, error)
	// Get fulfillment by id
	GetFulfillment(ctx context.Context, in *GetFulfillmentRequest, opts ...grpc.CallOption) (*GetFulfillmentResponse, error)
	// Update fulfillment by id
	UpdateFulfillment(ctx context.Context, in *UpdateFulfillmentRequest, opts ...grpc.CallOption) (*UpdateFulfillmentResponse, error)
	// Execute compensation task, sync fulfillment status pending_payment to unconfirmed
	ExecuteCompensationTask(ctx context.Context, in *ExecuteCompensationTaskRequest, opts ...grpc.CallOption) (*ExecuteCompensationTaskResponse, error)
}

type fulfillmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFulfillmentServiceClient(cc grpc.ClientConnInterface) FulfillmentServiceClient {
	return &fulfillmentServiceClient{cc}
}

func (c *fulfillmentServiceClient) EnrollPet(ctx context.Context, in *EnrollPetRequest, opts ...grpc.CallOption) (*EnrollPetResponse, error) {
	out := new(EnrollPetResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.FulfillmentService/EnrollPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentServiceClient) RemovePet(ctx context.Context, in *RemovePetRequest, opts ...grpc.CallOption) (*RemovePetResponse, error) {
	out := new(RemovePetResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.FulfillmentService/RemovePet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentServiceClient) ListFulfillments(ctx context.Context, in *ListFulfillmentsRequest, opts ...grpc.CallOption) (*ListFulfillmentsResponse, error) {
	out := new(ListFulfillmentsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.FulfillmentService/ListFulfillments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentServiceClient) CreateFulfillment(ctx context.Context, in *CreateFulfillmentRequest, opts ...grpc.CallOption) (*CreateFulfillmentResponse, error) {
	out := new(CreateFulfillmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.FulfillmentService/CreateFulfillment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentServiceClient) GetFulfillment(ctx context.Context, in *GetFulfillmentRequest, opts ...grpc.CallOption) (*GetFulfillmentResponse, error) {
	out := new(GetFulfillmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.FulfillmentService/GetFulfillment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentServiceClient) UpdateFulfillment(ctx context.Context, in *UpdateFulfillmentRequest, opts ...grpc.CallOption) (*UpdateFulfillmentResponse, error) {
	out := new(UpdateFulfillmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.FulfillmentService/UpdateFulfillment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentServiceClient) ExecuteCompensationTask(ctx context.Context, in *ExecuteCompensationTaskRequest, opts ...grpc.CallOption) (*ExecuteCompensationTaskResponse, error) {
	out := new(ExecuteCompensationTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.FulfillmentService/ExecuteCompensationTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FulfillmentServiceServer is the server API for FulfillmentService service.
// All implementations must embed UnimplementedFulfillmentServiceServer
// for forward compatibility
type FulfillmentServiceServer interface {
	// Enroll a pet into a group class instance
	EnrollPet(context.Context, *EnrollPetRequest) (*EnrollPetResponse, error)
	// Remove a pet from a group class instance
	RemovePet(context.Context, *RemovePetRequest) (*RemovePetResponse, error)
	// List fulfillment by filter
	ListFulfillments(context.Context, *ListFulfillmentsRequest) (*ListFulfillmentsResponse, error)
	// Create fulfillment
	CreateFulfillment(context.Context, *CreateFulfillmentRequest) (*CreateFulfillmentResponse, error)
	// Get fulfillment by id
	GetFulfillment(context.Context, *GetFulfillmentRequest) (*GetFulfillmentResponse, error)
	// Update fulfillment by id
	UpdateFulfillment(context.Context, *UpdateFulfillmentRequest) (*UpdateFulfillmentResponse, error)
	// Execute compensation task, sync fulfillment status pending_payment to unconfirmed
	ExecuteCompensationTask(context.Context, *ExecuteCompensationTaskRequest) (*ExecuteCompensationTaskResponse, error)
	mustEmbedUnimplementedFulfillmentServiceServer()
}

// UnimplementedFulfillmentServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFulfillmentServiceServer struct {
}

func (UnimplementedFulfillmentServiceServer) EnrollPet(context.Context, *EnrollPetRequest) (*EnrollPetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnrollPet not implemented")
}
func (UnimplementedFulfillmentServiceServer) RemovePet(context.Context, *RemovePetRequest) (*RemovePetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePet not implemented")
}
func (UnimplementedFulfillmentServiceServer) ListFulfillments(context.Context, *ListFulfillmentsRequest) (*ListFulfillmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFulfillments not implemented")
}
func (UnimplementedFulfillmentServiceServer) CreateFulfillment(context.Context, *CreateFulfillmentRequest) (*CreateFulfillmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFulfillment not implemented")
}
func (UnimplementedFulfillmentServiceServer) GetFulfillment(context.Context, *GetFulfillmentRequest) (*GetFulfillmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFulfillment not implemented")
}
func (UnimplementedFulfillmentServiceServer) UpdateFulfillment(context.Context, *UpdateFulfillmentRequest) (*UpdateFulfillmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFulfillment not implemented")
}
func (UnimplementedFulfillmentServiceServer) ExecuteCompensationTask(context.Context, *ExecuteCompensationTaskRequest) (*ExecuteCompensationTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteCompensationTask not implemented")
}
func (UnimplementedFulfillmentServiceServer) mustEmbedUnimplementedFulfillmentServiceServer() {}

// UnsafeFulfillmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FulfillmentServiceServer will
// result in compilation errors.
type UnsafeFulfillmentServiceServer interface {
	mustEmbedUnimplementedFulfillmentServiceServer()
}

func RegisterFulfillmentServiceServer(s grpc.ServiceRegistrar, srv FulfillmentServiceServer) {
	s.RegisterService(&FulfillmentService_ServiceDesc, srv)
}

func _FulfillmentService_EnrollPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnrollPetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).EnrollPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.FulfillmentService/EnrollPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).EnrollPet(ctx, req.(*EnrollPetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentService_RemovePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).RemovePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.FulfillmentService/RemovePet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).RemovePet(ctx, req.(*RemovePetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentService_ListFulfillments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFulfillmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).ListFulfillments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.FulfillmentService/ListFulfillments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).ListFulfillments(ctx, req.(*ListFulfillmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentService_CreateFulfillment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFulfillmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).CreateFulfillment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.FulfillmentService/CreateFulfillment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).CreateFulfillment(ctx, req.(*CreateFulfillmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentService_GetFulfillment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFulfillmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).GetFulfillment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.FulfillmentService/GetFulfillment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).GetFulfillment(ctx, req.(*GetFulfillmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentService_UpdateFulfillment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFulfillmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).UpdateFulfillment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.FulfillmentService/UpdateFulfillment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).UpdateFulfillment(ctx, req.(*UpdateFulfillmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentService_ExecuteCompensationTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteCompensationTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).ExecuteCompensationTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.FulfillmentService/ExecuteCompensationTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).ExecuteCompensationTask(ctx, req.(*ExecuteCompensationTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FulfillmentService_ServiceDesc is the grpc.ServiceDesc for FulfillmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FulfillmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.fulfillment.v1.FulfillmentService",
	HandlerType: (*FulfillmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "EnrollPet",
			Handler:    _FulfillmentService_EnrollPet_Handler,
		},
		{
			MethodName: "RemovePet",
			Handler:    _FulfillmentService_RemovePet_Handler,
		},
		{
			MethodName: "ListFulfillments",
			Handler:    _FulfillmentService_ListFulfillments_Handler,
		},
		{
			MethodName: "CreateFulfillment",
			Handler:    _FulfillmentService_CreateFulfillment_Handler,
		},
		{
			MethodName: "GetFulfillment",
			Handler:    _FulfillmentService_GetFulfillment_Handler,
		},
		{
			MethodName: "UpdateFulfillment",
			Handler:    _FulfillmentService_UpdateFulfillment_Handler,
		},
		{
			MethodName: "ExecuteCompensationTask",
			Handler:    _FulfillmentService_ExecuteCompensationTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/fulfillment/v1/fulfillment_service.proto",
}
