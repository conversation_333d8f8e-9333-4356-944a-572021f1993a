// @since 2025-03-31 15:46:22
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/fulfillment/v1/fulfillment_service.proto

package fulfillmentsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The request for enrolling a pet into a group class instance
type EnrollPetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The business ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The training group class instance ID
	InstanceId int64 `protobuf:"varint,2,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	// The pet's ID
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The company ID
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// The fulfillment source
	Source v1.Source `protobuf:"varint,5,opt,name=source,proto3,enum=moego.models.fulfillment.v1.Source" json:"source,omitempty"`
	// The enrolled staff
	StaffId *int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *EnrollPetRequest) Reset() {
	*x = EnrollPetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrollPetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrollPetRequest) ProtoMessage() {}

func (x *EnrollPetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrollPetRequest.ProtoReflect.Descriptor instead.
func (*EnrollPetRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{0}
}

func (x *EnrollPetRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *EnrollPetRequest) GetInstanceId() int64 {
	if x != nil {
		return x.InstanceId
	}
	return 0
}

func (x *EnrollPetRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *EnrollPetRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *EnrollPetRequest) GetSource() v1.Source {
	if x != nil {
		return x.Source
	}
	return v1.Source(0)
}

func (x *EnrollPetRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// The response for enrolling a pet into a group class instance
type EnrollPetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The order ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// The fulfillment ID
	FulfillmentId int64 `protobuf:"varint,2,opt,name=fulfillment_id,json=fulfillmentId,proto3" json:"fulfillment_id,omitempty"`
}

func (x *EnrollPetResponse) Reset() {
	*x = EnrollPetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrollPetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrollPetResponse) ProtoMessage() {}

func (x *EnrollPetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrollPetResponse.ProtoReflect.Descriptor instead.
func (*EnrollPetResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{1}
}

func (x *EnrollPetResponse) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *EnrollPetResponse) GetFulfillmentId() int64 {
	if x != nil {
		return x.FulfillmentId
	}
	return 0
}

// The request for removing a pet from a group class instance
type RemovePetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The company ID, optional
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// The training group class instance ID
	InstanceId int64 `protobuf:"varint,2,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	// The pet's ID
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// Whether to auto refund the order, default is false
	AutoRefundOrder bool `protobuf:"varint,4,opt,name=auto_refund_order,json=autoRefundOrder,proto3" json:"auto_refund_order,omitempty"`
}

func (x *RemovePetRequest) Reset() {
	*x = RemovePetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemovePetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePetRequest) ProtoMessage() {}

func (x *RemovePetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePetRequest.ProtoReflect.Descriptor instead.
func (*RemovePetRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{2}
}

func (x *RemovePetRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *RemovePetRequest) GetInstanceId() int64 {
	if x != nil {
		return x.InstanceId
	}
	return 0
}

func (x *RemovePetRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *RemovePetRequest) GetAutoRefundOrder() bool {
	if x != nil {
		return x.AutoRefundOrder
	}
	return false
}

// The response for removing a pet from a group class instance
type RemovePetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemovePetResponse) Reset() {
	*x = RemovePetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemovePetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePetResponse) ProtoMessage() {}

func (x *RemovePetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePetResponse.ProtoReflect.Descriptor instead.
func (*RemovePetResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{3}
}

// The request for ListFulfillments
type ListFulfillmentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// The business ID
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// The filter
	Filter *ListFulfillmentsRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// The pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListFulfillmentsRequest) Reset() {
	*x = ListFulfillmentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFulfillmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentsRequest) ProtoMessage() {}

func (x *ListFulfillmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentsRequest.ProtoReflect.Descriptor instead.
func (*ListFulfillmentsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListFulfillmentsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListFulfillmentsRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListFulfillmentsRequest) GetFilter() *ListFulfillmentsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListFulfillmentsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// The response for ListFulfillments
type ListFulfillmentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fulfillment list
	Fulfillments []*v1.FulfillmentModel `protobuf:"bytes,1,rep,name=fulfillments,proto3" json:"fulfillments,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListFulfillmentsResponse) Reset() {
	*x = ListFulfillmentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFulfillmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentsResponse) ProtoMessage() {}

func (x *ListFulfillmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentsResponse.ProtoReflect.Descriptor instead.
func (*ListFulfillmentsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListFulfillmentsResponse) GetFulfillments() []*v1.FulfillmentModel {
	if x != nil {
		return x.Fulfillments
	}
	return nil
}

func (x *ListFulfillmentsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// The request for CreateFulfillment
type CreateFulfillmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fulfillment detail
	Fulfillment *v1.FulfillmentCreateDef `protobuf:"bytes,1,opt,name=fulfillment,proto3" json:"fulfillment,omitempty"`
	// Group class create detail
	GroupClasses []*v1.GroupClassCreateDef `protobuf:"bytes,2,rep,name=group_classes,json=groupClasses,proto3" json:"group_classes,omitempty"`
}

func (x *CreateFulfillmentRequest) Reset() {
	*x = CreateFulfillmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateFulfillmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFulfillmentRequest) ProtoMessage() {}

func (x *CreateFulfillmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFulfillmentRequest.ProtoReflect.Descriptor instead.
func (*CreateFulfillmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{6}
}

func (x *CreateFulfillmentRequest) GetFulfillment() *v1.FulfillmentCreateDef {
	if x != nil {
		return x.Fulfillment
	}
	return nil
}

func (x *CreateFulfillmentRequest) GetGroupClasses() []*v1.GroupClassCreateDef {
	if x != nil {
		return x.GroupClasses
	}
	return nil
}

// The response for CreateFulfillment
type CreateFulfillmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fulfillment ID
	FulfillmentId int64 `protobuf:"varint,1,opt,name=fulfillment_id,json=fulfillmentId,proto3" json:"fulfillment_id,omitempty"`
}

func (x *CreateFulfillmentResponse) Reset() {
	*x = CreateFulfillmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateFulfillmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFulfillmentResponse) ProtoMessage() {}

func (x *CreateFulfillmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFulfillmentResponse.ProtoReflect.Descriptor instead.
func (*CreateFulfillmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{7}
}

func (x *CreateFulfillmentResponse) GetFulfillmentId() int64 {
	if x != nil {
		return x.FulfillmentId
	}
	return 0
}

// The request for GetFulfillment
type GetFulfillmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fulfillment ID
	FulfillmentId int64 `protobuf:"varint,1,opt,name=fulfillment_id,json=fulfillmentId,proto3" json:"fulfillment_id,omitempty"`
}

func (x *GetFulfillmentRequest) Reset() {
	*x = GetFulfillmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFulfillmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentRequest) ProtoMessage() {}

func (x *GetFulfillmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentRequest.ProtoReflect.Descriptor instead.
func (*GetFulfillmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetFulfillmentRequest) GetFulfillmentId() int64 {
	if x != nil {
		return x.FulfillmentId
	}
	return 0
}

// The response for GetFulfillment
type GetFulfillmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fulfillment detail
	Fulfillment *v1.FulfillmentModel `protobuf:"bytes,1,opt,name=fulfillment,proto3" json:"fulfillment,omitempty"`
}

func (x *GetFulfillmentResponse) Reset() {
	*x = GetFulfillmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFulfillmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFulfillmentResponse) ProtoMessage() {}

func (x *GetFulfillmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFulfillmentResponse.ProtoReflect.Descriptor instead.
func (*GetFulfillmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetFulfillmentResponse) GetFulfillment() *v1.FulfillmentModel {
	if x != nil {
		return x.Fulfillment
	}
	return nil
}

// The message for UpdateFulfillment
type UpdateFulfillmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fulfillment update def
	Fulfillment *v1.FulfillmentUpdateDef `protobuf:"bytes,2,opt,name=fulfillment,proto3" json:"fulfillment,omitempty"`
}

func (x *UpdateFulfillmentRequest) Reset() {
	*x = UpdateFulfillmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFulfillmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFulfillmentRequest) ProtoMessage() {}

func (x *UpdateFulfillmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFulfillmentRequest.ProtoReflect.Descriptor instead.
func (*UpdateFulfillmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateFulfillmentRequest) GetFulfillment() *v1.FulfillmentUpdateDef {
	if x != nil {
		return x.Fulfillment
	}
	return nil
}

// The message for UpdateFulfillment
type UpdateFulfillmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateFulfillmentResponse) Reset() {
	*x = UpdateFulfillmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFulfillmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFulfillmentResponse) ProtoMessage() {}

func (x *UpdateFulfillmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFulfillmentResponse.ProtoReflect.Descriptor instead.
func (*UpdateFulfillmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{11}
}

// The request for ExecuteCompensationTask
type ExecuteCompensationTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExecuteCompensationTaskRequest) Reset() {
	*x = ExecuteCompensationTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteCompensationTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteCompensationTaskRequest) ProtoMessage() {}

func (x *ExecuteCompensationTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteCompensationTaskRequest.ProtoReflect.Descriptor instead.
func (*ExecuteCompensationTaskRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{12}
}

// The response for ExecuteCompensationTask
type ExecuteCompensationTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExecuteCompensationTaskResponse) Reset() {
	*x = ExecuteCompensationTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteCompensationTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteCompensationTaskResponse) ProtoMessage() {}

func (x *ExecuteCompensationTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteCompensationTaskResponse.ProtoReflect.Descriptor instead.
func (*ExecuteCompensationTaskResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{13}
}

// The filter
type ListFulfillmentsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet id
	PetIds []int64 `protobuf:"varint,1,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// The service id
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// The customer ids
	CustomerIds []int64 `protobuf:"varint,3,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// The status
	Statuses []v1.Status `protobuf:"varint,4,rep,packed,name=statuses,proto3,enum=moego.models.fulfillment.v1.Status" json:"statuses,omitempty"`
	// The fulfillment id
	FulfillmentIds []int64 `protobuf:"varint,5,rep,packed,name=fulfillment_ids,json=fulfillmentIds,proto3" json:"fulfillment_ids,omitempty"`
}

func (x *ListFulfillmentsRequest_Filter) Reset() {
	*x = ListFulfillmentsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFulfillmentsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentsRequest_Filter) ProtoMessage() {}

func (x *ListFulfillmentsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListFulfillmentsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListFulfillmentsRequest_Filter) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *ListFulfillmentsRequest_Filter) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *ListFulfillmentsRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListFulfillmentsRequest_Filter) GetStatuses() []v1.Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *ListFulfillmentsRequest_Filter) GetFulfillmentIds() []int64 {
	if x != nil {
		return x.FulfillmentIds
	}
	return nil
}

var File_moego_service_fulfillment_v1_fulfillment_service_proto protoreflect.FileDescriptor

var file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x66,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x75,
	0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xad, 0x02, 0x0a,
	0x10, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05,
	0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x47, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x22, 0x55, 0x0a, 0x11,
	0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0xc4, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xd2, 0x04, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01,
	0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x54, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x46,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x1a, 0xac, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x29, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12,
	0x33, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18,
	0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x12, 0x54, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x13, 0xfa, 0x42, 0x10,
	0x92, 0x01, 0x0d, 0x10, 0x64, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x0f, 0x66, 0x75,
	0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xc5, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x51, 0x0a, 0x0c, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xc6, 0x01, 0x0a,
	0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x66, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75,
	0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x66, 0x52, 0x0b, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x55,
	0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x65, 0x73, 0x22, 0x42, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x66, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x47, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0d, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0x69, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0b,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0b, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x6f, 0x0a,
	0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x66, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75,
	0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x66, 0x52, 0x0b, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x1b,
	0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x20, 0x0a, 0x1e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x65, 0x6e, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x21, 0x0a,
	0x1f, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x65, 0x6e, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x32, 0x98, 0x07, 0x0a, 0x12, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6c, 0x0a, 0x09, 0x45, 0x6e, 0x72, 0x6f, 0x6c,
	0x6c, 0x50, 0x65, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x09, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50,
	0x65, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75,
	0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x11,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x17, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x65, 0x6e, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x65, 0x6e, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x65, 0x6e, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x24,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescOnce sync.Once
	file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescData = file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDesc
)

func file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescGZIP() []byte {
	file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescOnce.Do(func() {
		file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescData)
	})
	return file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDescData
}

var file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_moego_service_fulfillment_v1_fulfillment_service_proto_goTypes = []interface{}{
	(*EnrollPetRequest)(nil),                // 0: moego.service.fulfillment.v1.EnrollPetRequest
	(*EnrollPetResponse)(nil),               // 1: moego.service.fulfillment.v1.EnrollPetResponse
	(*RemovePetRequest)(nil),                // 2: moego.service.fulfillment.v1.RemovePetRequest
	(*RemovePetResponse)(nil),               // 3: moego.service.fulfillment.v1.RemovePetResponse
	(*ListFulfillmentsRequest)(nil),         // 4: moego.service.fulfillment.v1.ListFulfillmentsRequest
	(*ListFulfillmentsResponse)(nil),        // 5: moego.service.fulfillment.v1.ListFulfillmentsResponse
	(*CreateFulfillmentRequest)(nil),        // 6: moego.service.fulfillment.v1.CreateFulfillmentRequest
	(*CreateFulfillmentResponse)(nil),       // 7: moego.service.fulfillment.v1.CreateFulfillmentResponse
	(*GetFulfillmentRequest)(nil),           // 8: moego.service.fulfillment.v1.GetFulfillmentRequest
	(*GetFulfillmentResponse)(nil),          // 9: moego.service.fulfillment.v1.GetFulfillmentResponse
	(*UpdateFulfillmentRequest)(nil),        // 10: moego.service.fulfillment.v1.UpdateFulfillmentRequest
	(*UpdateFulfillmentResponse)(nil),       // 11: moego.service.fulfillment.v1.UpdateFulfillmentResponse
	(*ExecuteCompensationTaskRequest)(nil),  // 12: moego.service.fulfillment.v1.ExecuteCompensationTaskRequest
	(*ExecuteCompensationTaskResponse)(nil), // 13: moego.service.fulfillment.v1.ExecuteCompensationTaskResponse
	(*ListFulfillmentsRequest_Filter)(nil),  // 14: moego.service.fulfillment.v1.ListFulfillmentsRequest.Filter
	(v1.Source)(0),                          // 15: moego.models.fulfillment.v1.Source
	(*v2.PaginationRequest)(nil),            // 16: moego.utils.v2.PaginationRequest
	(*v1.FulfillmentModel)(nil),             // 17: moego.models.fulfillment.v1.FulfillmentModel
	(*v2.PaginationResponse)(nil),           // 18: moego.utils.v2.PaginationResponse
	(*v1.FulfillmentCreateDef)(nil),         // 19: moego.models.fulfillment.v1.FulfillmentCreateDef
	(*v1.GroupClassCreateDef)(nil),          // 20: moego.models.fulfillment.v1.GroupClassCreateDef
	(*v1.FulfillmentUpdateDef)(nil),         // 21: moego.models.fulfillment.v1.FulfillmentUpdateDef
	(v1.Status)(0),                          // 22: moego.models.fulfillment.v1.Status
}
var file_moego_service_fulfillment_v1_fulfillment_service_proto_depIdxs = []int32{
	15, // 0: moego.service.fulfillment.v1.EnrollPetRequest.source:type_name -> moego.models.fulfillment.v1.Source
	14, // 1: moego.service.fulfillment.v1.ListFulfillmentsRequest.filter:type_name -> moego.service.fulfillment.v1.ListFulfillmentsRequest.Filter
	16, // 2: moego.service.fulfillment.v1.ListFulfillmentsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	17, // 3: moego.service.fulfillment.v1.ListFulfillmentsResponse.fulfillments:type_name -> moego.models.fulfillment.v1.FulfillmentModel
	18, // 4: moego.service.fulfillment.v1.ListFulfillmentsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	19, // 5: moego.service.fulfillment.v1.CreateFulfillmentRequest.fulfillment:type_name -> moego.models.fulfillment.v1.FulfillmentCreateDef
	20, // 6: moego.service.fulfillment.v1.CreateFulfillmentRequest.group_classes:type_name -> moego.models.fulfillment.v1.GroupClassCreateDef
	17, // 7: moego.service.fulfillment.v1.GetFulfillmentResponse.fulfillment:type_name -> moego.models.fulfillment.v1.FulfillmentModel
	21, // 8: moego.service.fulfillment.v1.UpdateFulfillmentRequest.fulfillment:type_name -> moego.models.fulfillment.v1.FulfillmentUpdateDef
	22, // 9: moego.service.fulfillment.v1.ListFulfillmentsRequest.Filter.statuses:type_name -> moego.models.fulfillment.v1.Status
	0,  // 10: moego.service.fulfillment.v1.FulfillmentService.EnrollPet:input_type -> moego.service.fulfillment.v1.EnrollPetRequest
	2,  // 11: moego.service.fulfillment.v1.FulfillmentService.RemovePet:input_type -> moego.service.fulfillment.v1.RemovePetRequest
	4,  // 12: moego.service.fulfillment.v1.FulfillmentService.ListFulfillments:input_type -> moego.service.fulfillment.v1.ListFulfillmentsRequest
	6,  // 13: moego.service.fulfillment.v1.FulfillmentService.CreateFulfillment:input_type -> moego.service.fulfillment.v1.CreateFulfillmentRequest
	8,  // 14: moego.service.fulfillment.v1.FulfillmentService.GetFulfillment:input_type -> moego.service.fulfillment.v1.GetFulfillmentRequest
	10, // 15: moego.service.fulfillment.v1.FulfillmentService.UpdateFulfillment:input_type -> moego.service.fulfillment.v1.UpdateFulfillmentRequest
	12, // 16: moego.service.fulfillment.v1.FulfillmentService.ExecuteCompensationTask:input_type -> moego.service.fulfillment.v1.ExecuteCompensationTaskRequest
	1,  // 17: moego.service.fulfillment.v1.FulfillmentService.EnrollPet:output_type -> moego.service.fulfillment.v1.EnrollPetResponse
	3,  // 18: moego.service.fulfillment.v1.FulfillmentService.RemovePet:output_type -> moego.service.fulfillment.v1.RemovePetResponse
	5,  // 19: moego.service.fulfillment.v1.FulfillmentService.ListFulfillments:output_type -> moego.service.fulfillment.v1.ListFulfillmentsResponse
	7,  // 20: moego.service.fulfillment.v1.FulfillmentService.CreateFulfillment:output_type -> moego.service.fulfillment.v1.CreateFulfillmentResponse
	9,  // 21: moego.service.fulfillment.v1.FulfillmentService.GetFulfillment:output_type -> moego.service.fulfillment.v1.GetFulfillmentResponse
	11, // 22: moego.service.fulfillment.v1.FulfillmentService.UpdateFulfillment:output_type -> moego.service.fulfillment.v1.UpdateFulfillmentResponse
	13, // 23: moego.service.fulfillment.v1.FulfillmentService.ExecuteCompensationTask:output_type -> moego.service.fulfillment.v1.ExecuteCompensationTaskResponse
	17, // [17:24] is the sub-list for method output_type
	10, // [10:17] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_service_fulfillment_v1_fulfillment_service_proto_init() }
func file_moego_service_fulfillment_v1_fulfillment_service_proto_init() {
	if File_moego_service_fulfillment_v1_fulfillment_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrollPetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrollPetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemovePetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemovePetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFulfillmentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFulfillmentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateFulfillmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateFulfillmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFulfillmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFulfillmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFulfillmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFulfillmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecuteCompensationTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecuteCompensationTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFulfillmentsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_fulfillment_v1_fulfillment_service_proto_goTypes,
		DependencyIndexes: file_moego_service_fulfillment_v1_fulfillment_service_proto_depIdxs,
		MessageInfos:      file_moego_service_fulfillment_v1_fulfillment_service_proto_msgTypes,
	}.Build()
	File_moego_service_fulfillment_v1_fulfillment_service_proto = out.File
	file_moego_service_fulfillment_v1_fulfillment_service_proto_rawDesc = nil
	file_moego_service_fulfillment_v1_fulfillment_service_proto_goTypes = nil
	file_moego_service_fulfillment_v1_fulfillment_service_proto_depIdxs = nil
}
