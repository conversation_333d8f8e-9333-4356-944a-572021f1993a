// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/file/v2/file_service.proto

package filesvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/file/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetDownloadPresignedUrlRequest
type GetDownloadPresignedUrlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file identity
	//
	// Types that are assignable to FileIdentity:
	//
	//	*GetDownloadPresignedUrlRequest_FileId
	//	*GetDownloadPresignedUrlRequest_S3Path
	FileIdentity isGetDownloadPresignedUrlRequest_FileIdentity `protobuf_oneof:"file_identity"`
	// The effective duration of the download url, in seconds, default: 300
	Duration *int32 `protobuf:"varint,8,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
}

func (x *GetDownloadPresignedUrlRequest) Reset() {
	*x = GetDownloadPresignedUrlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDownloadPresignedUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDownloadPresignedUrlRequest) ProtoMessage() {}

func (x *GetDownloadPresignedUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDownloadPresignedUrlRequest.ProtoReflect.Descriptor instead.
func (*GetDownloadPresignedUrlRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{0}
}

func (m *GetDownloadPresignedUrlRequest) GetFileIdentity() isGetDownloadPresignedUrlRequest_FileIdentity {
	if m != nil {
		return m.FileIdentity
	}
	return nil
}

func (x *GetDownloadPresignedUrlRequest) GetFileId() int64 {
	if x, ok := x.GetFileIdentity().(*GetDownloadPresignedUrlRequest_FileId); ok {
		return x.FileId
	}
	return 0
}

func (x *GetDownloadPresignedUrlRequest) GetS3Path() string {
	if x, ok := x.GetFileIdentity().(*GetDownloadPresignedUrlRequest_S3Path); ok {
		return x.S3Path
	}
	return ""
}

func (x *GetDownloadPresignedUrlRequest) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

type isGetDownloadPresignedUrlRequest_FileIdentity interface {
	isGetDownloadPresignedUrlRequest_FileIdentity()
}

type GetDownloadPresignedUrlRequest_FileId struct {
	// file id in MoeGo
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3,oneof"`
}

type GetDownloadPresignedUrlRequest_S3Path struct {
	// aws s3 path, like: s3://bucket/path...
	S3Path string `protobuf:"bytes,2,opt,name=s3_path,json=s3Path,proto3,oneof"`
}

func (*GetDownloadPresignedUrlRequest_FileId) isGetDownloadPresignedUrlRequest_FileIdentity() {}

func (*GetDownloadPresignedUrlRequest_S3Path) isGetDownloadPresignedUrlRequest_FileIdentity() {}

// GetDownloadPresignedUrlResponse
type GetDownloadPresignedUrlResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// url for download
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *GetDownloadPresignedUrlResponse) Reset() {
	*x = GetDownloadPresignedUrlResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDownloadPresignedUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDownloadPresignedUrlResponse) ProtoMessage() {}

func (x *GetDownloadPresignedUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDownloadPresignedUrlResponse.ProtoReflect.Descriptor instead.
func (*GetDownloadPresignedUrlResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetDownloadPresignedUrlResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// GetUploadPresignedUrlRequest
type GetUploadPresignedUrlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id for the creator
	CreatorId int64 `protobuf:"varint,1,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// usage for the file ,eg: photo,avatar...
	Usage string `protobuf:"bytes,2,opt,name=usage,proto3" json:"usage,omitempty"`
	// file md5 after base64
	Md5 string `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	// file name(with extension)
	FileName string `protobuf:"bytes,4,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// file size byte
	FileSizeByte int64 `protobuf:"varint,5,opt,name=file_size_byte,json=fileSizeByte,proto3" json:"file_size_byte,omitempty"`
	// owner type,eg:staff,pet...
	OwnerType string `protobuf:"bytes,6,opt,name=owner_type,json=ownerType,proto3" json:"owner_type,omitempty"`
	// owner id
	OwnerId int64 `protobuf:"varint,7,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// source
	//
	// Types that are assignable to Source:
	//
	//	*GetUploadPresignedUrlRequest_Platform
	//	*GetUploadPresignedUrlRequest_Tenant
	Source isGetUploadPresignedUrlRequest_Source `protobuf_oneof:"source"`
	// file metadata
	Metadata map[string]string `protobuf:"bytes,14,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetUploadPresignedUrlRequest) Reset() {
	*x = GetUploadPresignedUrlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUploadPresignedUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUploadPresignedUrlRequest) ProtoMessage() {}

func (x *GetUploadPresignedUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUploadPresignedUrlRequest.ProtoReflect.Descriptor instead.
func (*GetUploadPresignedUrlRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetUploadPresignedUrlRequest) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *GetUploadPresignedUrlRequest) GetUsage() string {
	if x != nil {
		return x.Usage
	}
	return ""
}

func (x *GetUploadPresignedUrlRequest) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *GetUploadPresignedUrlRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *GetUploadPresignedUrlRequest) GetFileSizeByte() int64 {
	if x != nil {
		return x.FileSizeByte
	}
	return 0
}

func (x *GetUploadPresignedUrlRequest) GetOwnerType() string {
	if x != nil {
		return x.OwnerType
	}
	return ""
}

func (x *GetUploadPresignedUrlRequest) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (m *GetUploadPresignedUrlRequest) GetSource() isGetUploadPresignedUrlRequest_Source {
	if m != nil {
		return m.Source
	}
	return nil
}

func (x *GetUploadPresignedUrlRequest) GetPlatform() *v2.PlatformSourceDef {
	if x, ok := x.GetSource().(*GetUploadPresignedUrlRequest_Platform); ok {
		return x.Platform
	}
	return nil
}

func (x *GetUploadPresignedUrlRequest) GetTenant() *v2.TenantSourceDef {
	if x, ok := x.GetSource().(*GetUploadPresignedUrlRequest_Tenant); ok {
		return x.Tenant
	}
	return nil
}

func (x *GetUploadPresignedUrlRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type isGetUploadPresignedUrlRequest_Source interface {
	isGetUploadPresignedUrlRequest_Source()
}

type GetUploadPresignedUrlRequest_Platform struct {
	// platform
	Platform *v2.PlatformSourceDef `protobuf:"bytes,8,opt,name=platform,proto3,oneof"`
}

type GetUploadPresignedUrlRequest_Tenant struct {
	// tenant
	Tenant *v2.TenantSourceDef `protobuf:"bytes,9,opt,name=tenant,proto3,oneof"`
}

func (*GetUploadPresignedUrlRequest_Platform) isGetUploadPresignedUrlRequest_Source() {}

func (*GetUploadPresignedUrlRequest_Tenant) isGetUploadPresignedUrlRequest_Source() {}

// GetPresignedUrlResponse
type GetUploadPresignedUrlResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// presigned url for upload
	PresignedUrl string `protobuf:"bytes,1,opt,name=presigned_url,json=presignedUrl,proto3" json:"presigned_url,omitempty"`
	// access url for download
	AccessUrl string `protobuf:"bytes,2,opt,name=access_url,json=accessUrl,proto3" json:"access_url,omitempty"`
	// deprecated, already contains in the metadata
	//
	// Deprecated: Do not use.
	ContentType string `protobuf:"bytes,3,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	// file id
	FileId int64 `protobuf:"varint,4,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	// the headers used for later uploads, must contain Content-Type and Content-MD5.
	Metadata map[string]string `protobuf:"bytes,8,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetUploadPresignedUrlResponse) Reset() {
	*x = GetUploadPresignedUrlResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUploadPresignedUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUploadPresignedUrlResponse) ProtoMessage() {}

func (x *GetUploadPresignedUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUploadPresignedUrlResponse.ProtoReflect.Descriptor instead.
func (*GetUploadPresignedUrlResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetUploadPresignedUrlResponse) GetPresignedUrl() string {
	if x != nil {
		return x.PresignedUrl
	}
	return ""
}

func (x *GetUploadPresignedUrlResponse) GetAccessUrl() string {
	if x != nil {
		return x.AccessUrl
	}
	return ""
}

// Deprecated: Do not use.
func (x *GetUploadPresignedUrlResponse) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *GetUploadPresignedUrlResponse) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

func (x *GetUploadPresignedUrlResponse) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// CreateExportTaskRequest
type CreateExportFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id for the creator
	CreatorId int64 `protobuf:"varint,1,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// usage for the file, must be one of FileUsage
	Usage string `protobuf:"bytes,2,opt,name=usage,proto3" json:"usage,omitempty"`
	// file name(with extension)
	FileName string `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// owner type,eg:staff,pet...
	OwnerType string `protobuf:"bytes,4,opt,name=owner_type,json=ownerType,proto3" json:"owner_type,omitempty"`
	// owner id
	OwnerId int64 `protobuf:"varint,5,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// source
	//
	// Types that are assignable to Source:
	//
	//	*CreateExportFileRequest_Platform
	//	*CreateExportFileRequest_Tenant
	Source isCreateExportFileRequest_Source `protobuf_oneof:"source"`
	// content disposition for download, deprecated, please set in metadata.
	//
	// Deprecated: Do not use.
	ContentDisposition *string `protobuf:"bytes,8,opt,name=content_disposition,json=contentDisposition,proto3,oneof" json:"content_disposition,omitempty"`
	// file metadata
	Metadata map[string]string `protobuf:"bytes,14,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CreateExportFileRequest) Reset() {
	*x = CreateExportFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExportFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExportFileRequest) ProtoMessage() {}

func (x *CreateExportFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExportFileRequest.ProtoReflect.Descriptor instead.
func (*CreateExportFileRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateExportFileRequest) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *CreateExportFileRequest) GetUsage() string {
	if x != nil {
		return x.Usage
	}
	return ""
}

func (x *CreateExportFileRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *CreateExportFileRequest) GetOwnerType() string {
	if x != nil {
		return x.OwnerType
	}
	return ""
}

func (x *CreateExportFileRequest) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (m *CreateExportFileRequest) GetSource() isCreateExportFileRequest_Source {
	if m != nil {
		return m.Source
	}
	return nil
}

func (x *CreateExportFileRequest) GetPlatform() *v2.PlatformSourceDef {
	if x, ok := x.GetSource().(*CreateExportFileRequest_Platform); ok {
		return x.Platform
	}
	return nil
}

func (x *CreateExportFileRequest) GetTenant() *v2.TenantSourceDef {
	if x, ok := x.GetSource().(*CreateExportFileRequest_Tenant); ok {
		return x.Tenant
	}
	return nil
}

// Deprecated: Do not use.
func (x *CreateExportFileRequest) GetContentDisposition() string {
	if x != nil && x.ContentDisposition != nil {
		return *x.ContentDisposition
	}
	return ""
}

func (x *CreateExportFileRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type isCreateExportFileRequest_Source interface {
	isCreateExportFileRequest_Source()
}

type CreateExportFileRequest_Platform struct {
	// platform
	Platform *v2.PlatformSourceDef `protobuf:"bytes,6,opt,name=platform,proto3,oneof"`
}

type CreateExportFileRequest_Tenant struct {
	// tenant
	Tenant *v2.TenantSourceDef `protobuf:"bytes,7,opt,name=tenant,proto3,oneof"`
}

func (*CreateExportFileRequest_Platform) isCreateExportFileRequest_Source() {}

func (*CreateExportFileRequest_Tenant) isCreateExportFileRequest_Source() {}

// CreateExportTaskResponse
type CreateExportFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
}

func (x *CreateExportFileResponse) Reset() {
	*x = CreateExportFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExportFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExportFileResponse) ProtoMessage() {}

func (x *CreateExportFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExportFileResponse.ProtoReflect.Descriptor instead.
func (*CreateExportFileResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateExportFileResponse) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

// QueryFileRequest
type QueryFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
}

func (x *QueryFileRequest) Reset() {
	*x = QueryFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryFileRequest) ProtoMessage() {}

func (x *QueryFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryFileRequest.ProtoReflect.Descriptor instead.
func (*QueryFileRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{6}
}

func (x *QueryFileRequest) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

// QueryFileResponse
type QueryFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file info
	File *v2.FileModel `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
}

func (x *QueryFileResponse) Reset() {
	*x = QueryFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryFileResponse) ProtoMessage() {}

func (x *QueryFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryFileResponse.ProtoReflect.Descriptor instead.
func (*QueryFileResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{7}
}

func (x *QueryFileResponse) GetFile() *v2.FileModel {
	if x != nil {
		return x.File
	}
	return nil
}

// UploadExportFileRequest
type UploadExportFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	// file content
	FileContent []byte `protobuf:"bytes,2,opt,name=file_content,json=fileContent,proto3" json:"file_content,omitempty"`
}

func (x *UploadExportFileRequest) Reset() {
	*x = UploadExportFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadExportFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadExportFileRequest) ProtoMessage() {}

func (x *UploadExportFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadExportFileRequest.ProtoReflect.Descriptor instead.
func (*UploadExportFileRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{8}
}

func (x *UploadExportFileRequest) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

func (x *UploadExportFileRequest) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

// UploadExportFileResponse
type UploadExportFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file status
	Status v2.FileStatus `protobuf:"varint,1,opt,name=status,proto3,enum=moego.models.file.v2.FileStatus" json:"status,omitempty"`
}

func (x *UploadExportFileResponse) Reset() {
	*x = UploadExportFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadExportFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadExportFileResponse) ProtoMessage() {}

func (x *UploadExportFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadExportFileResponse.ProtoReflect.Descriptor instead.
func (*UploadExportFileResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{9}
}

func (x *UploadExportFileResponse) GetStatus() v2.FileStatus {
	if x != nil {
		return x.Status
	}
	return v2.FileStatus(0)
}

// UpdateFileStatusRequest
type UpdateFileStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	// file status
	Status v2.FileStatus `protobuf:"varint,2,opt,name=status,proto3,enum=moego.models.file.v2.FileStatus" json:"status,omitempty"`
}

func (x *UpdateFileStatusRequest) Reset() {
	*x = UpdateFileStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFileStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFileStatusRequest) ProtoMessage() {}

func (x *UpdateFileStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFileStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateFileStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateFileStatusRequest) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

func (x *UpdateFileStatusRequest) GetStatus() v2.FileStatus {
	if x != nil {
		return x.Status
	}
	return v2.FileStatus(0)
}

// UpdateFileStatusResponse
type UpdateFileStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateFileStatusResponse) Reset() {
	*x = UpdateFileStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFileStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFileStatusResponse) ProtoMessage() {}

func (x *UpdateFileStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFileStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateFileStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{11}
}

// upload file
type UploadFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id for the creator
	CreatorId int64 `protobuf:"varint,1,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// usage for the file ,eg: photo,avatar...
	Usage string `protobuf:"bytes,2,opt,name=usage,proto3" json:"usage,omitempty"`
	// file content
	FileContent []byte `protobuf:"bytes,3,opt,name=file_content,json=fileContent,proto3" json:"file_content,omitempty"`
	// file name(with extension)
	FileName string `protobuf:"bytes,4,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// owner type,eg:staff,pet...
	OwnerType string `protobuf:"bytes,5,opt,name=owner_type,json=ownerType,proto3" json:"owner_type,omitempty"`
	// owner id
	OwnerId int64 `protobuf:"varint,6,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// source
	//
	// Types that are assignable to Source:
	//
	//	*UploadFileRequest_Platform
	//	*UploadFileRequest_Tenant
	Source isUploadFileRequest_Source `protobuf_oneof:"source"`
	// content disposition for download, deprecated, please set in metadata.
	//
	// Deprecated: Do not use.
	ContentDisposition *string `protobuf:"bytes,9,opt,name=content_disposition,json=contentDisposition,proto3,oneof" json:"content_disposition,omitempty"`
	// file metadata
	Metadata map[string]string `protobuf:"bytes,14,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UploadFileRequest) Reset() {
	*x = UploadFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileRequest) ProtoMessage() {}

func (x *UploadFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileRequest.ProtoReflect.Descriptor instead.
func (*UploadFileRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{12}
}

func (x *UploadFileRequest) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *UploadFileRequest) GetUsage() string {
	if x != nil {
		return x.Usage
	}
	return ""
}

func (x *UploadFileRequest) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

func (x *UploadFileRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UploadFileRequest) GetOwnerType() string {
	if x != nil {
		return x.OwnerType
	}
	return ""
}

func (x *UploadFileRequest) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (m *UploadFileRequest) GetSource() isUploadFileRequest_Source {
	if m != nil {
		return m.Source
	}
	return nil
}

func (x *UploadFileRequest) GetPlatform() *v2.PlatformSourceDef {
	if x, ok := x.GetSource().(*UploadFileRequest_Platform); ok {
		return x.Platform
	}
	return nil
}

func (x *UploadFileRequest) GetTenant() *v2.TenantSourceDef {
	if x, ok := x.GetSource().(*UploadFileRequest_Tenant); ok {
		return x.Tenant
	}
	return nil
}

// Deprecated: Do not use.
func (x *UploadFileRequest) GetContentDisposition() string {
	if x != nil && x.ContentDisposition != nil {
		return *x.ContentDisposition
	}
	return ""
}

func (x *UploadFileRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type isUploadFileRequest_Source interface {
	isUploadFileRequest_Source()
}

type UploadFileRequest_Platform struct {
	// platform
	Platform *v2.PlatformSourceDef `protobuf:"bytes,7,opt,name=platform,proto3,oneof"`
}

type UploadFileRequest_Tenant struct {
	// tenant
	Tenant *v2.TenantSourceDef `protobuf:"bytes,8,opt,name=tenant,proto3,oneof"`
}

func (*UploadFileRequest_Platform) isUploadFileRequest_Source() {}

func (*UploadFileRequest_Tenant) isUploadFileRequest_Source() {}

// UploadFileResponse
type UploadFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	// access url for download
	AccessUrl string `protobuf:"bytes,2,opt,name=access_url,json=accessUrl,proto3" json:"access_url,omitempty"`
}

func (x *UploadFileResponse) Reset() {
	*x = UploadFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileResponse) ProtoMessage() {}

func (x *UploadFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileResponse.ProtoReflect.Descriptor instead.
func (*UploadFileResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{13}
}

func (x *UploadFileResponse) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

func (x *UploadFileResponse) GetAccessUrl() string {
	if x != nil {
		return x.AccessUrl
	}
	return ""
}

// FlushFileRequest
type FlushFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file identity
	//
	// Types that are assignable to FileIdentity:
	//
	//	*FlushFileRequest_FileId
	//	*FlushFileRequest_AwsS3Path
	FileIdentity isFlushFileRequest_FileIdentity `protobuf_oneof:"file_identity"`
}

func (x *FlushFileRequest) Reset() {
	*x = FlushFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlushFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushFileRequest) ProtoMessage() {}

func (x *FlushFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushFileRequest.ProtoReflect.Descriptor instead.
func (*FlushFileRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{14}
}

func (m *FlushFileRequest) GetFileIdentity() isFlushFileRequest_FileIdentity {
	if m != nil {
		return m.FileIdentity
	}
	return nil
}

func (x *FlushFileRequest) GetFileId() int64 {
	if x, ok := x.GetFileIdentity().(*FlushFileRequest_FileId); ok {
		return x.FileId
	}
	return 0
}

func (x *FlushFileRequest) GetAwsS3Path() string {
	if x, ok := x.GetFileIdentity().(*FlushFileRequest_AwsS3Path); ok {
		return x.AwsS3Path
	}
	return ""
}

type isFlushFileRequest_FileIdentity interface {
	isFlushFileRequest_FileIdentity()
}

type FlushFileRequest_FileId struct {
	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3,oneof"`
}

type FlushFileRequest_AwsS3Path struct {
	// aws s3 full path, like: s3://bucket/key
	AwsS3Path string `protobuf:"bytes,2,opt,name=aws_s3_path,json=awsS3Path,proto3,oneof"`
}

func (*FlushFileRequest_FileId) isFlushFileRequest_FileIdentity() {}

func (*FlushFileRequest_AwsS3Path) isFlushFileRequest_FileIdentity() {}

// FlushFileResponse
type FlushFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file info
	File *v2.FileModel `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
}

func (x *FlushFileResponse) Reset() {
	*x = FlushFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlushFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushFileResponse) ProtoMessage() {}

func (x *FlushFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushFileResponse.ProtoReflect.Descriptor instead.
func (*FlushFileResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_service_proto_rawDescGZIP(), []int{15}
}

func (x *FlushFileResponse) GetFile() *v2.FileModel {
	if x != nil {
		return x.File
	}
	return nil
}

var File_moego_service_file_v2_file_service_proto protoreflect.FileDescriptor

var file_moego_service_file_v2_file_service_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x32, 0x1a, 0x24, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaf, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x07, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x07, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x3a, 0x05,
	0x73, 0x33, 0x3a, 0x2f, 0x2f, 0x48, 0x00, 0x52, 0x06, 0x73, 0x33, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x2b, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xac, 0x02, 0x28, 0x01, 0x48, 0x01, 0x52,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x33, 0x0a, 0x1f, 0x47, 0x65,
	0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x65, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22,
	0xe2, 0x04, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x65,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x05, 0x75, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x32, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x03, 0x6d, 0x64, 0x35,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x32, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x27, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0xc8, 0x01, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x32, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x22, 0x07, 0x18, 0x80,
	0x80, 0x80, 0x32, 0x28, 0x00, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x42,
	0x79, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x32, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a,
	0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x45, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x3f, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x48,
	0x00, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x5d, 0x0a, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x65,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0d, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x22, 0xc0, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x50, 0x72, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70,
	0x72, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0c, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x5e, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72,
	0x65, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe2, 0x04, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x05, 0x75,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x32, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22,
	0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x45, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x3f, 0x0a, 0x06, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66,
	0x48, 0x00, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x13, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x18, 0x01, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0x80, 0x02, 0x48, 0x01, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x58,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0d, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x33, 0x0a, 0x18,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49,
	0x64, 0x22, 0x2b, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x48,
	0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x55, 0x0a, 0x17, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22,
	0x54, 0x0a, 0x18, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x78, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x1a, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xfe, 0x04, 0x0a, 0x11,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x2f, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x7a, 0x07, 0x10, 0x01,
	0x18, 0x80, 0x80, 0x80, 0x32, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x26, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0a, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12,
	0x3f, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x12, 0x42, 0x0a, 0x13, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x18,
	0x01, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x02, 0x48, 0x01, 0x52, 0x12, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x52, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0d, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4c, 0x0a, 0x12,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x55, 0x72, 0x6c, 0x22, 0x60, 0x0a, 0x10, 0x46, 0x6c,
	0x75, 0x73, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x00, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x77, 0x73,
	0x5f, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x09, 0x61, 0x77, 0x73, 0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x42, 0x0f, 0x0a, 0x0d, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0x48, 0x0a, 0x11,
	0x46, 0x6c, 0x75, 0x73, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x33, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x32, 0xa4, 0x07, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55,
	0x72, 0x6c, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55,
	0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x65,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x82, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50,
	0x72, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x65,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x50, 0x72, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x09, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x10, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x61, 0x0a, 0x0a, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x5e, 0x0a,
	0x09, 0x46, 0x6c, 0x75, 0x73, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x46, 0x6c, 0x75, 0x73, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x6c, 0x75, 0x73,
	0x68, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x77, 0x0a,
	0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x50, 0x01,
	0x5a, 0x54, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x3b, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_file_v2_file_service_proto_rawDescOnce sync.Once
	file_moego_service_file_v2_file_service_proto_rawDescData = file_moego_service_file_v2_file_service_proto_rawDesc
)

func file_moego_service_file_v2_file_service_proto_rawDescGZIP() []byte {
	file_moego_service_file_v2_file_service_proto_rawDescOnce.Do(func() {
		file_moego_service_file_v2_file_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_file_v2_file_service_proto_rawDescData)
	})
	return file_moego_service_file_v2_file_service_proto_rawDescData
}

var file_moego_service_file_v2_file_service_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_moego_service_file_v2_file_service_proto_goTypes = []interface{}{
	(*GetDownloadPresignedUrlRequest)(nil),  // 0: moego.service.file.v2.GetDownloadPresignedUrlRequest
	(*GetDownloadPresignedUrlResponse)(nil), // 1: moego.service.file.v2.GetDownloadPresignedUrlResponse
	(*GetUploadPresignedUrlRequest)(nil),    // 2: moego.service.file.v2.GetUploadPresignedUrlRequest
	(*GetUploadPresignedUrlResponse)(nil),   // 3: moego.service.file.v2.GetUploadPresignedUrlResponse
	(*CreateExportFileRequest)(nil),         // 4: moego.service.file.v2.CreateExportFileRequest
	(*CreateExportFileResponse)(nil),        // 5: moego.service.file.v2.CreateExportFileResponse
	(*QueryFileRequest)(nil),                // 6: moego.service.file.v2.QueryFileRequest
	(*QueryFileResponse)(nil),               // 7: moego.service.file.v2.QueryFileResponse
	(*UploadExportFileRequest)(nil),         // 8: moego.service.file.v2.UploadExportFileRequest
	(*UploadExportFileResponse)(nil),        // 9: moego.service.file.v2.UploadExportFileResponse
	(*UpdateFileStatusRequest)(nil),         // 10: moego.service.file.v2.UpdateFileStatusRequest
	(*UpdateFileStatusResponse)(nil),        // 11: moego.service.file.v2.UpdateFileStatusResponse
	(*UploadFileRequest)(nil),               // 12: moego.service.file.v2.UploadFileRequest
	(*UploadFileResponse)(nil),              // 13: moego.service.file.v2.UploadFileResponse
	(*FlushFileRequest)(nil),                // 14: moego.service.file.v2.FlushFileRequest
	(*FlushFileResponse)(nil),               // 15: moego.service.file.v2.FlushFileResponse
	nil,                                     // 16: moego.service.file.v2.GetUploadPresignedUrlRequest.MetadataEntry
	nil,                                     // 17: moego.service.file.v2.GetUploadPresignedUrlResponse.MetadataEntry
	nil,                                     // 18: moego.service.file.v2.CreateExportFileRequest.MetadataEntry
	nil,                                     // 19: moego.service.file.v2.UploadFileRequest.MetadataEntry
	(*v2.PlatformSourceDef)(nil),            // 20: moego.models.file.v2.PlatformSourceDef
	(*v2.TenantSourceDef)(nil),              // 21: moego.models.file.v2.TenantSourceDef
	(*v2.FileModel)(nil),                    // 22: moego.models.file.v2.FileModel
	(v2.FileStatus)(0),                      // 23: moego.models.file.v2.FileStatus
}
var file_moego_service_file_v2_file_service_proto_depIdxs = []int32{
	20, // 0: moego.service.file.v2.GetUploadPresignedUrlRequest.platform:type_name -> moego.models.file.v2.PlatformSourceDef
	21, // 1: moego.service.file.v2.GetUploadPresignedUrlRequest.tenant:type_name -> moego.models.file.v2.TenantSourceDef
	16, // 2: moego.service.file.v2.GetUploadPresignedUrlRequest.metadata:type_name -> moego.service.file.v2.GetUploadPresignedUrlRequest.MetadataEntry
	17, // 3: moego.service.file.v2.GetUploadPresignedUrlResponse.metadata:type_name -> moego.service.file.v2.GetUploadPresignedUrlResponse.MetadataEntry
	20, // 4: moego.service.file.v2.CreateExportFileRequest.platform:type_name -> moego.models.file.v2.PlatformSourceDef
	21, // 5: moego.service.file.v2.CreateExportFileRequest.tenant:type_name -> moego.models.file.v2.TenantSourceDef
	18, // 6: moego.service.file.v2.CreateExportFileRequest.metadata:type_name -> moego.service.file.v2.CreateExportFileRequest.MetadataEntry
	22, // 7: moego.service.file.v2.QueryFileResponse.file:type_name -> moego.models.file.v2.FileModel
	23, // 8: moego.service.file.v2.UploadExportFileResponse.status:type_name -> moego.models.file.v2.FileStatus
	23, // 9: moego.service.file.v2.UpdateFileStatusRequest.status:type_name -> moego.models.file.v2.FileStatus
	20, // 10: moego.service.file.v2.UploadFileRequest.platform:type_name -> moego.models.file.v2.PlatformSourceDef
	21, // 11: moego.service.file.v2.UploadFileRequest.tenant:type_name -> moego.models.file.v2.TenantSourceDef
	19, // 12: moego.service.file.v2.UploadFileRequest.metadata:type_name -> moego.service.file.v2.UploadFileRequest.MetadataEntry
	22, // 13: moego.service.file.v2.FlushFileResponse.file:type_name -> moego.models.file.v2.FileModel
	0,  // 14: moego.service.file.v2.FileService.GetDownloadPresignedUrl:input_type -> moego.service.file.v2.GetDownloadPresignedUrlRequest
	2,  // 15: moego.service.file.v2.FileService.GetUploadPresignedUrl:input_type -> moego.service.file.v2.GetUploadPresignedUrlRequest
	4,  // 16: moego.service.file.v2.FileService.CreateExportFile:input_type -> moego.service.file.v2.CreateExportFileRequest
	6,  // 17: moego.service.file.v2.FileService.QueryFile:input_type -> moego.service.file.v2.QueryFileRequest
	8,  // 18: moego.service.file.v2.FileService.UploadExportFile:input_type -> moego.service.file.v2.UploadExportFileRequest
	12, // 19: moego.service.file.v2.FileService.UploadFile:input_type -> moego.service.file.v2.UploadFileRequest
	10, // 20: moego.service.file.v2.FileService.UpdateFileStatus:input_type -> moego.service.file.v2.UpdateFileStatusRequest
	14, // 21: moego.service.file.v2.FileService.FlushFile:input_type -> moego.service.file.v2.FlushFileRequest
	1,  // 22: moego.service.file.v2.FileService.GetDownloadPresignedUrl:output_type -> moego.service.file.v2.GetDownloadPresignedUrlResponse
	3,  // 23: moego.service.file.v2.FileService.GetUploadPresignedUrl:output_type -> moego.service.file.v2.GetUploadPresignedUrlResponse
	5,  // 24: moego.service.file.v2.FileService.CreateExportFile:output_type -> moego.service.file.v2.CreateExportFileResponse
	7,  // 25: moego.service.file.v2.FileService.QueryFile:output_type -> moego.service.file.v2.QueryFileResponse
	9,  // 26: moego.service.file.v2.FileService.UploadExportFile:output_type -> moego.service.file.v2.UploadExportFileResponse
	13, // 27: moego.service.file.v2.FileService.UploadFile:output_type -> moego.service.file.v2.UploadFileResponse
	11, // 28: moego.service.file.v2.FileService.UpdateFileStatus:output_type -> moego.service.file.v2.UpdateFileStatusResponse
	15, // 29: moego.service.file.v2.FileService.FlushFile:output_type -> moego.service.file.v2.FlushFileResponse
	22, // [22:30] is the sub-list for method output_type
	14, // [14:22] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_moego_service_file_v2_file_service_proto_init() }
func file_moego_service_file_v2_file_service_proto_init() {
	if File_moego_service_file_v2_file_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_file_v2_file_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDownloadPresignedUrlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDownloadPresignedUrlResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUploadPresignedUrlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUploadPresignedUrlResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExportFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExportFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadExportFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadExportFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFileStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFileStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlushFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlushFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_file_v2_file_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*GetDownloadPresignedUrlRequest_FileId)(nil),
		(*GetDownloadPresignedUrlRequest_S3Path)(nil),
	}
	file_moego_service_file_v2_file_service_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*GetUploadPresignedUrlRequest_Platform)(nil),
		(*GetUploadPresignedUrlRequest_Tenant)(nil),
	}
	file_moego_service_file_v2_file_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*CreateExportFileRequest_Platform)(nil),
		(*CreateExportFileRequest_Tenant)(nil),
	}
	file_moego_service_file_v2_file_service_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*UploadFileRequest_Platform)(nil),
		(*UploadFileRequest_Tenant)(nil),
	}
	file_moego_service_file_v2_file_service_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*FlushFileRequest_FileId)(nil),
		(*FlushFileRequest_AwsS3Path)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_file_v2_file_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_file_v2_file_service_proto_goTypes,
		DependencyIndexes: file_moego_service_file_v2_file_service_proto_depIdxs,
		MessageInfos:      file_moego_service_file_v2_file_service_proto_msgTypes,
	}.Build()
	File_moego_service_file_v2_file_service_proto = out.File
	file_moego_service_file_v2_file_service_proto_rawDesc = nil
	file_moego_service_file_v2_file_service_proto_goTypes = nil
	file_moego_service_file_v2_file_service_proto_depIdxs = nil
}
