// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/payment/v2/payment_terminal_service.proto

package paymentsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get terminal request
type GetTerminalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetTerminalRequest) Reset() {
	*x = GetTerminalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTerminalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTerminalRequest) ProtoMessage() {}

func (x *GetTerminalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTerminalRequest.ProtoReflect.Descriptor instead.
func (*GetTerminalRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetTerminalRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get terminal result
type GetTerminalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付终端
	Terminal *v2.Terminal `protobuf:"bytes,1,opt,name=terminal,proto3" json:"terminal,omitempty"`
}

func (x *GetTerminalResponse) Reset() {
	*x = GetTerminalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTerminalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTerminalResponse) ProtoMessage() {}

func (x *GetTerminalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTerminalResponse.ProtoReflect.Descriptor instead.
func (*GetTerminalResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetTerminalResponse) GetTerminal() *v2.Terminal {
	if x != nil {
		return x.Terminal
	}
	return nil
}

// list terminal request
type ListTerminalsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 可选的过滤条件
	Filter *ListTerminalsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 分页参数
	Pagination *v21.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListTerminalsRequest) Reset() {
	*x = ListTerminalsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTerminalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTerminalsRequest) ProtoMessage() {}

func (x *ListTerminalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTerminalsRequest.ProtoReflect.Descriptor instead.
func (*ListTerminalsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListTerminalsRequest) GetFilter() *ListTerminalsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListTerminalsRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list terminal result
type ListTerminalsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付终端列表
	Terminals []*v2.Terminal `protobuf:"bytes,1,rep,name=terminals,proto3" json:"terminals,omitempty"`
	// 分页信息
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListTerminalsResponse) Reset() {
	*x = ListTerminalsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTerminalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTerminalsResponse) ProtoMessage() {}

func (x *ListTerminalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTerminalsResponse.ProtoReflect.Descriptor instead.
func (*ListTerminalsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListTerminalsResponse) GetTerminals() []*v2.Terminal {
	if x != nil {
		return x.Terminals
	}
	return nil
}

func (x *ListTerminalsResponse) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 可选的过滤条件
type ListTerminalsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user
	User *v2.User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *ListTerminalsRequest_Filter) Reset() {
	*x = ListTerminalsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTerminalsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTerminalsRequest_Filter) ProtoMessage() {}

func (x *ListTerminalsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTerminalsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListTerminalsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ListTerminalsRequest_Filter) GetUser() *v2.User {
	if x != nil {
		return x.User
	}
	return nil
}

var File_moego_service_payment_v2_payment_terminal_service_proto protoreflect.FileDescriptor

var file_moego_service_payment_v2_payment_terminal_service_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x24, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x54, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x22, 0xe5, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4d, 0x0a, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x3b,
	0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0x9c, 0x01, 0x0a, 0x15,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x09, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x09, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0xf6, 0x01, 0x0a, 0x16, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6a, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x70, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_payment_v2_payment_terminal_service_proto_rawDescOnce sync.Once
	file_moego_service_payment_v2_payment_terminal_service_proto_rawDescData = file_moego_service_payment_v2_payment_terminal_service_proto_rawDesc
)

func file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP() []byte {
	file_moego_service_payment_v2_payment_terminal_service_proto_rawDescOnce.Do(func() {
		file_moego_service_payment_v2_payment_terminal_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_payment_v2_payment_terminal_service_proto_rawDescData)
	})
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescData
}

var file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_service_payment_v2_payment_terminal_service_proto_goTypes = []interface{}{
	(*GetTerminalRequest)(nil),          // 0: moego.service.payment.v2.GetTerminalRequest
	(*GetTerminalResponse)(nil),         // 1: moego.service.payment.v2.GetTerminalResponse
	(*ListTerminalsRequest)(nil),        // 2: moego.service.payment.v2.ListTerminalsRequest
	(*ListTerminalsResponse)(nil),       // 3: moego.service.payment.v2.ListTerminalsResponse
	(*ListTerminalsRequest_Filter)(nil), // 4: moego.service.payment.v2.ListTerminalsRequest.Filter
	(*v2.Terminal)(nil),                 // 5: moego.models.payment.v2.Terminal
	(*v21.PaginationRequest)(nil),       // 6: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),      // 7: moego.utils.v2.PaginationResponse
	(*v2.User)(nil),                     // 8: moego.models.payment.v2.User
}
var file_moego_service_payment_v2_payment_terminal_service_proto_depIdxs = []int32{
	5, // 0: moego.service.payment.v2.GetTerminalResponse.terminal:type_name -> moego.models.payment.v2.Terminal
	4, // 1: moego.service.payment.v2.ListTerminalsRequest.filter:type_name -> moego.service.payment.v2.ListTerminalsRequest.Filter
	6, // 2: moego.service.payment.v2.ListTerminalsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	5, // 3: moego.service.payment.v2.ListTerminalsResponse.terminals:type_name -> moego.models.payment.v2.Terminal
	7, // 4: moego.service.payment.v2.ListTerminalsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	8, // 5: moego.service.payment.v2.ListTerminalsRequest.Filter.user:type_name -> moego.models.payment.v2.User
	0, // 6: moego.service.payment.v2.PaymentTerminalService.GetTerminal:input_type -> moego.service.payment.v2.GetTerminalRequest
	2, // 7: moego.service.payment.v2.PaymentTerminalService.ListTerminals:input_type -> moego.service.payment.v2.ListTerminalsRequest
	1, // 8: moego.service.payment.v2.PaymentTerminalService.GetTerminal:output_type -> moego.service.payment.v2.GetTerminalResponse
	3, // 9: moego.service.payment.v2.PaymentTerminalService.ListTerminals:output_type -> moego.service.payment.v2.ListTerminalsResponse
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_moego_service_payment_v2_payment_terminal_service_proto_init() }
func file_moego_service_payment_v2_payment_terminal_service_proto_init() {
	if File_moego_service_payment_v2_payment_terminal_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTerminalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTerminalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTerminalsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTerminalsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTerminalsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_payment_v2_payment_terminal_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_payment_v2_payment_terminal_service_proto_goTypes,
		DependencyIndexes: file_moego_service_payment_v2_payment_terminal_service_proto_depIdxs,
		MessageInfos:      file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes,
	}.Build()
	File_moego_service_payment_v2_payment_terminal_service_proto = out.File
	file_moego_service_payment_v2_payment_terminal_service_proto_rawDesc = nil
	file_moego_service_payment_v2_payment_terminal_service_proto_goTypes = nil
	file_moego_service_payment_v2_payment_terminal_service_proto_depIdxs = nil
}
