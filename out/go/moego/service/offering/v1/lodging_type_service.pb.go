// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/offering/v1/lodging_type_service.proto

package offeringsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
// Request body for create LodgingType service
type CreateLodgingTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// name of the lodging type
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description of the lodging type
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// images of this lodging type
	PhotoList []string `protobuf:"bytes,4,rep,name=photo_list,json=photoList,proto3" json:"photo_list,omitempty"`
	// max pet number of this lodging type
	MaxPetNum int32 `protobuf:"varint,5,opt,name=max_pet_num,json=maxPetNum,proto3" json:"max_pet_num,omitempty"`
	// max pet total weight of this lodging type
	//
	// Deprecated: Do not use.
	MaxPetTotalWeight int32 `protobuf:"varint,6,opt,name=max_pet_total_weight,json=maxPetTotalWeight,proto3" json:"max_pet_total_weight,omitempty"`
	// token staff id
	TokenStaffId int64 `protobuf:"varint,7,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// available for all pet size
	//
	// Deprecated: Do not use.
	AllPetSizes bool `protobuf:"varint,8,opt,name=all_pet_sizes,json=allPetSizes,proto3" json:"all_pet_sizes,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	// moe_pet_size.id list
	PetSizeIds []int64 `protobuf:"varint,9,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// lodging unit type in this lodging type
	LodgingUnitType v1.LodgingUnitType `protobuf:"varint,10,opt,name=lodging_unit_type,json=lodgingUnitType,proto3,enum=moego.models.offering.v1.LodgingUnitType" json:"lodging_unit_type,omitempty"`
	// whether the lodging type is available for all pet size
	PetSizeFilter bool `protobuf:"varint,11,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// source
	Source *v1.LodgingTypeModel_Source `protobuf:"varint,12,opt,name=source,proto3,enum=moego.models.offering.v1.LodgingTypeModel_Source,oneof" json:"source,omitempty"`
}

func (x *CreateLodgingTypeRequest) Reset() {
	*x = CreateLodgingTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLodgingTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLodgingTypeRequest) ProtoMessage() {}

func (x *CreateLodgingTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLodgingTypeRequest.ProtoReflect.Descriptor instead.
func (*CreateLodgingTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateLodgingTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateLodgingTypeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateLodgingTypeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateLodgingTypeRequest) GetPhotoList() []string {
	if x != nil {
		return x.PhotoList
	}
	return nil
}

func (x *CreateLodgingTypeRequest) GetMaxPetNum() int32 {
	if x != nil {
		return x.MaxPetNum
	}
	return 0
}

// Deprecated: Do not use.
func (x *CreateLodgingTypeRequest) GetMaxPetTotalWeight() int32 {
	if x != nil {
		return x.MaxPetTotalWeight
	}
	return 0
}

func (x *CreateLodgingTypeRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

// Deprecated: Do not use.
func (x *CreateLodgingTypeRequest) GetAllPetSizes() bool {
	if x != nil {
		return x.AllPetSizes
	}
	return false
}

func (x *CreateLodgingTypeRequest) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *CreateLodgingTypeRequest) GetLodgingUnitType() v1.LodgingUnitType {
	if x != nil {
		return x.LodgingUnitType
	}
	return v1.LodgingUnitType(0)
}

func (x *CreateLodgingTypeRequest) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *CreateLodgingTypeRequest) GetSource() v1.LodgingTypeModel_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.LodgingTypeModel_Source(0)
}

// *
// Request body for photo list
type PhotoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// image list
	PhotoList []string `protobuf:"bytes,1,rep,name=photo_list,json=photoList,proto3" json:"photo_list,omitempty"`
}

func (x *PhotoList) Reset() {
	*x = PhotoList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhotoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhotoList) ProtoMessage() {}

func (x *PhotoList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhotoList.ProtoReflect.Descriptor instead.
func (*PhotoList) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{1}
}

func (x *PhotoList) GetPhotoList() []string {
	if x != nil {
		return x.PhotoList
	}
	return nil
}

// *
// Response body for create LodgingType
type CreateLodgingTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type
	LodgingType *v1.LodgingTypeModel `protobuf:"bytes,1,opt,name=lodging_type,json=lodgingType,proto3" json:"lodging_type,omitempty"`
}

func (x *CreateLodgingTypeResponse) Reset() {
	*x = CreateLodgingTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLodgingTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLodgingTypeResponse) ProtoMessage() {}

func (x *CreateLodgingTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLodgingTypeResponse.ProtoReflect.Descriptor instead.
func (*CreateLodgingTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateLodgingTypeResponse) GetLodgingType() *v1.LodgingTypeModel {
	if x != nil {
		return x.LodgingType
	}
	return nil
}

// *
// Request body for update LodgingType
type UpdateLodgingTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging type
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// token staff id
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// company id for authentication
	CompanyId *int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// name of the lodging type
	Name *string `protobuf:"bytes,4,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// description of the lodging type
	Description *string `protobuf:"bytes,5,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// images of this lodging type
	PhotoList *PhotoList `protobuf:"bytes,6,opt,name=photo_list,json=photoList,proto3,oneof" json:"photo_list,omitempty"`
	// max pet number of this lodging type
	MaxPetNum *int32 `protobuf:"varint,7,opt,name=max_pet_num,json=maxPetNum,proto3,oneof" json:"max_pet_num,omitempty"`
	// max pet total weight of this lodging type
	//
	// Deprecated: Do not use.
	MaxPetTotalWeight *int32 `protobuf:"varint,8,opt,name=max_pet_total_weight,json=maxPetTotalWeight,proto3,oneof" json:"max_pet_total_weight,omitempty"`
	// available for all pet size
	//
	// Deprecated: Do not use.
	AllPetSizes *bool `protobuf:"varint,9,opt,name=all_pet_sizes,json=allPetSizes,proto3,oneof" json:"all_pet_sizes,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	// moe_pet_size.id list
	PetSizeIds []int64 `protobuf:"varint,10,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// lodging unit type in this lodging type
	LodgingUnitType *v1.LodgingUnitType `protobuf:"varint,11,opt,name=lodging_unit_type,json=lodgingUnitType,proto3,enum=moego.models.offering.v1.LodgingUnitType,oneof" json:"lodging_unit_type,omitempty"`
	// whether the lodging type is available for all pet size
	PetSizeFilter *bool `protobuf:"varint,12,opt,name=pet_size_filter,json=petSizeFilter,proto3,oneof" json:"pet_size_filter,omitempty"`
	// source
	Source *v1.LodgingTypeModel_Source `protobuf:"varint,13,opt,name=source,proto3,enum=moego.models.offering.v1.LodgingTypeModel_Source,oneof" json:"source,omitempty"`
}

func (x *UpdateLodgingTypeRequest) Reset() {
	*x = UpdateLodgingTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLodgingTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLodgingTypeRequest) ProtoMessage() {}

func (x *UpdateLodgingTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLodgingTypeRequest.ProtoReflect.Descriptor instead.
func (*UpdateLodgingTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateLodgingTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLodgingTypeRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *UpdateLodgingTypeRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *UpdateLodgingTypeRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateLodgingTypeRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateLodgingTypeRequest) GetPhotoList() *PhotoList {
	if x != nil {
		return x.PhotoList
	}
	return nil
}

func (x *UpdateLodgingTypeRequest) GetMaxPetNum() int32 {
	if x != nil && x.MaxPetNum != nil {
		return *x.MaxPetNum
	}
	return 0
}

// Deprecated: Do not use.
func (x *UpdateLodgingTypeRequest) GetMaxPetTotalWeight() int32 {
	if x != nil && x.MaxPetTotalWeight != nil {
		return *x.MaxPetTotalWeight
	}
	return 0
}

// Deprecated: Do not use.
func (x *UpdateLodgingTypeRequest) GetAllPetSizes() bool {
	if x != nil && x.AllPetSizes != nil {
		return *x.AllPetSizes
	}
	return false
}

func (x *UpdateLodgingTypeRequest) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *UpdateLodgingTypeRequest) GetLodgingUnitType() v1.LodgingUnitType {
	if x != nil && x.LodgingUnitType != nil {
		return *x.LodgingUnitType
	}
	return v1.LodgingUnitType(0)
}

func (x *UpdateLodgingTypeRequest) GetPetSizeFilter() bool {
	if x != nil && x.PetSizeFilter != nil {
		return *x.PetSizeFilter
	}
	return false
}

func (x *UpdateLodgingTypeRequest) GetSource() v1.LodgingTypeModel_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.LodgingTypeModel_Source(0)
}

// *
// Response body for update LodgingType
type UpdateLodgingTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type
	LodgingType *v1.LodgingTypeModel `protobuf:"bytes,1,opt,name=lodging_type,json=lodgingType,proto3" json:"lodging_type,omitempty"`
}

func (x *UpdateLodgingTypeResponse) Reset() {
	*x = UpdateLodgingTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLodgingTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLodgingTypeResponse) ProtoMessage() {}

func (x *UpdateLodgingTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLodgingTypeResponse.ProtoReflect.Descriptor instead.
func (*UpdateLodgingTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateLodgingTypeResponse) GetLodgingType() *v1.LodgingTypeModel {
	if x != nil {
		return x.LodgingType
	}
	return nil
}

// *
// Request body for delete LodgingType
type DeleteLodgingTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging type
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// token staff id
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// company id for authentication
	CompanyId *int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *DeleteLodgingTypeRequest) Reset() {
	*x = DeleteLodgingTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLodgingTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLodgingTypeRequest) ProtoMessage() {}

func (x *DeleteLodgingTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLodgingTypeRequest.ProtoReflect.Descriptor instead.
func (*DeleteLodgingTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteLodgingTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteLodgingTypeRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *DeleteLodgingTypeRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// *
// Response body for delete LodgingType
type DeleteLodgingTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteLodgingTypeResponse) Reset() {
	*x = DeleteLodgingTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLodgingTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLodgingTypeResponse) ProtoMessage() {}

func (x *DeleteLodgingTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLodgingTypeResponse.ProtoReflect.Descriptor instead.
func (*DeleteLodgingTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{6}
}

// *
// Request body for get LodgingType list
type GetLodgingTypeListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetLodgingTypeListRequest) Reset() {
	*x = GetLodgingTypeListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLodgingTypeListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLodgingTypeListRequest) ProtoMessage() {}

func (x *GetLodgingTypeListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLodgingTypeListRequest.ProtoReflect.Descriptor instead.
func (*GetLodgingTypeListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetLodgingTypeListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// *
// get LodgingType list response
type GetLodgingTypeListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type list
	LodgingTypeList []*v1.LodgingTypeModel `protobuf:"bytes,1,rep,name=lodging_type_list,json=lodgingTypeList,proto3" json:"lodging_type_list,omitempty"`
}

func (x *GetLodgingTypeListResponse) Reset() {
	*x = GetLodgingTypeListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLodgingTypeListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLodgingTypeListResponse) ProtoMessage() {}

func (x *GetLodgingTypeListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLodgingTypeListResponse.ProtoReflect.Descriptor instead.
func (*GetLodgingTypeListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetLodgingTypeListResponse) GetLodgingTypeList() []*v1.LodgingTypeModel {
	if x != nil {
		return x.LodgingTypeList
	}
	return nil
}

// *
// Request body for get LodgingType
type MGetLodgingTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type id list
	IdList []int64 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
}

func (x *MGetLodgingTypeRequest) Reset() {
	*x = MGetLodgingTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MGetLodgingTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MGetLodgingTypeRequest) ProtoMessage() {}

func (x *MGetLodgingTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MGetLodgingTypeRequest.ProtoReflect.Descriptor instead.
func (*MGetLodgingTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{9}
}

func (x *MGetLodgingTypeRequest) GetIdList() []int64 {
	if x != nil {
		return x.IdList
	}
	return nil
}

// *
// get LodgingType list response
type MGetLodgingTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type list
	LodgingTypeList []*v1.LodgingTypeModel `protobuf:"bytes,1,rep,name=lodging_type_list,json=lodgingTypeList,proto3" json:"lodging_type_list,omitempty"`
}

func (x *MGetLodgingTypeResponse) Reset() {
	*x = MGetLodgingTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MGetLodgingTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MGetLodgingTypeResponse) ProtoMessage() {}

func (x *MGetLodgingTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MGetLodgingTypeResponse.ProtoReflect.Descriptor instead.
func (*MGetLodgingTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{10}
}

func (x *MGetLodgingTypeResponse) GetLodgingTypeList() []*v1.LodgingTypeModel {
	if x != nil {
		return x.LodgingTypeList
	}
	return nil
}

// The params for sort lodging type by ids
type SortLodgingTypeByIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids of lodging type to sort
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// the company id
	CompanyId *int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// the login staff id
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *SortLodgingTypeByIdsRequest) Reset() {
	*x = SortLodgingTypeByIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortLodgingTypeByIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortLodgingTypeByIdsRequest) ProtoMessage() {}

func (x *SortLodgingTypeByIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortLodgingTypeByIdsRequest.ProtoReflect.Descriptor instead.
func (*SortLodgingTypeByIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{11}
}

func (x *SortLodgingTypeByIdsRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *SortLodgingTypeByIdsRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *SortLodgingTypeByIdsRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// The result for sort lodging type by ids
type SortLodgingTypeByIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortLodgingTypeByIdsResponse) Reset() {
	*x = SortLodgingTypeByIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortLodgingTypeByIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortLodgingTypeByIdsResponse) ProtoMessage() {}

func (x *SortLodgingTypeByIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortLodgingTypeByIdsResponse.ProtoReflect.Descriptor instead.
func (*SortLodgingTypeByIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP(), []int{12}
}

var File_moego_service_offering_v1_lodging_type_service_proto protoreflect.FileDescriptor

var file_moego_service_offering_v1_lodging_type_service_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xde, 0x04, 0x0a, 0x18,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x08, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0b, 0x6d,
	0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x50, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x12, 0x33, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2d, 0x0a, 0x0e, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0d, 0x61, 0x6c, 0x6c, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x73,
	0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x49,
	0x64, 0x73, 0x12, 0x55, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e,
	0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65, 0x74,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x4e, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x34, 0x0a, 0x09,
	0x50, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0a, 0x70, 0x68, 0x6f,
	0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x00, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x6a, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4d, 0x0a, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0b, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0xe8,
	0x06, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x23, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72,
	0x05, 0x10, 0x00, 0x18, 0x80, 0x08, 0x48, 0x02, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x74,
	0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x48, 0x03, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00,
	0x48, 0x04, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x88, 0x01, 0x01,
	0x12, 0x38, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x02,
	0x18, 0x01, 0x48, 0x05, 0x52, 0x11, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0d, 0x61, 0x6c,
	0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x02, 0x18, 0x01, 0x48, 0x06, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x53,
	0x69, 0x7a, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70,
	0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x73, 0x12, 0x5a, 0x0a, 0x11, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x07, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x48, 0x08,
	0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88,
	0x01, 0x01, 0x12, 0x4e, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6d, 0x61,
	0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x6d, 0x61,
	0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70,
	0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x6a, 0x0a, 0x19, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x0e, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x43, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x74, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x3b,
	0x0a, 0x16, 0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x00, 0x52, 0x06, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x71, 0x0a, 0x17, 0x4d,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xb3,
	0x01, 0x0a, 0x1b, 0x53, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d,
	0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69,
	0x64, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x22, 0x1e, 0x0a, 0x1c, 0x53, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x32, 0x9c, 0x06, 0x0a, 0x12, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7e, 0x0a, 0x11, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x11, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x78, 0x0a, 0x0f, 0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x14, 0x53, 0x6f,
	0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49,
	0x64, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x83, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_offering_v1_lodging_type_service_proto_rawDescOnce sync.Once
	file_moego_service_offering_v1_lodging_type_service_proto_rawDescData = file_moego_service_offering_v1_lodging_type_service_proto_rawDesc
)

func file_moego_service_offering_v1_lodging_type_service_proto_rawDescGZIP() []byte {
	file_moego_service_offering_v1_lodging_type_service_proto_rawDescOnce.Do(func() {
		file_moego_service_offering_v1_lodging_type_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_offering_v1_lodging_type_service_proto_rawDescData)
	})
	return file_moego_service_offering_v1_lodging_type_service_proto_rawDescData
}

var file_moego_service_offering_v1_lodging_type_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_service_offering_v1_lodging_type_service_proto_goTypes = []interface{}{
	(*CreateLodgingTypeRequest)(nil),     // 0: moego.service.offering.v1.CreateLodgingTypeRequest
	(*PhotoList)(nil),                    // 1: moego.service.offering.v1.PhotoList
	(*CreateLodgingTypeResponse)(nil),    // 2: moego.service.offering.v1.CreateLodgingTypeResponse
	(*UpdateLodgingTypeRequest)(nil),     // 3: moego.service.offering.v1.UpdateLodgingTypeRequest
	(*UpdateLodgingTypeResponse)(nil),    // 4: moego.service.offering.v1.UpdateLodgingTypeResponse
	(*DeleteLodgingTypeRequest)(nil),     // 5: moego.service.offering.v1.DeleteLodgingTypeRequest
	(*DeleteLodgingTypeResponse)(nil),    // 6: moego.service.offering.v1.DeleteLodgingTypeResponse
	(*GetLodgingTypeListRequest)(nil),    // 7: moego.service.offering.v1.GetLodgingTypeListRequest
	(*GetLodgingTypeListResponse)(nil),   // 8: moego.service.offering.v1.GetLodgingTypeListResponse
	(*MGetLodgingTypeRequest)(nil),       // 9: moego.service.offering.v1.MGetLodgingTypeRequest
	(*MGetLodgingTypeResponse)(nil),      // 10: moego.service.offering.v1.MGetLodgingTypeResponse
	(*SortLodgingTypeByIdsRequest)(nil),  // 11: moego.service.offering.v1.SortLodgingTypeByIdsRequest
	(*SortLodgingTypeByIdsResponse)(nil), // 12: moego.service.offering.v1.SortLodgingTypeByIdsResponse
	(v1.LodgingUnitType)(0),              // 13: moego.models.offering.v1.LodgingUnitType
	(v1.LodgingTypeModel_Source)(0),      // 14: moego.models.offering.v1.LodgingTypeModel.Source
	(*v1.LodgingTypeModel)(nil),          // 15: moego.models.offering.v1.LodgingTypeModel
}
var file_moego_service_offering_v1_lodging_type_service_proto_depIdxs = []int32{
	13, // 0: moego.service.offering.v1.CreateLodgingTypeRequest.lodging_unit_type:type_name -> moego.models.offering.v1.LodgingUnitType
	14, // 1: moego.service.offering.v1.CreateLodgingTypeRequest.source:type_name -> moego.models.offering.v1.LodgingTypeModel.Source
	15, // 2: moego.service.offering.v1.CreateLodgingTypeResponse.lodging_type:type_name -> moego.models.offering.v1.LodgingTypeModel
	1,  // 3: moego.service.offering.v1.UpdateLodgingTypeRequest.photo_list:type_name -> moego.service.offering.v1.PhotoList
	13, // 4: moego.service.offering.v1.UpdateLodgingTypeRequest.lodging_unit_type:type_name -> moego.models.offering.v1.LodgingUnitType
	14, // 5: moego.service.offering.v1.UpdateLodgingTypeRequest.source:type_name -> moego.models.offering.v1.LodgingTypeModel.Source
	15, // 6: moego.service.offering.v1.UpdateLodgingTypeResponse.lodging_type:type_name -> moego.models.offering.v1.LodgingTypeModel
	15, // 7: moego.service.offering.v1.GetLodgingTypeListResponse.lodging_type_list:type_name -> moego.models.offering.v1.LodgingTypeModel
	15, // 8: moego.service.offering.v1.MGetLodgingTypeResponse.lodging_type_list:type_name -> moego.models.offering.v1.LodgingTypeModel
	0,  // 9: moego.service.offering.v1.LodgingTypeService.CreateLodgingType:input_type -> moego.service.offering.v1.CreateLodgingTypeRequest
	3,  // 10: moego.service.offering.v1.LodgingTypeService.UpdateLodgingType:input_type -> moego.service.offering.v1.UpdateLodgingTypeRequest
	5,  // 11: moego.service.offering.v1.LodgingTypeService.DeleteLodgingType:input_type -> moego.service.offering.v1.DeleteLodgingTypeRequest
	7,  // 12: moego.service.offering.v1.LodgingTypeService.GetLodgingTypeList:input_type -> moego.service.offering.v1.GetLodgingTypeListRequest
	9,  // 13: moego.service.offering.v1.LodgingTypeService.MGetLodgingType:input_type -> moego.service.offering.v1.MGetLodgingTypeRequest
	11, // 14: moego.service.offering.v1.LodgingTypeService.SortLodgingTypeByIds:input_type -> moego.service.offering.v1.SortLodgingTypeByIdsRequest
	2,  // 15: moego.service.offering.v1.LodgingTypeService.CreateLodgingType:output_type -> moego.service.offering.v1.CreateLodgingTypeResponse
	4,  // 16: moego.service.offering.v1.LodgingTypeService.UpdateLodgingType:output_type -> moego.service.offering.v1.UpdateLodgingTypeResponse
	6,  // 17: moego.service.offering.v1.LodgingTypeService.DeleteLodgingType:output_type -> moego.service.offering.v1.DeleteLodgingTypeResponse
	8,  // 18: moego.service.offering.v1.LodgingTypeService.GetLodgingTypeList:output_type -> moego.service.offering.v1.GetLodgingTypeListResponse
	10, // 19: moego.service.offering.v1.LodgingTypeService.MGetLodgingType:output_type -> moego.service.offering.v1.MGetLodgingTypeResponse
	12, // 20: moego.service.offering.v1.LodgingTypeService.SortLodgingTypeByIds:output_type -> moego.service.offering.v1.SortLodgingTypeByIdsResponse
	15, // [15:21] is the sub-list for method output_type
	9,  // [9:15] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_moego_service_offering_v1_lodging_type_service_proto_init() }
func file_moego_service_offering_v1_lodging_type_service_proto_init() {
	if File_moego_service_offering_v1_lodging_type_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLodgingTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhotoList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLodgingTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLodgingTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLodgingTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLodgingTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLodgingTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLodgingTypeListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLodgingTypeListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MGetLodgingTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MGetLodgingTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortLodgingTypeByIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortLodgingTypeByIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_lodging_type_service_proto_msgTypes[11].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_offering_v1_lodging_type_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_offering_v1_lodging_type_service_proto_goTypes,
		DependencyIndexes: file_moego_service_offering_v1_lodging_type_service_proto_depIdxs,
		MessageInfos:      file_moego_service_offering_v1_lodging_type_service_proto_msgTypes,
	}.Build()
	File_moego_service_offering_v1_lodging_type_service_proto = out.File
	file_moego_service_offering_v1_lodging_type_service_proto_rawDesc = nil
	file_moego_service_offering_v1_lodging_type_service_proto_goTypes = nil
	file_moego_service_offering_v1_lodging_type_service_proto_depIdxs = nil
}
