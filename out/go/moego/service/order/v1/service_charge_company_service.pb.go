// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/order/v1/service_charge_company_service.proto

package ordersvcpb

import (
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get service charge request params
type GetCompanyServiceChargeListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// is active
	IsActive *bool `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// is mandatory
	IsMandatory *bool `protobuf:"varint,4,opt,name=is_mandatory,json=isMandatory,proto3,oneof" json:"is_mandatory,omitempty"`
	// include deleted, default false
	IncludedDeleted *bool `protobuf:"varint,5,opt,name=included_deleted,json=includedDeleted,proto3,oneof" json:"included_deleted,omitempty"`
	// include tax ids. ignored if tax_ids is empty
	TaxIds []int32 `protobuf:"varint,6,rep,packed,name=tax_ids,json=taxIds,proto3" json:"tax_ids,omitempty"`
	// surcharge type
	SurchargeType *v1.SurchargeType `protobuf:"varint,7,opt,name=surcharge_type,json=surchargeType,proto3,enum=moego.models.order.v1.SurchargeType,oneof" json:"surcharge_type,omitempty"`
}

func (x *GetCompanyServiceChargeListRequest) Reset() {
	*x = GetCompanyServiceChargeListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyServiceChargeListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyServiceChargeListRequest) ProtoMessage() {}

func (x *GetCompanyServiceChargeListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyServiceChargeListRequest.ProtoReflect.Descriptor instead.
func (*GetCompanyServiceChargeListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCompanyServiceChargeListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetCompanyServiceChargeListRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *GetCompanyServiceChargeListRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *GetCompanyServiceChargeListRequest) GetIsMandatory() bool {
	if x != nil && x.IsMandatory != nil {
		return *x.IsMandatory
	}
	return false
}

func (x *GetCompanyServiceChargeListRequest) GetIncludedDeleted() bool {
	if x != nil && x.IncludedDeleted != nil {
		return *x.IncludedDeleted
	}
	return false
}

func (x *GetCompanyServiceChargeListRequest) GetTaxIds() []int32 {
	if x != nil {
		return x.TaxIds
	}
	return nil
}

func (x *GetCompanyServiceChargeListRequest) GetSurchargeType() v1.SurchargeType {
	if x != nil && x.SurchargeType != nil {
		return *x.SurchargeType
	}
	return v1.SurchargeType(0)
}

// add service charge request params
type AddCompanyServiceChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// split method
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// price, must be positive
	Price float64 `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`
	// tax id, 0 or null means no tax
	TaxId int32 `protobuf:"varint,5,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// is mandatory, preserved 8-19 for future use
	IsMandatory *bool `protobuf:"varint,6,opt,name=is_mandatory,json=isMandatory,proto3,oneof" json:"is_mandatory,omitempty"`
	// is active
	IsActive *bool `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// is all location
	IsAllLocation *bool `protobuf:"varint,8,opt,name=is_all_location,json=isAllLocation,proto3,oneof" json:"is_all_location,omitempty"`
	// apply to upcoming
	ApplyUpcomingAppt *bool `protobuf:"varint,9,opt,name=apply_upcoming_appt,json=applyUpcomingAppt,proto3,oneof" json:"apply_upcoming_appt,omitempty"`
	// operator id
	OperatorId int64 `protobuf:"varint,10,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// service charge location override data
	LocationOverrideList []*v1.ServiceChargeLocationOverride `protobuf:"bytes,11,rep,name=location_override_list,json=locationOverrideList,proto3" json:"location_override_list,omitempty"`
	// auto apply status
	AutoApplyStatus *v1.ServiceCharge_AutoApplyStatus `protobuf:"varint,12,opt,name=auto_apply_status,json=autoApplyStatus,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyStatus,oneof" json:"auto_apply_status,omitempty"`
	// auto apply condition
	AutoApplyCondition *v1.ServiceCharge_AutoApplyCondition `protobuf:"varint,13,opt,name=auto_apply_condition,json=autoApplyCondition,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyCondition,oneof" json:"auto_apply_condition,omitempty"`
	// auto apply time, unit: minute
	// Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
	AutoApplyTime *int32 `protobuf:"varint,14,opt,name=auto_apply_time,json=autoApplyTime,proto3,oneof" json:"auto_apply_time,omitempty"`
	// auto apply time type, default is AUTO_APPLY_TIME_TYPE_CERTAIN_TIME
	// Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
	AutoApplyTimeType *v1.ServiceCharge_AutoApplyTimeType `protobuf:"varint,15,opt,name=auto_apply_time_type,json=autoApplyTimeType,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyTimeType,oneof" json:"auto_apply_time_type,omitempty"`
	// auto support service
	// Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
	ServiceItemTypes []v11.ServiceItemType `protobuf:"varint,16,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// apply type
	ApplyType *v1.ServiceCharge_ApplyType `protobuf:"varint,17,opt,name=apply_type,json=applyType,proto3,enum=moego.models.order.v1.ServiceCharge_ApplyType,oneof" json:"apply_type,omitempty"`
	// surcharge type
	SurchargeType *v1.SurchargeType `protobuf:"varint,18,opt,name=surcharge_type,json=surchargeType,proto3,enum=moego.models.order.v1.SurchargeType,oneof" json:"surcharge_type,omitempty"`
	// charge method
	ChargeMethod *v1.ChargeMethod `protobuf:"varint,19,opt,name=charge_method,json=chargeMethod,proto3,enum=moego.models.order.v1.ChargeMethod,oneof" json:"charge_method,omitempty"`
	// food source
	FoodSource *v1.FoodSourceDef `protobuf:"bytes,20,opt,name=food_source,json=foodSource,proto3,oneof" json:"food_source,omitempty"`
	// 24-hours period rule
	// charge type
	TimeBasedPricingType *v1.ServiceCharge_TimeBasedPricingType `protobuf:"varint,21,opt,name=time_based_pricing_type,json=timeBasedPricingType,proto3,enum=moego.models.order.v1.ServiceCharge_TimeBasedPricingType,oneof" json:"time_based_pricing_type,omitempty"`
	// multiple pets charge type
	MultiplePetsChargeType *v1.ServiceCharge_MultiplePetsChargeType `protobuf:"varint,22,opt,name=multiple_pets_charge_type,json=multiplePetsChargeType,proto3,enum=moego.models.order.v1.ServiceCharge_MultiplePetsChargeType,oneof" json:"multiple_pets_charge_type,omitempty"`
	// 24-hours period rule
	HourlyExceedRules []*v1.ServiceChargeExceedHourRule `protobuf:"bytes,23,rep,name=hourly_exceed_rules,json=hourlyExceedRules,proto3" json:"hourly_exceed_rules,omitempty"`
	// whether the service charge is available for all services
	EnableServiceFilter *bool `protobuf:"varint,24,opt,name=enable_service_filter,json=enableServiceFilter,proto3,oneof" json:"enable_service_filter,omitempty"`
	// service filters
	ServiceFilterRules []*v1.ServiceFilter `protobuf:"bytes,25,rep,name=service_filter_rules,json=serviceFilterRules,proto3" json:"service_filter_rules,omitempty"`
	// source
	Source *v1.ServiceCharge_Source `protobuf:"varint,26,opt,name=source,proto3,enum=moego.models.order.v1.ServiceCharge_Source,oneof" json:"source,omitempty"`
}

func (x *AddCompanyServiceChargeRequest) Reset() {
	*x = AddCompanyServiceChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddCompanyServiceChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCompanyServiceChargeRequest) ProtoMessage() {}

func (x *AddCompanyServiceChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCompanyServiceChargeRequest.ProtoReflect.Descriptor instead.
func (*AddCompanyServiceChargeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP(), []int{1}
}

func (x *AddCompanyServiceChargeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AddCompanyServiceChargeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddCompanyServiceChargeRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AddCompanyServiceChargeRequest) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *AddCompanyServiceChargeRequest) GetTaxId() int32 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *AddCompanyServiceChargeRequest) GetIsMandatory() bool {
	if x != nil && x.IsMandatory != nil {
		return *x.IsMandatory
	}
	return false
}

func (x *AddCompanyServiceChargeRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *AddCompanyServiceChargeRequest) GetIsAllLocation() bool {
	if x != nil && x.IsAllLocation != nil {
		return *x.IsAllLocation
	}
	return false
}

func (x *AddCompanyServiceChargeRequest) GetApplyUpcomingAppt() bool {
	if x != nil && x.ApplyUpcomingAppt != nil {
		return *x.ApplyUpcomingAppt
	}
	return false
}

func (x *AddCompanyServiceChargeRequest) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *AddCompanyServiceChargeRequest) GetLocationOverrideList() []*v1.ServiceChargeLocationOverride {
	if x != nil {
		return x.LocationOverrideList
	}
	return nil
}

func (x *AddCompanyServiceChargeRequest) GetAutoApplyStatus() v1.ServiceCharge_AutoApplyStatus {
	if x != nil && x.AutoApplyStatus != nil {
		return *x.AutoApplyStatus
	}
	return v1.ServiceCharge_AutoApplyStatus(0)
}

func (x *AddCompanyServiceChargeRequest) GetAutoApplyCondition() v1.ServiceCharge_AutoApplyCondition {
	if x != nil && x.AutoApplyCondition != nil {
		return *x.AutoApplyCondition
	}
	return v1.ServiceCharge_AutoApplyCondition(0)
}

func (x *AddCompanyServiceChargeRequest) GetAutoApplyTime() int32 {
	if x != nil && x.AutoApplyTime != nil {
		return *x.AutoApplyTime
	}
	return 0
}

func (x *AddCompanyServiceChargeRequest) GetAutoApplyTimeType() v1.ServiceCharge_AutoApplyTimeType {
	if x != nil && x.AutoApplyTimeType != nil {
		return *x.AutoApplyTimeType
	}
	return v1.ServiceCharge_AutoApplyTimeType(0)
}

func (x *AddCompanyServiceChargeRequest) GetServiceItemTypes() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *AddCompanyServiceChargeRequest) GetApplyType() v1.ServiceCharge_ApplyType {
	if x != nil && x.ApplyType != nil {
		return *x.ApplyType
	}
	return v1.ServiceCharge_ApplyType(0)
}

func (x *AddCompanyServiceChargeRequest) GetSurchargeType() v1.SurchargeType {
	if x != nil && x.SurchargeType != nil {
		return *x.SurchargeType
	}
	return v1.SurchargeType(0)
}

func (x *AddCompanyServiceChargeRequest) GetChargeMethod() v1.ChargeMethod {
	if x != nil && x.ChargeMethod != nil {
		return *x.ChargeMethod
	}
	return v1.ChargeMethod(0)
}

func (x *AddCompanyServiceChargeRequest) GetFoodSource() *v1.FoodSourceDef {
	if x != nil {
		return x.FoodSource
	}
	return nil
}

func (x *AddCompanyServiceChargeRequest) GetTimeBasedPricingType() v1.ServiceCharge_TimeBasedPricingType {
	if x != nil && x.TimeBasedPricingType != nil {
		return *x.TimeBasedPricingType
	}
	return v1.ServiceCharge_TimeBasedPricingType(0)
}

func (x *AddCompanyServiceChargeRequest) GetMultiplePetsChargeType() v1.ServiceCharge_MultiplePetsChargeType {
	if x != nil && x.MultiplePetsChargeType != nil {
		return *x.MultiplePetsChargeType
	}
	return v1.ServiceCharge_MultiplePetsChargeType(0)
}

func (x *AddCompanyServiceChargeRequest) GetHourlyExceedRules() []*v1.ServiceChargeExceedHourRule {
	if x != nil {
		return x.HourlyExceedRules
	}
	return nil
}

func (x *AddCompanyServiceChargeRequest) GetEnableServiceFilter() bool {
	if x != nil && x.EnableServiceFilter != nil {
		return *x.EnableServiceFilter
	}
	return false
}

func (x *AddCompanyServiceChargeRequest) GetServiceFilterRules() []*v1.ServiceFilter {
	if x != nil {
		return x.ServiceFilterRules
	}
	return nil
}

func (x *AddCompanyServiceChargeRequest) GetSource() v1.ServiceCharge_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.ServiceCharge_Source(0)
}

// update service charge request params
type UpdateCompanyServiceChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// id, exist for update
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// split method
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// price, must be positive
	Price float64 `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
	// tax id, 0 or null means no tax
	TaxId int32 `protobuf:"varint,6,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// is mandatory
	IsMandatory *bool `protobuf:"varint,7,opt,name=is_mandatory,json=isMandatory,proto3,oneof" json:"is_mandatory,omitempty"`
	// is active
	IsActive *bool `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// is all location
	IsAllLocation *bool `protobuf:"varint,9,opt,name=is_all_location,json=isAllLocation,proto3,oneof" json:"is_all_location,omitempty"`
	// apply to upcoming
	ApplyUpcomingAppt *bool `protobuf:"varint,10,opt,name=apply_upcoming_appt,json=applyUpcomingAppt,proto3,oneof" json:"apply_upcoming_appt,omitempty"`
	// single location id
	SingleLocationId *int64 `protobuf:"varint,11,opt,name=single_location_id,json=singleLocationId,proto3,oneof" json:"single_location_id,omitempty"`
	// operator id
	OperatorId int64 `protobuf:"varint,12,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// service charge location override data
	LocationOverrideList []*v1.ServiceChargeLocationOverride `protobuf:"bytes,13,rep,name=location_override_list,json=locationOverrideList,proto3" json:"location_override_list,omitempty"`
	// auto apply status
	AutoApplyStatus *v1.ServiceCharge_AutoApplyStatus `protobuf:"varint,14,opt,name=auto_apply_status,json=autoApplyStatus,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyStatus,oneof" json:"auto_apply_status,omitempty"`
	// auto apply condition
	AutoApplyCondition *v1.ServiceCharge_AutoApplyCondition `protobuf:"varint,15,opt,name=auto_apply_condition,json=autoApplyCondition,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyCondition,oneof" json:"auto_apply_condition,omitempty"`
	// auto apply time, unit: minute
	// Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
	AutoApplyTime *int32 `protobuf:"varint,16,opt,name=auto_apply_time,json=autoApplyTime,proto3,oneof" json:"auto_apply_time,omitempty"`
	// auto apply time type, default is AUTO_APPLY_TIME_TYPE_CERTAIN_TIME
	// Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
	AutoApplyTimeType *v1.ServiceCharge_AutoApplyTimeType `protobuf:"varint,17,opt,name=auto_apply_time_type,json=autoApplyTimeType,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyTimeType,oneof" json:"auto_apply_time_type,omitempty"`
	// auto support service
	// Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
	ServiceItemTypes []v11.ServiceItemType `protobuf:"varint,18,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// apply type
	ApplyType *v1.ServiceCharge_ApplyType `protobuf:"varint,19,opt,name=apply_type,json=applyType,proto3,enum=moego.models.order.v1.ServiceCharge_ApplyType,oneof" json:"apply_type,omitempty"`
	// charge method
	ChargeMethod *v1.ChargeMethod `protobuf:"varint,20,opt,name=charge_method,json=chargeMethod,proto3,enum=moego.models.order.v1.ChargeMethod,oneof" json:"charge_method,omitempty"`
	// food source ids
	FoodSource *v1.FoodSourceDef `protobuf:"bytes,21,opt,name=food_source,json=foodSource,proto3,oneof" json:"food_source,omitempty"`
	// 24-hours period rule
	// charge type
	TimeBasedPricingType *v1.ServiceCharge_TimeBasedPricingType `protobuf:"varint,22,opt,name=time_based_pricing_type,json=timeBasedPricingType,proto3,enum=moego.models.order.v1.ServiceCharge_TimeBasedPricingType,oneof" json:"time_based_pricing_type,omitempty"`
	// multiple pets charge type
	MultiplePetsChargeType *v1.ServiceCharge_MultiplePetsChargeType `protobuf:"varint,23,opt,name=multiple_pets_charge_type,json=multiplePetsChargeType,proto3,enum=moego.models.order.v1.ServiceCharge_MultiplePetsChargeType,oneof" json:"multiple_pets_charge_type,omitempty"`
	// 24-hours period rule
	HourlyExceedRules []*v1.ServiceChargeExceedHourRule `protobuf:"bytes,24,rep,name=hourly_exceed_rules,json=hourlyExceedRules,proto3" json:"hourly_exceed_rules,omitempty"`
	// whether the service charge is available for all services
	EnableServiceFilter *bool `protobuf:"varint,25,opt,name=enable_service_filter,json=enableServiceFilter,proto3,oneof" json:"enable_service_filter,omitempty"`
	// service filters
	ServiceFilterRules []*v1.ServiceFilter `protobuf:"bytes,26,rep,name=service_filter_rules,json=serviceFilterRules,proto3" json:"service_filter_rules,omitempty"`
	// source
	Source *v1.ServiceCharge_Source `protobuf:"varint,27,opt,name=source,proto3,enum=moego.models.order.v1.ServiceCharge_Source,oneof" json:"source,omitempty"`
}

func (x *UpdateCompanyServiceChargeRequest) Reset() {
	*x = UpdateCompanyServiceChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCompanyServiceChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCompanyServiceChargeRequest) ProtoMessage() {}

func (x *UpdateCompanyServiceChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCompanyServiceChargeRequest.ProtoReflect.Descriptor instead.
func (*UpdateCompanyServiceChargeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateCompanyServiceChargeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateCompanyServiceChargeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCompanyServiceChargeRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateCompanyServiceChargeRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateCompanyServiceChargeRequest) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *UpdateCompanyServiceChargeRequest) GetTaxId() int32 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *UpdateCompanyServiceChargeRequest) GetIsMandatory() bool {
	if x != nil && x.IsMandatory != nil {
		return *x.IsMandatory
	}
	return false
}

func (x *UpdateCompanyServiceChargeRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *UpdateCompanyServiceChargeRequest) GetIsAllLocation() bool {
	if x != nil && x.IsAllLocation != nil {
		return *x.IsAllLocation
	}
	return false
}

func (x *UpdateCompanyServiceChargeRequest) GetApplyUpcomingAppt() bool {
	if x != nil && x.ApplyUpcomingAppt != nil {
		return *x.ApplyUpcomingAppt
	}
	return false
}

func (x *UpdateCompanyServiceChargeRequest) GetSingleLocationId() int64 {
	if x != nil && x.SingleLocationId != nil {
		return *x.SingleLocationId
	}
	return 0
}

func (x *UpdateCompanyServiceChargeRequest) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateCompanyServiceChargeRequest) GetLocationOverrideList() []*v1.ServiceChargeLocationOverride {
	if x != nil {
		return x.LocationOverrideList
	}
	return nil
}

func (x *UpdateCompanyServiceChargeRequest) GetAutoApplyStatus() v1.ServiceCharge_AutoApplyStatus {
	if x != nil && x.AutoApplyStatus != nil {
		return *x.AutoApplyStatus
	}
	return v1.ServiceCharge_AutoApplyStatus(0)
}

func (x *UpdateCompanyServiceChargeRequest) GetAutoApplyCondition() v1.ServiceCharge_AutoApplyCondition {
	if x != nil && x.AutoApplyCondition != nil {
		return *x.AutoApplyCondition
	}
	return v1.ServiceCharge_AutoApplyCondition(0)
}

func (x *UpdateCompanyServiceChargeRequest) GetAutoApplyTime() int32 {
	if x != nil && x.AutoApplyTime != nil {
		return *x.AutoApplyTime
	}
	return 0
}

func (x *UpdateCompanyServiceChargeRequest) GetAutoApplyTimeType() v1.ServiceCharge_AutoApplyTimeType {
	if x != nil && x.AutoApplyTimeType != nil {
		return *x.AutoApplyTimeType
	}
	return v1.ServiceCharge_AutoApplyTimeType(0)
}

func (x *UpdateCompanyServiceChargeRequest) GetServiceItemTypes() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *UpdateCompanyServiceChargeRequest) GetApplyType() v1.ServiceCharge_ApplyType {
	if x != nil && x.ApplyType != nil {
		return *x.ApplyType
	}
	return v1.ServiceCharge_ApplyType(0)
}

func (x *UpdateCompanyServiceChargeRequest) GetChargeMethod() v1.ChargeMethod {
	if x != nil && x.ChargeMethod != nil {
		return *x.ChargeMethod
	}
	return v1.ChargeMethod(0)
}

func (x *UpdateCompanyServiceChargeRequest) GetFoodSource() *v1.FoodSourceDef {
	if x != nil {
		return x.FoodSource
	}
	return nil
}

func (x *UpdateCompanyServiceChargeRequest) GetTimeBasedPricingType() v1.ServiceCharge_TimeBasedPricingType {
	if x != nil && x.TimeBasedPricingType != nil {
		return *x.TimeBasedPricingType
	}
	return v1.ServiceCharge_TimeBasedPricingType(0)
}

func (x *UpdateCompanyServiceChargeRequest) GetMultiplePetsChargeType() v1.ServiceCharge_MultiplePetsChargeType {
	if x != nil && x.MultiplePetsChargeType != nil {
		return *x.MultiplePetsChargeType
	}
	return v1.ServiceCharge_MultiplePetsChargeType(0)
}

func (x *UpdateCompanyServiceChargeRequest) GetHourlyExceedRules() []*v1.ServiceChargeExceedHourRule {
	if x != nil {
		return x.HourlyExceedRules
	}
	return nil
}

func (x *UpdateCompanyServiceChargeRequest) GetEnableServiceFilter() bool {
	if x != nil && x.EnableServiceFilter != nil {
		return *x.EnableServiceFilter
	}
	return false
}

func (x *UpdateCompanyServiceChargeRequest) GetServiceFilterRules() []*v1.ServiceFilter {
	if x != nil {
		return x.ServiceFilterRules
	}
	return nil
}

func (x *UpdateCompanyServiceChargeRequest) GetSource() v1.ServiceCharge_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.ServiceCharge_Source(0)
}

// get Company service charge list response
type GetCompanyServiceChargeListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge list
	ServiceCharge []*v1.ServiceCharge `protobuf:"bytes,1,rep,name=service_charge,json=serviceCharge,proto3" json:"service_charge,omitempty"`
}

func (x *GetCompanyServiceChargeListResponse) Reset() {
	*x = GetCompanyServiceChargeListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyServiceChargeListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyServiceChargeListResponse) ProtoMessage() {}

func (x *GetCompanyServiceChargeListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyServiceChargeListResponse.ProtoReflect.Descriptor instead.
func (*GetCompanyServiceChargeListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetCompanyServiceChargeListResponse) GetServiceCharge() []*v1.ServiceCharge {
	if x != nil {
		return x.ServiceCharge
	}
	return nil
}

// order migrate service response
type OrderMigrateServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *OrderMigrateServiceRequest) Reset() {
	*x = OrderMigrateServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderMigrateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderMigrateServiceRequest) ProtoMessage() {}

func (x *OrderMigrateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderMigrateServiceRequest.ProtoReflect.Descriptor instead.
func (*OrderMigrateServiceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP(), []int{4}
}

func (x *OrderMigrateServiceRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// order migrate service response
type OrderMigrateServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OrderMigrateServiceResponse) Reset() {
	*x = OrderMigrateServiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderMigrateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderMigrateServiceResponse) ProtoMessage() {}

func (x *OrderMigrateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderMigrateServiceResponse.ProtoReflect.Descriptor instead.
func (*OrderMigrateServiceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP(), []int{5}
}

// list associated food source request
type ListSurchargeAssociatedFoodSourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// exist service charge id
	ExistServiceChargeId *int64 `protobuf:"varint,2,opt,name=exist_service_charge_id,json=existServiceChargeId,proto3,oneof" json:"exist_service_charge_id,omitempty"`
}

func (x *ListSurchargeAssociatedFoodSourceRequest) Reset() {
	*x = ListSurchargeAssociatedFoodSourceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSurchargeAssociatedFoodSourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSurchargeAssociatedFoodSourceRequest) ProtoMessage() {}

func (x *ListSurchargeAssociatedFoodSourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSurchargeAssociatedFoodSourceRequest.ProtoReflect.Descriptor instead.
func (*ListSurchargeAssociatedFoodSourceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListSurchargeAssociatedFoodSourceRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListSurchargeAssociatedFoodSourceRequest) GetExistServiceChargeId() int64 {
	if x != nil && x.ExistServiceChargeId != nil {
		return *x.ExistServiceChargeId
	}
	return 0
}

// list associated food source response
type ListSurchargeAssociatedFoodSourceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// food source ids
	FoodSourceIds []int64 `protobuf:"varint,1,rep,packed,name=food_source_ids,json=foodSourceIds,proto3" json:"food_source_ids,omitempty"`
}

func (x *ListSurchargeAssociatedFoodSourceResponse) Reset() {
	*x = ListSurchargeAssociatedFoodSourceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSurchargeAssociatedFoodSourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSurchargeAssociatedFoodSourceResponse) ProtoMessage() {}

func (x *ListSurchargeAssociatedFoodSourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSurchargeAssociatedFoodSourceResponse.ProtoReflect.Descriptor instead.
func (*ListSurchargeAssociatedFoodSourceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP(), []int{7}
}

func (x *ListSurchargeAssociatedFoodSourceResponse) GetFoodSourceIds() []int64 {
	if x != nil {
		return x.FoodSourceIds
	}
	return nil
}

// Get feeding and medication charge request
type GetFeedingMedicationChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// schedule type
	ScheduleType v12.BusinessPetScheduleType `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.business_customer.v1.BusinessPetScheduleType" json:"schedule_type,omitempty"`
	// food source ids
	FoodSourceIds []int64 `protobuf:"varint,4,rep,packed,name=food_source_ids,json=foodSourceIds,proto3" json:"food_source_ids,omitempty"`
	// service item type
	ServiceItemType v11.ServiceItemType `protobuf:"varint,5,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *GetFeedingMedicationChargeRequest) Reset() {
	*x = GetFeedingMedicationChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeedingMedicationChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeedingMedicationChargeRequest) ProtoMessage() {}

func (x *GetFeedingMedicationChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeedingMedicationChargeRequest.ProtoReflect.Descriptor instead.
func (*GetFeedingMedicationChargeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetFeedingMedicationChargeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetFeedingMedicationChargeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetFeedingMedicationChargeRequest) GetScheduleType() v12.BusinessPetScheduleType {
	if x != nil {
		return x.ScheduleType
	}
	return v12.BusinessPetScheduleType(0)
}

func (x *GetFeedingMedicationChargeRequest) GetFoodSourceIds() []int64 {
	if x != nil {
		return x.FoodSourceIds
	}
	return nil
}

func (x *GetFeedingMedicationChargeRequest) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

// Get feeding and medication charge response
type GetFeedingMedicationChargeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge list
	ServiceCharges []*v1.FeedingMedicationChargeView `protobuf:"bytes,1,rep,name=service_charges,json=serviceCharges,proto3" json:"service_charges,omitempty"`
}

func (x *GetFeedingMedicationChargeResponse) Reset() {
	*x = GetFeedingMedicationChargeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeedingMedicationChargeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeedingMedicationChargeResponse) ProtoMessage() {}

func (x *GetFeedingMedicationChargeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeedingMedicationChargeResponse.ProtoReflect.Descriptor instead.
func (*GetFeedingMedicationChargeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetFeedingMedicationChargeResponse) GetServiceCharges() []*v1.FeedingMedicationChargeView {
	if x != nil {
		return x.ServiceCharges
	}
	return nil
}

var File_moego_service_order_v1_service_charge_company_service_proto protoreflect.FileDescriptor

var file_moego_service_order_v1_service_charge_company_service_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x4b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9e, 0x03, 0x0a,
	0x22, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52,
	0x0b, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x2e, 0x0a, 0x10, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0f, 0x69, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x06, 0x74, 0x61, 0x78, 0x49, 0x64, 0x73, 0x12, 0x5c, 0x0a, 0x0e, 0x73, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x48, 0x03, 0x52, 0x0d, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x6f, 0x72, 0x79, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xbc, 0x12,
	0x0a, 0x1e, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x96, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x31, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xe8, 0x07,
	0x48, 0x00, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x24, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28,
	0x00, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01,
	0x52, 0x0b, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x88, 0x01, 0x01,
	0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x0d, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12,
	0x33, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x11,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x6a, 0x0a, 0x16, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x14, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x71, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x05,
	0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x7a, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x06, 0x52, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x37, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05,
	0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x07, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x78, 0x0a, 0x14, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75,
	0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x08, 0x52, 0x11, 0x61,
	0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x68, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92,
	0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x5e, 0x0a,
	0x0a, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x09, 0x52,
	0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5c, 0x0a,
	0x0e, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0a, 0x52, 0x0d, 0x73, 0x75, 0x72, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x0d, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x48, 0x0b, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x0b, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65,
	0x66, 0x48, 0x0c, 0x52, 0x0a, 0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x81, 0x01, 0x0a, 0x17, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0d, 0x52, 0x14, 0x74,
	0x69, 0x6d, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x87, 0x01, 0x0a, 0x19, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x70, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x48, 0x0e, 0x52, 0x16, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x50,
	0x65, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x75, 0x0a, 0x13, 0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x5f, 0x65, 0x78, 0x63, 0x65, 0x65,
	0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x75, 0x6c,
	0x65, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00, 0x10, 0x1e, 0x22, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x11, 0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x45, 0x78, 0x63, 0x65,
	0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x48, 0x0f, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x56, 0x0a, 0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x10, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x6f, 0x72, 0x79, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x42, 0x14, 0x0a,
	0x12, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a, 0x10,
	0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x1a, 0x0a,
	0x18, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0xcb, 0x12, 0x0a,
	0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10,
	0x01, 0x18, 0x96, 0x01, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x31, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xe8, 0x07,
	0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x24, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28,
	0x00, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02,
	0x52, 0x0b, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x88, 0x01, 0x01,
	0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x0d, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12,
	0x33, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x11,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x12, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x06, 0x52, 0x10, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x6a, 0x0a, 0x16, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x14,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x71, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x48, 0x07, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x7a, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x08, 0x52, 0x12, 0x61, 0x75,
	0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c,
	0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x09, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x6f,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x78, 0x0a, 0x14,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0a,
	0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x68, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x12, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0xfa,
	0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x5e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x0b, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x59, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0c, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x0b, 0x66,
	0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x48, 0x0d, 0x52, 0x0a, 0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x81, 0x01, 0x0a, 0x17, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x0e, 0x52, 0x14, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x87, 0x01, 0x0a, 0x19,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x50, 0x65,
	0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0f, 0x52, 0x16, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x70, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x75, 0x0a, 0x13, 0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x5f,
	0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x18, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x48, 0x6f,
	0x75, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00,
	0x10, 0x1e, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x11, 0x68, 0x6f, 0x75, 0x72, 0x6c,
	0x79, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x15,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x48, 0x10, 0x52, 0x13, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a, 0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x1a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x54, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x11, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d,
	0x5f, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x42, 0x0c, 0x0a,
	0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x16, 0x0a, 0x14, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x73, 0x69, 0x6e, 0x67,
	0x6c, 0x65, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x42, 0x14,
	0x0a, 0x12, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x70, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x72, 0x0a, 0x23, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4b, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52,
	0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x22, 0x3b,
	0x0a, 0x1a, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x1d, 0x0a, 0x1b, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb3, 0x01, 0x0a, 0x28, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x43, 0x0a, 0x17, 0x65, 0x78, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x14, 0x65, 0x78, 0x69,
	0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x22, 0x53, 0x0a, 0x29, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x64, 0x73, 0x22, 0xff, 0x02, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x46, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x6b, 0x0a,
	0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0c, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x0f, 0x66, 0x6f,
	0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x00, 0x18, 0x01, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x46,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b,
	0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x32, 0xef, 0x06, 0x0a, 0x1b,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x77, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x7d, 0x0a,
	0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x39, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x7e, 0x0a, 0x13,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa8, 0x01, 0x0a,
	0x21, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x46,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7a, 0x0a,
	0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_order_v1_service_charge_company_service_proto_rawDescOnce sync.Once
	file_moego_service_order_v1_service_charge_company_service_proto_rawDescData = file_moego_service_order_v1_service_charge_company_service_proto_rawDesc
)

func file_moego_service_order_v1_service_charge_company_service_proto_rawDescGZIP() []byte {
	file_moego_service_order_v1_service_charge_company_service_proto_rawDescOnce.Do(func() {
		file_moego_service_order_v1_service_charge_company_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_order_v1_service_charge_company_service_proto_rawDescData)
	})
	return file_moego_service_order_v1_service_charge_company_service_proto_rawDescData
}

var file_moego_service_order_v1_service_charge_company_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_service_order_v1_service_charge_company_service_proto_goTypes = []interface{}{
	(*GetCompanyServiceChargeListRequest)(nil),        // 0: moego.service.order.v1.GetCompanyServiceChargeListRequest
	(*AddCompanyServiceChargeRequest)(nil),            // 1: moego.service.order.v1.AddCompanyServiceChargeRequest
	(*UpdateCompanyServiceChargeRequest)(nil),         // 2: moego.service.order.v1.UpdateCompanyServiceChargeRequest
	(*GetCompanyServiceChargeListResponse)(nil),       // 3: moego.service.order.v1.GetCompanyServiceChargeListResponse
	(*OrderMigrateServiceRequest)(nil),                // 4: moego.service.order.v1.OrderMigrateServiceRequest
	(*OrderMigrateServiceResponse)(nil),               // 5: moego.service.order.v1.OrderMigrateServiceResponse
	(*ListSurchargeAssociatedFoodSourceRequest)(nil),  // 6: moego.service.order.v1.ListSurchargeAssociatedFoodSourceRequest
	(*ListSurchargeAssociatedFoodSourceResponse)(nil), // 7: moego.service.order.v1.ListSurchargeAssociatedFoodSourceResponse
	(*GetFeedingMedicationChargeRequest)(nil),         // 8: moego.service.order.v1.GetFeedingMedicationChargeRequest
	(*GetFeedingMedicationChargeResponse)(nil),        // 9: moego.service.order.v1.GetFeedingMedicationChargeResponse
	(v1.SurchargeType)(0),                             // 10: moego.models.order.v1.SurchargeType
	(*v1.ServiceChargeLocationOverride)(nil),          // 11: moego.models.order.v1.ServiceChargeLocationOverride
	(v1.ServiceCharge_AutoApplyStatus)(0),             // 12: moego.models.order.v1.ServiceCharge.AutoApplyStatus
	(v1.ServiceCharge_AutoApplyCondition)(0),          // 13: moego.models.order.v1.ServiceCharge.AutoApplyCondition
	(v1.ServiceCharge_AutoApplyTimeType)(0),           // 14: moego.models.order.v1.ServiceCharge.AutoApplyTimeType
	(v11.ServiceItemType)(0),                          // 15: moego.models.offering.v1.ServiceItemType
	(v1.ServiceCharge_ApplyType)(0),                   // 16: moego.models.order.v1.ServiceCharge.ApplyType
	(v1.ChargeMethod)(0),                              // 17: moego.models.order.v1.ChargeMethod
	(*v1.FoodSourceDef)(nil),                          // 18: moego.models.order.v1.FoodSourceDef
	(v1.ServiceCharge_TimeBasedPricingType)(0),        // 19: moego.models.order.v1.ServiceCharge.TimeBasedPricingType
	(v1.ServiceCharge_MultiplePetsChargeType)(0),      // 20: moego.models.order.v1.ServiceCharge.MultiplePetsChargeType
	(*v1.ServiceChargeExceedHourRule)(nil),            // 21: moego.models.order.v1.ServiceChargeExceedHourRule
	(*v1.ServiceFilter)(nil),                          // 22: moego.models.order.v1.ServiceFilter
	(v1.ServiceCharge_Source)(0),                      // 23: moego.models.order.v1.ServiceCharge.Source
	(*v1.ServiceCharge)(nil),                          // 24: moego.models.order.v1.ServiceCharge
	(v12.BusinessPetScheduleType)(0),                  // 25: moego.models.business_customer.v1.BusinessPetScheduleType
	(*v1.FeedingMedicationChargeView)(nil),            // 26: moego.models.order.v1.FeedingMedicationChargeView
}
var file_moego_service_order_v1_service_charge_company_service_proto_depIdxs = []int32{
	10, // 0: moego.service.order.v1.GetCompanyServiceChargeListRequest.surcharge_type:type_name -> moego.models.order.v1.SurchargeType
	11, // 1: moego.service.order.v1.AddCompanyServiceChargeRequest.location_override_list:type_name -> moego.models.order.v1.ServiceChargeLocationOverride
	12, // 2: moego.service.order.v1.AddCompanyServiceChargeRequest.auto_apply_status:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyStatus
	13, // 3: moego.service.order.v1.AddCompanyServiceChargeRequest.auto_apply_condition:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyCondition
	14, // 4: moego.service.order.v1.AddCompanyServiceChargeRequest.auto_apply_time_type:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyTimeType
	15, // 5: moego.service.order.v1.AddCompanyServiceChargeRequest.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	16, // 6: moego.service.order.v1.AddCompanyServiceChargeRequest.apply_type:type_name -> moego.models.order.v1.ServiceCharge.ApplyType
	10, // 7: moego.service.order.v1.AddCompanyServiceChargeRequest.surcharge_type:type_name -> moego.models.order.v1.SurchargeType
	17, // 8: moego.service.order.v1.AddCompanyServiceChargeRequest.charge_method:type_name -> moego.models.order.v1.ChargeMethod
	18, // 9: moego.service.order.v1.AddCompanyServiceChargeRequest.food_source:type_name -> moego.models.order.v1.FoodSourceDef
	19, // 10: moego.service.order.v1.AddCompanyServiceChargeRequest.time_based_pricing_type:type_name -> moego.models.order.v1.ServiceCharge.TimeBasedPricingType
	20, // 11: moego.service.order.v1.AddCompanyServiceChargeRequest.multiple_pets_charge_type:type_name -> moego.models.order.v1.ServiceCharge.MultiplePetsChargeType
	21, // 12: moego.service.order.v1.AddCompanyServiceChargeRequest.hourly_exceed_rules:type_name -> moego.models.order.v1.ServiceChargeExceedHourRule
	22, // 13: moego.service.order.v1.AddCompanyServiceChargeRequest.service_filter_rules:type_name -> moego.models.order.v1.ServiceFilter
	23, // 14: moego.service.order.v1.AddCompanyServiceChargeRequest.source:type_name -> moego.models.order.v1.ServiceCharge.Source
	11, // 15: moego.service.order.v1.UpdateCompanyServiceChargeRequest.location_override_list:type_name -> moego.models.order.v1.ServiceChargeLocationOverride
	12, // 16: moego.service.order.v1.UpdateCompanyServiceChargeRequest.auto_apply_status:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyStatus
	13, // 17: moego.service.order.v1.UpdateCompanyServiceChargeRequest.auto_apply_condition:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyCondition
	14, // 18: moego.service.order.v1.UpdateCompanyServiceChargeRequest.auto_apply_time_type:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyTimeType
	15, // 19: moego.service.order.v1.UpdateCompanyServiceChargeRequest.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	16, // 20: moego.service.order.v1.UpdateCompanyServiceChargeRequest.apply_type:type_name -> moego.models.order.v1.ServiceCharge.ApplyType
	17, // 21: moego.service.order.v1.UpdateCompanyServiceChargeRequest.charge_method:type_name -> moego.models.order.v1.ChargeMethod
	18, // 22: moego.service.order.v1.UpdateCompanyServiceChargeRequest.food_source:type_name -> moego.models.order.v1.FoodSourceDef
	19, // 23: moego.service.order.v1.UpdateCompanyServiceChargeRequest.time_based_pricing_type:type_name -> moego.models.order.v1.ServiceCharge.TimeBasedPricingType
	20, // 24: moego.service.order.v1.UpdateCompanyServiceChargeRequest.multiple_pets_charge_type:type_name -> moego.models.order.v1.ServiceCharge.MultiplePetsChargeType
	21, // 25: moego.service.order.v1.UpdateCompanyServiceChargeRequest.hourly_exceed_rules:type_name -> moego.models.order.v1.ServiceChargeExceedHourRule
	22, // 26: moego.service.order.v1.UpdateCompanyServiceChargeRequest.service_filter_rules:type_name -> moego.models.order.v1.ServiceFilter
	23, // 27: moego.service.order.v1.UpdateCompanyServiceChargeRequest.source:type_name -> moego.models.order.v1.ServiceCharge.Source
	24, // 28: moego.service.order.v1.GetCompanyServiceChargeListResponse.service_charge:type_name -> moego.models.order.v1.ServiceCharge
	25, // 29: moego.service.order.v1.GetFeedingMedicationChargeRequest.schedule_type:type_name -> moego.models.business_customer.v1.BusinessPetScheduleType
	15, // 30: moego.service.order.v1.GetFeedingMedicationChargeRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	26, // 31: moego.service.order.v1.GetFeedingMedicationChargeResponse.service_charges:type_name -> moego.models.order.v1.FeedingMedicationChargeView
	0,  // 32: moego.service.order.v1.ServiceChargeCompanyService.GetCompanyServiceChargeList:input_type -> moego.service.order.v1.GetCompanyServiceChargeListRequest
	1,  // 33: moego.service.order.v1.ServiceChargeCompanyService.AddCompanyServiceCharge:input_type -> moego.service.order.v1.AddCompanyServiceChargeRequest
	2,  // 34: moego.service.order.v1.ServiceChargeCompanyService.UpdateCompanyServiceCharge:input_type -> moego.service.order.v1.UpdateCompanyServiceChargeRequest
	4,  // 35: moego.service.order.v1.ServiceChargeCompanyService.OrderMigrateService:input_type -> moego.service.order.v1.OrderMigrateServiceRequest
	6,  // 36: moego.service.order.v1.ServiceChargeCompanyService.ListSurchargeAssociatedFoodSource:input_type -> moego.service.order.v1.ListSurchargeAssociatedFoodSourceRequest
	8,  // 37: moego.service.order.v1.ServiceChargeCompanyService.GetFeedingMedicationCharge:input_type -> moego.service.order.v1.GetFeedingMedicationChargeRequest
	3,  // 38: moego.service.order.v1.ServiceChargeCompanyService.GetCompanyServiceChargeList:output_type -> moego.service.order.v1.GetCompanyServiceChargeListResponse
	24, // 39: moego.service.order.v1.ServiceChargeCompanyService.AddCompanyServiceCharge:output_type -> moego.models.order.v1.ServiceCharge
	24, // 40: moego.service.order.v1.ServiceChargeCompanyService.UpdateCompanyServiceCharge:output_type -> moego.models.order.v1.ServiceCharge
	5,  // 41: moego.service.order.v1.ServiceChargeCompanyService.OrderMigrateService:output_type -> moego.service.order.v1.OrderMigrateServiceResponse
	7,  // 42: moego.service.order.v1.ServiceChargeCompanyService.ListSurchargeAssociatedFoodSource:output_type -> moego.service.order.v1.ListSurchargeAssociatedFoodSourceResponse
	9,  // 43: moego.service.order.v1.ServiceChargeCompanyService.GetFeedingMedicationCharge:output_type -> moego.service.order.v1.GetFeedingMedicationChargeResponse
	38, // [38:44] is the sub-list for method output_type
	32, // [32:38] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_moego_service_order_v1_service_charge_company_service_proto_init() }
func file_moego_service_order_v1_service_charge_company_service_proto_init() {
	if File_moego_service_order_v1_service_charge_company_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyServiceChargeListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddCompanyServiceChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCompanyServiceChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyServiceChargeListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderMigrateServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderMigrateServiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSurchargeAssociatedFoodSourceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSurchargeAssociatedFoodSourceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeedingMedicationChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeedingMedicationChargeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_service_charge_company_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_order_v1_service_charge_company_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_order_v1_service_charge_company_service_proto_goTypes,
		DependencyIndexes: file_moego_service_order_v1_service_charge_company_service_proto_depIdxs,
		MessageInfos:      file_moego_service_order_v1_service_charge_company_service_proto_msgTypes,
	}.Build()
	File_moego_service_order_v1_service_charge_company_service_proto = out.File
	file_moego_service_order_v1_service_charge_company_service_proto_rawDesc = nil
	file_moego_service_order_v1_service_charge_company_service_proto_goTypes = nil
	file_moego_service_order_v1_service_charge_company_service_proto_depIdxs = nil
}
