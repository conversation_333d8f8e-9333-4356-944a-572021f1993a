// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/map/v1/usps_service.proto

package mapsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/usps/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AddressRequest
type AddressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The two-character state code of the address.
	State string `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	// This is the city name of the address.
	City *string `protobuf:"bytes,2,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// The number of a building along with the name of the road or street on which it is located.
	StreetAddress string `protobuf:"bytes,3,opt,name=street_address,json=streetAddress,proto3" json:"street_address,omitempty"`
	// The secondary unit designator, such as apartment(APT) or suite(STE) number, defining the exact location of the address within a building.
	// For more information please see Postal Explorer.
	SecondaryAddress *string `protobuf:"bytes,4,opt,name=secondary_address,json=secondaryAddress,proto3,oneof" json:"secondary_address,omitempty"`
	// This is the 5-digit ZIP code.
	Zipcode *string `protobuf:"bytes,5,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// This is the 4-digit component of the ZIP+4 code.
	// Using the correct ZIP+4 reduces the number of times your mail is handled and can decrease the chance of a misdelivery or error.
	ZipPlus4 *string `protobuf:"bytes,6,opt,name=zip_plus4,json=zipPlus4,proto3,oneof" json:"zip_plus4,omitempty"`
	// Firm/business corresponding to the address.
	Firm *string `protobuf:"bytes,7,opt,name=firm,proto3,oneof" json:"firm,omitempty"`
	// This is the urbanization code relevant only for Puerto Rico addresses.
	Urbanization *string `protobuf:"bytes,8,opt,name=urbanization,proto3,oneof" json:"urbanization,omitempty"`
}

func (x *AddressRequest) Reset() {
	*x = AddressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressRequest) ProtoMessage() {}

func (x *AddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressRequest.ProtoReflect.Descriptor instead.
func (*AddressRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_map_v1_usps_service_proto_rawDescGZIP(), []int{0}
}

func (x *AddressRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *AddressRequest) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *AddressRequest) GetStreetAddress() string {
	if x != nil {
		return x.StreetAddress
	}
	return ""
}

func (x *AddressRequest) GetSecondaryAddress() string {
	if x != nil && x.SecondaryAddress != nil {
		return *x.SecondaryAddress
	}
	return ""
}

func (x *AddressRequest) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *AddressRequest) GetZipPlus4() string {
	if x != nil && x.ZipPlus4 != nil {
		return *x.ZipPlus4
	}
	return ""
}

func (x *AddressRequest) GetFirm() string {
	if x != nil && x.Firm != nil {
		return *x.Firm
	}
	return ""
}

func (x *AddressRequest) GetUrbanization() string {
	if x != nil && x.Urbanization != nil {
		return *x.Urbanization
	}
	return ""
}

// AddressResponse
type AddressResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Standardized address components
	Address *v1.AddressInfo `protobuf:"bytes,1,opt,name=address,proto3,oneof" json:"address,omitempty"`
	// This is the firm/business name at the address.
	Firm *string `protobuf:"bytes,2,opt,name=firm,proto3,oneof" json:"firm,omitempty"`
	// Additional delivery-related info (DPV, carrier route, delivery point, etc.)
	AdditionalInfo *v1.AddressAdditionalInfo `protobuf:"bytes,3,opt,name=additional_info,json=additionalInfo,proto3,oneof" json:"additional_info,omitempty"`
	// Codes that indicate how to improve the address input to get a better match.
	// Code 32 will indicate "Default address: The address you entered was found but more information is needed (such as an apartment, suite, or box number."
	// The recommended change would be to add additional information, such as an apartment, suite, or box number, to match to a specific address.
	// Code 22 will indicate "Multiple addresses were found for the information you entered, and no default exists."
	// The address could not be resolved as entered and more information would be needed to identify the address.
	Corrections []*v1.AddressCorrection `protobuf:"bytes,4,rep,name=corrections,proto3" json:"corrections,omitempty"`
	// Codes that indicate if an address is an exact match.
	// Code 31 will be returned "Single Response - exact match" indicating that the address was correctly matched to a ZIP+4 record.
	Matches []*v1.AddressMatch `protobuf:"bytes,5,rep,name=matches,proto3" json:"matches,omitempty"`
	// warnings
	Warnings []string `protobuf:"bytes,6,rep,name=warnings,proto3" json:"warnings,omitempty"`
}

func (x *AddressResponse) Reset() {
	*x = AddressResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressResponse) ProtoMessage() {}

func (x *AddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressResponse.ProtoReflect.Descriptor instead.
func (*AddressResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_map_v1_usps_service_proto_rawDescGZIP(), []int{1}
}

func (x *AddressResponse) GetAddress() *v1.AddressInfo {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *AddressResponse) GetFirm() string {
	if x != nil && x.Firm != nil {
		return *x.Firm
	}
	return ""
}

func (x *AddressResponse) GetAdditionalInfo() *v1.AddressAdditionalInfo {
	if x != nil {
		return x.AdditionalInfo
	}
	return nil
}

func (x *AddressResponse) GetCorrections() []*v1.AddressCorrection {
	if x != nil {
		return x.Corrections
	}
	return nil
}

func (x *AddressResponse) GetMatches() []*v1.AddressMatch {
	if x != nil {
		return x.Matches
	}
	return nil
}

func (x *AddressResponse) GetWarnings() []string {
	if x != nil {
		return x.Warnings
	}
	return nil
}

// ZipcodeRequest
type ZipcodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The two-character state code of the address.
	State string `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	// This is the city name of the address.
	City string `protobuf:"bytes,2,opt,name=city,proto3" json:"city,omitempty"`
	// The number of a building along with the name of the road or street on which it is located.
	StreetAddress string `protobuf:"bytes,3,opt,name=street_address,json=streetAddress,proto3" json:"street_address,omitempty"`
	// The secondary unit designator, such as apartment(APT) or suite(STE) number, defining the exact location of the address within a building.
	// For more information please see Postal Explorer.
	SecondaryAddress *string `protobuf:"bytes,4,opt,name=secondary_address,json=secondaryAddress,proto3,oneof" json:"secondary_address,omitempty"`
	// This is the 5-digit ZIP code.
	Zipcode *string `protobuf:"bytes,5,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// This is the 4-digit component of the ZIP+4 code.
	// Using the correct ZIP+4 reduces the number of times your mail is handled and can decrease the chance of a misdelivery or error.
	ZipPlus4 *string `protobuf:"bytes,6,opt,name=zip_plus4,json=zipPlus4,proto3,oneof" json:"zip_plus4,omitempty"`
	// Firm/business corresponding to the address.
	Firm *string `protobuf:"bytes,7,opt,name=firm,proto3,oneof" json:"firm,omitempty"`
}

func (x *ZipcodeRequest) Reset() {
	*x = ZipcodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZipcodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZipcodeRequest) ProtoMessage() {}

func (x *ZipcodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZipcodeRequest.ProtoReflect.Descriptor instead.
func (*ZipcodeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_map_v1_usps_service_proto_rawDescGZIP(), []int{2}
}

func (x *ZipcodeRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ZipcodeRequest) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *ZipcodeRequest) GetStreetAddress() string {
	if x != nil {
		return x.StreetAddress
	}
	return ""
}

func (x *ZipcodeRequest) GetSecondaryAddress() string {
	if x != nil && x.SecondaryAddress != nil {
		return *x.SecondaryAddress
	}
	return ""
}

func (x *ZipcodeRequest) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *ZipcodeRequest) GetZipPlus4() string {
	if x != nil && x.ZipPlus4 != nil {
		return *x.ZipPlus4
	}
	return ""
}

func (x *ZipcodeRequest) GetFirm() string {
	if x != nil && x.Firm != nil {
		return *x.Firm
	}
	return ""
}

// ZipcodeResponse
type ZipcodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address info
	Address *v1.AddressInfo `protobuf:"bytes,1,opt,name=address,proto3,oneof" json:"address,omitempty"`
	// This is the firm/business name at the address.
	Firm *string `protobuf:"bytes,2,opt,name=firm,proto3,oneof" json:"firm,omitempty"`
}

func (x *ZipcodeResponse) Reset() {
	*x = ZipcodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZipcodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZipcodeResponse) ProtoMessage() {}

func (x *ZipcodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZipcodeResponse.ProtoReflect.Descriptor instead.
func (*ZipcodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_map_v1_usps_service_proto_rawDescGZIP(), []int{3}
}

func (x *ZipcodeResponse) GetAddress() *v1.AddressInfo {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *ZipcodeResponse) GetFirm() string {
	if x != nil && x.Firm != nil {
		return *x.Firm
	}
	return ""
}

// CityStateRequest
type CityStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This is the 5-digit ZIP code.
	Zipcode string `protobuf:"bytes,1,opt,name=zipcode,proto3" json:"zipcode,omitempty"`
}

func (x *CityStateRequest) Reset() {
	*x = CityStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CityStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CityStateRequest) ProtoMessage() {}

func (x *CityStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CityStateRequest.ProtoReflect.Descriptor instead.
func (*CityStateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_map_v1_usps_service_proto_rawDescGZIP(), []int{4}
}

func (x *CityStateRequest) GetZipcode() string {
	if x != nil {
		return x.Zipcode
	}
	return ""
}

// CityStateResponse
type CityStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This is the 5-digit ZIP code.
	Zipcode *string `protobuf:"bytes,1,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// This is the city name of the address.
	City *string `protobuf:"bytes,2,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// This is two-character state code of the address.
	State *string `protobuf:"bytes,3,opt,name=state,proto3,oneof" json:"state,omitempty"`
}

func (x *CityStateResponse) Reset() {
	*x = CityStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CityStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CityStateResponse) ProtoMessage() {}

func (x *CityStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_map_v1_usps_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CityStateResponse.ProtoReflect.Descriptor instead.
func (*CityStateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_map_v1_usps_service_proto_rawDescGZIP(), []int{5}
}

func (x *CityStateResponse) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *CityStateResponse) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *CityStateResponse) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

var File_moego_service_map_v1_usps_service_proto protoreflect.FileDescriptor

var file_moego_service_map_v1_usps_service_proto_rawDesc = []byte{
	0x0a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x1a,
	0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x75, 0x73,
	0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x70, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x8d, 0x03, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x3c, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72,
	0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x10, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01,
	0x05, 0x48, 0x02, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x2a, 0x0a, 0x09, 0x7a, 0x69, 0x70, 0x5f, 0x70, 0x6c, 0x75, 0x73, 0x34, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01, 0x04, 0x48, 0x03, 0x52, 0x08,
	0x7a, 0x69, 0x70, 0x50, 0x6c, 0x75, 0x73, 0x34, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x66,
	0x69, 0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x04, 0x66, 0x69, 0x72,
	0x6d, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x75, 0x72, 0x62, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x0c, 0x75, 0x72,
	0x62, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a,
	0x05, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x0a, 0x0a, 0x08,
	0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x7a, 0x69, 0x70,
	0x5f, 0x70, 0x6c, 0x75, 0x73, 0x34, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x66, 0x69, 0x72, 0x6d, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x75, 0x72, 0x62, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x95, 0x03, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x75, 0x73, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x66, 0x69, 0x72, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x66, 0x69, 0x72, 0x6d, 0x88, 0x01, 0x01, 0x12,
	0x59, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x75, 0x73, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x02, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x49, 0x0a, 0x0b, 0x63, 0x6f,
	0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x75,
	0x73, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x75, 0x73, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x42,
	0x0a, 0x0a, 0x08, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x66, 0x69, 0x72, 0x6d, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0xc5, 0x02, 0x0a, 0x0e, 0x5a, 0x69, 0x70,
	0x63, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x02, 0x18, 0x3c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79,
	0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x10, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x07, 0x7a, 0x69, 0x70,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x98, 0x01, 0x05, 0x48, 0x01, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x2a, 0x0a, 0x09, 0x7a, 0x69, 0x70, 0x5f, 0x70, 0x6c, 0x75, 0x73, 0x34, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01, 0x04, 0x48,
	0x02, 0x52, 0x08, 0x7a, 0x69, 0x70, 0x50, 0x6c, 0x75, 0x73, 0x34, 0x88, 0x01, 0x01, 0x12, 0x17,
	0x0a, 0x04, 0x66, 0x69, 0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x04,
	0x66, 0x69, 0x72, 0x6d, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x7a, 0x69,
	0x70, 0x5f, 0x70, 0x6c, 0x75, 0x73, 0x34, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x66, 0x69, 0x72, 0x6d,
	0x22, 0x81, 0x01, 0x0a, 0x0f, 0x5a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x75, 0x73, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x66, 0x69, 0x72, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x66, 0x69, 0x72, 0x6d, 0x88, 0x01, 0x01, 0x42,
	0x0a, 0x0a, 0x08, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x66, 0x69, 0x72, 0x6d, 0x22, 0x36, 0x0a, 0x10, 0x43, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x98, 0x01, 0x05, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x85, 0x01, 0x0a,
	0x11, 0x43, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1d, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x17, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x32, 0xa9, 0x02, 0x0a, 0x19, 0x55, 0x6e, 0x69, 0x74, 0x65, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x56, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x07, 0x5a, 0x69,
	0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x5a, 0x69, 0x70,
	0x63, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x5a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x5c, 0x0a, 0x09, 0x43, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x74, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x61,
	0x70, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_map_v1_usps_service_proto_rawDescOnce sync.Once
	file_moego_service_map_v1_usps_service_proto_rawDescData = file_moego_service_map_v1_usps_service_proto_rawDesc
)

func file_moego_service_map_v1_usps_service_proto_rawDescGZIP() []byte {
	file_moego_service_map_v1_usps_service_proto_rawDescOnce.Do(func() {
		file_moego_service_map_v1_usps_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_map_v1_usps_service_proto_rawDescData)
	})
	return file_moego_service_map_v1_usps_service_proto_rawDescData
}

var file_moego_service_map_v1_usps_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_service_map_v1_usps_service_proto_goTypes = []interface{}{
	(*AddressRequest)(nil),           // 0: moego.service.map.v1.AddressRequest
	(*AddressResponse)(nil),          // 1: moego.service.map.v1.AddressResponse
	(*ZipcodeRequest)(nil),           // 2: moego.service.map.v1.ZipcodeRequest
	(*ZipcodeResponse)(nil),          // 3: moego.service.map.v1.ZipcodeResponse
	(*CityStateRequest)(nil),         // 4: moego.service.map.v1.CityStateRequest
	(*CityStateResponse)(nil),        // 5: moego.service.map.v1.CityStateResponse
	(*v1.AddressInfo)(nil),           // 6: moego.models.usps.v1.AddressInfo
	(*v1.AddressAdditionalInfo)(nil), // 7: moego.models.usps.v1.AddressAdditionalInfo
	(*v1.AddressCorrection)(nil),     // 8: moego.models.usps.v1.AddressCorrection
	(*v1.AddressMatch)(nil),          // 9: moego.models.usps.v1.AddressMatch
}
var file_moego_service_map_v1_usps_service_proto_depIdxs = []int32{
	6, // 0: moego.service.map.v1.AddressResponse.address:type_name -> moego.models.usps.v1.AddressInfo
	7, // 1: moego.service.map.v1.AddressResponse.additional_info:type_name -> moego.models.usps.v1.AddressAdditionalInfo
	8, // 2: moego.service.map.v1.AddressResponse.corrections:type_name -> moego.models.usps.v1.AddressCorrection
	9, // 3: moego.service.map.v1.AddressResponse.matches:type_name -> moego.models.usps.v1.AddressMatch
	6, // 4: moego.service.map.v1.ZipcodeResponse.address:type_name -> moego.models.usps.v1.AddressInfo
	0, // 5: moego.service.map.v1.UnitedStatesPostalService.Address:input_type -> moego.service.map.v1.AddressRequest
	2, // 6: moego.service.map.v1.UnitedStatesPostalService.Zipcode:input_type -> moego.service.map.v1.ZipcodeRequest
	4, // 7: moego.service.map.v1.UnitedStatesPostalService.CityState:input_type -> moego.service.map.v1.CityStateRequest
	1, // 8: moego.service.map.v1.UnitedStatesPostalService.Address:output_type -> moego.service.map.v1.AddressResponse
	3, // 9: moego.service.map.v1.UnitedStatesPostalService.Zipcode:output_type -> moego.service.map.v1.ZipcodeResponse
	5, // 10: moego.service.map.v1.UnitedStatesPostalService.CityState:output_type -> moego.service.map.v1.CityStateResponse
	8, // [8:11] is the sub-list for method output_type
	5, // [5:8] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_service_map_v1_usps_service_proto_init() }
func file_moego_service_map_v1_usps_service_proto_init() {
	if File_moego_service_map_v1_usps_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_map_v1_usps_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_map_v1_usps_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_map_v1_usps_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ZipcodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_map_v1_usps_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ZipcodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_map_v1_usps_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CityStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_map_v1_usps_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CityStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_map_v1_usps_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_map_v1_usps_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_service_map_v1_usps_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_map_v1_usps_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_service_map_v1_usps_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_map_v1_usps_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_map_v1_usps_service_proto_goTypes,
		DependencyIndexes: file_moego_service_map_v1_usps_service_proto_depIdxs,
		MessageInfos:      file_moego_service_map_v1_usps_service_proto_msgTypes,
	}.Build()
	File_moego_service_map_v1_usps_service_proto = out.File
	file_moego_service_map_v1_usps_service_proto_rawDesc = nil
	file_moego_service_map_v1_usps_service_proto_goTypes = nil
	file_moego_service_map_v1_usps_service_proto_depIdxs = nil
}
