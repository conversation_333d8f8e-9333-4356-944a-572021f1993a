// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/organization/v1/staff_service.proto

package organizationsvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// init staff availability request
type InitStaffAvailabilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// staff id list
	StaffIds []int64 `protobuf:"varint,3,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// start business id
	StartBusinessId *int64 `protobuf:"varint,4,opt,name=start_business_id,json=startBusinessId,proto3,oneof" json:"start_business_id,omitempty"`
	// end business id
	EndBusinessId *int64 `protobuf:"varint,5,opt,name=end_business_id,json=endBusinessId,proto3,oneof" json:"end_business_id,omitempty"`
}

func (x *InitStaffAvailabilityRequest) Reset() {
	*x = InitStaffAvailabilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitStaffAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitStaffAvailabilityRequest) ProtoMessage() {}

func (x *InitStaffAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitStaffAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*InitStaffAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{0}
}

func (x *InitStaffAvailabilityRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *InitStaffAvailabilityRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *InitStaffAvailabilityRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *InitStaffAvailabilityRequest) GetStartBusinessId() int64 {
	if x != nil && x.StartBusinessId != nil {
		return *x.StartBusinessId
	}
	return 0
}

func (x *InitStaffAvailabilityRequest) GetEndBusinessId() int64 {
	if x != nil && x.EndBusinessId != nil {
		return *x.EndBusinessId
	}
	return 0
}

// init staff availability response
type InitStaffAvailabilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// results
	Results []*InitStaffAvailabilityResponse_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *InitStaffAvailabilityResponse) Reset() {
	*x = InitStaffAvailabilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitStaffAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitStaffAvailabilityResponse) ProtoMessage() {}

func (x *InitStaffAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitStaffAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*InitStaffAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{1}
}

func (x *InitStaffAvailabilityResponse) GetResults() []*InitStaffAvailabilityResponse_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

// request to create a staff
type CreateStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id from token
	TokenStaffId int64 `protobuf:"varint,1,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,2,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// business id from token
	TokenBusinessId int64 `protobuf:"varint,3,opt,name=token_business_id,json=tokenBusinessId,proto3" json:"token_business_id,omitempty"`
	// staff profile def
	StaffProfile *v1.CreateStaffDef `protobuf:"bytes,6,opt,name=staff_profile,json=staffProfile,proto3" json:"staff_profile,omitempty"`
	// working business def
	WorkingLocation *v1.StaffWorkingLocationDef `protobuf:"bytes,7,opt,name=working_location,json=workingLocation,proto3,oneof" json:"working_location,omitempty"`
	// access control def
	AccessControl *v1.StaffAccessControlDef `protobuf:"bytes,8,opt,name=access_control,json=accessControl,proto3,oneof" json:"access_control,omitempty"`
	// notification def
	NotificationSetting *v1.StaffNotificationDef `protobuf:"bytes,9,opt,name=notification_setting,json=notificationSetting,proto3,oneof" json:"notification_setting,omitempty"`
	// payroll setting def
	PayrollSetting *v1.StaffPayrollSettingDef `protobuf:"bytes,10,opt,name=payroll_setting,json=payrollSetting,proto3,oneof" json:"payroll_setting,omitempty"`
	// send invite link params
	InviteLink *v1.SendInviteLinkParamsDef `protobuf:"bytes,11,opt,name=invite_link,json=inviteLink,proto3,oneof" json:"invite_link,omitempty"`
	// staff login time
	LoginTime *v1.StaffLoginTimeDef `protobuf:"bytes,12,opt,name=login_time,json=loginTime,proto3,oneof" json:"login_time,omitempty"`
}

func (x *CreateStaffRequest) Reset() {
	*x = CreateStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffRequest) ProtoMessage() {}

func (x *CreateStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffRequest.ProtoReflect.Descriptor instead.
func (*CreateStaffRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateStaffRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *CreateStaffRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *CreateStaffRequest) GetTokenBusinessId() int64 {
	if x != nil {
		return x.TokenBusinessId
	}
	return 0
}

func (x *CreateStaffRequest) GetStaffProfile() *v1.CreateStaffDef {
	if x != nil {
		return x.StaffProfile
	}
	return nil
}

func (x *CreateStaffRequest) GetWorkingLocation() *v1.StaffWorkingLocationDef {
	if x != nil {
		return x.WorkingLocation
	}
	return nil
}

func (x *CreateStaffRequest) GetAccessControl() *v1.StaffAccessControlDef {
	if x != nil {
		return x.AccessControl
	}
	return nil
}

func (x *CreateStaffRequest) GetNotificationSetting() *v1.StaffNotificationDef {
	if x != nil {
		return x.NotificationSetting
	}
	return nil
}

func (x *CreateStaffRequest) GetPayrollSetting() *v1.StaffPayrollSettingDef {
	if x != nil {
		return x.PayrollSetting
	}
	return nil
}

func (x *CreateStaffRequest) GetInviteLink() *v1.SendInviteLinkParamsDef {
	if x != nil {
		return x.InviteLink
	}
	return nil
}

func (x *CreateStaffRequest) GetLoginTime() *v1.StaffLoginTimeDef {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

// response to create a staff
type CreateStaffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id for the created staff
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateStaffResponse) Reset() {
	*x = CreateStaffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffResponse) ProtoMessage() {}

func (x *CreateStaffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffResponse.ProtoReflect.Descriptor instead.
func (*CreateStaffResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateStaffResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// request to update a staff
type UpdateStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id from token
	TokenStaffId int64 `protobuf:"varint,1,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,2,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// business id from token
	TokenBusinessId int64 `protobuf:"varint,3,opt,name=token_business_id,json=tokenBusinessId,proto3" json:"token_business_id,omitempty"`
	// staff id to update
	Id int64 `protobuf:"varint,6,opt,name=id,proto3" json:"id,omitempty"`
	// staff profile def
	StaffProfile *v1.UpdateStaffDef `protobuf:"bytes,7,opt,name=staff_profile,json=staffProfile,proto3,oneof" json:"staff_profile,omitempty"`
	// working business def
	WorkingLocation *v1.StaffWorkingLocationDef `protobuf:"bytes,8,opt,name=working_location,json=workingLocation,proto3,oneof" json:"working_location,omitempty"`
	// access control def
	AccessControl *v1.StaffAccessControlDef `protobuf:"bytes,9,opt,name=access_control,json=accessControl,proto3,oneof" json:"access_control,omitempty"`
	// notification def
	NotificationSetting *v1.StaffNotificationDef `protobuf:"bytes,10,opt,name=notification_setting,json=notificationSetting,proto3,oneof" json:"notification_setting,omitempty"`
	// payroll setting def
	PayrollSetting *v1.StaffPayrollSettingDef `protobuf:"bytes,11,opt,name=payroll_setting,json=payrollSetting,proto3,oneof" json:"payroll_setting,omitempty"`
	// send invite link params
	InviteLink *v1.SendInviteLinkParamsDef `protobuf:"bytes,12,opt,name=invite_link,json=inviteLink,proto3,oneof" json:"invite_link,omitempty"`
	// staff login time
	LoginTime *v1.StaffLoginTimeDef `protobuf:"bytes,13,opt,name=login_time,json=loginTime,proto3,oneof" json:"login_time,omitempty"`
}

func (x *UpdateStaffRequest) Reset() {
	*x = UpdateStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffRequest) ProtoMessage() {}

func (x *UpdateStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffRequest.ProtoReflect.Descriptor instead.
func (*UpdateStaffRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateStaffRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *UpdateStaffRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *UpdateStaffRequest) GetTokenBusinessId() int64 {
	if x != nil {
		return x.TokenBusinessId
	}
	return 0
}

func (x *UpdateStaffRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateStaffRequest) GetStaffProfile() *v1.UpdateStaffDef {
	if x != nil {
		return x.StaffProfile
	}
	return nil
}

func (x *UpdateStaffRequest) GetWorkingLocation() *v1.StaffWorkingLocationDef {
	if x != nil {
		return x.WorkingLocation
	}
	return nil
}

func (x *UpdateStaffRequest) GetAccessControl() *v1.StaffAccessControlDef {
	if x != nil {
		return x.AccessControl
	}
	return nil
}

func (x *UpdateStaffRequest) GetNotificationSetting() *v1.StaffNotificationDef {
	if x != nil {
		return x.NotificationSetting
	}
	return nil
}

func (x *UpdateStaffRequest) GetPayrollSetting() *v1.StaffPayrollSettingDef {
	if x != nil {
		return x.PayrollSetting
	}
	return nil
}

func (x *UpdateStaffRequest) GetInviteLink() *v1.SendInviteLinkParamsDef {
	if x != nil {
		return x.InviteLink
	}
	return nil
}

func (x *UpdateStaffRequest) GetLoginTime() *v1.StaffLoginTimeDef {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

// response to update a staff
type UpdateStaffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateStaffResponse) Reset() {
	*x = UpdateStaffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffResponse) ProtoMessage() {}

func (x *UpdateStaffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffResponse.ProtoReflect.Descriptor instead.
func (*UpdateStaffResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateStaffResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// request to delete a staff
type DeleteStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the staff to delete
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,3,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// enterprise hub need to change company owner,
	// so it need to delete the owner and create a new owner staff
	IsNeedToDeleteOwner *bool `protobuf:"varint,4,opt,name=is_need_to_delete_owner,json=isNeedToDeleteOwner,proto3,oneof" json:"is_need_to_delete_owner,omitempty"`
}

func (x *DeleteStaffRequest) Reset() {
	*x = DeleteStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffRequest) ProtoMessage() {}

func (x *DeleteStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffRequest.ProtoReflect.Descriptor instead.
func (*DeleteStaffRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteStaffRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteStaffRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *DeleteStaffRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *DeleteStaffRequest) GetIsNeedToDeleteOwner() bool {
	if x != nil && x.IsNeedToDeleteOwner != nil {
		return *x.IsNeedToDeleteOwner
	}
	return false
}

// response to delete a staff
type DeleteStaffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// delete result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DeleteStaffResponse) Reset() {
	*x = DeleteStaffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffResponse) ProtoMessage() {}

func (x *DeleteStaffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffResponse.ProtoReflect.Descriptor instead.
func (*DeleteStaffResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteStaffResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// request to get staff detail
type GetStaffDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,3,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *GetStaffDetailRequest) Reset() {
	*x = GetStaffDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffDetailRequest) ProtoMessage() {}

func (x *GetStaffDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffDetailRequest.ProtoReflect.Descriptor instead.
func (*GetStaffDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetStaffDetailRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetStaffDetailRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetStaffDetailRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// response to get staff detail
type GetStaffDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff detail
	Staff *v1.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
}

func (x *GetStaffDetailResponse) Reset() {
	*x = GetStaffDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffDetailResponse) ProtoMessage() {}

func (x *GetStaffDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffDetailResponse.ProtoReflect.Descriptor instead.
func (*GetStaffDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetStaffDetailResponse) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

// request to get staff full detail
type GetStaffFullDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff id from token
	TokenStaffId *int64 `protobuf:"varint,6,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof" json:"token_staff_id,omitempty"`
	// company id from token
	TokenCompanyId *int64 `protobuf:"varint,7,opt,name=token_company_id,json=tokenCompanyId,proto3,oneof" json:"token_company_id,omitempty"`
}

func (x *GetStaffFullDetailRequest) Reset() {
	*x = GetStaffFullDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffFullDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffFullDetailRequest) ProtoMessage() {}

func (x *GetStaffFullDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffFullDetailRequest.ProtoReflect.Descriptor instead.
func (*GetStaffFullDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetStaffFullDetailRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetStaffFullDetailRequest) GetTokenStaffId() int64 {
	if x != nil && x.TokenStaffId != nil {
		return *x.TokenStaffId
	}
	return 0
}

func (x *GetStaffFullDetailRequest) GetTokenCompanyId() int64 {
	if x != nil && x.TokenCompanyId != nil {
		return *x.TokenCompanyId
	}
	return 0
}

// response to get staff full detail
type GetStaffFullDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff info
	StaffProfile *v1.StaffBasicView `protobuf:"bytes,2,opt,name=staff_profile,json=staffProfile,proto3" json:"staff_profile,omitempty"`
	// working location
	WorkingLocation *v1.StaffWorkingLocationDef `protobuf:"bytes,3,opt,name=working_location,json=workingLocation,proto3" json:"working_location,omitempty"`
	// access control
	AccessControl *v1.StaffAccessControlDef `protobuf:"bytes,4,opt,name=access_control,json=accessControl,proto3" json:"access_control,omitempty"`
	// notification setting
	NotificationSetting *v1.StaffNotificationDef `protobuf:"bytes,5,opt,name=notification_setting,json=notificationSetting,proto3" json:"notification_setting,omitempty"`
	// payroll setting
	PayrollSetting *v1.StaffPayrollSettingDef `protobuf:"bytes,6,opt,name=payroll_setting,json=payrollSetting,proto3" json:"payroll_setting,omitempty"`
	// staff email
	StaffEmail *v1.StaffEmailDef `protobuf:"bytes,7,opt,name=staff_email,json=staffEmail,proto3" json:"staff_email,omitempty"`
	// staff van
	StaffVan *v1.StaffVanDef `protobuf:"bytes,8,opt,name=staff_van,json=staffVan,proto3" json:"staff_van,omitempty"`
	// staff login time
	LoginTime *v1.StaffLoginTimeDef `protobuf:"bytes,9,opt,name=login_time,json=loginTime,proto3" json:"login_time,omitempty"`
}

func (x *GetStaffFullDetailResponse) Reset() {
	*x = GetStaffFullDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffFullDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffFullDetailResponse) ProtoMessage() {}

func (x *GetStaffFullDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffFullDetailResponse.ProtoReflect.Descriptor instead.
func (*GetStaffFullDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetStaffFullDetailResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetStaffFullDetailResponse) GetStaffProfile() *v1.StaffBasicView {
	if x != nil {
		return x.StaffProfile
	}
	return nil
}

func (x *GetStaffFullDetailResponse) GetWorkingLocation() *v1.StaffWorkingLocationDef {
	if x != nil {
		return x.WorkingLocation
	}
	return nil
}

func (x *GetStaffFullDetailResponse) GetAccessControl() *v1.StaffAccessControlDef {
	if x != nil {
		return x.AccessControl
	}
	return nil
}

func (x *GetStaffFullDetailResponse) GetNotificationSetting() *v1.StaffNotificationDef {
	if x != nil {
		return x.NotificationSetting
	}
	return nil
}

func (x *GetStaffFullDetailResponse) GetPayrollSetting() *v1.StaffPayrollSettingDef {
	if x != nil {
		return x.PayrollSetting
	}
	return nil
}

func (x *GetStaffFullDetailResponse) GetStaffEmail() *v1.StaffEmailDef {
	if x != nil {
		return x.StaffEmail
	}
	return nil
}

func (x *GetStaffFullDetailResponse) GetStaffVan() *v1.StaffVanDef {
	if x != nil {
		return x.StaffVan
	}
	return nil
}

func (x *GetStaffFullDetailResponse) GetLoginTime() *v1.StaffLoginTimeDef {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

// request to get staff list
type QueryStaffListByPaginationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id from token
	TokenStaffId *int64 `protobuf:"varint,1,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof" json:"token_staff_id,omitempty"`
	// company id from token
	TokenCompanyId *int64 `protobuf:"varint,2,opt,name=token_company_id,json=tokenCompanyId,proto3,oneof" json:"token_company_id,omitempty"` // 3-5: preserved for future use
	// business id list
	BusinessIds []int64 `protobuf:"varint,6,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"` // 7-13: preserved for future use, for example filter by staff type, status, etc.
	// order by params
	OrderBys []*v2.OrderBy `protobuf:"bytes,14,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *QueryStaffListByPaginationRequest) Reset() {
	*x = QueryStaffListByPaginationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStaffListByPaginationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStaffListByPaginationRequest) ProtoMessage() {}

func (x *QueryStaffListByPaginationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStaffListByPaginationRequest.ProtoReflect.Descriptor instead.
func (*QueryStaffListByPaginationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{12}
}

func (x *QueryStaffListByPaginationRequest) GetTokenStaffId() int64 {
	if x != nil && x.TokenStaffId != nil {
		return *x.TokenStaffId
	}
	return 0
}

func (x *QueryStaffListByPaginationRequest) GetTokenCompanyId() int64 {
	if x != nil && x.TokenCompanyId != nil {
		return *x.TokenCompanyId
	}
	return 0
}

func (x *QueryStaffListByPaginationRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *QueryStaffListByPaginationRequest) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *QueryStaffListByPaginationRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// response to get staff list
type QueryStaffListByPaginationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staffs []*v1.StaffInfoDef `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *QueryStaffListByPaginationResponse) Reset() {
	*x = QueryStaffListByPaginationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStaffListByPaginationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStaffListByPaginationResponse) ProtoMessage() {}

func (x *QueryStaffListByPaginationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStaffListByPaginationResponse.ProtoReflect.Descriptor instead.
func (*QueryStaffListByPaginationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{13}
}

func (x *QueryStaffListByPaginationResponse) GetStaffs() []*v1.StaffInfoDef {
	if x != nil {
		return x.Staffs
	}
	return nil
}

func (x *QueryStaffListByPaginationResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get staffs by account id request
type GetStaffsByAccountIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *GetStaffsByAccountIdRequest) Reset() {
	*x = GetStaffsByAccountIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffsByAccountIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffsByAccountIdRequest) ProtoMessage() {}

func (x *GetStaffsByAccountIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffsByAccountIdRequest.ProtoReflect.Descriptor instead.
func (*GetStaffsByAccountIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetStaffsByAccountIdRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

// get staffs by account id response
type GetStaffsByAccountIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staffs
	Staffs []*v1.StaffModel `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
}

func (x *GetStaffsByAccountIdResponse) Reset() {
	*x = GetStaffsByAccountIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffsByAccountIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffsByAccountIdResponse) ProtoMessage() {}

func (x *GetStaffsByAccountIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffsByAccountIdResponse.ProtoReflect.Descriptor instead.
func (*GetStaffsByAccountIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetStaffsByAccountIdResponse) GetStaffs() []*v1.StaffModel {
	if x != nil {
		return x.Staffs
	}
	return nil
}

// get staff by company request
type GetStaffByCompanyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// account id
	AccountId int64 `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// use it to choose the staff in the company when the company did not migrate
	// otherwise, it should be empty
	BusinessId *int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *GetStaffByCompanyRequest) Reset() {
	*x = GetStaffByCompanyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffByCompanyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffByCompanyRequest) ProtoMessage() {}

func (x *GetStaffByCompanyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffByCompanyRequest.ProtoReflect.Descriptor instead.
func (*GetStaffByCompanyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetStaffByCompanyRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetStaffByCompanyRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *GetStaffByCompanyRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// get staff by company response
type GetStaffByCompanyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff
	Staff *v1.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
}

func (x *GetStaffByCompanyResponse) Reset() {
	*x = GetStaffByCompanyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffByCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffByCompanyResponse) ProtoMessage() {}

func (x *GetStaffByCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffByCompanyResponse.ProtoReflect.Descriptor instead.
func (*GetStaffByCompanyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetStaffByCompanyResponse) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

// update account last visit business request
type UpdateAccountLastVisitBusinessRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// last visited_at
	VisitedAt int64 `protobuf:"varint,3,opt,name=visited_at,json=visitedAt,proto3" json:"visited_at,omitempty"`
	// last visit business id
	LastVisitBusinessId int64 `protobuf:"varint,4,opt,name=last_visit_business_id,json=lastVisitBusinessId,proto3" json:"last_visit_business_id,omitempty"`
}

func (x *UpdateAccountLastVisitBusinessRequest) Reset() {
	*x = UpdateAccountLastVisitBusinessRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccountLastVisitBusinessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountLastVisitBusinessRequest) ProtoMessage() {}

func (x *UpdateAccountLastVisitBusinessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountLastVisitBusinessRequest.ProtoReflect.Descriptor instead.
func (*UpdateAccountLastVisitBusinessRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateAccountLastVisitBusinessRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *UpdateAccountLastVisitBusinessRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateAccountLastVisitBusinessRequest) GetVisitedAt() int64 {
	if x != nil {
		return x.VisitedAt
	}
	return 0
}

func (x *UpdateAccountLastVisitBusinessRequest) GetLastVisitBusinessId() int64 {
	if x != nil {
		return x.LastVisitBusinessId
	}
	return 0
}

// update account last visit business response
type UpdateAccountLastVisitBusinessResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateAccountLastVisitBusinessResponse) Reset() {
	*x = UpdateAccountLastVisitBusinessResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccountLastVisitBusinessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountLastVisitBusinessResponse) ProtoMessage() {}

func (x *UpdateAccountLastVisitBusinessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountLastVisitBusinessResponse.ProtoReflect.Descriptor instead.
func (*UpdateAccountLastVisitBusinessResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{19}
}

// request for get staff list by business ids
type GetStaffsByWorkingLocationIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// token staff id
	TokenStaffId int64 `protobuf:"varint,1,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// token company id
	TokenCompanyId int64 `protobuf:"varint,2,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// business ids, if empty, will get all working location staffs
	BusinessIds []int64 `protobuf:"varint,3,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// include deleted staffs
	IncludeDeleted *bool `protobuf:"varint,4,opt,name=include_deleted,json=includeDeleted,proto3,oneof" json:"include_deleted,omitempty"`
}

func (x *GetStaffsByWorkingLocationIdsRequest) Reset() {
	*x = GetStaffsByWorkingLocationIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffsByWorkingLocationIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffsByWorkingLocationIdsRequest) ProtoMessage() {}

func (x *GetStaffsByWorkingLocationIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffsByWorkingLocationIdsRequest.ProtoReflect.Descriptor instead.
func (*GetStaffsByWorkingLocationIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetStaffsByWorkingLocationIdsRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *GetStaffsByWorkingLocationIdsRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *GetStaffsByWorkingLocationIdsRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *GetStaffsByWorkingLocationIdsRequest) GetIncludeDeleted() bool {
	if x != nil && x.IncludeDeleted != nil {
		return *x.IncludeDeleted
	}
	return false
}

// response for get working staff list by business ids
type GetStaffsByWorkingLocationIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	LocationStaffs []*v1.LocationStaffsDef `protobuf:"bytes,1,rep,name=location_staffs,json=locationStaffs,proto3" json:"location_staffs,omitempty"`
	// total location count
	TotalLocationCount int64 `protobuf:"varint,2,opt,name=total_location_count,json=totalLocationCount,proto3" json:"total_location_count,omitempty"`
	// total staff count
	TotalStaffCount int64 `protobuf:"varint,3,opt,name=total_staff_count,json=totalStaffCount,proto3" json:"total_staff_count,omitempty"`
}

func (x *GetStaffsByWorkingLocationIdsResponse) Reset() {
	*x = GetStaffsByWorkingLocationIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffsByWorkingLocationIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffsByWorkingLocationIdsResponse) ProtoMessage() {}

func (x *GetStaffsByWorkingLocationIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffsByWorkingLocationIdsResponse.ProtoReflect.Descriptor instead.
func (*GetStaffsByWorkingLocationIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetStaffsByWorkingLocationIdsResponse) GetLocationStaffs() []*v1.LocationStaffsDef {
	if x != nil {
		return x.LocationStaffs
	}
	return nil
}

func (x *GetStaffsByWorkingLocationIdsResponse) GetTotalLocationCount() int64 {
	if x != nil {
		return x.TotalLocationCount
	}
	return 0
}

func (x *GetStaffsByWorkingLocationIdsResponse) GetTotalStaffCount() int64 {
	if x != nil {
		return x.TotalStaffCount
	}
	return 0
}

// request for get enterprise staff list by location ids
type GetEnterpriseStaffsByWorkingLocationIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business ids, if empty, will get all working location staffs
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsRequest) Reset() {
	*x = GetEnterpriseStaffsByWorkingLocationIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseStaffsByWorkingLocationIdsRequest) ProtoMessage() {}

func (x *GetEnterpriseStaffsByWorkingLocationIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseStaffsByWorkingLocationIdsRequest.ProtoReflect.Descriptor instead.
func (*GetEnterpriseStaffsByWorkingLocationIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// response for get enterprise working staff list by business ids
type GetEnterpriseStaffsByWorkingLocationIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	LocationStaffs []*v1.LocationStaffsDef `protobuf:"bytes,1,rep,name=location_staffs,json=locationStaffs,proto3" json:"location_staffs,omitempty"`
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsResponse) Reset() {
	*x = GetEnterpriseStaffsByWorkingLocationIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseStaffsByWorkingLocationIdsResponse) ProtoMessage() {}

func (x *GetEnterpriseStaffsByWorkingLocationIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseStaffsByWorkingLocationIdsResponse.ProtoReflect.Descriptor instead.
func (*GetEnterpriseStaffsByWorkingLocationIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsResponse) GetLocationStaffs() []*v1.LocationStaffsDef {
	if x != nil {
		return x.LocationStaffs
	}
	return nil
}

// query staff by ids request
type QueryStaffByIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff ids
	StaffIds []int64 `protobuf:"varint,1,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
}

func (x *QueryStaffByIdsRequest) Reset() {
	*x = QueryStaffByIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStaffByIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStaffByIdsRequest) ProtoMessage() {}

func (x *QueryStaffByIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStaffByIdsRequest.ProtoReflect.Descriptor instead.
func (*QueryStaffByIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{24}
}

func (x *QueryStaffByIdsRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// query staff by ids response
type QueryStaffByIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staffs []*v1.StaffModel `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
}

func (x *QueryStaffByIdsResponse) Reset() {
	*x = QueryStaffByIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStaffByIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStaffByIdsResponse) ProtoMessage() {}

func (x *QueryStaffByIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStaffByIdsResponse.ProtoReflect.Descriptor instead.
func (*QueryStaffByIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{25}
}

func (x *QueryStaffByIdsResponse) GetStaffs() []*v1.StaffModel {
	if x != nil {
		return x.Staffs
	}
	return nil
}

// query staff by company id  request
type QueryStaffByCompanyIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *QueryStaffByCompanyIdRequest) Reset() {
	*x = QueryStaffByCompanyIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStaffByCompanyIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStaffByCompanyIdRequest) ProtoMessage() {}

func (x *QueryStaffByCompanyIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStaffByCompanyIdRequest.ProtoReflect.Descriptor instead.
func (*QueryStaffByCompanyIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{26}
}

func (x *QueryStaffByCompanyIdRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *QueryStaffByCompanyIdRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// query staff by company id  response
type QueryStaffByCompanyIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staffs []*v1.StaffModel `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *QueryStaffByCompanyIdResponse) Reset() {
	*x = QueryStaffByCompanyIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStaffByCompanyIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStaffByCompanyIdResponse) ProtoMessage() {}

func (x *QueryStaffByCompanyIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStaffByCompanyIdResponse.ProtoReflect.Descriptor instead.
func (*QueryStaffByCompanyIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{27}
}

func (x *QueryStaffByCompanyIdResponse) GetStaffs() []*v1.StaffModel {
	if x != nil {
		return x.Staffs
	}
	return nil
}

func (x *QueryStaffByCompanyIdResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// request for get show on calendar staffs
type GetShowOnCalendarStaffsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// token staff id
	TokenStaffId int64 `protobuf:"varint,1,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// token company id
	TokenCompanyId int64 `protobuf:"varint,2,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// business ids, if empty, will get all working location staffs
	BusinessIds []int64 `protobuf:"varint,3,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *GetShowOnCalendarStaffsRequest) Reset() {
	*x = GetShowOnCalendarStaffsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetShowOnCalendarStaffsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetShowOnCalendarStaffsRequest) ProtoMessage() {}

func (x *GetShowOnCalendarStaffsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetShowOnCalendarStaffsRequest.ProtoReflect.Descriptor instead.
func (*GetShowOnCalendarStaffsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetShowOnCalendarStaffsRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *GetShowOnCalendarStaffsRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *GetShowOnCalendarStaffsRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// response for get show on calendar staffs
type GetShowOnCalendarStaffsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	LocationStaffs []*v1.LocationStaffsDef `protobuf:"bytes,1,rep,name=location_staffs,json=locationStaffs,proto3" json:"location_staffs,omitempty"`
}

func (x *GetShowOnCalendarStaffsResponse) Reset() {
	*x = GetShowOnCalendarStaffsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetShowOnCalendarStaffsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetShowOnCalendarStaffsResponse) ProtoMessage() {}

func (x *GetShowOnCalendarStaffsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetShowOnCalendarStaffsResponse.ProtoReflect.Descriptor instead.
func (*GetShowOnCalendarStaffsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetShowOnCalendarStaffsResponse) GetLocationStaffs() []*v1.LocationStaffsDef {
	if x != nil {
		return x.LocationStaffs
	}
	return nil
}

// response for get show on calendar staff ids
type GetShowOnCalendarStaffIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff ids
	LocationStaffIds []*v1.LocationStaffIdsDef `protobuf:"bytes,1,rep,name=location_staff_ids,json=locationStaffIds,proto3" json:"location_staff_ids,omitempty"`
}

func (x *GetShowOnCalendarStaffIdsResponse) Reset() {
	*x = GetShowOnCalendarStaffIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetShowOnCalendarStaffIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetShowOnCalendarStaffIdsResponse) ProtoMessage() {}

func (x *GetShowOnCalendarStaffIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetShowOnCalendarStaffIdsResponse.ProtoReflect.Descriptor instead.
func (*GetShowOnCalendarStaffIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetShowOnCalendarStaffIdsResponse) GetLocationStaffIds() []*v1.LocationStaffIdsDef {
	if x != nil {
		return x.LocationStaffIds
	}
	return nil
}

// staff data transfer request
type MigrateStaffDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *MigrateStaffDataRequest) Reset() {
	*x = MigrateStaffDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigrateStaffDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigrateStaffDataRequest) ProtoMessage() {}

func (x *MigrateStaffDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigrateStaffDataRequest.ProtoReflect.Descriptor instead.
func (*MigrateStaffDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{31}
}

func (x *MigrateStaffDataRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// migrate data response staff mapping def
type StaffMappingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// origin staff id
	FromStaffId int64 `protobuf:"varint,1,opt,name=from_staff_id,json=fromStaffId,proto3" json:"from_staff_id,omitempty"`
	// merged to the new staff id
	ToStaffId int64 `protobuf:"varint,2,opt,name=to_staff_id,json=toStaffId,proto3" json:"to_staff_id,omitempty"`
}

func (x *StaffMappingDef) Reset() {
	*x = StaffMappingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffMappingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffMappingDef) ProtoMessage() {}

func (x *StaffMappingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffMappingDef.ProtoReflect.Descriptor instead.
func (*StaffMappingDef) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{32}
}

func (x *StaffMappingDef) GetFromStaffId() int64 {
	if x != nil {
		return x.FromStaffId
	}
	return 0
}

func (x *StaffMappingDef) GetToStaffId() int64 {
	if x != nil {
		return x.ToStaffId
	}
	return 0
}

// staff data migrate response
type MigrateStaffDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff mapping list
	StaffMapping []*StaffMappingDef `protobuf:"bytes,1,rep,name=staff_mapping,json=staffMapping,proto3" json:"staff_mapping,omitempty"`
}

func (x *MigrateStaffDataResponse) Reset() {
	*x = MigrateStaffDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigrateStaffDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigrateStaffDataResponse) ProtoMessage() {}

func (x *MigrateStaffDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigrateStaffDataResponse.ProtoReflect.Descriptor instead.
func (*MigrateStaffDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{33}
}

func (x *MigrateStaffDataResponse) GetStaffMapping() []*StaffMappingDef {
	if x != nil {
		return x.StaffMapping
	}
	return nil
}

// count staff with role request
type CountStaffWithRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, deprecated, now only filter by role_id
	//
	// Deprecated: Do not use.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// role id
	RoleId int64 `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
}

func (x *CountStaffWithRoleRequest) Reset() {
	*x = CountStaffWithRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountStaffWithRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountStaffWithRoleRequest) ProtoMessage() {}

func (x *CountStaffWithRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountStaffWithRoleRequest.ProtoReflect.Descriptor instead.
func (*CountStaffWithRoleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{34}
}

// Deprecated: Do not use.
func (x *CountStaffWithRoleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CountStaffWithRoleRequest) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

// count staff with role response
type CountStaffWithRoleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff count
	StaffCount int64 `protobuf:"varint,1,opt,name=staff_count,json=staffCount,proto3" json:"staff_count,omitempty"`
}

func (x *CountStaffWithRoleResponse) Reset() {
	*x = CountStaffWithRoleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountStaffWithRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountStaffWithRoleResponse) ProtoMessage() {}

func (x *CountStaffWithRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountStaffWithRoleResponse.ProtoReflect.Descriptor instead.
func (*CountStaffWithRoleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{35}
}

func (x *CountStaffWithRoleResponse) GetStaffCount() int64 {
	if x != nil {
		return x.StaffCount
	}
	return 0
}

// get staffs in working location request
type GetStaffsByWorkingLocationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// working location id
	WorkingLocationId int64 `protobuf:"varint,2,opt,name=working_location_id,json=workingLocationId,proto3" json:"working_location_id,omitempty"`
}

func (x *GetStaffsByWorkingLocationRequest) Reset() {
	*x = GetStaffsByWorkingLocationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffsByWorkingLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffsByWorkingLocationRequest) ProtoMessage() {}

func (x *GetStaffsByWorkingLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffsByWorkingLocationRequest.ProtoReflect.Descriptor instead.
func (*GetStaffsByWorkingLocationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetStaffsByWorkingLocationRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetStaffsByWorkingLocationRequest) GetWorkingLocationId() int64 {
	if x != nil {
		return x.WorkingLocationId
	}
	return 0
}

// get staffs by working location response
type GetStaffsByWorkingLocationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staffs []*v1.StaffBasicView `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
}

func (x *GetStaffsByWorkingLocationResponse) Reset() {
	*x = GetStaffsByWorkingLocationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffsByWorkingLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffsByWorkingLocationResponse) ProtoMessage() {}

func (x *GetStaffsByWorkingLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffsByWorkingLocationResponse.ProtoReflect.Descriptor instead.
func (*GetStaffsByWorkingLocationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetStaffsByWorkingLocationResponse) GetStaffs() []*v1.StaffBasicView {
	if x != nil {
		return x.Staffs
	}
	return nil
}

// request for get clock in out staffs of current staff
type GetClockInOutStaffsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// token staff id
	TokenStaffId int64 `protobuf:"varint,1,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// token company id
	TokenCompanyId int64 `protobuf:"varint,2,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// clock in/out date
	Date string `protobuf:"bytes,6,opt,name=date,proto3" json:"date,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,7,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// is for themselves, permission control from api layer
	IsForThemselves *bool `protobuf:"varint,8,opt,name=is_for_themselves,json=isForThemselves,proto3,oneof" json:"is_for_themselves,omitempty"`
}

func (x *GetClockInOutStaffsRequest) Reset() {
	*x = GetClockInOutStaffsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClockInOutStaffsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClockInOutStaffsRequest) ProtoMessage() {}

func (x *GetClockInOutStaffsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClockInOutStaffsRequest.ProtoReflect.Descriptor instead.
func (*GetClockInOutStaffsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{38}
}

func (x *GetClockInOutStaffsRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *GetClockInOutStaffsRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *GetClockInOutStaffsRequest) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *GetClockInOutStaffsRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *GetClockInOutStaffsRequest) GetIsForThemselves() bool {
	if x != nil && x.IsForThemselves != nil {
		return *x.IsForThemselves
	}
	return false
}

// response for get clock in out staffs of current staff
type GetClockInOutStaffsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	ClockInOutStaffs []*v1.ClockInOutStaffDef `protobuf:"bytes,1,rep,name=clock_in_out_staffs,json=clockInOutStaffs,proto3" json:"clock_in_out_staffs,omitempty"`
}

func (x *GetClockInOutStaffsResponse) Reset() {
	*x = GetClockInOutStaffsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClockInOutStaffsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClockInOutStaffsResponse) ProtoMessage() {}

func (x *GetClockInOutStaffsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClockInOutStaffsResponse.ProtoReflect.Descriptor instead.
func (*GetClockInOutStaffsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{39}
}

func (x *GetClockInOutStaffsResponse) GetClockInOutStaffs() []*v1.ClockInOutStaffDef {
	if x != nil {
		return x.ClockInOutStaffs
	}
	return nil
}

// request to create a enterprise owner
type CreateEnterpriseOwnerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// account_id
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// staff profile def
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name of the staff
	LastName string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// role id for enterprise owner
	RoleId int64 `protobuf:"varint,5,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// source
	Source *v1.StaffSource `protobuf:"varint,6,opt,name=source,proto3,enum=moego.models.organization.v1.StaffSource,oneof" json:"source,omitempty"`
}

func (x *CreateEnterpriseOwnerRequest) Reset() {
	*x = CreateEnterpriseOwnerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEnterpriseOwnerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEnterpriseOwnerRequest) ProtoMessage() {}

func (x *CreateEnterpriseOwnerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEnterpriseOwnerRequest.ProtoReflect.Descriptor instead.
func (*CreateEnterpriseOwnerRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{40}
}

func (x *CreateEnterpriseOwnerRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreateEnterpriseOwnerRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *CreateEnterpriseOwnerRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CreateEnterpriseOwnerRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CreateEnterpriseOwnerRequest) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *CreateEnterpriseOwnerRequest) GetSource() v1.StaffSource {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.StaffSource(0)
}

// response to create a enterprise staff
type CreateEnterpriseOwnerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id for the created staff
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateEnterpriseOwnerResponse) Reset() {
	*x = CreateEnterpriseOwnerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEnterpriseOwnerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEnterpriseOwnerResponse) ProtoMessage() {}

func (x *CreateEnterpriseOwnerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEnterpriseOwnerResponse.ProtoReflect.Descriptor instead.
func (*CreateEnterpriseOwnerResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{41}
}

func (x *CreateEnterpriseOwnerResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// request to send invite staff link
type SendInviteStaffLinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id from token
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// email
	Email string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *SendInviteStaffLinkRequest) Reset() {
	*x = SendInviteStaffLinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendInviteStaffLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendInviteStaffLinkRequest) ProtoMessage() {}

func (x *SendInviteStaffLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendInviteStaffLinkRequest.ProtoReflect.Descriptor instead.
func (*SendInviteStaffLinkRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{42}
}

func (x *SendInviteStaffLinkRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *SendInviteStaffLinkRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SendInviteStaffLinkRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// response to send invite staff link
type SendInviteStaffLinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendInviteStaffLinkResponse) Reset() {
	*x = SendInviteStaffLinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendInviteStaffLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendInviteStaffLinkResponse) ProtoMessage() {}

func (x *SendInviteStaffLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendInviteStaffLinkResponse.ProtoReflect.Descriptor instead.
func (*SendInviteStaffLinkResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{43}
}

// get staff invited link status request
type ListOwnerStaffInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId []int64 `protobuf:"varint,1,rep,packed,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *ListOwnerStaffInfoRequest) Reset() {
	*x = ListOwnerStaffInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOwnerStaffInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOwnerStaffInfoRequest) ProtoMessage() {}

func (x *ListOwnerStaffInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOwnerStaffInfoRequest.ProtoReflect.Descriptor instead.
func (*ListOwnerStaffInfoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{44}
}

func (x *ListOwnerStaffInfoRequest) GetCompanyId() []int64 {
	if x != nil {
		return x.CompanyId
	}
	return nil
}

// get staff invited link status response
type ListOwnerStaffInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// own staffs
	OwnStaffs map[int64]*v1.OwnerStaffDef `protobuf:"bytes,1,rep,name=own_staffs,json=ownStaffs,proto3" json:"own_staffs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListOwnerStaffInfoResponse) Reset() {
	*x = ListOwnerStaffInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOwnerStaffInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOwnerStaffInfoResponse) ProtoMessage() {}

func (x *ListOwnerStaffInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOwnerStaffInfoResponse.ProtoReflect.Descriptor instead.
func (*ListOwnerStaffInfoResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{45}
}

func (x *ListOwnerStaffInfoResponse) GetOwnStaffs() map[int64]*v1.OwnerStaffDef {
	if x != nil {
		return x.OwnStaffs
	}
	return nil
}

// request to create a owner staff
type CreateStaffRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// account id to bind on company,
	AccountId *int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// staff profile def
	FirstName *string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// last name of the staff
	LastName *string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// email
	Email *string `protobuf:"bytes,5,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// employee category
	EmployeeCategory v1.StaffEmployeeCategory `protobuf:"varint,6,opt,name=employee_category,json=employeeCategory,proto3,enum=moego.models.organization.v1.StaffEmployeeCategory" json:"employee_category,omitempty"`
	// status
	Status *v1.StaffModel_Status `protobuf:"varint,7,opt,name=status,proto3,enum=moego.models.organization.v1.StaffModel_Status,oneof" json:"status,omitempty"`
	// role id
	RoleId *int64 `protobuf:"varint,8,opt,name=role_id,json=roleId,proto3,oneof" json:"role_id,omitempty"`
}

func (x *CreateStaffRecordRequest) Reset() {
	*x = CreateStaffRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffRecordRequest) ProtoMessage() {}

func (x *CreateStaffRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffRecordRequest.ProtoReflect.Descriptor instead.
func (*CreateStaffRecordRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{46}
}

func (x *CreateStaffRecordRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateStaffRecordRequest) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *CreateStaffRecordRequest) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *CreateStaffRecordRequest) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *CreateStaffRecordRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CreateStaffRecordRequest) GetEmployeeCategory() v1.StaffEmployeeCategory {
	if x != nil {
		return x.EmployeeCategory
	}
	return v1.StaffEmployeeCategory(0)
}

func (x *CreateStaffRecordRequest) GetStatus() v1.StaffModel_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.StaffModel_Status(0)
}

func (x *CreateStaffRecordRequest) GetRoleId() int64 {
	if x != nil && x.RoleId != nil {
		return *x.RoleId
	}
	return 0
}

// response to create a owner staff
type CreateStaffRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff
	Staff *v1.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
}

func (x *CreateStaffRecordResponse) Reset() {
	*x = CreateStaffRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffRecordResponse) ProtoMessage() {}

func (x *CreateStaffRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffRecordResponse.ProtoReflect.Descriptor instead.
func (*CreateStaffRecordResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{47}
}

func (x *CreateStaffRecordResponse) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

// update owner staff, can delete staff or change account id
type UpdateStaffRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// account id to bind on own staff
	AccountId *int64 `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// first_name
	FirstName *string `protobuf:"bytes,4,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// last_name
	LastName *string `protobuf:"bytes,5,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// email
	Email *string `protobuf:"bytes,6,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// status
	Status *v1.StaffModel_Status `protobuf:"varint,7,opt,name=status,proto3,enum=moego.models.organization.v1.StaffModel_Status,oneof" json:"status,omitempty"`
	// employee category
	EmployeeCategory *v1.StaffEmployeeCategory `protobuf:"varint,8,opt,name=employee_category,json=employeeCategory,proto3,enum=moego.models.organization.v1.StaffEmployeeCategory,oneof" json:"employee_category,omitempty"`
	// working in all locations when set owner ,working_in_all_locations should be set true
	WorkingInAllLocations *bool `protobuf:"varint,9,opt,name=working_in_all_locations,json=workingInAllLocations,proto3,oneof" json:"working_in_all_locations,omitempty"`
	// role id,can be set zero,if set zero,will upgrade staff to owner
	RoleId *int64 `protobuf:"varint,10,opt,name=role_id,json=roleId,proto3,oneof" json:"role_id,omitempty"`
}

func (x *UpdateStaffRecordRequest) Reset() {
	*x = UpdateStaffRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffRecordRequest) ProtoMessage() {}

func (x *UpdateStaffRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffRecordRequest.ProtoReflect.Descriptor instead.
func (*UpdateStaffRecordRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{48}
}

func (x *UpdateStaffRecordRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateStaffRecordRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateStaffRecordRequest) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *UpdateStaffRecordRequest) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *UpdateStaffRecordRequest) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *UpdateStaffRecordRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *UpdateStaffRecordRequest) GetStatus() v1.StaffModel_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.StaffModel_Status(0)
}

func (x *UpdateStaffRecordRequest) GetEmployeeCategory() v1.StaffEmployeeCategory {
	if x != nil && x.EmployeeCategory != nil {
		return *x.EmployeeCategory
	}
	return v1.StaffEmployeeCategory(0)
}

func (x *UpdateStaffRecordRequest) GetWorkingInAllLocations() bool {
	if x != nil && x.WorkingInAllLocations != nil {
		return *x.WorkingInAllLocations
	}
	return false
}

func (x *UpdateStaffRecordRequest) GetRoleId() int64 {
	if x != nil && x.RoleId != nil {
		return *x.RoleId
	}
	return 0
}

// response to update a owner staff
type UpdateStaffRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff
	Staff *v1.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
}

func (x *UpdateStaffRecordResponse) Reset() {
	*x = UpdateStaffRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffRecordResponse) ProtoMessage() {}

func (x *UpdateStaffRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffRecordResponse.ProtoReflect.Descriptor instead.
func (*UpdateStaffRecordResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{49}
}

func (x *UpdateStaffRecordResponse) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

// get enterprise staff by account
type GetEnterpriseStaffsByAccountIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// filter
	Filter *GetEnterpriseStaffsByAccountIdRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *GetEnterpriseStaffsByAccountIdRequest) Reset() {
	*x = GetEnterpriseStaffsByAccountIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseStaffsByAccountIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseStaffsByAccountIdRequest) ProtoMessage() {}

func (x *GetEnterpriseStaffsByAccountIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseStaffsByAccountIdRequest.ProtoReflect.Descriptor instead.
func (*GetEnterpriseStaffsByAccountIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{50}
}

func (x *GetEnterpriseStaffsByAccountIdRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *GetEnterpriseStaffsByAccountIdRequest) GetFilter() *GetEnterpriseStaffsByAccountIdRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// get enterprise staff by account response
type GetEnterpriseStaffsByAccountIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staffs []*v1.StaffModel `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
}

func (x *GetEnterpriseStaffsByAccountIdResponse) Reset() {
	*x = GetEnterpriseStaffsByAccountIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseStaffsByAccountIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseStaffsByAccountIdResponse) ProtoMessage() {}

func (x *GetEnterpriseStaffsByAccountIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseStaffsByAccountIdResponse.ProtoReflect.Descriptor instead.
func (*GetEnterpriseStaffsByAccountIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{51}
}

func (x *GetEnterpriseStaffsByAccountIdResponse) GetStaffs() []*v1.StaffModel {
	if x != nil {
		return x.Staffs
	}
	return nil
}

// create enterprise staff request
type CreateEnterpriseStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// staff id from token
	Profile *v11.CreateStaffProfile `protobuf:"bytes,2,opt,name=profile,proto3" json:"profile,omitempty"`
	// invite link
	InviteLink *v1.SendInviteLinkParamsDef `protobuf:"bytes,3,opt,name=invite_link,json=inviteLink,proto3,oneof" json:"invite_link,omitempty"`
}

func (x *CreateEnterpriseStaffRequest) Reset() {
	*x = CreateEnterpriseStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEnterpriseStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEnterpriseStaffRequest) ProtoMessage() {}

func (x *CreateEnterpriseStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEnterpriseStaffRequest.ProtoReflect.Descriptor instead.
func (*CreateEnterpriseStaffRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{52}
}

func (x *CreateEnterpriseStaffRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreateEnterpriseStaffRequest) GetProfile() *v11.CreateStaffProfile {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *CreateEnterpriseStaffRequest) GetInviteLink() *v1.SendInviteLinkParamsDef {
	if x != nil {
		return x.InviteLink
	}
	return nil
}

// create enterprise staff response
type CreateEnterpriseStaffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// staff model
	Staff *v1.StaffModel `protobuf:"bytes,2,opt,name=staff,proto3" json:"staff,omitempty"`
	// staff email
	StaffEmail *v1.StaffEmailDef `protobuf:"bytes,3,opt,name=staff_email,json=staffEmail,proto3" json:"staff_email,omitempty"`
}

func (x *CreateEnterpriseStaffResponse) Reset() {
	*x = CreateEnterpriseStaffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEnterpriseStaffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEnterpriseStaffResponse) ProtoMessage() {}

func (x *CreateEnterpriseStaffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEnterpriseStaffResponse.ProtoReflect.Descriptor instead.
func (*CreateEnterpriseStaffResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{53}
}

func (x *CreateEnterpriseStaffResponse) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreateEnterpriseStaffResponse) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *CreateEnterpriseStaffResponse) GetStaffEmail() *v1.StaffEmailDef {
	if x != nil {
		return x.StaffEmail
	}
	return nil
}

// update enterprise staff request
type UpdateEnterpriseStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// staff profile
	Profile *v11.UpdateStaffProfile `protobuf:"bytes,3,opt,name=profile,proto3,oneof" json:"profile,omitempty"`
	// invite link
	InviteLink *v1.SendInviteLinkParamsDef `protobuf:"bytes,4,opt,name=invite_link,json=inviteLink,proto3,oneof" json:"invite_link,omitempty"`
}

func (x *UpdateEnterpriseStaffRequest) Reset() {
	*x = UpdateEnterpriseStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEnterpriseStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEnterpriseStaffRequest) ProtoMessage() {}

func (x *UpdateEnterpriseStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEnterpriseStaffRequest.ProtoReflect.Descriptor instead.
func (*UpdateEnterpriseStaffRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{54}
}

func (x *UpdateEnterpriseStaffRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateEnterpriseStaffRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *UpdateEnterpriseStaffRequest) GetProfile() *v11.UpdateStaffProfile {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *UpdateEnterpriseStaffRequest) GetInviteLink() *v1.SendInviteLinkParamsDef {
	if x != nil {
		return x.InviteLink
	}
	return nil
}

// update enterprise staff response
type UpdateEnterpriseStaffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff model
	Staff *v1.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
	// staff email
	StaffEmail *v1.StaffEmailDef `protobuf:"bytes,2,opt,name=staff_email,json=staffEmail,proto3" json:"staff_email,omitempty"`
}

func (x *UpdateEnterpriseStaffResponse) Reset() {
	*x = UpdateEnterpriseStaffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEnterpriseStaffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEnterpriseStaffResponse) ProtoMessage() {}

func (x *UpdateEnterpriseStaffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEnterpriseStaffResponse.ProtoReflect.Descriptor instead.
func (*UpdateEnterpriseStaffResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{55}
}

func (x *UpdateEnterpriseStaffResponse) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *UpdateEnterpriseStaffResponse) GetStaffEmail() *v1.StaffEmailDef {
	if x != nil {
		return x.StaffEmail
	}
	return nil
}

// get enterprise staff request
type GetEnterpriseStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *GetEnterpriseStaffRequest) Reset() {
	*x = GetEnterpriseStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseStaffRequest) ProtoMessage() {}

func (x *GetEnterpriseStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseStaffRequest.ProtoReflect.Descriptor instead.
func (*GetEnterpriseStaffRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{56}
}

func (x *GetEnterpriseStaffRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetEnterpriseStaffRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// get enterprise staff response
type GetEnterpriseStaffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff model
	Staff *v1.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
	// staff email
	StaffEmail *v1.StaffEmailDef `protobuf:"bytes,2,opt,name=staff_email,json=staffEmail,proto3" json:"staff_email,omitempty"`
}

func (x *GetEnterpriseStaffResponse) Reset() {
	*x = GetEnterpriseStaffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseStaffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseStaffResponse) ProtoMessage() {}

func (x *GetEnterpriseStaffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseStaffResponse.ProtoReflect.Descriptor instead.
func (*GetEnterpriseStaffResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{57}
}

func (x *GetEnterpriseStaffResponse) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *GetEnterpriseStaffResponse) GetStaffEmail() *v1.StaffEmailDef {
	if x != nil {
		return x.StaffEmail
	}
	return nil
}

// delete enterprise staff request
type DeleteEnterpriseStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *DeleteEnterpriseStaffRequest) Reset() {
	*x = DeleteEnterpriseStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEnterpriseStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEnterpriseStaffRequest) ProtoMessage() {}

func (x *DeleteEnterpriseStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEnterpriseStaffRequest.ProtoReflect.Descriptor instead.
func (*DeleteEnterpriseStaffRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{58}
}

func (x *DeleteEnterpriseStaffRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteEnterpriseStaffRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// delete enterprise staff response
type DeleteEnterpriseStaffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteEnterpriseStaffResponse) Reset() {
	*x = DeleteEnterpriseStaffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEnterpriseStaffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEnterpriseStaffResponse) ProtoMessage() {}

func (x *DeleteEnterpriseStaffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEnterpriseStaffResponse.ProtoReflect.Descriptor instead.
func (*DeleteEnterpriseStaffResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{59}
}

// list staff email def response
type ListStaffEmailDefsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff ids
	StaffIds []int64 `protobuf:"varint,1,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
}

func (x *ListStaffEmailDefsRequest) Reset() {
	*x = ListStaffEmailDefsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffEmailDefsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffEmailDefsRequest) ProtoMessage() {}

func (x *ListStaffEmailDefsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffEmailDefsRequest.ProtoReflect.Descriptor instead.
func (*ListStaffEmailDefsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{60}
}

func (x *ListStaffEmailDefsRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// list staff email def response
type ListStaffEmailDefsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff email list
	StaffEmails map[int64]*v1.StaffEmailDef `protobuf:"bytes,1,rep,name=staff_emails,json=staffEmails,proto3" json:"staff_emails,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListStaffEmailDefsResponse) Reset() {
	*x = ListStaffEmailDefsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffEmailDefsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffEmailDefsResponse) ProtoMessage() {}

func (x *ListStaffEmailDefsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffEmailDefsResponse.ProtoReflect.Descriptor instead.
func (*ListStaffEmailDefsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{61}
}

func (x *ListStaffEmailDefsResponse) GetStaffEmails() map[int64]*v1.StaffEmailDef {
	if x != nil {
		return x.StaffEmails
	}
	return nil
}

// send invite link request
type SendInviteLinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// send invite link def
	InviteLink *v1.SendInviteLinkDef `protobuf:"bytes,1,opt,name=invite_link,json=inviteLink,proto3" json:"invite_link,omitempty"`
	// enterprise id
	EnterpriseId *int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
}

func (x *SendInviteLinkRequest) Reset() {
	*x = SendInviteLinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendInviteLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendInviteLinkRequest) ProtoMessage() {}

func (x *SendInviteLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendInviteLinkRequest.ProtoReflect.Descriptor instead.
func (*SendInviteLinkRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{62}
}

func (x *SendInviteLinkRequest) GetInviteLink() *v1.SendInviteLinkDef {
	if x != nil {
		return x.InviteLink
	}
	return nil
}

func (x *SendInviteLinkRequest) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

// send invite link response
type SendInviteLinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendInviteLinkResponse) Reset() {
	*x = SendInviteLinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendInviteLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendInviteLinkResponse) ProtoMessage() {}

func (x *SendInviteLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendInviteLinkResponse.ProtoReflect.Descriptor instead.
func (*SendInviteLinkResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{63}
}

// unlink staff request
type UnlinkStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	EnterpriseId *int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
}

func (x *UnlinkStaffRequest) Reset() {
	*x = UnlinkStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlinkStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlinkStaffRequest) ProtoMessage() {}

func (x *UnlinkStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlinkStaffRequest.ProtoReflect.Descriptor instead.
func (*UnlinkStaffRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{64}
}

func (x *UnlinkStaffRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UnlinkStaffRequest) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

// unlink staff response
type UnlinkStaffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UnlinkStaffResponse) Reset() {
	*x = UnlinkStaffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlinkStaffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlinkStaffResponse) ProtoMessage() {}

func (x *UnlinkStaffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlinkStaffResponse.ProtoReflect.Descriptor instead.
func (*UnlinkStaffResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{65}
}

// get staff login time request
type GetStaffLoginTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Check if the current time is within the allowed login time
	NeedCheckAllowed bool `protobuf:"varint,2,opt,name=need_check_allowed,json=needCheckAllowed,proto3" json:"need_check_allowed,omitempty"`
	// company id
	CompanyId *int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *GetStaffLoginTimeRequest) Reset() {
	*x = GetStaffLoginTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffLoginTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffLoginTimeRequest) ProtoMessage() {}

func (x *GetStaffLoginTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffLoginTimeRequest.ProtoReflect.Descriptor instead.
func (*GetStaffLoginTimeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{66}
}

func (x *GetStaffLoginTimeRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GetStaffLoginTimeRequest) GetNeedCheckAllowed() bool {
	if x != nil {
		return x.NeedCheckAllowed
	}
	return false
}

func (x *GetStaffLoginTimeRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// get staff login time response
type GetStaffLoginTimeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff login time
	LoginTime *v1.StaffLoginTimeModel `protobuf:"bytes,1,opt,name=login_time,json=loginTime,proto3" json:"login_time,omitempty"`
	// if current time is within the allowed login time
	IsAllowed *bool `protobuf:"varint,2,opt,name=is_allowed,json=isAllowed,proto3,oneof" json:"is_allowed,omitempty"`
	// if is_allowed is false, next time to login, unix timestamp in seconds
	NextLoginTime *int64 `protobuf:"varint,3,opt,name=next_login_time,json=nextLoginTime,proto3,oneof" json:"next_login_time,omitempty"`
}

func (x *GetStaffLoginTimeResponse) Reset() {
	*x = GetStaffLoginTimeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffLoginTimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffLoginTimeResponse) ProtoMessage() {}

func (x *GetStaffLoginTimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffLoginTimeResponse.ProtoReflect.Descriptor instead.
func (*GetStaffLoginTimeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{67}
}

func (x *GetStaffLoginTimeResponse) GetLoginTime() *v1.StaffLoginTimeModel {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

func (x *GetStaffLoginTimeResponse) GetIsAllowed() bool {
	if x != nil && x.IsAllowed != nil {
		return *x.IsAllowed
	}
	return false
}

func (x *GetStaffLoginTimeResponse) GetNextLoginTime() int64 {
	if x != nil && x.NextLoginTime != nil {
		return *x.NextLoginTime
	}
	return 0
}

// check staff login time request
type CheckStaffLoginTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *CheckStaffLoginTimeRequest) Reset() {
	*x = CheckStaffLoginTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckStaffLoginTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckStaffLoginTimeRequest) ProtoMessage() {}

func (x *CheckStaffLoginTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckStaffLoginTimeRequest.ProtoReflect.Descriptor instead.
func (*CheckStaffLoginTimeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{68}
}

func (x *CheckStaffLoginTimeRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// check staff login time response
type CheckStaffLoginTimeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if current time is within the allowed login time
	IsAllowed bool `protobuf:"varint,1,opt,name=is_allowed,json=isAllowed,proto3" json:"is_allowed,omitempty"`
	// if is_allowed is false, here's a pop up message
	PopUpMessage *string `protobuf:"bytes,2,opt,name=pop_up_message,json=popUpMessage,proto3,oneof" json:"pop_up_message,omitempty"`
}

func (x *CheckStaffLoginTimeResponse) Reset() {
	*x = CheckStaffLoginTimeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckStaffLoginTimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckStaffLoginTimeResponse) ProtoMessage() {}

func (x *CheckStaffLoginTimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckStaffLoginTimeResponse.ProtoReflect.Descriptor instead.
func (*CheckStaffLoginTimeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{69}
}

func (x *CheckStaffLoginTimeResponse) GetIsAllowed() bool {
	if x != nil {
		return x.IsAllowed
	}
	return false
}

func (x *CheckStaffLoginTimeResponse) GetPopUpMessage() string {
	if x != nil && x.PopUpMessage != nil {
		return *x.PopUpMessage
	}
	return ""
}

// get staffs with role request
type GetStaffByPhoneNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, deprecated, now only filter by role_id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *GetStaffByPhoneNumberRequest) Reset() {
	*x = GetStaffByPhoneNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffByPhoneNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffByPhoneNumberRequest) ProtoMessage() {}

func (x *GetStaffByPhoneNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffByPhoneNumberRequest.ProtoReflect.Descriptor instead.
func (*GetStaffByPhoneNumberRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{70}
}

func (x *GetStaffByPhoneNumberRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetStaffByPhoneNumberRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// get staffs with role response
type GetStaffByPhoneNumberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staff *v1.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
}

func (x *GetStaffByPhoneNumberResponse) Reset() {
	*x = GetStaffByPhoneNumberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffByPhoneNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffByPhoneNumberResponse) ProtoMessage() {}

func (x *GetStaffByPhoneNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffByPhoneNumberResponse.ProtoReflect.Descriptor instead.
func (*GetStaffByPhoneNumberResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{71}
}

func (x *GetStaffByPhoneNumberResponse) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

// get staffs with role request
type GetStaffsByRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	RoleId int64 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetStaffsByRoleRequest) Reset() {
	*x = GetStaffsByRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffsByRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffsByRoleRequest) ProtoMessage() {}

func (x *GetStaffsByRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffsByRoleRequest.ProtoReflect.Descriptor instead.
func (*GetStaffsByRoleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{72}
}

func (x *GetStaffsByRoleRequest) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *GetStaffsByRoleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get staffs with role response
type GetStaffsByRoleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staffs []*v1.StaffModel `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
}

func (x *GetStaffsByRoleResponse) Reset() {
	*x = GetStaffsByRoleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffsByRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffsByRoleResponse) ProtoMessage() {}

func (x *GetStaffsByRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffsByRoleResponse.ProtoReflect.Descriptor instead.
func (*GetStaffsByRoleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{73}
}

func (x *GetStaffsByRoleResponse) GetStaffs() []*v1.StaffModel {
	if x != nil {
		return x.Staffs
	}
	return nil
}

// GetBusinessStaffAvailabilityTypeRequest staff availability 配置
type GetBusinessStaffAvailabilityTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetBusinessStaffAvailabilityTypeRequest) Reset() {
	*x = GetBusinessStaffAvailabilityTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessStaffAvailabilityTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessStaffAvailabilityTypeRequest) ProtoMessage() {}

func (x *GetBusinessStaffAvailabilityTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessStaffAvailabilityTypeRequest.ProtoReflect.Descriptor instead.
func (*GetBusinessStaffAvailabilityTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{74}
}

func (x *GetBusinessStaffAvailabilityTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetBusinessStaffAvailabilityTypeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// UpdateBusinessStaffAvailabilityTypeRequest staff availability 配置
type UpdateBusinessStaffAvailabilityTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// availability type
	AvailabilityType v1.AvailabilityType `protobuf:"varint,2,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType" json:"availability_type,omitempty"`
}

func (x *UpdateBusinessStaffAvailabilityTypeRequest) Reset() {
	*x = UpdateBusinessStaffAvailabilityTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBusinessStaffAvailabilityTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBusinessStaffAvailabilityTypeRequest) ProtoMessage() {}

func (x *UpdateBusinessStaffAvailabilityTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBusinessStaffAvailabilityTypeRequest.ProtoReflect.Descriptor instead.
func (*UpdateBusinessStaffAvailabilityTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{75}
}

func (x *UpdateBusinessStaffAvailabilityTypeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateBusinessStaffAvailabilityTypeRequest) GetAvailabilityType() v1.AvailabilityType {
	if x != nil {
		return x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// GetBusinessStaffAvailabilityTypeResponse 返回staff 类型的结果
type GetBusinessStaffAvailabilityTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// availability type
	AvailabilityType v1.AvailabilityType `protobuf:"varint,1,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType" json:"availability_type,omitempty"`
}

func (x *GetBusinessStaffAvailabilityTypeResponse) Reset() {
	*x = GetBusinessStaffAvailabilityTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessStaffAvailabilityTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessStaffAvailabilityTypeResponse) ProtoMessage() {}

func (x *GetBusinessStaffAvailabilityTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessStaffAvailabilityTypeResponse.ProtoReflect.Descriptor instead.
func (*GetBusinessStaffAvailabilityTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{76}
}

func (x *GetBusinessStaffAvailabilityTypeResponse) GetAvailabilityType() v1.AvailabilityType {
	if x != nil {
		return x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// GetStaffAvailabilityRequest 获取staff 的 availability
type GetStaffAvailabilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id list, staff will init when staff_id no exist
	StaffIdList []int64 `protobuf:"varint,3,rep,packed,name=staff_id_list,json=staffIdList,proto3" json:"staff_id_list,omitempty"`
	// availability type
	AvailabilityType *v1.AvailabilityType `protobuf:"varint,4,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType,oneof" json:"availability_type,omitempty"`
}

func (x *GetStaffAvailabilityRequest) Reset() {
	*x = GetStaffAvailabilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityRequest) ProtoMessage() {}

func (x *GetStaffAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{77}
}

func (x *GetStaffAvailabilityRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetStaffAvailabilityRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffAvailabilityRequest) GetStaffIdList() []int64 {
	if x != nil {
		return x.StaffIdList
	}
	return nil
}

func (x *GetStaffAvailabilityRequest) GetAvailabilityType() v1.AvailabilityType {
	if x != nil && x.AvailabilityType != nil {
		return *x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// StaffAvailabilityResponse
type GetStaffAvailabilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff available list
	StaffAvailabilityList []*v1.StaffAvailability `protobuf:"bytes,1,rep,name=staff_availability_list,json=staffAvailabilityList,proto3" json:"staff_availability_list,omitempty"`
}

func (x *GetStaffAvailabilityResponse) Reset() {
	*x = GetStaffAvailabilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityResponse) ProtoMessage() {}

func (x *GetStaffAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{78}
}

func (x *GetStaffAvailabilityResponse) GetStaffAvailabilityList() []*v1.StaffAvailability {
	if x != nil {
		return x.StaffAvailabilityList
	}
	return nil
}

// GetStaffCalenderViewRequest
type GetStaffCalenderViewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// start date
	StartDate string `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// availability type
	AvailabilityType *v1.AvailabilityType `protobuf:"varint,5,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType,oneof" json:"availability_type,omitempty"`
}

func (x *GetStaffCalenderViewRequest) Reset() {
	*x = GetStaffCalenderViewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffCalenderViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffCalenderViewRequest) ProtoMessage() {}

func (x *GetStaffCalenderViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffCalenderViewRequest.ProtoReflect.Descriptor instead.
func (*GetStaffCalenderViewRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{79}
}

func (x *GetStaffCalenderViewRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetStaffCalenderViewRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffCalenderViewRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GetStaffCalenderViewRequest) GetAvailabilityType() v1.AvailabilityType {
	if x != nil && x.AvailabilityType != nil {
		return *x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// CalenderStaff
type CalenderStaff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// schedule type
	ScheduleType v1.ScheduleType `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.organization.v1.ScheduleType" json:"schedule_type,omitempty"`
	// slot daily setting
	SlotAvailabilityDayMap map[string]*v1.SlotAvailabilityDay `protobuf:"bytes,4,rep,name=slot_availability_day_map,json=slotAvailabilityDayMap,proto3" json:"slot_availability_day_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// slot start sunday
	SlotStartSunday string `protobuf:"bytes,6,opt,name=slot_start_sunday,json=slotStartSunday,proto3" json:"slot_start_sunday,omitempty"`
	// schedule type
	TimeScheduleType v1.ScheduleType `protobuf:"varint,7,opt,name=time_schedule_type,json=timeScheduleType,proto3,enum=moego.models.organization.v1.ScheduleType" json:"time_schedule_type,omitempty"`
	// time daily setting
	TimeAvailabilityDayMap map[string]*v1.TimeAvailabilityDay `protobuf:"bytes,8,rep,name=time_availability_day_map,json=timeAvailabilityDayMap,proto3" json:"time_availability_day_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// time start sunday
	TimeStartSunday string `protobuf:"bytes,9,opt,name=time_start_sunday,json=timeStartSunday,proto3" json:"time_start_sunday,omitempty"`
}

func (x *CalenderStaff) Reset() {
	*x = CalenderStaff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalenderStaff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalenderStaff) ProtoMessage() {}

func (x *CalenderStaff) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalenderStaff.ProtoReflect.Descriptor instead.
func (*CalenderStaff) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{80}
}

func (x *CalenderStaff) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CalenderStaff) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *CalenderStaff) GetScheduleType() v1.ScheduleType {
	if x != nil {
		return x.ScheduleType
	}
	return v1.ScheduleType(0)
}

func (x *CalenderStaff) GetSlotAvailabilityDayMap() map[string]*v1.SlotAvailabilityDay {
	if x != nil {
		return x.SlotAvailabilityDayMap
	}
	return nil
}

func (x *CalenderStaff) GetSlotStartSunday() string {
	if x != nil {
		return x.SlotStartSunday
	}
	return ""
}

func (x *CalenderStaff) GetTimeScheduleType() v1.ScheduleType {
	if x != nil {
		return x.TimeScheduleType
	}
	return v1.ScheduleType(0)
}

func (x *CalenderStaff) GetTimeAvailabilityDayMap() map[string]*v1.TimeAvailabilityDay {
	if x != nil {
		return x.TimeAvailabilityDayMap
	}
	return nil
}

func (x *CalenderStaff) GetTimeStartSunday() string {
	if x != nil {
		return x.TimeStartSunday
	}
	return ""
}

// GetStaffCalenderViewResponse
type GetStaffCalenderViewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff available list
	StaffList []*CalenderStaff `protobuf:"bytes,1,rep,name=staff_list,json=staffList,proto3" json:"staff_list,omitempty"`
}

func (x *GetStaffCalenderViewResponse) Reset() {
	*x = GetStaffCalenderViewResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffCalenderViewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffCalenderViewResponse) ProtoMessage() {}

func (x *GetStaffCalenderViewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffCalenderViewResponse.ProtoReflect.Descriptor instead.
func (*GetStaffCalenderViewResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{81}
}

func (x *GetStaffCalenderViewResponse) GetStaffList() []*CalenderStaff {
	if x != nil {
		return x.StaffList
	}
	return nil
}

// UpdateStaffAvailabilityRequest
type UpdateStaffAvailabilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff available list
	StaffAvailabilityList []*v1.StaffAvailabilityDef `protobuf:"bytes,3,rep,name=staff_availability_list,json=staffAvailabilityList,proto3" json:"staff_availability_list,omitempty"`
}

func (x *UpdateStaffAvailabilityRequest) Reset() {
	*x = UpdateStaffAvailabilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffAvailabilityRequest) ProtoMessage() {}

func (x *UpdateStaffAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*UpdateStaffAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{82}
}

func (x *UpdateStaffAvailabilityRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateStaffAvailabilityRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateStaffAvailabilityRequest) GetStaffAvailabilityList() []*v1.StaffAvailabilityDef {
	if x != nil {
		return x.StaffAvailabilityList
	}
	return nil
}

// UpdateStaffAvailabilityRequest
type UpdateStaffAvailabilityOverrideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// staff availability override day list
	OverrideDays []*v1.SlotAvailabilityDayDef `protobuf:"bytes,4,rep,name=override_days,json=overrideDays,proto3" json:"override_days,omitempty"`
	// time override days
	TimeOverrideDays []*v1.TimeAvailabilityDayDef `protobuf:"bytes,5,rep,name=time_override_days,json=timeOverrideDays,proto3" json:"time_override_days,omitempty"`
}

func (x *UpdateStaffAvailabilityOverrideRequest) Reset() {
	*x = UpdateStaffAvailabilityOverrideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffAvailabilityOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffAvailabilityOverrideRequest) ProtoMessage() {}

func (x *UpdateStaffAvailabilityOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffAvailabilityOverrideRequest.ProtoReflect.Descriptor instead.
func (*UpdateStaffAvailabilityOverrideRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{83}
}

func (x *UpdateStaffAvailabilityOverrideRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateStaffAvailabilityOverrideRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateStaffAvailabilityOverrideRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateStaffAvailabilityOverrideRequest) GetOverrideDays() []*v1.SlotAvailabilityDayDef {
	if x != nil {
		return x.OverrideDays
	}
	return nil
}

func (x *UpdateStaffAvailabilityOverrideRequest) GetTimeOverrideDays() []*v1.TimeAvailabilityDayDef {
	if x != nil {
		return x.TimeOverrideDays
	}
	return nil
}

// DeleteStaffAvailabilityOverrideRequest
type DeleteStaffAvailabilityOverrideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// staff availability override day list
	OverrideDays []string `protobuf:"bytes,4,rep,name=override_days,json=overrideDays,proto3" json:"override_days,omitempty"`
	// availability type
	AvailabilityType *v1.AvailabilityType `protobuf:"varint,5,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType,oneof" json:"availability_type,omitempty"`
}

func (x *DeleteStaffAvailabilityOverrideRequest) Reset() {
	*x = DeleteStaffAvailabilityOverrideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffAvailabilityOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffAvailabilityOverrideRequest) ProtoMessage() {}

func (x *DeleteStaffAvailabilityOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffAvailabilityOverrideRequest.ProtoReflect.Descriptor instead.
func (*DeleteStaffAvailabilityOverrideRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{84}
}

func (x *DeleteStaffAvailabilityOverrideRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeleteStaffAvailabilityOverrideRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DeleteStaffAvailabilityOverrideRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *DeleteStaffAvailabilityOverrideRequest) GetOverrideDays() []string {
	if x != nil {
		return x.OverrideDays
	}
	return nil
}

func (x *DeleteStaffAvailabilityOverrideRequest) GetAvailabilityType() v1.AvailabilityType {
	if x != nil && x.AvailabilityType != nil {
		return *x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// DeleteStaffAvailabilityOverrideResponse
type DeleteStaffAvailabilityOverrideResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteStaffAvailabilityOverrideResponse) Reset() {
	*x = DeleteStaffAvailabilityOverrideResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffAvailabilityOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffAvailabilityOverrideResponse) ProtoMessage() {}

func (x *DeleteStaffAvailabilityOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffAvailabilityOverrideResponse.ProtoReflect.Descriptor instead.
func (*DeleteStaffAvailabilityOverrideResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{85}
}

// GetStaffAvailabilityOverrideRequest
type GetStaffAvailabilityOverrideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id, if empty, will return all staffs
	StaffIds []int64 `protobuf:"varint,3,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// availability type
	AvailabilityType *v1.AvailabilityType `protobuf:"varint,4,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType,oneof" json:"availability_type,omitempty"`
}

func (x *GetStaffAvailabilityOverrideRequest) Reset() {
	*x = GetStaffAvailabilityOverrideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityOverrideRequest) ProtoMessage() {}

func (x *GetStaffAvailabilityOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityOverrideRequest.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityOverrideRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{86}
}

func (x *GetStaffAvailabilityOverrideRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetStaffAvailabilityOverrideRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffAvailabilityOverrideRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *GetStaffAvailabilityOverrideRequest) GetAvailabilityType() v1.AvailabilityType {
	if x != nil && x.AvailabilityType != nil {
		return *x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// SlotAvailabilityDayList
type SlotAvailabilityDayList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// slot availability day
	Slots []*v1.SlotAvailabilityDay `protobuf:"bytes,1,rep,name=slots,proto3" json:"slots,omitempty"`
}

func (x *SlotAvailabilityDayList) Reset() {
	*x = SlotAvailabilityDayList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotAvailabilityDayList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotAvailabilityDayList) ProtoMessage() {}

func (x *SlotAvailabilityDayList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotAvailabilityDayList.ProtoReflect.Descriptor instead.
func (*SlotAvailabilityDayList) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{87}
}

func (x *SlotAvailabilityDayList) GetSlots() []*v1.SlotAvailabilityDay {
	if x != nil {
		return x.Slots
	}
	return nil
}

// TimeAvailabilityDayList
type TimeAvailabilityDayList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// time availability day
	Slots []*v1.TimeAvailabilityDay `protobuf:"bytes,1,rep,name=slots,proto3" json:"slots,omitempty"`
}

func (x *TimeAvailabilityDayList) Reset() {
	*x = TimeAvailabilityDayList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeAvailabilityDayList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeAvailabilityDayList) ProtoMessage() {}

func (x *TimeAvailabilityDayList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeAvailabilityDayList.ProtoReflect.Descriptor instead.
func (*TimeAvailabilityDayList) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{88}
}

func (x *TimeAvailabilityDayList) GetSlots() []*v1.TimeAvailabilityDay {
	if x != nil {
		return x.Slots
	}
	return nil
}

// GetStaffAvailabilityOverrideResponse
type GetStaffAvailabilityOverrideResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key is staff id
	OverrideDays map[int64]*SlotAvailabilityDayList `protobuf:"bytes,1,rep,name=override_days,json=overrideDays,proto3" json:"override_days,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// time override days
	TimeOverrideDays map[int64]*TimeAvailabilityDayList `protobuf:"bytes,3,rep,name=time_override_days,json=timeOverrideDays,proto3" json:"time_override_days,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetStaffAvailabilityOverrideResponse) Reset() {
	*x = GetStaffAvailabilityOverrideResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityOverrideResponse) ProtoMessage() {}

func (x *GetStaffAvailabilityOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityOverrideResponse.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityOverrideResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{89}
}

func (x *GetStaffAvailabilityOverrideResponse) GetOverrideDays() map[int64]*SlotAvailabilityDayList {
	if x != nil {
		return x.OverrideDays
	}
	return nil
}

func (x *GetStaffAvailabilityOverrideResponse) GetTimeOverrideDays() map[int64]*TimeAvailabilityDayList {
	if x != nil {
		return x.TimeOverrideDays
	}
	return nil
}

// get staff availability status
type GetStaffAvailabilityStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id list
	StaffIdList []int64 `protobuf:"varint,2,rep,packed,name=staff_id_list,json=staffIdList,proto3" json:"staff_id_list,omitempty"`
}

func (x *GetStaffAvailabilityStatusRequest) Reset() {
	*x = GetStaffAvailabilityStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityStatusRequest) ProtoMessage() {}

func (x *GetStaffAvailabilityStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityStatusRequest.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{90}
}

func (x *GetStaffAvailabilityStatusRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffAvailabilityStatusRequest) GetStaffIdList() []int64 {
	if x != nil {
		return x.StaffIdList
	}
	return nil
}

// GetStaffAvailabilityStatusResponse
type GetStaffAvailabilityStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// map staff id to is available
	StaffAvailability map[int64]bool `protobuf:"bytes,1,rep,name=staff_availability,json=staffAvailability,proto3" json:"staff_availability,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *GetStaffAvailabilityStatusResponse) Reset() {
	*x = GetStaffAvailabilityStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityStatusResponse) ProtoMessage() {}

func (x *GetStaffAvailabilityStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityStatusResponse.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{91}
}

func (x *GetStaffAvailabilityStatusResponse) GetStaffAvailability() map[int64]bool {
	if x != nil {
		return x.StaffAvailability
	}
	return nil
}

// UpdateStaffAvailabilityResponse
type UpdateStaffAvailabilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateStaffAvailabilityResponse) Reset() {
	*x = UpdateStaffAvailabilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffAvailabilityResponse) ProtoMessage() {}

func (x *UpdateStaffAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*UpdateStaffAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{92}
}

// ListSlotFreeServicesRequest
type ListSlotFreeServicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,2,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
}

func (x *ListSlotFreeServicesRequest) Reset() {
	*x = ListSlotFreeServicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSlotFreeServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSlotFreeServicesRequest) ProtoMessage() {}

func (x *ListSlotFreeServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSlotFreeServicesRequest.ProtoReflect.Descriptor instead.
func (*ListSlotFreeServicesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{93}
}

func (x *ListSlotFreeServicesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListSlotFreeServicesRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// ListSlotFreeServicesResponse
type ListSlotFreeServicesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// slot free staff service defs
	Defs []*v1.SlotFreeStaffServiceDef `protobuf:"bytes,1,rep,name=defs,proto3" json:"defs,omitempty"`
}

func (x *ListSlotFreeServicesResponse) Reset() {
	*x = ListSlotFreeServicesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSlotFreeServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSlotFreeServicesResponse) ProtoMessage() {}

func (x *ListSlotFreeServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSlotFreeServicesResponse.ProtoReflect.Descriptor instead.
func (*ListSlotFreeServicesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{94}
}

func (x *ListSlotFreeServicesResponse) GetDefs() []*v1.SlotFreeStaffServiceDef {
	if x != nil {
		return x.Defs
	}
	return nil
}

// UpdateSlotFreeServicesRequest
type UpdateSlotFreeServicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// slot free staff service defs
	Defs []*v1.SlotFreeStaffServiceDef `protobuf:"bytes,3,rep,name=defs,proto3" json:"defs,omitempty"`
}

func (x *UpdateSlotFreeServicesRequest) Reset() {
	*x = UpdateSlotFreeServicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSlotFreeServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSlotFreeServicesRequest) ProtoMessage() {}

func (x *UpdateSlotFreeServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSlotFreeServicesRequest.ProtoReflect.Descriptor instead.
func (*UpdateSlotFreeServicesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{95}
}

func (x *UpdateSlotFreeServicesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateSlotFreeServicesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateSlotFreeServicesRequest) GetDefs() []*v1.SlotFreeStaffServiceDef {
	if x != nil {
		return x.Defs
	}
	return nil
}

// UpdateSlotFreeServicesResponse
type UpdateSlotFreeServicesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateSlotFreeServicesResponse) Reset() {
	*x = UpdateSlotFreeServicesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSlotFreeServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSlotFreeServicesResponse) ProtoMessage() {}

func (x *UpdateSlotFreeServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSlotFreeServicesResponse.ProtoReflect.Descriptor instead.
func (*UpdateSlotFreeServicesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{96}
}

// result
type InitStaffAvailabilityResponse_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int32 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int32 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// schedule type
	ScheduleType int32 `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3" json:"schedule_type,omitempty"`
	// day of week
	DayOfWeek dayofweek.DayOfWeek `protobuf:"varint,4,opt,name=day_of_week,json=dayOfWeek,proto3,enum=google.type.DayOfWeek" json:"day_of_week,omitempty"`
	// staff name
	StaffName string `protobuf:"bytes,5,opt,name=staff_name,json=staffName,proto3" json:"staff_name,omitempty"`
	// start time
	ObStartTime int32 `protobuf:"varint,6,opt,name=ob_start_time,json=obStartTime,proto3" json:"ob_start_time,omitempty"`
	// end time
	ObEndTime int32 `protobuf:"varint,7,opt,name=ob_end_time,json=obEndTime,proto3" json:"ob_end_time,omitempty"`
	// start time
	SmStartTime int32 `protobuf:"varint,8,opt,name=sm_start_time,json=smStartTime,proto3" json:"sm_start_time,omitempty"`
	// end time
	SmEndTime int32 `protobuf:"varint,9,opt,name=sm_end_time,json=smEndTime,proto3" json:"sm_end_time,omitempty"`
}

func (x *InitStaffAvailabilityResponse_Result) Reset() {
	*x = InitStaffAvailabilityResponse_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitStaffAvailabilityResponse_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitStaffAvailabilityResponse_Result) ProtoMessage() {}

func (x *InitStaffAvailabilityResponse_Result) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitStaffAvailabilityResponse_Result.ProtoReflect.Descriptor instead.
func (*InitStaffAvailabilityResponse_Result) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *InitStaffAvailabilityResponse_Result) GetBusinessId() int32 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *InitStaffAvailabilityResponse_Result) GetStaffId() int32 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *InitStaffAvailabilityResponse_Result) GetScheduleType() int32 {
	if x != nil {
		return x.ScheduleType
	}
	return 0
}

func (x *InitStaffAvailabilityResponse_Result) GetDayOfWeek() dayofweek.DayOfWeek {
	if x != nil {
		return x.DayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

func (x *InitStaffAvailabilityResponse_Result) GetStaffName() string {
	if x != nil {
		return x.StaffName
	}
	return ""
}

func (x *InitStaffAvailabilityResponse_Result) GetObStartTime() int32 {
	if x != nil {
		return x.ObStartTime
	}
	return 0
}

func (x *InitStaffAvailabilityResponse_Result) GetObEndTime() int32 {
	if x != nil {
		return x.ObEndTime
	}
	return 0
}

func (x *InitStaffAvailabilityResponse_Result) GetSmStartTime() int32 {
	if x != nil {
		return x.SmStartTime
	}
	return 0
}

func (x *InitStaffAvailabilityResponse_Result) GetSmEndTime() int32 {
	if x != nil {
		return x.SmEndTime
	}
	return 0
}

// filter
type GetEnterpriseStaffsByAccountIdRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// include deleted staffs, default is false
	IncludeDeleted bool `protobuf:"varint,1,opt,name=include_deleted,json=includeDeleted,proto3" json:"include_deleted,omitempty"`
	// include not allow login in, default is false
	IncludeNotAllowLoginIn bool `protobuf:"varint,2,opt,name=include_not_allow_login_in,json=includeNotAllowLoginIn,proto3" json:"include_not_allow_login_in,omitempty"`
}

func (x *GetEnterpriseStaffsByAccountIdRequest_Filter) Reset() {
	*x = GetEnterpriseStaffsByAccountIdRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseStaffsByAccountIdRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseStaffsByAccountIdRequest_Filter) ProtoMessage() {}

func (x *GetEnterpriseStaffsByAccountIdRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_staff_service_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseStaffsByAccountIdRequest_Filter.ProtoReflect.Descriptor instead.
func (*GetEnterpriseStaffsByAccountIdRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_staff_service_proto_rawDescGZIP(), []int{50, 0}
}

func (x *GetEnterpriseStaffsByAccountIdRequest_Filter) GetIncludeDeleted() bool {
	if x != nil {
		return x.IncludeDeleted
	}
	return false
}

func (x *GetEnterpriseStaffsByAccountIdRequest_Filter) GetIncludeNotAllowLoginIn() bool {
	if x != nil {
		return x.IncludeNotAllowLoginIn
	}
	return false
}

var File_moego_service_organization_v1_staff_service_proto protoreflect.FileDescriptor

var file_moego_service_organization_v1_staff_service_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x64, 0x61, 0x79, 0x6f, 0x66, 0x77, 0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65,
	0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xda, 0x02, 0x0a, 0x1c, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x25,
	0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x38, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x0f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x34, 0x0a, 0x0f, 0x65, 0x6e, 0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0xc9,
	0x03, 0x0a, 0x1d, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5d, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a,
	0xc8, 0x02, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x0b, 0x64,
	0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x09, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x57,
	0x65, 0x65, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x62, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x6f, 0x62, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6f, 0x62, 0x45,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x6d, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73,
	0x6d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x73, 0x6d,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x73, 0x6d, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa1, 0x07, 0x0a, 0x12, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x51, 0x0a,
	0x0d, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44,
	0x65, 0x66, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x65, 0x0a, 0x10, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x66, 0x48, 0x00, 0x52, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x5f, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x6a, 0x0a, 0x14, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52, 0x13, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x88, 0x01, 0x01, 0x12, 0x62, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x5f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x50, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x66, 0x48, 0x03, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x44, 0x65, 0x66, 0x48, 0x04, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69,
	0x6e, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x53, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x48, 0x05, 0x52, 0x09, 0x6c, 0x6f,
	0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x77,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x25,
	0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd1, 0x07, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0e,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x56, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x66, 0x66, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a, 0x10, 0x77, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x0f, 0x77, 0x6f,
	0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x5f, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52,
	0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x88, 0x01,
	0x01, 0x12, 0x6a, 0x0a, 0x14, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x66, 0x48, 0x03, 0x52, 0x13, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x62, 0x0a,
	0x0f, 0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x79, 0x72, 0x6f,
	0x6c, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x48, 0x04, 0x52, 0x0e,
	0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x88, 0x01,
	0x01, 0x12, 0x5b, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x44, 0x65, 0x66, 0x48, 0x05, 0x52,
	0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x53,
	0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x44, 0x65, 0x66, 0x48, 0x06, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x42, 0x17, 0x0a,
	0x15, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x61, 0x79, 0x72, 0x6f,
	0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6c,
	0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x2f, 0x0a, 0x13, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xd4, 0x01, 0x0a, 0x12, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x17, 0x69, 0x73,
	0x5f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x13, 0x69,
	0x73, 0x4e, 0x65, 0x65, 0x64, 0x54, 0x6f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x65,
	0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x22, 0x2f, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x86, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x28, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0c, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x22, 0xb6, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x46, 0x75, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x0e,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x01, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0xe9,
	0x05, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x46, 0x75, 0x6c, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x51, 0x0a,
	0x0d, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x61, 0x73, 0x69, 0x63, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x60, 0x0a, 0x10, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x66, 0x52, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65, 0x66, 0x52,
	0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x65,
	0x0a, 0x14, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66,
	0x52, 0x13, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x5d, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x50, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x12, 0x4c, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x46, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x76, 0x61, 0x6e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x56, 0x61, 0x6e, 0x44, 0x65, 0x66,
	0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x56, 0x61, 0x6e, 0x12, 0x4e, 0x0a, 0x0a, 0x6c, 0x6f,
	0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x52,
	0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd1, 0x02, 0x0a, 0x21, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x29, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x10, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x34, 0x0a,
	0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x42, 0x79, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0xac,
	0x01, 0x0a, 0x22, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x79, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65,
	0x66, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x45, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x22, 0x60, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0xa9, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x22, 0x5b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x22,
	0xd9, 0x01, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4c, 0x61, 0x73, 0x74, 0x56, 0x69, 0x73, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x76, 0x69, 0x73, 0x69, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x76, 0x69, 0x73, 0x69, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3c, 0x0a,
	0x16, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x56, 0x69, 0x73, 0x69,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x26, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x61, 0x73, 0x74,
	0x56, 0x69, 0x73, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf4, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa,
	0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x0f, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22, 0xdf, 0x01, 0x0a,
	0x25, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x44, 0x65, 0x66,
	0x52, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73,
	0x12, 0x30, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x8b,
	0x01, 0x0a, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x8b, 0x01, 0x0a,
	0x2f, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x58, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0x48, 0x0a, 0x16, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10,
	0xc8, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x73, 0x22, 0x5b, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x73, 0x22, 0x93, 0x01, 0x0a, 0x1c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xaf, 0x01, 0x0a, 0x1d, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x4c, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa3, 0x01, 0x0a, 0x1e, 0x47, 0x65,
	0x74, 0x53, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0e,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22,
	0x7b, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x58, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0x84, 0x01, 0x0a,
	0x21, 0x47, 0x65, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x5f, 0x0a, 0x12, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x44, 0x65,
	0x66, 0x52, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x73, 0x22, 0x41, 0x0a, 0x17, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x55, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x72, 0x6f,
	0x6d, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0b, 0x74, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x6f, 0x0a,
	0x18, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x0d, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66,
	0x52, 0x0c, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x22, 0x67,
	0x0a, 0x19, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x69, 0x74, 0x68,
	0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x09, 0x18, 0x01, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x3d, 0x0a, 0x1a, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x69, 0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x84, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x13, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x11, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x6a, 0x0a,
	0x22, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x61, 0x73, 0x69, 0x63, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0xa2, 0x02, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32,
	0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64,
	0x7b, 0x32, 0x7d, 0x24, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x09, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa,
	0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x2f, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x66, 0x6f,
	0x72, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x73, 0x65, 0x6c, 0x76, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x0f, 0x69, 0x73, 0x46, 0x6f, 0x72, 0x54, 0x68, 0x65, 0x6d, 0x73,
	0x65, 0x6c, 0x76, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x69, 0x73, 0x5f,
	0x66, 0x6f, 0x72, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x73, 0x65, 0x6c, 0x76, 0x65, 0x73, 0x22, 0x7e,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a,
	0x13, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49,
	0x6e, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x52, 0x10, 0x63, 0x6c,
	0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0xbb,
	0x02, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x32, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x26, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x08, 0x6c,
	0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28,
	0x00, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x2f, 0x0a, 0x1d,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x89, 0x01,
	0x0a, 0x1a, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x64,
	0x60, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x1d, 0x0a, 0x1b, 0x53, 0x65, 0x6e,
	0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x6e, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3a, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x22, 0xf0, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x4f, 0x77, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x1a, 0x69, 0x0a, 0x0e,
	0x4f, 0x77, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf8, 0x03, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x48, 0x01, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x32, 0x48, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x03, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x60,
	0x0a, 0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x10,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x4c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x48, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x25,
	0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x05, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x09, 0x0a, 0x07, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x22, 0x5b, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x22,
	0xe8, 0x04, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x22, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01,
	0x12, 0x4c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x48, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x65,
	0x0a, 0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x48, 0x05,
	0x52, 0x10, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x18, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x6e, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x15, 0x77, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x49, 0x6e, 0x41, 0x6c, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x07, 0x52,
	0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e,
	0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x0a,
	0x0a, 0x08, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x5b, 0x0a, 0x19, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x22, 0xa3, 0x02, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42,
	0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x63, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x6d,
	0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x12, 0x3a, 0x0a, 0x1a, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x74,
	0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4e, 0x6f,
	0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x22, 0x6a, 0x0a,
	0x26, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0xfa, 0x01, 0x0a, 0x1c, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x48, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x5b, 0x0a, 0x0b, 0x69, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c,
	0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0xd2, 0x01, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x3e, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x12, 0x4c, 0x0a,
	0x0b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x52,
	0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x9b, 0x02, 0x0a, 0x1c,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x4d, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x48, 0x00, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x5b, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c,
	0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x0a,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0xad, 0x01, 0x0a, 0x1d, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x12, 0x4c, 0x0a, 0x0b, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x50, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0xaa, 0x01, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x12, 0x4c, 0x0a, 0x0b, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x53, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0x1f, 0x0a,
	0x1d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x48,
	0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x44, 0x65, 0x66, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x09, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e,
	0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x22, 0xf8, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x6b, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x41, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xa5, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x50, 0x0a,
	0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b,
	0x44, 0x65, 0x66, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x12,
	0x28, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x18, 0x0a, 0x16, 0x53,
	0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x72, 0x0a, 0x12, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x55, 0x6e, 0x6c,
	0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x9f, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x6e,
	0x65, 0x65, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12,
	0x22, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x22, 0xe1, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x50, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x6c,
	0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x01, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x69,
	0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x40, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x7a, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x41,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x0e, 0x70, 0x6f, 0x70, 0x5f, 0x75, 0x70,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x0c, 0x70, 0x6f, 0x70, 0x55, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x6f, 0x70, 0x5f, 0x75, 0x70, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x74, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x0b, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x5f, 0x0a, 0x1d, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x22, 0x62, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52,
	0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22,
	0x5b, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x52, 0x6f,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0x7b, 0x0a, 0x27,
	0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0xb3, 0x01, 0x0a, 0x2a, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x5b, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x87, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x11,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa1, 0x02, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x6c, 0x0a, 0x11, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x87, 0x01,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67,
	0x0a, 0x17, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x15, 0x73, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x92, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x6c, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xd8, 0x06, 0x0a,
	0x0d, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x19,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x0d,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x83, 0x01,
	0x0a, 0x19, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x2e,
	0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x44, 0x61, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x73, 0x6c, 0x6f,
	0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79,
	0x4d, 0x61, 0x70, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x73, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x73, 0x6c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x12,
	0x58, 0x0a, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x19, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x64, 0x61, 0x79, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x74, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4d, 0x61, 0x70, 0x12,
	0x2a, 0x0a, 0x11, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x75,
	0x6e, 0x64, 0x61, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x1a, 0x7c, 0x0a, 0x1b, 0x53,
	0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44,
	0x61, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x7c, 0x0a, 0x1b, 0x54, 0x69, 0x6d,
	0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6b, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0xe8, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x74, 0x0a, 0x17, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x15, 0x73, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0xf1, 0x02, 0x0a, 0x26, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x12, 0x63, 0x0a, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x79,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x0c, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x6c, 0x0a, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x44, 0x61, 0x79, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10,
	0x64, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x44,
	0x61, 0x79, 0x73, 0x22, 0xc7, 0x02, 0x0a, 0x26, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x6c, 0x0a, 0x11, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x29, 0x0a,
	0x27, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa2, 0x02, 0x0a, 0x23, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x6c, 0x0a, 0x11, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x62, 0x0a,
	0x17, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x44, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x05, 0x73, 0x6c, 0x6f, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x05, 0x73, 0x6c, 0x6f, 0x74,
	0x73, 0x22, 0x62, 0x0a, 0x17, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x05,
	0x73, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x05,
	0x73, 0x6c, 0x6f, 0x74, 0x73, 0x22, 0xa2, 0x04, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a,
	0x0a, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x44, 0x61, 0x79, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x87, 0x01, 0x0a, 0x12, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x79,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x59, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x44, 0x61, 0x79, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x44, 0x61, 0x79, 0x73, 0x1a, 0x77, 0x0a, 0x11, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x44, 0x61, 0x79, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4c, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x7b, 0x0a,
	0x15, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x44, 0x61, 0x79,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7b, 0x0a, 0x21, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xf4, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87,
	0x01, 0x0a, 0x12, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x58, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x73, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x1a, 0x44, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x21,
	0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x6e, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x09, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x73, 0x22, 0x69, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x49, 0x0a, 0x04, 0x64, 0x65, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x04, 0x64, 0x65, 0x66, 0x73, 0x22, 0xc6, 0x01, 0x0a,
	0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x53, 0x0a, 0x04, 0x64, 0x65, 0x66, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c,
	0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52,
	0x04, 0x64, 0x65, 0x66, 0x73, 0x22, 0x20, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xf6, 0x36, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x76, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x7f, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x8b, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x46, 0x75,
	0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x46, 0x75, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x46, 0x75, 0x6c, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x91, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xad, 0x01, 0x0a,
	0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x61,
	0x73, 0x74, 0x56, 0x69, 0x73, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12,
	0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x61, 0x73,
	0x74, 0x56, 0x69, 0x73, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x56, 0x69, 0x73, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x0b,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa1, 0x01, 0x0a,
	0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x79, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0xaa, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79,
	0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x73, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f,
	0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x80, 0x01,
	0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x49, 0x64,
	0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x49, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x98, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x19,
	0x47, 0x65, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x68, 0x6f,
	0x77, 0x4f, 0x6e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x68, 0x6f, 0x77,
	0x4f, 0x6e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x10, 0x4d,
	0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x57, 0x69, 0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x57, 0x69, 0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x69,
	0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0xa3, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79,
	0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8c, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x6c,
	0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x39,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f,
	0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xc8, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x73, 0x12, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x92, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x13, 0x53,
	0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69,
	0x6e, 0x6b, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x6e,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86,
	0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xad, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42,
	0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a,
	0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x89, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01,
	0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x73, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x44, 0x65, 0x66, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d,
	0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b,
	0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74,
	0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a,
	0x0b, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x6e, 0x6c,
	0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a,
	0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x80, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79,
	0x52, 0x6f, 0x6c, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79,
	0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xb5, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb2, 0x01, 0x0a, 0x23,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x91, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0xaa, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb2,
	0x01, 0x0a, 0x1f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x12, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0xa9, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x8f, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x92, 0x01, 0x0a, 0x15, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x95, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72,
	0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x8f, 0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x64, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_organization_v1_staff_service_proto_rawDescOnce sync.Once
	file_moego_service_organization_v1_staff_service_proto_rawDescData = file_moego_service_organization_v1_staff_service_proto_rawDesc
)

func file_moego_service_organization_v1_staff_service_proto_rawDescGZIP() []byte {
	file_moego_service_organization_v1_staff_service_proto_rawDescOnce.Do(func() {
		file_moego_service_organization_v1_staff_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_organization_v1_staff_service_proto_rawDescData)
	})
	return file_moego_service_organization_v1_staff_service_proto_rawDescData
}

var file_moego_service_organization_v1_staff_service_proto_msgTypes = make([]protoimpl.MessageInfo, 106)
var file_moego_service_organization_v1_staff_service_proto_goTypes = []interface{}{
	(*InitStaffAvailabilityRequest)(nil),                    // 0: moego.service.organization.v1.InitStaffAvailabilityRequest
	(*InitStaffAvailabilityResponse)(nil),                   // 1: moego.service.organization.v1.InitStaffAvailabilityResponse
	(*CreateStaffRequest)(nil),                              // 2: moego.service.organization.v1.CreateStaffRequest
	(*CreateStaffResponse)(nil),                             // 3: moego.service.organization.v1.CreateStaffResponse
	(*UpdateStaffRequest)(nil),                              // 4: moego.service.organization.v1.UpdateStaffRequest
	(*UpdateStaffResponse)(nil),                             // 5: moego.service.organization.v1.UpdateStaffResponse
	(*DeleteStaffRequest)(nil),                              // 6: moego.service.organization.v1.DeleteStaffRequest
	(*DeleteStaffResponse)(nil),                             // 7: moego.service.organization.v1.DeleteStaffResponse
	(*GetStaffDetailRequest)(nil),                           // 8: moego.service.organization.v1.GetStaffDetailRequest
	(*GetStaffDetailResponse)(nil),                          // 9: moego.service.organization.v1.GetStaffDetailResponse
	(*GetStaffFullDetailRequest)(nil),                       // 10: moego.service.organization.v1.GetStaffFullDetailRequest
	(*GetStaffFullDetailResponse)(nil),                      // 11: moego.service.organization.v1.GetStaffFullDetailResponse
	(*QueryStaffListByPaginationRequest)(nil),               // 12: moego.service.organization.v1.QueryStaffListByPaginationRequest
	(*QueryStaffListByPaginationResponse)(nil),              // 13: moego.service.organization.v1.QueryStaffListByPaginationResponse
	(*GetStaffsByAccountIdRequest)(nil),                     // 14: moego.service.organization.v1.GetStaffsByAccountIdRequest
	(*GetStaffsByAccountIdResponse)(nil),                    // 15: moego.service.organization.v1.GetStaffsByAccountIdResponse
	(*GetStaffByCompanyRequest)(nil),                        // 16: moego.service.organization.v1.GetStaffByCompanyRequest
	(*GetStaffByCompanyResponse)(nil),                       // 17: moego.service.organization.v1.GetStaffByCompanyResponse
	(*UpdateAccountLastVisitBusinessRequest)(nil),           // 18: moego.service.organization.v1.UpdateAccountLastVisitBusinessRequest
	(*UpdateAccountLastVisitBusinessResponse)(nil),          // 19: moego.service.organization.v1.UpdateAccountLastVisitBusinessResponse
	(*GetStaffsByWorkingLocationIdsRequest)(nil),            // 20: moego.service.organization.v1.GetStaffsByWorkingLocationIdsRequest
	(*GetStaffsByWorkingLocationIdsResponse)(nil),           // 21: moego.service.organization.v1.GetStaffsByWorkingLocationIdsResponse
	(*GetEnterpriseStaffsByWorkingLocationIdsRequest)(nil),  // 22: moego.service.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsRequest
	(*GetEnterpriseStaffsByWorkingLocationIdsResponse)(nil), // 23: moego.service.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsResponse
	(*QueryStaffByIdsRequest)(nil),                          // 24: moego.service.organization.v1.QueryStaffByIdsRequest
	(*QueryStaffByIdsResponse)(nil),                         // 25: moego.service.organization.v1.QueryStaffByIdsResponse
	(*QueryStaffByCompanyIdRequest)(nil),                    // 26: moego.service.organization.v1.QueryStaffByCompanyIdRequest
	(*QueryStaffByCompanyIdResponse)(nil),                   // 27: moego.service.organization.v1.QueryStaffByCompanyIdResponse
	(*GetShowOnCalendarStaffsRequest)(nil),                  // 28: moego.service.organization.v1.GetShowOnCalendarStaffsRequest
	(*GetShowOnCalendarStaffsResponse)(nil),                 // 29: moego.service.organization.v1.GetShowOnCalendarStaffsResponse
	(*GetShowOnCalendarStaffIdsResponse)(nil),               // 30: moego.service.organization.v1.GetShowOnCalendarStaffIdsResponse
	(*MigrateStaffDataRequest)(nil),                         // 31: moego.service.organization.v1.MigrateStaffDataRequest
	(*StaffMappingDef)(nil),                                 // 32: moego.service.organization.v1.StaffMappingDef
	(*MigrateStaffDataResponse)(nil),                        // 33: moego.service.organization.v1.MigrateStaffDataResponse
	(*CountStaffWithRoleRequest)(nil),                       // 34: moego.service.organization.v1.CountStaffWithRoleRequest
	(*CountStaffWithRoleResponse)(nil),                      // 35: moego.service.organization.v1.CountStaffWithRoleResponse
	(*GetStaffsByWorkingLocationRequest)(nil),               // 36: moego.service.organization.v1.GetStaffsByWorkingLocationRequest
	(*GetStaffsByWorkingLocationResponse)(nil),              // 37: moego.service.organization.v1.GetStaffsByWorkingLocationResponse
	(*GetClockInOutStaffsRequest)(nil),                      // 38: moego.service.organization.v1.GetClockInOutStaffsRequest
	(*GetClockInOutStaffsResponse)(nil),                     // 39: moego.service.organization.v1.GetClockInOutStaffsResponse
	(*CreateEnterpriseOwnerRequest)(nil),                    // 40: moego.service.organization.v1.CreateEnterpriseOwnerRequest
	(*CreateEnterpriseOwnerResponse)(nil),                   // 41: moego.service.organization.v1.CreateEnterpriseOwnerResponse
	(*SendInviteStaffLinkRequest)(nil),                      // 42: moego.service.organization.v1.SendInviteStaffLinkRequest
	(*SendInviteStaffLinkResponse)(nil),                     // 43: moego.service.organization.v1.SendInviteStaffLinkResponse
	(*ListOwnerStaffInfoRequest)(nil),                       // 44: moego.service.organization.v1.ListOwnerStaffInfoRequest
	(*ListOwnerStaffInfoResponse)(nil),                      // 45: moego.service.organization.v1.ListOwnerStaffInfoResponse
	(*CreateStaffRecordRequest)(nil),                        // 46: moego.service.organization.v1.CreateStaffRecordRequest
	(*CreateStaffRecordResponse)(nil),                       // 47: moego.service.organization.v1.CreateStaffRecordResponse
	(*UpdateStaffRecordRequest)(nil),                        // 48: moego.service.organization.v1.UpdateStaffRecordRequest
	(*UpdateStaffRecordResponse)(nil),                       // 49: moego.service.organization.v1.UpdateStaffRecordResponse
	(*GetEnterpriseStaffsByAccountIdRequest)(nil),           // 50: moego.service.organization.v1.GetEnterpriseStaffsByAccountIdRequest
	(*GetEnterpriseStaffsByAccountIdResponse)(nil),          // 51: moego.service.organization.v1.GetEnterpriseStaffsByAccountIdResponse
	(*CreateEnterpriseStaffRequest)(nil),                    // 52: moego.service.organization.v1.CreateEnterpriseStaffRequest
	(*CreateEnterpriseStaffResponse)(nil),                   // 53: moego.service.organization.v1.CreateEnterpriseStaffResponse
	(*UpdateEnterpriseStaffRequest)(nil),                    // 54: moego.service.organization.v1.UpdateEnterpriseStaffRequest
	(*UpdateEnterpriseStaffResponse)(nil),                   // 55: moego.service.organization.v1.UpdateEnterpriseStaffResponse
	(*GetEnterpriseStaffRequest)(nil),                       // 56: moego.service.organization.v1.GetEnterpriseStaffRequest
	(*GetEnterpriseStaffResponse)(nil),                      // 57: moego.service.organization.v1.GetEnterpriseStaffResponse
	(*DeleteEnterpriseStaffRequest)(nil),                    // 58: moego.service.organization.v1.DeleteEnterpriseStaffRequest
	(*DeleteEnterpriseStaffResponse)(nil),                   // 59: moego.service.organization.v1.DeleteEnterpriseStaffResponse
	(*ListStaffEmailDefsRequest)(nil),                       // 60: moego.service.organization.v1.ListStaffEmailDefsRequest
	(*ListStaffEmailDefsResponse)(nil),                      // 61: moego.service.organization.v1.ListStaffEmailDefsResponse
	(*SendInviteLinkRequest)(nil),                           // 62: moego.service.organization.v1.SendInviteLinkRequest
	(*SendInviteLinkResponse)(nil),                          // 63: moego.service.organization.v1.SendInviteLinkResponse
	(*UnlinkStaffRequest)(nil),                              // 64: moego.service.organization.v1.UnlinkStaffRequest
	(*UnlinkStaffResponse)(nil),                             // 65: moego.service.organization.v1.UnlinkStaffResponse
	(*GetStaffLoginTimeRequest)(nil),                        // 66: moego.service.organization.v1.GetStaffLoginTimeRequest
	(*GetStaffLoginTimeResponse)(nil),                       // 67: moego.service.organization.v1.GetStaffLoginTimeResponse
	(*CheckStaffLoginTimeRequest)(nil),                      // 68: moego.service.organization.v1.CheckStaffLoginTimeRequest
	(*CheckStaffLoginTimeResponse)(nil),                     // 69: moego.service.organization.v1.CheckStaffLoginTimeResponse
	(*GetStaffByPhoneNumberRequest)(nil),                    // 70: moego.service.organization.v1.GetStaffByPhoneNumberRequest
	(*GetStaffByPhoneNumberResponse)(nil),                   // 71: moego.service.organization.v1.GetStaffByPhoneNumberResponse
	(*GetStaffsByRoleRequest)(nil),                          // 72: moego.service.organization.v1.GetStaffsByRoleRequest
	(*GetStaffsByRoleResponse)(nil),                         // 73: moego.service.organization.v1.GetStaffsByRoleResponse
	(*GetBusinessStaffAvailabilityTypeRequest)(nil),         // 74: moego.service.organization.v1.GetBusinessStaffAvailabilityTypeRequest
	(*UpdateBusinessStaffAvailabilityTypeRequest)(nil),      // 75: moego.service.organization.v1.UpdateBusinessStaffAvailabilityTypeRequest
	(*GetBusinessStaffAvailabilityTypeResponse)(nil),        // 76: moego.service.organization.v1.GetBusinessStaffAvailabilityTypeResponse
	(*GetStaffAvailabilityRequest)(nil),                     // 77: moego.service.organization.v1.GetStaffAvailabilityRequest
	(*GetStaffAvailabilityResponse)(nil),                    // 78: moego.service.organization.v1.GetStaffAvailabilityResponse
	(*GetStaffCalenderViewRequest)(nil),                     // 79: moego.service.organization.v1.GetStaffCalenderViewRequest
	(*CalenderStaff)(nil),                                   // 80: moego.service.organization.v1.CalenderStaff
	(*GetStaffCalenderViewResponse)(nil),                    // 81: moego.service.organization.v1.GetStaffCalenderViewResponse
	(*UpdateStaffAvailabilityRequest)(nil),                  // 82: moego.service.organization.v1.UpdateStaffAvailabilityRequest
	(*UpdateStaffAvailabilityOverrideRequest)(nil),          // 83: moego.service.organization.v1.UpdateStaffAvailabilityOverrideRequest
	(*DeleteStaffAvailabilityOverrideRequest)(nil),          // 84: moego.service.organization.v1.DeleteStaffAvailabilityOverrideRequest
	(*DeleteStaffAvailabilityOverrideResponse)(nil),         // 85: moego.service.organization.v1.DeleteStaffAvailabilityOverrideResponse
	(*GetStaffAvailabilityOverrideRequest)(nil),             // 86: moego.service.organization.v1.GetStaffAvailabilityOverrideRequest
	(*SlotAvailabilityDayList)(nil),                         // 87: moego.service.organization.v1.SlotAvailabilityDayList
	(*TimeAvailabilityDayList)(nil),                         // 88: moego.service.organization.v1.TimeAvailabilityDayList
	(*GetStaffAvailabilityOverrideResponse)(nil),            // 89: moego.service.organization.v1.GetStaffAvailabilityOverrideResponse
	(*GetStaffAvailabilityStatusRequest)(nil),               // 90: moego.service.organization.v1.GetStaffAvailabilityStatusRequest
	(*GetStaffAvailabilityStatusResponse)(nil),              // 91: moego.service.organization.v1.GetStaffAvailabilityStatusResponse
	(*UpdateStaffAvailabilityResponse)(nil),                 // 92: moego.service.organization.v1.UpdateStaffAvailabilityResponse
	(*ListSlotFreeServicesRequest)(nil),                     // 93: moego.service.organization.v1.ListSlotFreeServicesRequest
	(*ListSlotFreeServicesResponse)(nil),                    // 94: moego.service.organization.v1.ListSlotFreeServicesResponse
	(*UpdateSlotFreeServicesRequest)(nil),                   // 95: moego.service.organization.v1.UpdateSlotFreeServicesRequest
	(*UpdateSlotFreeServicesResponse)(nil),                  // 96: moego.service.organization.v1.UpdateSlotFreeServicesResponse
	(*InitStaffAvailabilityResponse_Result)(nil),            // 97: moego.service.organization.v1.InitStaffAvailabilityResponse.Result
	nil, // 98: moego.service.organization.v1.ListOwnerStaffInfoResponse.OwnStaffsEntry
	(*GetEnterpriseStaffsByAccountIdRequest_Filter)(nil), // 99: moego.service.organization.v1.GetEnterpriseStaffsByAccountIdRequest.Filter
	nil,                                // 100: moego.service.organization.v1.ListStaffEmailDefsResponse.StaffEmailsEntry
	nil,                                // 101: moego.service.organization.v1.CalenderStaff.SlotAvailabilityDayMapEntry
	nil,                                // 102: moego.service.organization.v1.CalenderStaff.TimeAvailabilityDayMapEntry
	nil,                                // 103: moego.service.organization.v1.GetStaffAvailabilityOverrideResponse.OverrideDaysEntry
	nil,                                // 104: moego.service.organization.v1.GetStaffAvailabilityOverrideResponse.TimeOverrideDaysEntry
	nil,                                // 105: moego.service.organization.v1.GetStaffAvailabilityStatusResponse.StaffAvailabilityEntry
	(*v1.CreateStaffDef)(nil),          // 106: moego.models.organization.v1.CreateStaffDef
	(*v1.StaffWorkingLocationDef)(nil), // 107: moego.models.organization.v1.StaffWorkingLocationDef
	(*v1.StaffAccessControlDef)(nil),   // 108: moego.models.organization.v1.StaffAccessControlDef
	(*v1.StaffNotificationDef)(nil),    // 109: moego.models.organization.v1.StaffNotificationDef
	(*v1.StaffPayrollSettingDef)(nil),  // 110: moego.models.organization.v1.StaffPayrollSettingDef
	(*v1.SendInviteLinkParamsDef)(nil), // 111: moego.models.organization.v1.SendInviteLinkParamsDef
	(*v1.StaffLoginTimeDef)(nil),       // 112: moego.models.organization.v1.StaffLoginTimeDef
	(*v1.UpdateStaffDef)(nil),          // 113: moego.models.organization.v1.UpdateStaffDef
	(*v1.StaffModel)(nil),              // 114: moego.models.organization.v1.StaffModel
	(*v1.StaffBasicView)(nil),          // 115: moego.models.organization.v1.StaffBasicView
	(*v1.StaffEmailDef)(nil),           // 116: moego.models.organization.v1.StaffEmailDef
	(*v1.StaffVanDef)(nil),             // 117: moego.models.organization.v1.StaffVanDef
	(*v2.OrderBy)(nil),                 // 118: moego.utils.v2.OrderBy
	(*v2.PaginationRequest)(nil),       // 119: moego.utils.v2.PaginationRequest
	(*v1.StaffInfoDef)(nil),            // 120: moego.models.organization.v1.StaffInfoDef
	(*v2.PaginationResponse)(nil),      // 121: moego.utils.v2.PaginationResponse
	(*v1.LocationStaffsDef)(nil),       // 122: moego.models.organization.v1.LocationStaffsDef
	(*v1.LocationStaffIdsDef)(nil),     // 123: moego.models.organization.v1.LocationStaffIdsDef
	(*v1.ClockInOutStaffDef)(nil),      // 124: moego.models.organization.v1.ClockInOutStaffDef
	(v1.StaffSource)(0),                // 125: moego.models.organization.v1.StaffSource
	(v1.StaffEmployeeCategory)(0),      // 126: moego.models.organization.v1.StaffEmployeeCategory
	(v1.StaffModel_Status)(0),          // 127: moego.models.organization.v1.StaffModel.Status
	(*v11.CreateStaffProfile)(nil),     // 128: moego.models.enterprise.v1.CreateStaffProfile
	(*v11.UpdateStaffProfile)(nil),     // 129: moego.models.enterprise.v1.UpdateStaffProfile
	(*v1.SendInviteLinkDef)(nil),       // 130: moego.models.organization.v1.SendInviteLinkDef
	(*v1.StaffLoginTimeModel)(nil),     // 131: moego.models.organization.v1.StaffLoginTimeModel
	(v1.AvailabilityType)(0),           // 132: moego.models.organization.v1.AvailabilityType
	(*v1.StaffAvailability)(nil),       // 133: moego.models.organization.v1.StaffAvailability
	(v1.ScheduleType)(0),               // 134: moego.models.organization.v1.ScheduleType
	(*v1.StaffAvailabilityDef)(nil),    // 135: moego.models.organization.v1.StaffAvailabilityDef
	(*v1.SlotAvailabilityDayDef)(nil),  // 136: moego.models.organization.v1.SlotAvailabilityDayDef
	(*v1.TimeAvailabilityDayDef)(nil),  // 137: moego.models.organization.v1.TimeAvailabilityDayDef
	(*v1.SlotAvailabilityDay)(nil),     // 138: moego.models.organization.v1.SlotAvailabilityDay
	(*v1.TimeAvailabilityDay)(nil),     // 139: moego.models.organization.v1.TimeAvailabilityDay
	(*v1.SlotFreeStaffServiceDef)(nil), // 140: moego.models.organization.v1.SlotFreeStaffServiceDef
	(dayofweek.DayOfWeek)(0),           // 141: google.type.DayOfWeek
	(*v1.OwnerStaffDef)(nil),           // 142: moego.models.organization.v1.OwnerStaffDef
}
var file_moego_service_organization_v1_staff_service_proto_depIdxs = []int32{
	97,  // 0: moego.service.organization.v1.InitStaffAvailabilityResponse.results:type_name -> moego.service.organization.v1.InitStaffAvailabilityResponse.Result
	106, // 1: moego.service.organization.v1.CreateStaffRequest.staff_profile:type_name -> moego.models.organization.v1.CreateStaffDef
	107, // 2: moego.service.organization.v1.CreateStaffRequest.working_location:type_name -> moego.models.organization.v1.StaffWorkingLocationDef
	108, // 3: moego.service.organization.v1.CreateStaffRequest.access_control:type_name -> moego.models.organization.v1.StaffAccessControlDef
	109, // 4: moego.service.organization.v1.CreateStaffRequest.notification_setting:type_name -> moego.models.organization.v1.StaffNotificationDef
	110, // 5: moego.service.organization.v1.CreateStaffRequest.payroll_setting:type_name -> moego.models.organization.v1.StaffPayrollSettingDef
	111, // 6: moego.service.organization.v1.CreateStaffRequest.invite_link:type_name -> moego.models.organization.v1.SendInviteLinkParamsDef
	112, // 7: moego.service.organization.v1.CreateStaffRequest.login_time:type_name -> moego.models.organization.v1.StaffLoginTimeDef
	113, // 8: moego.service.organization.v1.UpdateStaffRequest.staff_profile:type_name -> moego.models.organization.v1.UpdateStaffDef
	107, // 9: moego.service.organization.v1.UpdateStaffRequest.working_location:type_name -> moego.models.organization.v1.StaffWorkingLocationDef
	108, // 10: moego.service.organization.v1.UpdateStaffRequest.access_control:type_name -> moego.models.organization.v1.StaffAccessControlDef
	109, // 11: moego.service.organization.v1.UpdateStaffRequest.notification_setting:type_name -> moego.models.organization.v1.StaffNotificationDef
	110, // 12: moego.service.organization.v1.UpdateStaffRequest.payroll_setting:type_name -> moego.models.organization.v1.StaffPayrollSettingDef
	111, // 13: moego.service.organization.v1.UpdateStaffRequest.invite_link:type_name -> moego.models.organization.v1.SendInviteLinkParamsDef
	112, // 14: moego.service.organization.v1.UpdateStaffRequest.login_time:type_name -> moego.models.organization.v1.StaffLoginTimeDef
	114, // 15: moego.service.organization.v1.GetStaffDetailResponse.staff:type_name -> moego.models.organization.v1.StaffModel
	115, // 16: moego.service.organization.v1.GetStaffFullDetailResponse.staff_profile:type_name -> moego.models.organization.v1.StaffBasicView
	107, // 17: moego.service.organization.v1.GetStaffFullDetailResponse.working_location:type_name -> moego.models.organization.v1.StaffWorkingLocationDef
	108, // 18: moego.service.organization.v1.GetStaffFullDetailResponse.access_control:type_name -> moego.models.organization.v1.StaffAccessControlDef
	109, // 19: moego.service.organization.v1.GetStaffFullDetailResponse.notification_setting:type_name -> moego.models.organization.v1.StaffNotificationDef
	110, // 20: moego.service.organization.v1.GetStaffFullDetailResponse.payroll_setting:type_name -> moego.models.organization.v1.StaffPayrollSettingDef
	116, // 21: moego.service.organization.v1.GetStaffFullDetailResponse.staff_email:type_name -> moego.models.organization.v1.StaffEmailDef
	117, // 22: moego.service.organization.v1.GetStaffFullDetailResponse.staff_van:type_name -> moego.models.organization.v1.StaffVanDef
	112, // 23: moego.service.organization.v1.GetStaffFullDetailResponse.login_time:type_name -> moego.models.organization.v1.StaffLoginTimeDef
	118, // 24: moego.service.organization.v1.QueryStaffListByPaginationRequest.order_bys:type_name -> moego.utils.v2.OrderBy
	119, // 25: moego.service.organization.v1.QueryStaffListByPaginationRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	120, // 26: moego.service.organization.v1.QueryStaffListByPaginationResponse.staffs:type_name -> moego.models.organization.v1.StaffInfoDef
	121, // 27: moego.service.organization.v1.QueryStaffListByPaginationResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	114, // 28: moego.service.organization.v1.GetStaffsByAccountIdResponse.staffs:type_name -> moego.models.organization.v1.StaffModel
	114, // 29: moego.service.organization.v1.GetStaffByCompanyResponse.staff:type_name -> moego.models.organization.v1.StaffModel
	122, // 30: moego.service.organization.v1.GetStaffsByWorkingLocationIdsResponse.location_staffs:type_name -> moego.models.organization.v1.LocationStaffsDef
	122, // 31: moego.service.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsResponse.location_staffs:type_name -> moego.models.organization.v1.LocationStaffsDef
	114, // 32: moego.service.organization.v1.QueryStaffByIdsResponse.staffs:type_name -> moego.models.organization.v1.StaffModel
	119, // 33: moego.service.organization.v1.QueryStaffByCompanyIdRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	114, // 34: moego.service.organization.v1.QueryStaffByCompanyIdResponse.staffs:type_name -> moego.models.organization.v1.StaffModel
	121, // 35: moego.service.organization.v1.QueryStaffByCompanyIdResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	122, // 36: moego.service.organization.v1.GetShowOnCalendarStaffsResponse.location_staffs:type_name -> moego.models.organization.v1.LocationStaffsDef
	123, // 37: moego.service.organization.v1.GetShowOnCalendarStaffIdsResponse.location_staff_ids:type_name -> moego.models.organization.v1.LocationStaffIdsDef
	32,  // 38: moego.service.organization.v1.MigrateStaffDataResponse.staff_mapping:type_name -> moego.service.organization.v1.StaffMappingDef
	115, // 39: moego.service.organization.v1.GetStaffsByWorkingLocationResponse.staffs:type_name -> moego.models.organization.v1.StaffBasicView
	124, // 40: moego.service.organization.v1.GetClockInOutStaffsResponse.clock_in_out_staffs:type_name -> moego.models.organization.v1.ClockInOutStaffDef
	125, // 41: moego.service.organization.v1.CreateEnterpriseOwnerRequest.source:type_name -> moego.models.organization.v1.StaffSource
	98,  // 42: moego.service.organization.v1.ListOwnerStaffInfoResponse.own_staffs:type_name -> moego.service.organization.v1.ListOwnerStaffInfoResponse.OwnStaffsEntry
	126, // 43: moego.service.organization.v1.CreateStaffRecordRequest.employee_category:type_name -> moego.models.organization.v1.StaffEmployeeCategory
	127, // 44: moego.service.organization.v1.CreateStaffRecordRequest.status:type_name -> moego.models.organization.v1.StaffModel.Status
	114, // 45: moego.service.organization.v1.CreateStaffRecordResponse.staff:type_name -> moego.models.organization.v1.StaffModel
	127, // 46: moego.service.organization.v1.UpdateStaffRecordRequest.status:type_name -> moego.models.organization.v1.StaffModel.Status
	126, // 47: moego.service.organization.v1.UpdateStaffRecordRequest.employee_category:type_name -> moego.models.organization.v1.StaffEmployeeCategory
	114, // 48: moego.service.organization.v1.UpdateStaffRecordResponse.staff:type_name -> moego.models.organization.v1.StaffModel
	99,  // 49: moego.service.organization.v1.GetEnterpriseStaffsByAccountIdRequest.filter:type_name -> moego.service.organization.v1.GetEnterpriseStaffsByAccountIdRequest.Filter
	114, // 50: moego.service.organization.v1.GetEnterpriseStaffsByAccountIdResponse.staffs:type_name -> moego.models.organization.v1.StaffModel
	128, // 51: moego.service.organization.v1.CreateEnterpriseStaffRequest.profile:type_name -> moego.models.enterprise.v1.CreateStaffProfile
	111, // 52: moego.service.organization.v1.CreateEnterpriseStaffRequest.invite_link:type_name -> moego.models.organization.v1.SendInviteLinkParamsDef
	114, // 53: moego.service.organization.v1.CreateEnterpriseStaffResponse.staff:type_name -> moego.models.organization.v1.StaffModel
	116, // 54: moego.service.organization.v1.CreateEnterpriseStaffResponse.staff_email:type_name -> moego.models.organization.v1.StaffEmailDef
	129, // 55: moego.service.organization.v1.UpdateEnterpriseStaffRequest.profile:type_name -> moego.models.enterprise.v1.UpdateStaffProfile
	111, // 56: moego.service.organization.v1.UpdateEnterpriseStaffRequest.invite_link:type_name -> moego.models.organization.v1.SendInviteLinkParamsDef
	114, // 57: moego.service.organization.v1.UpdateEnterpriseStaffResponse.staff:type_name -> moego.models.organization.v1.StaffModel
	116, // 58: moego.service.organization.v1.UpdateEnterpriseStaffResponse.staff_email:type_name -> moego.models.organization.v1.StaffEmailDef
	114, // 59: moego.service.organization.v1.GetEnterpriseStaffResponse.staff:type_name -> moego.models.organization.v1.StaffModel
	116, // 60: moego.service.organization.v1.GetEnterpriseStaffResponse.staff_email:type_name -> moego.models.organization.v1.StaffEmailDef
	100, // 61: moego.service.organization.v1.ListStaffEmailDefsResponse.staff_emails:type_name -> moego.service.organization.v1.ListStaffEmailDefsResponse.StaffEmailsEntry
	130, // 62: moego.service.organization.v1.SendInviteLinkRequest.invite_link:type_name -> moego.models.organization.v1.SendInviteLinkDef
	131, // 63: moego.service.organization.v1.GetStaffLoginTimeResponse.login_time:type_name -> moego.models.organization.v1.StaffLoginTimeModel
	114, // 64: moego.service.organization.v1.GetStaffByPhoneNumberResponse.staff:type_name -> moego.models.organization.v1.StaffModel
	114, // 65: moego.service.organization.v1.GetStaffsByRoleResponse.staffs:type_name -> moego.models.organization.v1.StaffModel
	132, // 66: moego.service.organization.v1.UpdateBusinessStaffAvailabilityTypeRequest.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	132, // 67: moego.service.organization.v1.GetBusinessStaffAvailabilityTypeResponse.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	132, // 68: moego.service.organization.v1.GetStaffAvailabilityRequest.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	133, // 69: moego.service.organization.v1.GetStaffAvailabilityResponse.staff_availability_list:type_name -> moego.models.organization.v1.StaffAvailability
	132, // 70: moego.service.organization.v1.GetStaffCalenderViewRequest.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	134, // 71: moego.service.organization.v1.CalenderStaff.schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	101, // 72: moego.service.organization.v1.CalenderStaff.slot_availability_day_map:type_name -> moego.service.organization.v1.CalenderStaff.SlotAvailabilityDayMapEntry
	134, // 73: moego.service.organization.v1.CalenderStaff.time_schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	102, // 74: moego.service.organization.v1.CalenderStaff.time_availability_day_map:type_name -> moego.service.organization.v1.CalenderStaff.TimeAvailabilityDayMapEntry
	80,  // 75: moego.service.organization.v1.GetStaffCalenderViewResponse.staff_list:type_name -> moego.service.organization.v1.CalenderStaff
	135, // 76: moego.service.organization.v1.UpdateStaffAvailabilityRequest.staff_availability_list:type_name -> moego.models.organization.v1.StaffAvailabilityDef
	136, // 77: moego.service.organization.v1.UpdateStaffAvailabilityOverrideRequest.override_days:type_name -> moego.models.organization.v1.SlotAvailabilityDayDef
	137, // 78: moego.service.organization.v1.UpdateStaffAvailabilityOverrideRequest.time_override_days:type_name -> moego.models.organization.v1.TimeAvailabilityDayDef
	132, // 79: moego.service.organization.v1.DeleteStaffAvailabilityOverrideRequest.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	132, // 80: moego.service.organization.v1.GetStaffAvailabilityOverrideRequest.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	138, // 81: moego.service.organization.v1.SlotAvailabilityDayList.slots:type_name -> moego.models.organization.v1.SlotAvailabilityDay
	139, // 82: moego.service.organization.v1.TimeAvailabilityDayList.slots:type_name -> moego.models.organization.v1.TimeAvailabilityDay
	103, // 83: moego.service.organization.v1.GetStaffAvailabilityOverrideResponse.override_days:type_name -> moego.service.organization.v1.GetStaffAvailabilityOverrideResponse.OverrideDaysEntry
	104, // 84: moego.service.organization.v1.GetStaffAvailabilityOverrideResponse.time_override_days:type_name -> moego.service.organization.v1.GetStaffAvailabilityOverrideResponse.TimeOverrideDaysEntry
	105, // 85: moego.service.organization.v1.GetStaffAvailabilityStatusResponse.staff_availability:type_name -> moego.service.organization.v1.GetStaffAvailabilityStatusResponse.StaffAvailabilityEntry
	140, // 86: moego.service.organization.v1.ListSlotFreeServicesResponse.defs:type_name -> moego.models.organization.v1.SlotFreeStaffServiceDef
	140, // 87: moego.service.organization.v1.UpdateSlotFreeServicesRequest.defs:type_name -> moego.models.organization.v1.SlotFreeStaffServiceDef
	141, // 88: moego.service.organization.v1.InitStaffAvailabilityResponse.Result.day_of_week:type_name -> google.type.DayOfWeek
	142, // 89: moego.service.organization.v1.ListOwnerStaffInfoResponse.OwnStaffsEntry.value:type_name -> moego.models.organization.v1.OwnerStaffDef
	116, // 90: moego.service.organization.v1.ListStaffEmailDefsResponse.StaffEmailsEntry.value:type_name -> moego.models.organization.v1.StaffEmailDef
	138, // 91: moego.service.organization.v1.CalenderStaff.SlotAvailabilityDayMapEntry.value:type_name -> moego.models.organization.v1.SlotAvailabilityDay
	139, // 92: moego.service.organization.v1.CalenderStaff.TimeAvailabilityDayMapEntry.value:type_name -> moego.models.organization.v1.TimeAvailabilityDay
	87,  // 93: moego.service.organization.v1.GetStaffAvailabilityOverrideResponse.OverrideDaysEntry.value:type_name -> moego.service.organization.v1.SlotAvailabilityDayList
	88,  // 94: moego.service.organization.v1.GetStaffAvailabilityOverrideResponse.TimeOverrideDaysEntry.value:type_name -> moego.service.organization.v1.TimeAvailabilityDayList
	2,   // 95: moego.service.organization.v1.StaffService.CreateStaff:input_type -> moego.service.organization.v1.CreateStaffRequest
	8,   // 96: moego.service.organization.v1.StaffService.GetStaffDetail:input_type -> moego.service.organization.v1.GetStaffDetailRequest
	10,  // 97: moego.service.organization.v1.StaffService.GetStaffFullDetail:input_type -> moego.service.organization.v1.GetStaffFullDetailRequest
	14,  // 98: moego.service.organization.v1.StaffService.GetStaffsByAccountId:input_type -> moego.service.organization.v1.GetStaffsByAccountIdRequest
	16,  // 99: moego.service.organization.v1.StaffService.GetStaffByCompany:input_type -> moego.service.organization.v1.GetStaffByCompanyRequest
	18,  // 100: moego.service.organization.v1.StaffService.UpdateAccountLastVisitBusiness:input_type -> moego.service.organization.v1.UpdateAccountLastVisitBusinessRequest
	4,   // 101: moego.service.organization.v1.StaffService.UpdateStaff:input_type -> moego.service.organization.v1.UpdateStaffRequest
	6,   // 102: moego.service.organization.v1.StaffService.DeleteStaff:input_type -> moego.service.organization.v1.DeleteStaffRequest
	12,  // 103: moego.service.organization.v1.StaffService.QueryStaffListByPagination:input_type -> moego.service.organization.v1.QueryStaffListByPaginationRequest
	20,  // 104: moego.service.organization.v1.StaffService.GetStaffsByWorkingLocationIds:input_type -> moego.service.organization.v1.GetStaffsByWorkingLocationIdsRequest
	24,  // 105: moego.service.organization.v1.StaffService.QueryStaffByIds:input_type -> moego.service.organization.v1.QueryStaffByIdsRequest
	28,  // 106: moego.service.organization.v1.StaffService.GetShowOnCalendarStaffs:input_type -> moego.service.organization.v1.GetShowOnCalendarStaffsRequest
	28,  // 107: moego.service.organization.v1.StaffService.GetShowOnCalendarStaffIds:input_type -> moego.service.organization.v1.GetShowOnCalendarStaffsRequest
	31,  // 108: moego.service.organization.v1.StaffService.MigrateStaffData:input_type -> moego.service.organization.v1.MigrateStaffDataRequest
	34,  // 109: moego.service.organization.v1.StaffService.CountStaffWithRole:input_type -> moego.service.organization.v1.CountStaffWithRoleRequest
	36,  // 110: moego.service.organization.v1.StaffService.GetStaffsByWorkingLocation:input_type -> moego.service.organization.v1.GetStaffsByWorkingLocationRequest
	38,  // 111: moego.service.organization.v1.StaffService.GetClockInOutStaffs:input_type -> moego.service.organization.v1.GetClockInOutStaffsRequest
	22,  // 112: moego.service.organization.v1.StaffService.GetEnterpriseStaffsByWorkingLocationIds:input_type -> moego.service.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsRequest
	40,  // 113: moego.service.organization.v1.StaffService.CreateEnterpriseOwner:input_type -> moego.service.organization.v1.CreateEnterpriseOwnerRequest
	26,  // 114: moego.service.organization.v1.StaffService.QueryStaffByCompanyId:input_type -> moego.service.organization.v1.QueryStaffByCompanyIdRequest
	42,  // 115: moego.service.organization.v1.StaffService.SendInviteStaffLink:input_type -> moego.service.organization.v1.SendInviteStaffLinkRequest
	44,  // 116: moego.service.organization.v1.StaffService.ListOwnerStaffInfo:input_type -> moego.service.organization.v1.ListOwnerStaffInfoRequest
	46,  // 117: moego.service.organization.v1.StaffService.CreateStaffRecord:input_type -> moego.service.organization.v1.CreateStaffRecordRequest
	48,  // 118: moego.service.organization.v1.StaffService.UpdateStaffRecord:input_type -> moego.service.organization.v1.UpdateStaffRecordRequest
	50,  // 119: moego.service.organization.v1.StaffService.GetEnterpriseStaffsByAccountId:input_type -> moego.service.organization.v1.GetEnterpriseStaffsByAccountIdRequest
	52,  // 120: moego.service.organization.v1.StaffService.CreateEnterpriseStaff:input_type -> moego.service.organization.v1.CreateEnterpriseStaffRequest
	54,  // 121: moego.service.organization.v1.StaffService.UpdateEnterpriseStaff:input_type -> moego.service.organization.v1.UpdateEnterpriseStaffRequest
	56,  // 122: moego.service.organization.v1.StaffService.GetEnterpriseStaff:input_type -> moego.service.organization.v1.GetEnterpriseStaffRequest
	58,  // 123: moego.service.organization.v1.StaffService.DeleteEnterpriseStaff:input_type -> moego.service.organization.v1.DeleteEnterpriseStaffRequest
	60,  // 124: moego.service.organization.v1.StaffService.ListStaffEmailDefs:input_type -> moego.service.organization.v1.ListStaffEmailDefsRequest
	62,  // 125: moego.service.organization.v1.StaffService.SendInviteLink:input_type -> moego.service.organization.v1.SendInviteLinkRequest
	64,  // 126: moego.service.organization.v1.StaffService.UnlinkStaff:input_type -> moego.service.organization.v1.UnlinkStaffRequest
	66,  // 127: moego.service.organization.v1.StaffService.GetStaffLoginTime:input_type -> moego.service.organization.v1.GetStaffLoginTimeRequest
	68,  // 128: moego.service.organization.v1.StaffService.CheckStaffLoginTime:input_type -> moego.service.organization.v1.CheckStaffLoginTimeRequest
	70,  // 129: moego.service.organization.v1.StaffService.GetStaffByPhoneNumber:input_type -> moego.service.organization.v1.GetStaffByPhoneNumberRequest
	72,  // 130: moego.service.organization.v1.StaffService.GetStaffsByRole:input_type -> moego.service.organization.v1.GetStaffsByRoleRequest
	74,  // 131: moego.service.organization.v1.StaffService.GetBusinessStaffAvailabilityType:input_type -> moego.service.organization.v1.GetBusinessStaffAvailabilityTypeRequest
	75,  // 132: moego.service.organization.v1.StaffService.UpdateBusinessStaffAvailabilityType:input_type -> moego.service.organization.v1.UpdateBusinessStaffAvailabilityTypeRequest
	77,  // 133: moego.service.organization.v1.StaffService.GetStaffAvailability:input_type -> moego.service.organization.v1.GetStaffAvailabilityRequest
	82,  // 134: moego.service.organization.v1.StaffService.UpdateStaffAvailability:input_type -> moego.service.organization.v1.UpdateStaffAvailabilityRequest
	83,  // 135: moego.service.organization.v1.StaffService.UpdateStaffAvailabilityOverride:input_type -> moego.service.organization.v1.UpdateStaffAvailabilityOverrideRequest
	84,  // 136: moego.service.organization.v1.StaffService.DeleteStaffAvailabilityOverride:input_type -> moego.service.organization.v1.DeleteStaffAvailabilityOverrideRequest
	86,  // 137: moego.service.organization.v1.StaffService.GetStaffAvailabilityOverride:input_type -> moego.service.organization.v1.GetStaffAvailabilityOverrideRequest
	79,  // 138: moego.service.organization.v1.StaffService.GetStaffCalenderView:input_type -> moego.service.organization.v1.GetStaffCalenderViewRequest
	0,   // 139: moego.service.organization.v1.StaffService.InitStaffAvailability:input_type -> moego.service.organization.v1.InitStaffAvailabilityRequest
	93,  // 140: moego.service.organization.v1.StaffService.ListSlotFreeServices:input_type -> moego.service.organization.v1.ListSlotFreeServicesRequest
	95,  // 141: moego.service.organization.v1.StaffService.UpdateSlotFreeServices:input_type -> moego.service.organization.v1.UpdateSlotFreeServicesRequest
	3,   // 142: moego.service.organization.v1.StaffService.CreateStaff:output_type -> moego.service.organization.v1.CreateStaffResponse
	9,   // 143: moego.service.organization.v1.StaffService.GetStaffDetail:output_type -> moego.service.organization.v1.GetStaffDetailResponse
	11,  // 144: moego.service.organization.v1.StaffService.GetStaffFullDetail:output_type -> moego.service.organization.v1.GetStaffFullDetailResponse
	15,  // 145: moego.service.organization.v1.StaffService.GetStaffsByAccountId:output_type -> moego.service.organization.v1.GetStaffsByAccountIdResponse
	17,  // 146: moego.service.organization.v1.StaffService.GetStaffByCompany:output_type -> moego.service.organization.v1.GetStaffByCompanyResponse
	19,  // 147: moego.service.organization.v1.StaffService.UpdateAccountLastVisitBusiness:output_type -> moego.service.organization.v1.UpdateAccountLastVisitBusinessResponse
	5,   // 148: moego.service.organization.v1.StaffService.UpdateStaff:output_type -> moego.service.organization.v1.UpdateStaffResponse
	7,   // 149: moego.service.organization.v1.StaffService.DeleteStaff:output_type -> moego.service.organization.v1.DeleteStaffResponse
	13,  // 150: moego.service.organization.v1.StaffService.QueryStaffListByPagination:output_type -> moego.service.organization.v1.QueryStaffListByPaginationResponse
	21,  // 151: moego.service.organization.v1.StaffService.GetStaffsByWorkingLocationIds:output_type -> moego.service.organization.v1.GetStaffsByWorkingLocationIdsResponse
	25,  // 152: moego.service.organization.v1.StaffService.QueryStaffByIds:output_type -> moego.service.organization.v1.QueryStaffByIdsResponse
	29,  // 153: moego.service.organization.v1.StaffService.GetShowOnCalendarStaffs:output_type -> moego.service.organization.v1.GetShowOnCalendarStaffsResponse
	30,  // 154: moego.service.organization.v1.StaffService.GetShowOnCalendarStaffIds:output_type -> moego.service.organization.v1.GetShowOnCalendarStaffIdsResponse
	33,  // 155: moego.service.organization.v1.StaffService.MigrateStaffData:output_type -> moego.service.organization.v1.MigrateStaffDataResponse
	35,  // 156: moego.service.organization.v1.StaffService.CountStaffWithRole:output_type -> moego.service.organization.v1.CountStaffWithRoleResponse
	37,  // 157: moego.service.organization.v1.StaffService.GetStaffsByWorkingLocation:output_type -> moego.service.organization.v1.GetStaffsByWorkingLocationResponse
	39,  // 158: moego.service.organization.v1.StaffService.GetClockInOutStaffs:output_type -> moego.service.organization.v1.GetClockInOutStaffsResponse
	23,  // 159: moego.service.organization.v1.StaffService.GetEnterpriseStaffsByWorkingLocationIds:output_type -> moego.service.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsResponse
	41,  // 160: moego.service.organization.v1.StaffService.CreateEnterpriseOwner:output_type -> moego.service.organization.v1.CreateEnterpriseOwnerResponse
	27,  // 161: moego.service.organization.v1.StaffService.QueryStaffByCompanyId:output_type -> moego.service.organization.v1.QueryStaffByCompanyIdResponse
	43,  // 162: moego.service.organization.v1.StaffService.SendInviteStaffLink:output_type -> moego.service.organization.v1.SendInviteStaffLinkResponse
	45,  // 163: moego.service.organization.v1.StaffService.ListOwnerStaffInfo:output_type -> moego.service.organization.v1.ListOwnerStaffInfoResponse
	47,  // 164: moego.service.organization.v1.StaffService.CreateStaffRecord:output_type -> moego.service.organization.v1.CreateStaffRecordResponse
	49,  // 165: moego.service.organization.v1.StaffService.UpdateStaffRecord:output_type -> moego.service.organization.v1.UpdateStaffRecordResponse
	51,  // 166: moego.service.organization.v1.StaffService.GetEnterpriseStaffsByAccountId:output_type -> moego.service.organization.v1.GetEnterpriseStaffsByAccountIdResponse
	53,  // 167: moego.service.organization.v1.StaffService.CreateEnterpriseStaff:output_type -> moego.service.organization.v1.CreateEnterpriseStaffResponse
	55,  // 168: moego.service.organization.v1.StaffService.UpdateEnterpriseStaff:output_type -> moego.service.organization.v1.UpdateEnterpriseStaffResponse
	57,  // 169: moego.service.organization.v1.StaffService.GetEnterpriseStaff:output_type -> moego.service.organization.v1.GetEnterpriseStaffResponse
	59,  // 170: moego.service.organization.v1.StaffService.DeleteEnterpriseStaff:output_type -> moego.service.organization.v1.DeleteEnterpriseStaffResponse
	61,  // 171: moego.service.organization.v1.StaffService.ListStaffEmailDefs:output_type -> moego.service.organization.v1.ListStaffEmailDefsResponse
	63,  // 172: moego.service.organization.v1.StaffService.SendInviteLink:output_type -> moego.service.organization.v1.SendInviteLinkResponse
	65,  // 173: moego.service.organization.v1.StaffService.UnlinkStaff:output_type -> moego.service.organization.v1.UnlinkStaffResponse
	67,  // 174: moego.service.organization.v1.StaffService.GetStaffLoginTime:output_type -> moego.service.organization.v1.GetStaffLoginTimeResponse
	69,  // 175: moego.service.organization.v1.StaffService.CheckStaffLoginTime:output_type -> moego.service.organization.v1.CheckStaffLoginTimeResponse
	71,  // 176: moego.service.organization.v1.StaffService.GetStaffByPhoneNumber:output_type -> moego.service.organization.v1.GetStaffByPhoneNumberResponse
	73,  // 177: moego.service.organization.v1.StaffService.GetStaffsByRole:output_type -> moego.service.organization.v1.GetStaffsByRoleResponse
	76,  // 178: moego.service.organization.v1.StaffService.GetBusinessStaffAvailabilityType:output_type -> moego.service.organization.v1.GetBusinessStaffAvailabilityTypeResponse
	92,  // 179: moego.service.organization.v1.StaffService.UpdateBusinessStaffAvailabilityType:output_type -> moego.service.organization.v1.UpdateStaffAvailabilityResponse
	78,  // 180: moego.service.organization.v1.StaffService.GetStaffAvailability:output_type -> moego.service.organization.v1.GetStaffAvailabilityResponse
	92,  // 181: moego.service.organization.v1.StaffService.UpdateStaffAvailability:output_type -> moego.service.organization.v1.UpdateStaffAvailabilityResponse
	92,  // 182: moego.service.organization.v1.StaffService.UpdateStaffAvailabilityOverride:output_type -> moego.service.organization.v1.UpdateStaffAvailabilityResponse
	85,  // 183: moego.service.organization.v1.StaffService.DeleteStaffAvailabilityOverride:output_type -> moego.service.organization.v1.DeleteStaffAvailabilityOverrideResponse
	89,  // 184: moego.service.organization.v1.StaffService.GetStaffAvailabilityOverride:output_type -> moego.service.organization.v1.GetStaffAvailabilityOverrideResponse
	81,  // 185: moego.service.organization.v1.StaffService.GetStaffCalenderView:output_type -> moego.service.organization.v1.GetStaffCalenderViewResponse
	1,   // 186: moego.service.organization.v1.StaffService.InitStaffAvailability:output_type -> moego.service.organization.v1.InitStaffAvailabilityResponse
	94,  // 187: moego.service.organization.v1.StaffService.ListSlotFreeServices:output_type -> moego.service.organization.v1.ListSlotFreeServicesResponse
	96,  // 188: moego.service.organization.v1.StaffService.UpdateSlotFreeServices:output_type -> moego.service.organization.v1.UpdateSlotFreeServicesResponse
	142, // [142:189] is the sub-list for method output_type
	95,  // [95:142] is the sub-list for method input_type
	95,  // [95:95] is the sub-list for extension type_name
	95,  // [95:95] is the sub-list for extension extendee
	0,   // [0:95] is the sub-list for field type_name
}

func init() { file_moego_service_organization_v1_staff_service_proto_init() }
func file_moego_service_organization_v1_staff_service_proto_init() {
	if File_moego_service_organization_v1_staff_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_organization_v1_staff_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitStaffAvailabilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitStaffAvailabilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffFullDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffFullDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStaffListByPaginationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStaffListByPaginationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffsByAccountIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffsByAccountIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffByCompanyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffByCompanyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccountLastVisitBusinessRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccountLastVisitBusinessResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffsByWorkingLocationIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffsByWorkingLocationIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseStaffsByWorkingLocationIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseStaffsByWorkingLocationIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStaffByIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStaffByIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStaffByCompanyIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStaffByCompanyIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetShowOnCalendarStaffsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetShowOnCalendarStaffsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetShowOnCalendarStaffIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigrateStaffDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffMappingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigrateStaffDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountStaffWithRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountStaffWithRoleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffsByWorkingLocationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffsByWorkingLocationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClockInOutStaffsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClockInOutStaffsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEnterpriseOwnerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEnterpriseOwnerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendInviteStaffLinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendInviteStaffLinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOwnerStaffInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOwnerStaffInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseStaffsByAccountIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseStaffsByAccountIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEnterpriseStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEnterpriseStaffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEnterpriseStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEnterpriseStaffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseStaffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEnterpriseStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEnterpriseStaffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffEmailDefsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffEmailDefsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendInviteLinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendInviteLinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlinkStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlinkStaffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffLoginTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffLoginTimeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckStaffLoginTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckStaffLoginTimeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffByPhoneNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffByPhoneNumberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffsByRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffsByRoleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessStaffAvailabilityTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBusinessStaffAvailabilityTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessStaffAvailabilityTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffCalenderViewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalenderStaff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffCalenderViewResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffAvailabilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffAvailabilityOverrideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffAvailabilityOverrideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffAvailabilityOverrideResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityOverrideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotAvailabilityDayList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeAvailabilityDayList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityOverrideResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffAvailabilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSlotFreeServicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSlotFreeServicesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSlotFreeServicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSlotFreeServicesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitStaffAvailabilityResponse_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_staff_service_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseStaffsByAccountIdRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[38].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[40].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[46].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[48].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[52].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[54].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[62].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[64].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[66].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[67].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[69].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[77].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[79].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[84].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_staff_service_proto_msgTypes[86].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_organization_v1_staff_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   106,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_organization_v1_staff_service_proto_goTypes,
		DependencyIndexes: file_moego_service_organization_v1_staff_service_proto_depIdxs,
		MessageInfos:      file_moego_service_organization_v1_staff_service_proto_msgTypes,
	}.Build()
	File_moego_service_organization_v1_staff_service_proto = out.File
	file_moego_service_organization_v1_staff_service_proto_rawDesc = nil
	file_moego_service_organization_v1_staff_service_proto_goTypes = nil
	file_moego_service_organization_v1_staff_service_proto_depIdxs = nil
}
