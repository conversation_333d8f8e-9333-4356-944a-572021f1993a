// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/organization/v1/staff_service.proto

package organizationsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// StaffServiceClient is the client API for StaffService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StaffServiceClient interface {
	// api to create a staff
	CreateStaff(ctx context.Context, in *CreateStaffRequest, opts ...grpc.CallOption) (*CreateStaffResponse, error)
	// api to get a staff detail
	GetStaffDetail(ctx context.Context, in *GetStaffDetailRequest, opts ...grpc.CallOption) (*GetStaffDetailResponse, error)
	// api to get staff full detail
	GetStaffFullDetail(ctx context.Context, in *GetStaffFullDetailRequest, opts ...grpc.CallOption) (*GetStaffFullDetailResponse, error)
	// Api to get staffs by account id,including enterprise staff,migrated company and legacy company staff.
	// migrated company staff has no business_id and legacy company staff has a certain business_id.
	// Enterprise staffs place in the front and company staffs will sort desc by account_sort.
	GetStaffsByAccountId(ctx context.Context, in *GetStaffsByAccountIdRequest, opts ...grpc.CallOption) (*GetStaffsByAccountIdResponse, error)
	// get staff by company
	GetStaffByCompany(ctx context.Context, in *GetStaffByCompanyRequest, opts ...grpc.CallOption) (*GetStaffByCompanyResponse, error)
	// update account last visit business
	UpdateAccountLastVisitBusiness(ctx context.Context, in *UpdateAccountLastVisitBusinessRequest, opts ...grpc.CallOption) (*UpdateAccountLastVisitBusinessResponse, error)
	// update staff
	UpdateStaff(ctx context.Context, in *UpdateStaffRequest, opts ...grpc.CallOption) (*UpdateStaffResponse, error)
	// delete staff
	DeleteStaff(ctx context.Context, in *DeleteStaffRequest, opts ...grpc.CallOption) (*DeleteStaffResponse, error)
	// get staff info list by pagination
	QueryStaffListByPagination(ctx context.Context, in *QueryStaffListByPaginationRequest, opts ...grpc.CallOption) (*QueryStaffListByPaginationResponse, error)
	// get working staffs by location ids
	GetStaffsByWorkingLocationIds(ctx context.Context, in *GetStaffsByWorkingLocationIdsRequest, opts ...grpc.CallOption) (*GetStaffsByWorkingLocationIdsResponse, error)
	// query staff by ids
	QueryStaffByIds(ctx context.Context, in *QueryStaffByIdsRequest, opts ...grpc.CallOption) (*QueryStaffByIdsResponse, error)
	// get show on calendar staffs
	GetShowOnCalendarStaffs(ctx context.Context, in *GetShowOnCalendarStaffsRequest, opts ...grpc.CallOption) (*GetShowOnCalendarStaffsResponse, error)
	// get show on calendar staff ids
	GetShowOnCalendarStaffIds(ctx context.Context, in *GetShowOnCalendarStaffsRequest, opts ...grpc.CallOption) (*GetShowOnCalendarStaffIdsResponse, error)
	// staff data migrate
	MigrateStaffData(ctx context.Context, in *MigrateStaffDataRequest, opts ...grpc.CallOption) (*MigrateStaffDataResponse, error)
	// count staff with role
	CountStaffWithRole(ctx context.Context, in *CountStaffWithRoleRequest, opts ...grpc.CallOption) (*CountStaffWithRoleResponse, error)
	// get staffs by working location
	GetStaffsByWorkingLocation(ctx context.Context, in *GetStaffsByWorkingLocationRequest, opts ...grpc.CallOption) (*GetStaffsByWorkingLocationResponse, error)
	// get clock in out staffs of current staff
	GetClockInOutStaffs(ctx context.Context, in *GetClockInOutStaffsRequest, opts ...grpc.CallOption) (*GetClockInOutStaffsResponse, error)
	// get enterprise staffs by working location ids
	GetEnterpriseStaffsByWorkingLocationIds(ctx context.Context, in *GetEnterpriseStaffsByWorkingLocationIdsRequest, opts ...grpc.CallOption) (*GetEnterpriseStaffsByWorkingLocationIdsResponse, error)
	// create enterprise owner
	CreateEnterpriseOwner(ctx context.Context, in *CreateEnterpriseOwnerRequest, opts ...grpc.CallOption) (*CreateEnterpriseOwnerResponse, error)
	// query staffs by company id
	QueryStaffByCompanyId(ctx context.Context, in *QueryStaffByCompanyIdRequest, opts ...grpc.CallOption) (*QueryStaffByCompanyIdResponse, error)
	// get staffs by account id
	SendInviteStaffLink(ctx context.Context, in *SendInviteStaffLinkRequest, opts ...grpc.CallOption) (*SendInviteStaffLinkResponse, error)
	// get staff invited link status
	ListOwnerStaffInfo(ctx context.Context, in *ListOwnerStaffInfoRequest, opts ...grpc.CallOption) (*ListOwnerStaffInfoResponse, error)
	// create staff record
	CreateStaffRecord(ctx context.Context, in *CreateStaffRecordRequest, opts ...grpc.CallOption) (*CreateStaffRecordResponse, error)
	// update staff record
	UpdateStaffRecord(ctx context.Context, in *UpdateStaffRecordRequest, opts ...grpc.CallOption) (*UpdateStaffRecordResponse, error)
	// get enterprise staffs by account id
	GetEnterpriseStaffsByAccountId(ctx context.Context, in *GetEnterpriseStaffsByAccountIdRequest, opts ...grpc.CallOption) (*GetEnterpriseStaffsByAccountIdResponse, error)
	// get enterprise staff
	CreateEnterpriseStaff(ctx context.Context, in *CreateEnterpriseStaffRequest, opts ...grpc.CallOption) (*CreateEnterpriseStaffResponse, error)
	// update enterprise staff
	UpdateEnterpriseStaff(ctx context.Context, in *UpdateEnterpriseStaffRequest, opts ...grpc.CallOption) (*UpdateEnterpriseStaffResponse, error)
	// get enterprise staff
	GetEnterpriseStaff(ctx context.Context, in *GetEnterpriseStaffRequest, opts ...grpc.CallOption) (*GetEnterpriseStaffResponse, error)
	// delete enterprise staff
	DeleteEnterpriseStaff(ctx context.Context, in *DeleteEnterpriseStaffRequest, opts ...grpc.CallOption) (*DeleteEnterpriseStaffResponse, error)
	// list staff email def
	ListStaffEmailDefs(ctx context.Context, in *ListStaffEmailDefsRequest, opts ...grpc.CallOption) (*ListStaffEmailDefsResponse, error)
	// send invite link
	SendInviteLink(ctx context.Context, in *SendInviteLinkRequest, opts ...grpc.CallOption) (*SendInviteLinkResponse, error)
	// unlink staff
	UnlinkStaff(ctx context.Context, in *UnlinkStaffRequest, opts ...grpc.CallOption) (*UnlinkStaffResponse, error)
	// get staff login time
	GetStaffLoginTime(ctx context.Context, in *GetStaffLoginTimeRequest, opts ...grpc.CallOption) (*GetStaffLoginTimeResponse, error)
	// check staff login time
	CheckStaffLoginTime(ctx context.Context, in *CheckStaffLoginTimeRequest, opts ...grpc.CallOption) (*CheckStaffLoginTimeResponse, error)
	// get staffs with phone number
	GetStaffByPhoneNumber(ctx context.Context, in *GetStaffByPhoneNumberRequest, opts ...grpc.CallOption) (*GetStaffByPhoneNumberResponse, error)
	// get staffs with role
	GetStaffsByRole(ctx context.Context, in *GetStaffsByRoleRequest, opts ...grpc.CallOption) (*GetStaffsByRoleResponse, error)
	// get business级别的 staff slot availability type 返回by time or by slot
	GetBusinessStaffAvailabilityType(ctx context.Context, in *GetBusinessStaffAvailabilityTypeRequest, opts ...grpc.CallOption) (*GetBusinessStaffAvailabilityTypeResponse, error)
	// update business级别的 staff slot availability type
	UpdateBusinessStaffAvailabilityType(ctx context.Context, in *UpdateBusinessStaffAvailabilityTypeRequest, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResponse, error)
	// get staff slot availability
	GetStaffAvailability(ctx context.Context, in *GetStaffAvailabilityRequest, opts ...grpc.CallOption) (*GetStaffAvailabilityResponse, error)
	// update staff slot availability
	UpdateStaffAvailability(ctx context.Context, in *UpdateStaffAvailabilityRequest, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResponse, error)
	// override相关
	// update staff slot availability override
	UpdateStaffAvailabilityOverride(ctx context.Context, in *UpdateStaffAvailabilityOverrideRequest, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResponse, error)
	// delete staff slot availability override
	DeleteStaffAvailabilityOverride(ctx context.Context, in *DeleteStaffAvailabilityOverrideRequest, opts ...grpc.CallOption) (*DeleteStaffAvailabilityOverrideResponse, error)
	// get staff override config
	GetStaffAvailabilityOverride(ctx context.Context, in *GetStaffAvailabilityOverrideRequest, opts ...grpc.CallOption) (*GetStaffAvailabilityOverrideResponse, error)
	// get staff calender view
	GetStaffCalenderView(ctx context.Context, in *GetStaffCalenderViewRequest, opts ...grpc.CallOption) (*GetStaffCalenderViewResponse, error)
	// init staff availability
	InitStaffAvailability(ctx context.Context, in *InitStaffAvailabilityRequest, opts ...grpc.CallOption) (*InitStaffAvailabilityResponse, error)
	// list slot free services
	ListSlotFreeServices(ctx context.Context, in *ListSlotFreeServicesRequest, opts ...grpc.CallOption) (*ListSlotFreeServicesResponse, error)
	// update slot free services
	UpdateSlotFreeServices(ctx context.Context, in *UpdateSlotFreeServicesRequest, opts ...grpc.CallOption) (*UpdateSlotFreeServicesResponse, error)
}

type staffServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStaffServiceClient(cc grpc.ClientConnInterface) StaffServiceClient {
	return &staffServiceClient{cc}
}

func (c *staffServiceClient) CreateStaff(ctx context.Context, in *CreateStaffRequest, opts ...grpc.CallOption) (*CreateStaffResponse, error) {
	out := new(CreateStaffResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/CreateStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffDetail(ctx context.Context, in *GetStaffDetailRequest, opts ...grpc.CallOption) (*GetStaffDetailResponse, error) {
	out := new(GetStaffDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffFullDetail(ctx context.Context, in *GetStaffFullDetailRequest, opts ...grpc.CallOption) (*GetStaffFullDetailResponse, error) {
	out := new(GetStaffFullDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffFullDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffsByAccountId(ctx context.Context, in *GetStaffsByAccountIdRequest, opts ...grpc.CallOption) (*GetStaffsByAccountIdResponse, error) {
	out := new(GetStaffsByAccountIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffsByAccountId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffByCompany(ctx context.Context, in *GetStaffByCompanyRequest, opts ...grpc.CallOption) (*GetStaffByCompanyResponse, error) {
	out := new(GetStaffByCompanyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffByCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateAccountLastVisitBusiness(ctx context.Context, in *UpdateAccountLastVisitBusinessRequest, opts ...grpc.CallOption) (*UpdateAccountLastVisitBusinessResponse, error) {
	out := new(UpdateAccountLastVisitBusinessResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/UpdateAccountLastVisitBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaff(ctx context.Context, in *UpdateStaffRequest, opts ...grpc.CallOption) (*UpdateStaffResponse, error) {
	out := new(UpdateStaffResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/UpdateStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) DeleteStaff(ctx context.Context, in *DeleteStaffRequest, opts ...grpc.CallOption) (*DeleteStaffResponse, error) {
	out := new(DeleteStaffResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/DeleteStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) QueryStaffListByPagination(ctx context.Context, in *QueryStaffListByPaginationRequest, opts ...grpc.CallOption) (*QueryStaffListByPaginationResponse, error) {
	out := new(QueryStaffListByPaginationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/QueryStaffListByPagination", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffsByWorkingLocationIds(ctx context.Context, in *GetStaffsByWorkingLocationIdsRequest, opts ...grpc.CallOption) (*GetStaffsByWorkingLocationIdsResponse, error) {
	out := new(GetStaffsByWorkingLocationIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffsByWorkingLocationIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) QueryStaffByIds(ctx context.Context, in *QueryStaffByIdsRequest, opts ...grpc.CallOption) (*QueryStaffByIdsResponse, error) {
	out := new(QueryStaffByIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/QueryStaffByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetShowOnCalendarStaffs(ctx context.Context, in *GetShowOnCalendarStaffsRequest, opts ...grpc.CallOption) (*GetShowOnCalendarStaffsResponse, error) {
	out := new(GetShowOnCalendarStaffsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetShowOnCalendarStaffs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetShowOnCalendarStaffIds(ctx context.Context, in *GetShowOnCalendarStaffsRequest, opts ...grpc.CallOption) (*GetShowOnCalendarStaffIdsResponse, error) {
	out := new(GetShowOnCalendarStaffIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetShowOnCalendarStaffIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) MigrateStaffData(ctx context.Context, in *MigrateStaffDataRequest, opts ...grpc.CallOption) (*MigrateStaffDataResponse, error) {
	out := new(MigrateStaffDataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/MigrateStaffData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) CountStaffWithRole(ctx context.Context, in *CountStaffWithRoleRequest, opts ...grpc.CallOption) (*CountStaffWithRoleResponse, error) {
	out := new(CountStaffWithRoleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/CountStaffWithRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffsByWorkingLocation(ctx context.Context, in *GetStaffsByWorkingLocationRequest, opts ...grpc.CallOption) (*GetStaffsByWorkingLocationResponse, error) {
	out := new(GetStaffsByWorkingLocationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffsByWorkingLocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetClockInOutStaffs(ctx context.Context, in *GetClockInOutStaffsRequest, opts ...grpc.CallOption) (*GetClockInOutStaffsResponse, error) {
	out := new(GetClockInOutStaffsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetClockInOutStaffs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetEnterpriseStaffsByWorkingLocationIds(ctx context.Context, in *GetEnterpriseStaffsByWorkingLocationIdsRequest, opts ...grpc.CallOption) (*GetEnterpriseStaffsByWorkingLocationIdsResponse, error) {
	out := new(GetEnterpriseStaffsByWorkingLocationIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetEnterpriseStaffsByWorkingLocationIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) CreateEnterpriseOwner(ctx context.Context, in *CreateEnterpriseOwnerRequest, opts ...grpc.CallOption) (*CreateEnterpriseOwnerResponse, error) {
	out := new(CreateEnterpriseOwnerResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/CreateEnterpriseOwner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) QueryStaffByCompanyId(ctx context.Context, in *QueryStaffByCompanyIdRequest, opts ...grpc.CallOption) (*QueryStaffByCompanyIdResponse, error) {
	out := new(QueryStaffByCompanyIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/QueryStaffByCompanyId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) SendInviteStaffLink(ctx context.Context, in *SendInviteStaffLinkRequest, opts ...grpc.CallOption) (*SendInviteStaffLinkResponse, error) {
	out := new(SendInviteStaffLinkResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/SendInviteStaffLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) ListOwnerStaffInfo(ctx context.Context, in *ListOwnerStaffInfoRequest, opts ...grpc.CallOption) (*ListOwnerStaffInfoResponse, error) {
	out := new(ListOwnerStaffInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/ListOwnerStaffInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) CreateStaffRecord(ctx context.Context, in *CreateStaffRecordRequest, opts ...grpc.CallOption) (*CreateStaffRecordResponse, error) {
	out := new(CreateStaffRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/CreateStaffRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaffRecord(ctx context.Context, in *UpdateStaffRecordRequest, opts ...grpc.CallOption) (*UpdateStaffRecordResponse, error) {
	out := new(UpdateStaffRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/UpdateStaffRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetEnterpriseStaffsByAccountId(ctx context.Context, in *GetEnterpriseStaffsByAccountIdRequest, opts ...grpc.CallOption) (*GetEnterpriseStaffsByAccountIdResponse, error) {
	out := new(GetEnterpriseStaffsByAccountIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetEnterpriseStaffsByAccountId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) CreateEnterpriseStaff(ctx context.Context, in *CreateEnterpriseStaffRequest, opts ...grpc.CallOption) (*CreateEnterpriseStaffResponse, error) {
	out := new(CreateEnterpriseStaffResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/CreateEnterpriseStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateEnterpriseStaff(ctx context.Context, in *UpdateEnterpriseStaffRequest, opts ...grpc.CallOption) (*UpdateEnterpriseStaffResponse, error) {
	out := new(UpdateEnterpriseStaffResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/UpdateEnterpriseStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetEnterpriseStaff(ctx context.Context, in *GetEnterpriseStaffRequest, opts ...grpc.CallOption) (*GetEnterpriseStaffResponse, error) {
	out := new(GetEnterpriseStaffResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetEnterpriseStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) DeleteEnterpriseStaff(ctx context.Context, in *DeleteEnterpriseStaffRequest, opts ...grpc.CallOption) (*DeleteEnterpriseStaffResponse, error) {
	out := new(DeleteEnterpriseStaffResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/DeleteEnterpriseStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) ListStaffEmailDefs(ctx context.Context, in *ListStaffEmailDefsRequest, opts ...grpc.CallOption) (*ListStaffEmailDefsResponse, error) {
	out := new(ListStaffEmailDefsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/ListStaffEmailDefs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) SendInviteLink(ctx context.Context, in *SendInviteLinkRequest, opts ...grpc.CallOption) (*SendInviteLinkResponse, error) {
	out := new(SendInviteLinkResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/SendInviteLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UnlinkStaff(ctx context.Context, in *UnlinkStaffRequest, opts ...grpc.CallOption) (*UnlinkStaffResponse, error) {
	out := new(UnlinkStaffResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/UnlinkStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffLoginTime(ctx context.Context, in *GetStaffLoginTimeRequest, opts ...grpc.CallOption) (*GetStaffLoginTimeResponse, error) {
	out := new(GetStaffLoginTimeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffLoginTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) CheckStaffLoginTime(ctx context.Context, in *CheckStaffLoginTimeRequest, opts ...grpc.CallOption) (*CheckStaffLoginTimeResponse, error) {
	out := new(CheckStaffLoginTimeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/CheckStaffLoginTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffByPhoneNumber(ctx context.Context, in *GetStaffByPhoneNumberRequest, opts ...grpc.CallOption) (*GetStaffByPhoneNumberResponse, error) {
	out := new(GetStaffByPhoneNumberResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffByPhoneNumber", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffsByRole(ctx context.Context, in *GetStaffsByRoleRequest, opts ...grpc.CallOption) (*GetStaffsByRoleResponse, error) {
	out := new(GetStaffsByRoleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffsByRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetBusinessStaffAvailabilityType(ctx context.Context, in *GetBusinessStaffAvailabilityTypeRequest, opts ...grpc.CallOption) (*GetBusinessStaffAvailabilityTypeResponse, error) {
	out := new(GetBusinessStaffAvailabilityTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetBusinessStaffAvailabilityType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateBusinessStaffAvailabilityType(ctx context.Context, in *UpdateBusinessStaffAvailabilityTypeRequest, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResponse, error) {
	out := new(UpdateStaffAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/UpdateBusinessStaffAvailabilityType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffAvailability(ctx context.Context, in *GetStaffAvailabilityRequest, opts ...grpc.CallOption) (*GetStaffAvailabilityResponse, error) {
	out := new(GetStaffAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaffAvailability(ctx context.Context, in *UpdateStaffAvailabilityRequest, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResponse, error) {
	out := new(UpdateStaffAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/UpdateStaffAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaffAvailabilityOverride(ctx context.Context, in *UpdateStaffAvailabilityOverrideRequest, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResponse, error) {
	out := new(UpdateStaffAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/UpdateStaffAvailabilityOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) DeleteStaffAvailabilityOverride(ctx context.Context, in *DeleteStaffAvailabilityOverrideRequest, opts ...grpc.CallOption) (*DeleteStaffAvailabilityOverrideResponse, error) {
	out := new(DeleteStaffAvailabilityOverrideResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/DeleteStaffAvailabilityOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffAvailabilityOverride(ctx context.Context, in *GetStaffAvailabilityOverrideRequest, opts ...grpc.CallOption) (*GetStaffAvailabilityOverrideResponse, error) {
	out := new(GetStaffAvailabilityOverrideResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffAvailabilityOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffCalenderView(ctx context.Context, in *GetStaffCalenderViewRequest, opts ...grpc.CallOption) (*GetStaffCalenderViewResponse, error) {
	out := new(GetStaffCalenderViewResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/GetStaffCalenderView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) InitStaffAvailability(ctx context.Context, in *InitStaffAvailabilityRequest, opts ...grpc.CallOption) (*InitStaffAvailabilityResponse, error) {
	out := new(InitStaffAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/InitStaffAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) ListSlotFreeServices(ctx context.Context, in *ListSlotFreeServicesRequest, opts ...grpc.CallOption) (*ListSlotFreeServicesResponse, error) {
	out := new(ListSlotFreeServicesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/ListSlotFreeServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateSlotFreeServices(ctx context.Context, in *UpdateSlotFreeServicesRequest, opts ...grpc.CallOption) (*UpdateSlotFreeServicesResponse, error) {
	out := new(UpdateSlotFreeServicesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.StaffService/UpdateSlotFreeServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StaffServiceServer is the server API for StaffService service.
// All implementations must embed UnimplementedStaffServiceServer
// for forward compatibility
type StaffServiceServer interface {
	// api to create a staff
	CreateStaff(context.Context, *CreateStaffRequest) (*CreateStaffResponse, error)
	// api to get a staff detail
	GetStaffDetail(context.Context, *GetStaffDetailRequest) (*GetStaffDetailResponse, error)
	// api to get staff full detail
	GetStaffFullDetail(context.Context, *GetStaffFullDetailRequest) (*GetStaffFullDetailResponse, error)
	// Api to get staffs by account id,including enterprise staff,migrated company and legacy company staff.
	// migrated company staff has no business_id and legacy company staff has a certain business_id.
	// Enterprise staffs place in the front and company staffs will sort desc by account_sort.
	GetStaffsByAccountId(context.Context, *GetStaffsByAccountIdRequest) (*GetStaffsByAccountIdResponse, error)
	// get staff by company
	GetStaffByCompany(context.Context, *GetStaffByCompanyRequest) (*GetStaffByCompanyResponse, error)
	// update account last visit business
	UpdateAccountLastVisitBusiness(context.Context, *UpdateAccountLastVisitBusinessRequest) (*UpdateAccountLastVisitBusinessResponse, error)
	// update staff
	UpdateStaff(context.Context, *UpdateStaffRequest) (*UpdateStaffResponse, error)
	// delete staff
	DeleteStaff(context.Context, *DeleteStaffRequest) (*DeleteStaffResponse, error)
	// get staff info list by pagination
	QueryStaffListByPagination(context.Context, *QueryStaffListByPaginationRequest) (*QueryStaffListByPaginationResponse, error)
	// get working staffs by location ids
	GetStaffsByWorkingLocationIds(context.Context, *GetStaffsByWorkingLocationIdsRequest) (*GetStaffsByWorkingLocationIdsResponse, error)
	// query staff by ids
	QueryStaffByIds(context.Context, *QueryStaffByIdsRequest) (*QueryStaffByIdsResponse, error)
	// get show on calendar staffs
	GetShowOnCalendarStaffs(context.Context, *GetShowOnCalendarStaffsRequest) (*GetShowOnCalendarStaffsResponse, error)
	// get show on calendar staff ids
	GetShowOnCalendarStaffIds(context.Context, *GetShowOnCalendarStaffsRequest) (*GetShowOnCalendarStaffIdsResponse, error)
	// staff data migrate
	MigrateStaffData(context.Context, *MigrateStaffDataRequest) (*MigrateStaffDataResponse, error)
	// count staff with role
	CountStaffWithRole(context.Context, *CountStaffWithRoleRequest) (*CountStaffWithRoleResponse, error)
	// get staffs by working location
	GetStaffsByWorkingLocation(context.Context, *GetStaffsByWorkingLocationRequest) (*GetStaffsByWorkingLocationResponse, error)
	// get clock in out staffs of current staff
	GetClockInOutStaffs(context.Context, *GetClockInOutStaffsRequest) (*GetClockInOutStaffsResponse, error)
	// get enterprise staffs by working location ids
	GetEnterpriseStaffsByWorkingLocationIds(context.Context, *GetEnterpriseStaffsByWorkingLocationIdsRequest) (*GetEnterpriseStaffsByWorkingLocationIdsResponse, error)
	// create enterprise owner
	CreateEnterpriseOwner(context.Context, *CreateEnterpriseOwnerRequest) (*CreateEnterpriseOwnerResponse, error)
	// query staffs by company id
	QueryStaffByCompanyId(context.Context, *QueryStaffByCompanyIdRequest) (*QueryStaffByCompanyIdResponse, error)
	// get staffs by account id
	SendInviteStaffLink(context.Context, *SendInviteStaffLinkRequest) (*SendInviteStaffLinkResponse, error)
	// get staff invited link status
	ListOwnerStaffInfo(context.Context, *ListOwnerStaffInfoRequest) (*ListOwnerStaffInfoResponse, error)
	// create staff record
	CreateStaffRecord(context.Context, *CreateStaffRecordRequest) (*CreateStaffRecordResponse, error)
	// update staff record
	UpdateStaffRecord(context.Context, *UpdateStaffRecordRequest) (*UpdateStaffRecordResponse, error)
	// get enterprise staffs by account id
	GetEnterpriseStaffsByAccountId(context.Context, *GetEnterpriseStaffsByAccountIdRequest) (*GetEnterpriseStaffsByAccountIdResponse, error)
	// get enterprise staff
	CreateEnterpriseStaff(context.Context, *CreateEnterpriseStaffRequest) (*CreateEnterpriseStaffResponse, error)
	// update enterprise staff
	UpdateEnterpriseStaff(context.Context, *UpdateEnterpriseStaffRequest) (*UpdateEnterpriseStaffResponse, error)
	// get enterprise staff
	GetEnterpriseStaff(context.Context, *GetEnterpriseStaffRequest) (*GetEnterpriseStaffResponse, error)
	// delete enterprise staff
	DeleteEnterpriseStaff(context.Context, *DeleteEnterpriseStaffRequest) (*DeleteEnterpriseStaffResponse, error)
	// list staff email def
	ListStaffEmailDefs(context.Context, *ListStaffEmailDefsRequest) (*ListStaffEmailDefsResponse, error)
	// send invite link
	SendInviteLink(context.Context, *SendInviteLinkRequest) (*SendInviteLinkResponse, error)
	// unlink staff
	UnlinkStaff(context.Context, *UnlinkStaffRequest) (*UnlinkStaffResponse, error)
	// get staff login time
	GetStaffLoginTime(context.Context, *GetStaffLoginTimeRequest) (*GetStaffLoginTimeResponse, error)
	// check staff login time
	CheckStaffLoginTime(context.Context, *CheckStaffLoginTimeRequest) (*CheckStaffLoginTimeResponse, error)
	// get staffs with phone number
	GetStaffByPhoneNumber(context.Context, *GetStaffByPhoneNumberRequest) (*GetStaffByPhoneNumberResponse, error)
	// get staffs with role
	GetStaffsByRole(context.Context, *GetStaffsByRoleRequest) (*GetStaffsByRoleResponse, error)
	// get business级别的 staff slot availability type 返回by time or by slot
	GetBusinessStaffAvailabilityType(context.Context, *GetBusinessStaffAvailabilityTypeRequest) (*GetBusinessStaffAvailabilityTypeResponse, error)
	// update business级别的 staff slot availability type
	UpdateBusinessStaffAvailabilityType(context.Context, *UpdateBusinessStaffAvailabilityTypeRequest) (*UpdateStaffAvailabilityResponse, error)
	// get staff slot availability
	GetStaffAvailability(context.Context, *GetStaffAvailabilityRequest) (*GetStaffAvailabilityResponse, error)
	// update staff slot availability
	UpdateStaffAvailability(context.Context, *UpdateStaffAvailabilityRequest) (*UpdateStaffAvailabilityResponse, error)
	// override相关
	// update staff slot availability override
	UpdateStaffAvailabilityOverride(context.Context, *UpdateStaffAvailabilityOverrideRequest) (*UpdateStaffAvailabilityResponse, error)
	// delete staff slot availability override
	DeleteStaffAvailabilityOverride(context.Context, *DeleteStaffAvailabilityOverrideRequest) (*DeleteStaffAvailabilityOverrideResponse, error)
	// get staff override config
	GetStaffAvailabilityOverride(context.Context, *GetStaffAvailabilityOverrideRequest) (*GetStaffAvailabilityOverrideResponse, error)
	// get staff calender view
	GetStaffCalenderView(context.Context, *GetStaffCalenderViewRequest) (*GetStaffCalenderViewResponse, error)
	// init staff availability
	InitStaffAvailability(context.Context, *InitStaffAvailabilityRequest) (*InitStaffAvailabilityResponse, error)
	// list slot free services
	ListSlotFreeServices(context.Context, *ListSlotFreeServicesRequest) (*ListSlotFreeServicesResponse, error)
	// update slot free services
	UpdateSlotFreeServices(context.Context, *UpdateSlotFreeServicesRequest) (*UpdateSlotFreeServicesResponse, error)
	mustEmbedUnimplementedStaffServiceServer()
}

// UnimplementedStaffServiceServer must be embedded to have forward compatible implementations.
type UnimplementedStaffServiceServer struct {
}

func (UnimplementedStaffServiceServer) CreateStaff(context.Context, *CreateStaffRequest) (*CreateStaffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStaff not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffDetail(context.Context, *GetStaffDetailRequest) (*GetStaffDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffDetail not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffFullDetail(context.Context, *GetStaffFullDetailRequest) (*GetStaffFullDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffFullDetail not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffsByAccountId(context.Context, *GetStaffsByAccountIdRequest) (*GetStaffsByAccountIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffsByAccountId not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffByCompany(context.Context, *GetStaffByCompanyRequest) (*GetStaffByCompanyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffByCompany not implemented")
}
func (UnimplementedStaffServiceServer) UpdateAccountLastVisitBusiness(context.Context, *UpdateAccountLastVisitBusinessRequest) (*UpdateAccountLastVisitBusinessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAccountLastVisitBusiness not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaff(context.Context, *UpdateStaffRequest) (*UpdateStaffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaff not implemented")
}
func (UnimplementedStaffServiceServer) DeleteStaff(context.Context, *DeleteStaffRequest) (*DeleteStaffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStaff not implemented")
}
func (UnimplementedStaffServiceServer) QueryStaffListByPagination(context.Context, *QueryStaffListByPaginationRequest) (*QueryStaffListByPaginationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStaffListByPagination not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffsByWorkingLocationIds(context.Context, *GetStaffsByWorkingLocationIdsRequest) (*GetStaffsByWorkingLocationIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffsByWorkingLocationIds not implemented")
}
func (UnimplementedStaffServiceServer) QueryStaffByIds(context.Context, *QueryStaffByIdsRequest) (*QueryStaffByIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStaffByIds not implemented")
}
func (UnimplementedStaffServiceServer) GetShowOnCalendarStaffs(context.Context, *GetShowOnCalendarStaffsRequest) (*GetShowOnCalendarStaffsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetShowOnCalendarStaffs not implemented")
}
func (UnimplementedStaffServiceServer) GetShowOnCalendarStaffIds(context.Context, *GetShowOnCalendarStaffsRequest) (*GetShowOnCalendarStaffIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetShowOnCalendarStaffIds not implemented")
}
func (UnimplementedStaffServiceServer) MigrateStaffData(context.Context, *MigrateStaffDataRequest) (*MigrateStaffDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MigrateStaffData not implemented")
}
func (UnimplementedStaffServiceServer) CountStaffWithRole(context.Context, *CountStaffWithRoleRequest) (*CountStaffWithRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountStaffWithRole not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffsByWorkingLocation(context.Context, *GetStaffsByWorkingLocationRequest) (*GetStaffsByWorkingLocationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffsByWorkingLocation not implemented")
}
func (UnimplementedStaffServiceServer) GetClockInOutStaffs(context.Context, *GetClockInOutStaffsRequest) (*GetClockInOutStaffsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClockInOutStaffs not implemented")
}
func (UnimplementedStaffServiceServer) GetEnterpriseStaffsByWorkingLocationIds(context.Context, *GetEnterpriseStaffsByWorkingLocationIdsRequest) (*GetEnterpriseStaffsByWorkingLocationIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterpriseStaffsByWorkingLocationIds not implemented")
}
func (UnimplementedStaffServiceServer) CreateEnterpriseOwner(context.Context, *CreateEnterpriseOwnerRequest) (*CreateEnterpriseOwnerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEnterpriseOwner not implemented")
}
func (UnimplementedStaffServiceServer) QueryStaffByCompanyId(context.Context, *QueryStaffByCompanyIdRequest) (*QueryStaffByCompanyIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStaffByCompanyId not implemented")
}
func (UnimplementedStaffServiceServer) SendInviteStaffLink(context.Context, *SendInviteStaffLinkRequest) (*SendInviteStaffLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendInviteStaffLink not implemented")
}
func (UnimplementedStaffServiceServer) ListOwnerStaffInfo(context.Context, *ListOwnerStaffInfoRequest) (*ListOwnerStaffInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOwnerStaffInfo not implemented")
}
func (UnimplementedStaffServiceServer) CreateStaffRecord(context.Context, *CreateStaffRecordRequest) (*CreateStaffRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStaffRecord not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaffRecord(context.Context, *UpdateStaffRecordRequest) (*UpdateStaffRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffRecord not implemented")
}
func (UnimplementedStaffServiceServer) GetEnterpriseStaffsByAccountId(context.Context, *GetEnterpriseStaffsByAccountIdRequest) (*GetEnterpriseStaffsByAccountIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterpriseStaffsByAccountId not implemented")
}
func (UnimplementedStaffServiceServer) CreateEnterpriseStaff(context.Context, *CreateEnterpriseStaffRequest) (*CreateEnterpriseStaffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEnterpriseStaff not implemented")
}
func (UnimplementedStaffServiceServer) UpdateEnterpriseStaff(context.Context, *UpdateEnterpriseStaffRequest) (*UpdateEnterpriseStaffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEnterpriseStaff not implemented")
}
func (UnimplementedStaffServiceServer) GetEnterpriseStaff(context.Context, *GetEnterpriseStaffRequest) (*GetEnterpriseStaffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterpriseStaff not implemented")
}
func (UnimplementedStaffServiceServer) DeleteEnterpriseStaff(context.Context, *DeleteEnterpriseStaffRequest) (*DeleteEnterpriseStaffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEnterpriseStaff not implemented")
}
func (UnimplementedStaffServiceServer) ListStaffEmailDefs(context.Context, *ListStaffEmailDefsRequest) (*ListStaffEmailDefsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStaffEmailDefs not implemented")
}
func (UnimplementedStaffServiceServer) SendInviteLink(context.Context, *SendInviteLinkRequest) (*SendInviteLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendInviteLink not implemented")
}
func (UnimplementedStaffServiceServer) UnlinkStaff(context.Context, *UnlinkStaffRequest) (*UnlinkStaffResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlinkStaff not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffLoginTime(context.Context, *GetStaffLoginTimeRequest) (*GetStaffLoginTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffLoginTime not implemented")
}
func (UnimplementedStaffServiceServer) CheckStaffLoginTime(context.Context, *CheckStaffLoginTimeRequest) (*CheckStaffLoginTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckStaffLoginTime not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffByPhoneNumber(context.Context, *GetStaffByPhoneNumberRequest) (*GetStaffByPhoneNumberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffByPhoneNumber not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffsByRole(context.Context, *GetStaffsByRoleRequest) (*GetStaffsByRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffsByRole not implemented")
}
func (UnimplementedStaffServiceServer) GetBusinessStaffAvailabilityType(context.Context, *GetBusinessStaffAvailabilityTypeRequest) (*GetBusinessStaffAvailabilityTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinessStaffAvailabilityType not implemented")
}
func (UnimplementedStaffServiceServer) UpdateBusinessStaffAvailabilityType(context.Context, *UpdateBusinessStaffAvailabilityTypeRequest) (*UpdateStaffAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBusinessStaffAvailabilityType not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffAvailability(context.Context, *GetStaffAvailabilityRequest) (*GetStaffAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffAvailability not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaffAvailability(context.Context, *UpdateStaffAvailabilityRequest) (*UpdateStaffAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffAvailability not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaffAvailabilityOverride(context.Context, *UpdateStaffAvailabilityOverrideRequest) (*UpdateStaffAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffAvailabilityOverride not implemented")
}
func (UnimplementedStaffServiceServer) DeleteStaffAvailabilityOverride(context.Context, *DeleteStaffAvailabilityOverrideRequest) (*DeleteStaffAvailabilityOverrideResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStaffAvailabilityOverride not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffAvailabilityOverride(context.Context, *GetStaffAvailabilityOverrideRequest) (*GetStaffAvailabilityOverrideResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffAvailabilityOverride not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffCalenderView(context.Context, *GetStaffCalenderViewRequest) (*GetStaffCalenderViewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffCalenderView not implemented")
}
func (UnimplementedStaffServiceServer) InitStaffAvailability(context.Context, *InitStaffAvailabilityRequest) (*InitStaffAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitStaffAvailability not implemented")
}
func (UnimplementedStaffServiceServer) ListSlotFreeServices(context.Context, *ListSlotFreeServicesRequest) (*ListSlotFreeServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSlotFreeServices not implemented")
}
func (UnimplementedStaffServiceServer) UpdateSlotFreeServices(context.Context, *UpdateSlotFreeServicesRequest) (*UpdateSlotFreeServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSlotFreeServices not implemented")
}
func (UnimplementedStaffServiceServer) mustEmbedUnimplementedStaffServiceServer() {}

// UnsafeStaffServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StaffServiceServer will
// result in compilation errors.
type UnsafeStaffServiceServer interface {
	mustEmbedUnimplementedStaffServiceServer()
}

func RegisterStaffServiceServer(s grpc.ServiceRegistrar, srv StaffServiceServer) {
	s.RegisterService(&StaffService_ServiceDesc, srv)
}

func _StaffService_CreateStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStaffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).CreateStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/CreateStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).CreateStaff(ctx, req.(*CreateStaffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffDetail(ctx, req.(*GetStaffDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffFullDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffFullDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffFullDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffFullDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffFullDetail(ctx, req.(*GetStaffFullDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffsByAccountId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffsByAccountIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffsByAccountId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffsByAccountId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffsByAccountId(ctx, req.(*GetStaffsByAccountIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffByCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffByCompanyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffByCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffByCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffByCompany(ctx, req.(*GetStaffByCompanyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateAccountLastVisitBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAccountLastVisitBusinessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateAccountLastVisitBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/UpdateAccountLastVisitBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateAccountLastVisitBusiness(ctx, req.(*UpdateAccountLastVisitBusinessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/UpdateStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaff(ctx, req.(*UpdateStaffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_DeleteStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStaffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).DeleteStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/DeleteStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).DeleteStaff(ctx, req.(*DeleteStaffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_QueryStaffListByPagination_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStaffListByPaginationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).QueryStaffListByPagination(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/QueryStaffListByPagination",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).QueryStaffListByPagination(ctx, req.(*QueryStaffListByPaginationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffsByWorkingLocationIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffsByWorkingLocationIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffsByWorkingLocationIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffsByWorkingLocationIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffsByWorkingLocationIds(ctx, req.(*GetStaffsByWorkingLocationIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_QueryStaffByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStaffByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).QueryStaffByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/QueryStaffByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).QueryStaffByIds(ctx, req.(*QueryStaffByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetShowOnCalendarStaffs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShowOnCalendarStaffsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetShowOnCalendarStaffs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetShowOnCalendarStaffs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetShowOnCalendarStaffs(ctx, req.(*GetShowOnCalendarStaffsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetShowOnCalendarStaffIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShowOnCalendarStaffsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetShowOnCalendarStaffIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetShowOnCalendarStaffIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetShowOnCalendarStaffIds(ctx, req.(*GetShowOnCalendarStaffsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_MigrateStaffData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MigrateStaffDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).MigrateStaffData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/MigrateStaffData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).MigrateStaffData(ctx, req.(*MigrateStaffDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_CountStaffWithRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountStaffWithRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).CountStaffWithRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/CountStaffWithRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).CountStaffWithRole(ctx, req.(*CountStaffWithRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffsByWorkingLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffsByWorkingLocationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffsByWorkingLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffsByWorkingLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffsByWorkingLocation(ctx, req.(*GetStaffsByWorkingLocationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetClockInOutStaffs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClockInOutStaffsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetClockInOutStaffs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetClockInOutStaffs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetClockInOutStaffs(ctx, req.(*GetClockInOutStaffsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetEnterpriseStaffsByWorkingLocationIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseStaffsByWorkingLocationIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetEnterpriseStaffsByWorkingLocationIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetEnterpriseStaffsByWorkingLocationIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetEnterpriseStaffsByWorkingLocationIds(ctx, req.(*GetEnterpriseStaffsByWorkingLocationIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_CreateEnterpriseOwner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEnterpriseOwnerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).CreateEnterpriseOwner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/CreateEnterpriseOwner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).CreateEnterpriseOwner(ctx, req.(*CreateEnterpriseOwnerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_QueryStaffByCompanyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStaffByCompanyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).QueryStaffByCompanyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/QueryStaffByCompanyId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).QueryStaffByCompanyId(ctx, req.(*QueryStaffByCompanyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_SendInviteStaffLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendInviteStaffLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).SendInviteStaffLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/SendInviteStaffLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).SendInviteStaffLink(ctx, req.(*SendInviteStaffLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_ListOwnerStaffInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOwnerStaffInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).ListOwnerStaffInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/ListOwnerStaffInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).ListOwnerStaffInfo(ctx, req.(*ListOwnerStaffInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_CreateStaffRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStaffRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).CreateStaffRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/CreateStaffRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).CreateStaffRecord(ctx, req.(*CreateStaffRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaffRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaffRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/UpdateStaffRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaffRecord(ctx, req.(*UpdateStaffRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetEnterpriseStaffsByAccountId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseStaffsByAccountIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetEnterpriseStaffsByAccountId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetEnterpriseStaffsByAccountId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetEnterpriseStaffsByAccountId(ctx, req.(*GetEnterpriseStaffsByAccountIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_CreateEnterpriseStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEnterpriseStaffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).CreateEnterpriseStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/CreateEnterpriseStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).CreateEnterpriseStaff(ctx, req.(*CreateEnterpriseStaffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateEnterpriseStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEnterpriseStaffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateEnterpriseStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/UpdateEnterpriseStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateEnterpriseStaff(ctx, req.(*UpdateEnterpriseStaffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetEnterpriseStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseStaffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetEnterpriseStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetEnterpriseStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetEnterpriseStaff(ctx, req.(*GetEnterpriseStaffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_DeleteEnterpriseStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEnterpriseStaffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).DeleteEnterpriseStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/DeleteEnterpriseStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).DeleteEnterpriseStaff(ctx, req.(*DeleteEnterpriseStaffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_ListStaffEmailDefs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStaffEmailDefsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).ListStaffEmailDefs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/ListStaffEmailDefs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).ListStaffEmailDefs(ctx, req.(*ListStaffEmailDefsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_SendInviteLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendInviteLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).SendInviteLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/SendInviteLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).SendInviteLink(ctx, req.(*SendInviteLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UnlinkStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlinkStaffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UnlinkStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/UnlinkStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UnlinkStaff(ctx, req.(*UnlinkStaffRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffLoginTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffLoginTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffLoginTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffLoginTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffLoginTime(ctx, req.(*GetStaffLoginTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_CheckStaffLoginTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckStaffLoginTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).CheckStaffLoginTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/CheckStaffLoginTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).CheckStaffLoginTime(ctx, req.(*CheckStaffLoginTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffByPhoneNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffByPhoneNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffByPhoneNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffByPhoneNumber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffByPhoneNumber(ctx, req.(*GetStaffByPhoneNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffsByRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffsByRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffsByRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffsByRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffsByRole(ctx, req.(*GetStaffsByRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetBusinessStaffAvailabilityType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessStaffAvailabilityTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetBusinessStaffAvailabilityType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetBusinessStaffAvailabilityType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetBusinessStaffAvailabilityType(ctx, req.(*GetBusinessStaffAvailabilityTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateBusinessStaffAvailabilityType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBusinessStaffAvailabilityTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateBusinessStaffAvailabilityType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/UpdateBusinessStaffAvailabilityType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateBusinessStaffAvailabilityType(ctx, req.(*UpdateBusinessStaffAvailabilityTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffAvailability(ctx, req.(*GetStaffAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaffAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaffAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/UpdateStaffAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaffAvailability(ctx, req.(*UpdateStaffAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaffAvailabilityOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffAvailabilityOverrideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaffAvailabilityOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/UpdateStaffAvailabilityOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaffAvailabilityOverride(ctx, req.(*UpdateStaffAvailabilityOverrideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_DeleteStaffAvailabilityOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStaffAvailabilityOverrideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).DeleteStaffAvailabilityOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/DeleteStaffAvailabilityOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).DeleteStaffAvailabilityOverride(ctx, req.(*DeleteStaffAvailabilityOverrideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffAvailabilityOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffAvailabilityOverrideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffAvailabilityOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffAvailabilityOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffAvailabilityOverride(ctx, req.(*GetStaffAvailabilityOverrideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffCalenderView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffCalenderViewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffCalenderView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/GetStaffCalenderView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffCalenderView(ctx, req.(*GetStaffCalenderViewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_InitStaffAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitStaffAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).InitStaffAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/InitStaffAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).InitStaffAvailability(ctx, req.(*InitStaffAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_ListSlotFreeServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSlotFreeServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).ListSlotFreeServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/ListSlotFreeServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).ListSlotFreeServices(ctx, req.(*ListSlotFreeServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateSlotFreeServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSlotFreeServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateSlotFreeServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.StaffService/UpdateSlotFreeServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateSlotFreeServices(ctx, req.(*UpdateSlotFreeServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// StaffService_ServiceDesc is the grpc.ServiceDesc for StaffService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StaffService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.organization.v1.StaffService",
	HandlerType: (*StaffServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateStaff",
			Handler:    _StaffService_CreateStaff_Handler,
		},
		{
			MethodName: "GetStaffDetail",
			Handler:    _StaffService_GetStaffDetail_Handler,
		},
		{
			MethodName: "GetStaffFullDetail",
			Handler:    _StaffService_GetStaffFullDetail_Handler,
		},
		{
			MethodName: "GetStaffsByAccountId",
			Handler:    _StaffService_GetStaffsByAccountId_Handler,
		},
		{
			MethodName: "GetStaffByCompany",
			Handler:    _StaffService_GetStaffByCompany_Handler,
		},
		{
			MethodName: "UpdateAccountLastVisitBusiness",
			Handler:    _StaffService_UpdateAccountLastVisitBusiness_Handler,
		},
		{
			MethodName: "UpdateStaff",
			Handler:    _StaffService_UpdateStaff_Handler,
		},
		{
			MethodName: "DeleteStaff",
			Handler:    _StaffService_DeleteStaff_Handler,
		},
		{
			MethodName: "QueryStaffListByPagination",
			Handler:    _StaffService_QueryStaffListByPagination_Handler,
		},
		{
			MethodName: "GetStaffsByWorkingLocationIds",
			Handler:    _StaffService_GetStaffsByWorkingLocationIds_Handler,
		},
		{
			MethodName: "QueryStaffByIds",
			Handler:    _StaffService_QueryStaffByIds_Handler,
		},
		{
			MethodName: "GetShowOnCalendarStaffs",
			Handler:    _StaffService_GetShowOnCalendarStaffs_Handler,
		},
		{
			MethodName: "GetShowOnCalendarStaffIds",
			Handler:    _StaffService_GetShowOnCalendarStaffIds_Handler,
		},
		{
			MethodName: "MigrateStaffData",
			Handler:    _StaffService_MigrateStaffData_Handler,
		},
		{
			MethodName: "CountStaffWithRole",
			Handler:    _StaffService_CountStaffWithRole_Handler,
		},
		{
			MethodName: "GetStaffsByWorkingLocation",
			Handler:    _StaffService_GetStaffsByWorkingLocation_Handler,
		},
		{
			MethodName: "GetClockInOutStaffs",
			Handler:    _StaffService_GetClockInOutStaffs_Handler,
		},
		{
			MethodName: "GetEnterpriseStaffsByWorkingLocationIds",
			Handler:    _StaffService_GetEnterpriseStaffsByWorkingLocationIds_Handler,
		},
		{
			MethodName: "CreateEnterpriseOwner",
			Handler:    _StaffService_CreateEnterpriseOwner_Handler,
		},
		{
			MethodName: "QueryStaffByCompanyId",
			Handler:    _StaffService_QueryStaffByCompanyId_Handler,
		},
		{
			MethodName: "SendInviteStaffLink",
			Handler:    _StaffService_SendInviteStaffLink_Handler,
		},
		{
			MethodName: "ListOwnerStaffInfo",
			Handler:    _StaffService_ListOwnerStaffInfo_Handler,
		},
		{
			MethodName: "CreateStaffRecord",
			Handler:    _StaffService_CreateStaffRecord_Handler,
		},
		{
			MethodName: "UpdateStaffRecord",
			Handler:    _StaffService_UpdateStaffRecord_Handler,
		},
		{
			MethodName: "GetEnterpriseStaffsByAccountId",
			Handler:    _StaffService_GetEnterpriseStaffsByAccountId_Handler,
		},
		{
			MethodName: "CreateEnterpriseStaff",
			Handler:    _StaffService_CreateEnterpriseStaff_Handler,
		},
		{
			MethodName: "UpdateEnterpriseStaff",
			Handler:    _StaffService_UpdateEnterpriseStaff_Handler,
		},
		{
			MethodName: "GetEnterpriseStaff",
			Handler:    _StaffService_GetEnterpriseStaff_Handler,
		},
		{
			MethodName: "DeleteEnterpriseStaff",
			Handler:    _StaffService_DeleteEnterpriseStaff_Handler,
		},
		{
			MethodName: "ListStaffEmailDefs",
			Handler:    _StaffService_ListStaffEmailDefs_Handler,
		},
		{
			MethodName: "SendInviteLink",
			Handler:    _StaffService_SendInviteLink_Handler,
		},
		{
			MethodName: "UnlinkStaff",
			Handler:    _StaffService_UnlinkStaff_Handler,
		},
		{
			MethodName: "GetStaffLoginTime",
			Handler:    _StaffService_GetStaffLoginTime_Handler,
		},
		{
			MethodName: "CheckStaffLoginTime",
			Handler:    _StaffService_CheckStaffLoginTime_Handler,
		},
		{
			MethodName: "GetStaffByPhoneNumber",
			Handler:    _StaffService_GetStaffByPhoneNumber_Handler,
		},
		{
			MethodName: "GetStaffsByRole",
			Handler:    _StaffService_GetStaffsByRole_Handler,
		},
		{
			MethodName: "GetBusinessStaffAvailabilityType",
			Handler:    _StaffService_GetBusinessStaffAvailabilityType_Handler,
		},
		{
			MethodName: "UpdateBusinessStaffAvailabilityType",
			Handler:    _StaffService_UpdateBusinessStaffAvailabilityType_Handler,
		},
		{
			MethodName: "GetStaffAvailability",
			Handler:    _StaffService_GetStaffAvailability_Handler,
		},
		{
			MethodName: "UpdateStaffAvailability",
			Handler:    _StaffService_UpdateStaffAvailability_Handler,
		},
		{
			MethodName: "UpdateStaffAvailabilityOverride",
			Handler:    _StaffService_UpdateStaffAvailabilityOverride_Handler,
		},
		{
			MethodName: "DeleteStaffAvailabilityOverride",
			Handler:    _StaffService_DeleteStaffAvailabilityOverride_Handler,
		},
		{
			MethodName: "GetStaffAvailabilityOverride",
			Handler:    _StaffService_GetStaffAvailabilityOverride_Handler,
		},
		{
			MethodName: "GetStaffCalenderView",
			Handler:    _StaffService_GetStaffCalenderView_Handler,
		},
		{
			MethodName: "InitStaffAvailability",
			Handler:    _StaffService_InitStaffAvailability_Handler,
		},
		{
			MethodName: "ListSlotFreeServices",
			Handler:    _StaffService_ListSlotFreeServices_Handler,
		},
		{
			MethodName: "UpdateSlotFreeServices",
			Handler:    _StaffService_UpdateSlotFreeServices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/organization/v1/staff_service.proto",
}
