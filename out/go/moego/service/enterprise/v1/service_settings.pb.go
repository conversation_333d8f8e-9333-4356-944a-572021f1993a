// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/enterprise/v1/service_settings.proto

package enterprisesvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create lodging type
type CreateLodgingTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// lodging
	LodgingTypeDef *v1.CreateLodgingTypeDef `protobuf:"bytes,2,opt,name=lodging_type_def,json=lodgingTypeDef,proto3" json:"lodging_type_def,omitempty"`
}

func (x *CreateLodgingTypeRequest) Reset() {
	*x = CreateLodgingTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLodgingTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLodgingTypeRequest) ProtoMessage() {}

func (x *CreateLodgingTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLodgingTypeRequest.ProtoReflect.Descriptor instead.
func (*CreateLodgingTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{0}
}

func (x *CreateLodgingTypeRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreateLodgingTypeRequest) GetLodgingTypeDef() *v1.CreateLodgingTypeDef {
	if x != nil {
		return x.LodgingTypeDef
	}
	return nil
}

// *
// Response body for create LodgingType
type CreateLodgingTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type
	LodgingType *v1.LodgingType `protobuf:"bytes,1,opt,name=lodging_type,json=lodgingType,proto3" json:"lodging_type,omitempty"`
}

func (x *CreateLodgingTypeResponse) Reset() {
	*x = CreateLodgingTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLodgingTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLodgingTypeResponse) ProtoMessage() {}

func (x *CreateLodgingTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLodgingTypeResponse.ProtoReflect.Descriptor instead.
func (*CreateLodgingTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{1}
}

func (x *CreateLodgingTypeResponse) GetLodgingType() *v1.LodgingType {
	if x != nil {
		return x.LodgingType
	}
	return nil
}

// *
// Request body for update LodgingType
type UpdateLodgingTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging type
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// update lodging type
	LodgingTypeDef *v1.UpdateLodgingTypeDef `protobuf:"bytes,2,opt,name=lodging_type_def,json=lodgingTypeDef,proto3" json:"lodging_type_def,omitempty"`
}

func (x *UpdateLodgingTypeRequest) Reset() {
	*x = UpdateLodgingTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLodgingTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLodgingTypeRequest) ProtoMessage() {}

func (x *UpdateLodgingTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLodgingTypeRequest.ProtoReflect.Descriptor instead.
func (*UpdateLodgingTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateLodgingTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLodgingTypeRequest) GetLodgingTypeDef() *v1.UpdateLodgingTypeDef {
	if x != nil {
		return x.LodgingTypeDef
	}
	return nil
}

// *
// Response body for update LodgingType
type UpdateLodgingTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type
	LodgingType *v1.LodgingType `protobuf:"bytes,1,opt,name=lodging_type,json=lodgingType,proto3" json:"lodging_type,omitempty"`
}

func (x *UpdateLodgingTypeResponse) Reset() {
	*x = UpdateLodgingTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLodgingTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLodgingTypeResponse) ProtoMessage() {}

func (x *UpdateLodgingTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLodgingTypeResponse.ProtoReflect.Descriptor instead.
func (*UpdateLodgingTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateLodgingTypeResponse) GetLodgingType() *v1.LodgingType {
	if x != nil {
		return x.LodgingType
	}
	return nil
}

// *
// Request body for delete LodgingType
type DeleteLodgingTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging type
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteLodgingTypeRequest) Reset() {
	*x = DeleteLodgingTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLodgingTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLodgingTypeRequest) ProtoMessage() {}

func (x *DeleteLodgingTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLodgingTypeRequest.ProtoReflect.Descriptor instead.
func (*DeleteLodgingTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteLodgingTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// *
// Response body for delete LodgingType
type DeleteLodgingTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteLodgingTypeResponse) Reset() {
	*x = DeleteLodgingTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLodgingTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLodgingTypeResponse) ProtoMessage() {}

func (x *DeleteLodgingTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLodgingTypeResponse.ProtoReflect.Descriptor instead.
func (*DeleteLodgingTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{5}
}

// The params for sort lodging types
type SortLodgingTypesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids of lodging type to sort
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortLodgingTypesRequest) Reset() {
	*x = SortLodgingTypesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortLodgingTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortLodgingTypesRequest) ProtoMessage() {}

func (x *SortLodgingTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortLodgingTypesRequest.ProtoReflect.Descriptor instead.
func (*SortLodgingTypesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{6}
}

func (x *SortLodgingTypesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// The result for sort lodging types
type SortLodgingTypesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortLodgingTypesResponse) Reset() {
	*x = SortLodgingTypesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortLodgingTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortLodgingTypesResponse) ProtoMessage() {}

func (x *SortLodgingTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortLodgingTypesResponse.ProtoReflect.Descriptor instead.
func (*SortLodgingTypesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{7}
}

// list lodging types request
type ListLodgingTypesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListLodgingTypesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListLodgingTypesRequest) Reset() {
	*x = ListLodgingTypesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLodgingTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLodgingTypesRequest) ProtoMessage() {}

func (x *ListLodgingTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLodgingTypesRequest.ProtoReflect.Descriptor instead.
func (*ListLodgingTypesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{8}
}

func (x *ListLodgingTypesRequest) GetFilter() *ListLodgingTypesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list lodging types response
type ListLodgingTypesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging types
	LodgingTypes []*v1.LodgingType `protobuf:"bytes,1,rep,name=lodging_types,json=lodgingTypes,proto3" json:"lodging_types,omitempty"`
}

func (x *ListLodgingTypesResponse) Reset() {
	*x = ListLodgingTypesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLodgingTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLodgingTypesResponse) ProtoMessage() {}

func (x *ListLodgingTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLodgingTypesResponse.ProtoReflect.Descriptor instead.
func (*ListLodgingTypesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{9}
}

func (x *ListLodgingTypesResponse) GetLodgingTypes() []*v1.LodgingType {
	if x != nil {
		return x.LodgingTypes
	}
	return nil
}

// PushLodgingTypesRequest
type PushLodgingTypesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// ids
	LodgingTypeIds []int64 `protobuf:"varint,2,rep,packed,name=lodging_type_ids,json=lodgingTypeIds,proto3" json:"lodging_type_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,3,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushLodgingTypesRequest) Reset() {
	*x = PushLodgingTypesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushLodgingTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushLodgingTypesRequest) ProtoMessage() {}

func (x *PushLodgingTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushLodgingTypesRequest.ProtoReflect.Descriptor instead.
func (*PushLodgingTypesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{10}
}

func (x *PushLodgingTypesRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *PushLodgingTypesRequest) GetLodgingTypeIds() []int64 {
	if x != nil {
		return x.LodgingTypeIds
	}
	return nil
}

func (x *PushLodgingTypesRequest) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushLodgingTypesResponse
type PushLodgingTypesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushLodgingTypesResponse) Reset() {
	*x = PushLodgingTypesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushLodgingTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushLodgingTypesResponse) ProtoMessage() {}

func (x *PushLodgingTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushLodgingTypesResponse.ProtoReflect.Descriptor instead.
func (*PushLodgingTypesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{11}
}

func (x *PushLodgingTypesResponse) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushLodgingTypesResponse) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// init lodging types request
type InitLodgingTypesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
}

func (x *InitLodgingTypesRequest) Reset() {
	*x = InitLodgingTypesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitLodgingTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitLodgingTypesRequest) ProtoMessage() {}

func (x *InitLodgingTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitLodgingTypesRequest.ProtoReflect.Descriptor instead.
func (*InitLodgingTypesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{12}
}

func (x *InitLodgingTypesRequest) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

// init lodging types response
type InitLodgingTypesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InitLodgingTypesResponse) Reset() {
	*x = InitLodgingTypesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitLodgingTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitLodgingTypesResponse) ProtoMessage() {}

func (x *InitLodgingTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitLodgingTypesResponse.ProtoReflect.Descriptor instead.
func (*InitLodgingTypesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{13}
}

// filter
type ListLodgingTypesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,2,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
}

func (x *ListLodgingTypesRequest_Filter) Reset() {
	*x = ListLodgingTypesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLodgingTypesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLodgingTypesRequest_Filter) ProtoMessage() {}

func (x *ListLodgingTypesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_service_settings_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLodgingTypesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListLodgingTypesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListLodgingTypesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListLodgingTypesRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

var File_moego_service_enterprise_v1_service_settings_proto protoreflect.FileDescriptor

var file_moego_service_enterprise_v1_service_settings_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4,
	0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0d, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x10, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x44, 0x65, 0x66, 0x22, 0x67, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8f,
	0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x5a, 0x0a, 0x10, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x66,
	0x52, 0x0e, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x66,
	0x22, 0x67, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a,
	0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0x33, 0x0a, 0x18, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1b,
	0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3d, 0x0a, 0x17, 0x53,
	0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x1a, 0x0a, 0x18, 0x53, 0x6f,
	0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x53, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x41, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x22, 0x68, 0x0a, 0x18, 0x4c, 0x69,
	0x73, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x22, 0xac, 0x01, 0x0a, 0x17, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0e, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x73, 0x12,
	0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x22, 0x78, 0x0a, 0x18, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12,
	0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x40, 0x0a,
	0x17, 0x49, 0x6e, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x22,
	0x1a, 0x0a, 0x18, 0x49, 0x6e, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x89, 0x01, 0x0a, 0x23,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_enterprise_v1_service_settings_proto_rawDescOnce sync.Once
	file_moego_service_enterprise_v1_service_settings_proto_rawDescData = file_moego_service_enterprise_v1_service_settings_proto_rawDesc
)

func file_moego_service_enterprise_v1_service_settings_proto_rawDescGZIP() []byte {
	file_moego_service_enterprise_v1_service_settings_proto_rawDescOnce.Do(func() {
		file_moego_service_enterprise_v1_service_settings_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_enterprise_v1_service_settings_proto_rawDescData)
	})
	return file_moego_service_enterprise_v1_service_settings_proto_rawDescData
}

var file_moego_service_enterprise_v1_service_settings_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_moego_service_enterprise_v1_service_settings_proto_goTypes = []interface{}{
	(*CreateLodgingTypeRequest)(nil),       // 0: moego.service.enterprise.v1.CreateLodgingTypeRequest
	(*CreateLodgingTypeResponse)(nil),      // 1: moego.service.enterprise.v1.CreateLodgingTypeResponse
	(*UpdateLodgingTypeRequest)(nil),       // 2: moego.service.enterprise.v1.UpdateLodgingTypeRequest
	(*UpdateLodgingTypeResponse)(nil),      // 3: moego.service.enterprise.v1.UpdateLodgingTypeResponse
	(*DeleteLodgingTypeRequest)(nil),       // 4: moego.service.enterprise.v1.DeleteLodgingTypeRequest
	(*DeleteLodgingTypeResponse)(nil),      // 5: moego.service.enterprise.v1.DeleteLodgingTypeResponse
	(*SortLodgingTypesRequest)(nil),        // 6: moego.service.enterprise.v1.SortLodgingTypesRequest
	(*SortLodgingTypesResponse)(nil),       // 7: moego.service.enterprise.v1.SortLodgingTypesResponse
	(*ListLodgingTypesRequest)(nil),        // 8: moego.service.enterprise.v1.ListLodgingTypesRequest
	(*ListLodgingTypesResponse)(nil),       // 9: moego.service.enterprise.v1.ListLodgingTypesResponse
	(*PushLodgingTypesRequest)(nil),        // 10: moego.service.enterprise.v1.PushLodgingTypesRequest
	(*PushLodgingTypesResponse)(nil),       // 11: moego.service.enterprise.v1.PushLodgingTypesResponse
	(*InitLodgingTypesRequest)(nil),        // 12: moego.service.enterprise.v1.InitLodgingTypesRequest
	(*InitLodgingTypesResponse)(nil),       // 13: moego.service.enterprise.v1.InitLodgingTypesResponse
	(*ListLodgingTypesRequest_Filter)(nil), // 14: moego.service.enterprise.v1.ListLodgingTypesRequest.Filter
	(*v1.CreateLodgingTypeDef)(nil),        // 15: moego.models.enterprise.v1.CreateLodgingTypeDef
	(*v1.LodgingType)(nil),                 // 16: moego.models.enterprise.v1.LodgingType
	(*v1.UpdateLodgingTypeDef)(nil),        // 17: moego.models.enterprise.v1.UpdateLodgingTypeDef
	(*v1.TenantObject)(nil),                // 18: moego.models.enterprise.v1.TenantObject
}
var file_moego_service_enterprise_v1_service_settings_proto_depIdxs = []int32{
	15, // 0: moego.service.enterprise.v1.CreateLodgingTypeRequest.lodging_type_def:type_name -> moego.models.enterprise.v1.CreateLodgingTypeDef
	16, // 1: moego.service.enterprise.v1.CreateLodgingTypeResponse.lodging_type:type_name -> moego.models.enterprise.v1.LodgingType
	17, // 2: moego.service.enterprise.v1.UpdateLodgingTypeRequest.lodging_type_def:type_name -> moego.models.enterprise.v1.UpdateLodgingTypeDef
	16, // 3: moego.service.enterprise.v1.UpdateLodgingTypeResponse.lodging_type:type_name -> moego.models.enterprise.v1.LodgingType
	14, // 4: moego.service.enterprise.v1.ListLodgingTypesRequest.filter:type_name -> moego.service.enterprise.v1.ListLodgingTypesRequest.Filter
	16, // 5: moego.service.enterprise.v1.ListLodgingTypesResponse.lodging_types:type_name -> moego.models.enterprise.v1.LodgingType
	18, // 6: moego.service.enterprise.v1.PushLodgingTypesRequest.targets:type_name -> moego.models.enterprise.v1.TenantObject
	7,  // [7:7] is the sub-list for method output_type
	7,  // [7:7] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_service_enterprise_v1_service_settings_proto_init() }
func file_moego_service_enterprise_v1_service_settings_proto_init() {
	if File_moego_service_enterprise_v1_service_settings_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLodgingTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLodgingTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLodgingTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLodgingTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLodgingTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLodgingTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortLodgingTypesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortLodgingTypesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLodgingTypesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLodgingTypesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushLodgingTypesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushLodgingTypesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitLodgingTypesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitLodgingTypesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_service_settings_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLodgingTypesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_enterprise_v1_service_settings_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_service_enterprise_v1_service_settings_proto_goTypes,
		DependencyIndexes: file_moego_service_enterprise_v1_service_settings_proto_depIdxs,
		MessageInfos:      file_moego_service_enterprise_v1_service_settings_proto_msgTypes,
	}.Build()
	File_moego_service_enterprise_v1_service_settings_proto = out.File
	file_moego_service_enterprise_v1_service_settings_proto_rawDesc = nil
	file_moego_service_enterprise_v1_service_settings_proto_goTypes = nil
	file_moego_service_enterprise_v1_service_settings_proto_depIdxs = nil
}
