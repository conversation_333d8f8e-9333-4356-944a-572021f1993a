// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/enterprise/v1/price_book_service.proto

package enterprisesvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// field
type ListServiceChangeHistoriesRequest_OrderBy_Field int32

const (
	// 未指定
	ListServiceChangeHistoriesRequest_OrderBy_FIELD_UNSPECIFIED ListServiceChangeHistoriesRequest_OrderBy_Field = 0
	// 创建时间
	ListServiceChangeHistoriesRequest_OrderBy_UPDATED_AT ListServiceChangeHistoriesRequest_OrderBy_Field = 1
)

// Enum value maps for ListServiceChangeHistoriesRequest_OrderBy_Field.
var (
	ListServiceChangeHistoriesRequest_OrderBy_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "UPDATED_AT",
	}
	ListServiceChangeHistoriesRequest_OrderBy_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"UPDATED_AT":        1,
	}
)

func (x ListServiceChangeHistoriesRequest_OrderBy_Field) Enum() *ListServiceChangeHistoriesRequest_OrderBy_Field {
	p := new(ListServiceChangeHistoriesRequest_OrderBy_Field)
	*p = x
	return p
}

func (x ListServiceChangeHistoriesRequest_OrderBy_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListServiceChangeHistoriesRequest_OrderBy_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_enterprise_v1_price_book_service_proto_enumTypes[0].Descriptor()
}

func (ListServiceChangeHistoriesRequest_OrderBy_Field) Type() protoreflect.EnumType {
	return &file_moego_service_enterprise_v1_price_book_service_proto_enumTypes[0]
}

func (x ListServiceChangeHistoriesRequest_OrderBy_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListServiceChangeHistoriesRequest_OrderBy_Field.Descriptor instead.
func (ListServiceChangeHistoriesRequest_OrderBy_Field) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{34, 1, 0}
}

// ListPriceBooksRequest
type ListPriceBooksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id, use filter to provide enterprise id
	//
	// Deprecated: Do not use.
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// filter
	Filter *ListPriceBooksRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPriceBooksRequest) Reset() {
	*x = ListPriceBooksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPriceBooksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPriceBooksRequest) ProtoMessage() {}

func (x *ListPriceBooksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPriceBooksRequest.ProtoReflect.Descriptor instead.
func (*ListPriceBooksRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{0}
}

// Deprecated: Do not use.
func (x *ListPriceBooksRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ListPriceBooksRequest) GetFilter() *ListPriceBooksRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListPriceBooksResponse
type ListPriceBooksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price books
	PriceBooks []*v1.PriceBook `protobuf:"bytes,1,rep,name=price_books,json=priceBooks,proto3" json:"price_books,omitempty"`
}

func (x *ListPriceBooksResponse) Reset() {
	*x = ListPriceBooksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPriceBooksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPriceBooksResponse) ProtoMessage() {}

func (x *ListPriceBooksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPriceBooksResponse.ProtoReflect.Descriptor instead.
func (*ListPriceBooksResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListPriceBooksResponse) GetPriceBooks() []*v1.PriceBook {
	if x != nil {
		return x.PriceBooks
	}
	return nil
}

// CreatePriceBookRequest
type CreatePriceBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CreatePriceBookRequest) Reset() {
	*x = CreatePriceBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePriceBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePriceBookRequest) ProtoMessage() {}

func (x *CreatePriceBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePriceBookRequest.ProtoReflect.Descriptor instead.
func (*CreatePriceBookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePriceBookRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreatePriceBookRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// CreatePriceBookResponse
type CreatePriceBookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book
	PriceBook *v1.PriceBook `protobuf:"bytes,1,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
}

func (x *CreatePriceBookResponse) Reset() {
	*x = CreatePriceBookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePriceBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePriceBookResponse) ProtoMessage() {}

func (x *CreatePriceBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePriceBookResponse.ProtoReflect.Descriptor instead.
func (*CreatePriceBookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePriceBookResponse) GetPriceBook() *v1.PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

// InitPriceBookRequest
type InitPriceBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *InitPriceBookRequest) Reset() {
	*x = InitPriceBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitPriceBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitPriceBookRequest) ProtoMessage() {}

func (x *InitPriceBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitPriceBookRequest.ProtoReflect.Descriptor instead.
func (*InitPriceBookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{4}
}

func (x *InitPriceBookRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// InitPriceBookResponse
type InitPriceBookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book
	PriceBook *v1.PriceBook `protobuf:"bytes,1,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
}

func (x *InitPriceBookResponse) Reset() {
	*x = InitPriceBookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitPriceBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitPriceBookResponse) ProtoMessage() {}

func (x *InitPriceBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitPriceBookResponse.ProtoReflect.Descriptor instead.
func (*InitPriceBookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{5}
}

func (x *InitPriceBookResponse) GetPriceBook() *v1.PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

// UpdatePriceBookRequest
type UpdatePriceBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
}

func (x *UpdatePriceBookRequest) Reset() {
	*x = UpdatePriceBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePriceBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePriceBookRequest) ProtoMessage() {}

func (x *UpdatePriceBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePriceBookRequest.ProtoReflect.Descriptor instead.
func (*UpdatePriceBookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdatePriceBookRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePriceBookRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

// UpdatePriceBookResponse
type UpdatePriceBookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book
	PriceBook *v1.PriceBook `protobuf:"bytes,1,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
}

func (x *UpdatePriceBookResponse) Reset() {
	*x = UpdatePriceBookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePriceBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePriceBookResponse) ProtoMessage() {}

func (x *UpdatePriceBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePriceBookResponse.ProtoReflect.Descriptor instead.
func (*UpdatePriceBookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdatePriceBookResponse) GetPriceBook() *v1.PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

// DeletePriceBookRequest
type DeletePriceBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePriceBookRequest) Reset() {
	*x = DeletePriceBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePriceBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePriceBookRequest) ProtoMessage() {}

func (x *DeletePriceBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePriceBookRequest.ProtoReflect.Descriptor instead.
func (*DeletePriceBookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeletePriceBookRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeletePriceBookResponse
type DeletePriceBookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePriceBookResponse) Reset() {
	*x = DeletePriceBookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePriceBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePriceBookResponse) ProtoMessage() {}

func (x *DeletePriceBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePriceBookResponse.ProtoReflect.Descriptor instead.
func (*DeletePriceBookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{9}
}

// DuplicatePriceBookRequest
type DuplicatePriceBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// copy to target enterprise id, if not set, will copy to the same enterprise of the price book
	EnterpriseId *int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
	// name of the duplicated price book, if not set, will use the original price book name
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
}

func (x *DuplicatePriceBookRequest) Reset() {
	*x = DuplicatePriceBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicatePriceBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicatePriceBookRequest) ProtoMessage() {}

func (x *DuplicatePriceBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicatePriceBookRequest.ProtoReflect.Descriptor instead.
func (*DuplicatePriceBookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{10}
}

func (x *DuplicatePriceBookRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DuplicatePriceBookRequest) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

func (x *DuplicatePriceBookRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

// DuplicatePriceBookResponse
type DuplicatePriceBookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book
	PriceBook *v1.PriceBook `protobuf:"bytes,1,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
}

func (x *DuplicatePriceBookResponse) Reset() {
	*x = DuplicatePriceBookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicatePriceBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicatePriceBookResponse) ProtoMessage() {}

func (x *DuplicatePriceBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicatePriceBookResponse.ProtoReflect.Descriptor instead.
func (*DuplicatePriceBookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{11}
}

func (x *DuplicatePriceBookResponse) GetPriceBook() *v1.PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

// MigratePriceBookRequest
type MigratePriceBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id, if not set, will apply to all enterprises
	EnterpriseId *int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
}

func (x *MigratePriceBookRequest) Reset() {
	*x = MigratePriceBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigratePriceBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigratePriceBookRequest) ProtoMessage() {}

func (x *MigratePriceBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigratePriceBookRequest.ProtoReflect.Descriptor instead.
func (*MigratePriceBookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{12}
}

func (x *MigratePriceBookRequest) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

// MigratePriceBookResponse
type MigratePriceBookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MigratePriceBookResponse) Reset() {
	*x = MigratePriceBookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigratePriceBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigratePriceBookResponse) ProtoMessage() {}

func (x *MigratePriceBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigratePriceBookResponse.ProtoReflect.Descriptor instead.
func (*MigratePriceBookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{13}
}

// SaveServiceCategoriesRequest
type SaveServiceCategoriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// categories
	Categories []*v1.ServiceCategory `protobuf:"bytes,2,rep,name=categories,proto3" json:"categories,omitempty"`
	// service type
	ServiceType v11.ServiceType `protobuf:"varint,3,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// service item type
	ServiceItemType v11.ServiceItemType `protobuf:"varint,4,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *SaveServiceCategoriesRequest) Reset() {
	*x = SaveServiceCategoriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveServiceCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveServiceCategoriesRequest) ProtoMessage() {}

func (x *SaveServiceCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveServiceCategoriesRequest.ProtoReflect.Descriptor instead.
func (*SaveServiceCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{14}
}

func (x *SaveServiceCategoriesRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *SaveServiceCategoriesRequest) GetCategories() []*v1.ServiceCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *SaveServiceCategoriesRequest) GetServiceType() v11.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v11.ServiceType(0)
}

func (x *SaveServiceCategoriesRequest) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

// SaveServiceCategoriesResponse
type SaveServiceCategoriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveServiceCategoriesResponse) Reset() {
	*x = SaveServiceCategoriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveServiceCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveServiceCategoriesResponse) ProtoMessage() {}

func (x *SaveServiceCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveServiceCategoriesResponse.ProtoReflect.Descriptor instead.
func (*SaveServiceCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{15}
}

// ListServiceCategoriesRequest
type ListServiceCategoriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *ListServiceCategoriesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListServiceCategoriesRequest) Reset() {
	*x = ListServiceCategoriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceCategoriesRequest) ProtoMessage() {}

func (x *ListServiceCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceCategoriesRequest.ProtoReflect.Descriptor instead.
func (*ListServiceCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{16}
}

func (x *ListServiceCategoriesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceCategoriesRequest) GetFilter() *ListServiceCategoriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListServiceCategoriesResponse
type ListServiceCategoriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service categories
	ServiceCategories []*v1.ServiceCategory `protobuf:"bytes,1,rep,name=service_categories,json=serviceCategories,proto3" json:"service_categories,omitempty"`
}

func (x *ListServiceCategoriesResponse) Reset() {
	*x = ListServiceCategoriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceCategoriesResponse) ProtoMessage() {}

func (x *ListServiceCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceCategoriesResponse.ProtoReflect.Descriptor instead.
func (*ListServiceCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListServiceCategoriesResponse) GetServiceCategories() []*v1.ServiceCategory {
	if x != nil {
		return x.ServiceCategories
	}
	return nil
}

// ListPetBreedsRequest
type ListPetBreedsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *ListPetBreedsRequest) Reset() {
	*x = ListPetBreedsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBreedsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBreedsRequest) ProtoMessage() {}

func (x *ListPetBreedsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBreedsRequest.ProtoReflect.Descriptor instead.
func (*ListPetBreedsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{18}
}

func (x *ListPetBreedsRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// ListPetBreedsResponse
type ListPetBreedsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet breeds
	PetBreeds []*v1.PetBreed `protobuf:"bytes,1,rep,name=pet_breeds,json=petBreeds,proto3" json:"pet_breeds,omitempty"`
}

func (x *ListPetBreedsResponse) Reset() {
	*x = ListPetBreedsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBreedsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBreedsResponse) ProtoMessage() {}

func (x *ListPetBreedsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBreedsResponse.ProtoReflect.Descriptor instead.
func (*ListPetBreedsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{19}
}

func (x *ListPetBreedsResponse) GetPetBreeds() []*v1.PetBreed {
	if x != nil {
		return x.PetBreeds
	}
	return nil
}

// ListPetTypesRequest
type ListPetTypesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *ListPetTypesRequest) Reset() {
	*x = ListPetTypesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetTypesRequest) ProtoMessage() {}

func (x *ListPetTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetTypesRequest.ProtoReflect.Descriptor instead.
func (*ListPetTypesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{20}
}

func (x *ListPetTypesRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// ListPetTypesResponse
type ListPetTypesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet breeds
	PetTypes []*v1.PetType `protobuf:"bytes,1,rep,name=pet_types,json=petTypes,proto3" json:"pet_types,omitempty"`
}

func (x *ListPetTypesResponse) Reset() {
	*x = ListPetTypesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetTypesResponse) ProtoMessage() {}

func (x *ListPetTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetTypesResponse.ProtoReflect.Descriptor instead.
func (*ListPetTypesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{21}
}

func (x *ListPetTypesResponse) GetPetTypes() []*v1.PetType {
	if x != nil {
		return x.PetTypes
	}
	return nil
}

// CreateServiceRequest
type CreateServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// price book
	PriceBook *v1.PriceBook `protobuf:"bytes,2,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// service item type
	ServiceItemType v11.ServiceItemType `protobuf:"varint,4,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// category
	Category *v1.ServiceCategory `protobuf:"bytes,5,opt,name=category,proto3" json:"category,omitempty"`
	// description
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,7,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// color
	Color string `protobuf:"bytes,8,opt,name=color,proto3" json:"color,omitempty"`
	// sort
	Sort int64 `protobuf:"varint,9,opt,name=sort,proto3" json:"sort,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,10,opt,name=price,proto3" json:"price,omitempty"`
	// service price unit
	ServicePriceUnit v11.ServicePriceUnit `protobuf:"varint,11,opt,name=service_price_unit,json=servicePriceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"service_price_unit,omitempty"`
	// 万分位税率
	TaxRate int64 `protobuf:"varint,12,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,13,opt,name=duration,proto3" json:"duration,omitempty"`
	// max duration
	MaxDuration *durationpb.Duration `protobuf:"bytes,14,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// limitation
	Limitation *v1.Service_Limitation `protobuf:"bytes,15,opt,name=limitation,proto3" json:"limitation,omitempty"`
	// service type
	ServiceType v11.ServiceType `protobuf:"varint,16,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// images
	Images []string `protobuf:"bytes,17,rep,name=images,proto3" json:"images,omitempty"`
	// require_dedicated_staff, only for add-on
	RequireDedicatedStaff bool `protobuf:"varint,18,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// auto rule
	AutoRule *v1.Service_AutoRule `protobuf:"bytes,19,opt,name=auto_rule,json=autoRule,proto3" json:"auto_rule,omitempty"`
}

func (x *CreateServiceRequest) Reset() {
	*x = CreateServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceRequest) ProtoMessage() {}

func (x *CreateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{22}
}

func (x *CreateServiceRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreateServiceRequest) GetPriceBook() *v1.PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

func (x *CreateServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateServiceRequest) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

func (x *CreateServiceRequest) GetCategory() *v1.ServiceCategory {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *CreateServiceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateServiceRequest) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *CreateServiceRequest) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *CreateServiceRequest) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *CreateServiceRequest) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *CreateServiceRequest) GetServicePriceUnit() v11.ServicePriceUnit {
	if x != nil {
		return x.ServicePriceUnit
	}
	return v11.ServicePriceUnit(0)
}

func (x *CreateServiceRequest) GetTaxRate() int64 {
	if x != nil {
		return x.TaxRate
	}
	return 0
}

func (x *CreateServiceRequest) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *CreateServiceRequest) GetMaxDuration() *durationpb.Duration {
	if x != nil {
		return x.MaxDuration
	}
	return nil
}

func (x *CreateServiceRequest) GetLimitation() *v1.Service_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

func (x *CreateServiceRequest) GetServiceType() v11.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v11.ServiceType(0)
}

func (x *CreateServiceRequest) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *CreateServiceRequest) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *CreateServiceRequest) GetAutoRule() *v1.Service_AutoRule {
	if x != nil {
		return x.AutoRule
	}
	return nil
}

// CreateServiceResponse
type CreateServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service
	Service *v1.Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *CreateServiceResponse) Reset() {
	*x = CreateServiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceResponse) ProtoMessage() {}

func (x *CreateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{23}
}

func (x *CreateServiceResponse) GetService() *v1.Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// GetServiceRequest
type GetServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetServiceRequest) Reset() {
	*x = GetServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceRequest) ProtoMessage() {}

func (x *GetServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceRequest.ProtoReflect.Descriptor instead.
func (*GetServiceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetServiceRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetServiceResponse
type GetServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service
	Service *v1.Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *GetServiceResponse) Reset() {
	*x = GetServiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceResponse) ProtoMessage() {}

func (x *GetServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceResponse.ProtoReflect.Descriptor instead.
func (*GetServiceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetServiceResponse) GetService() *v1.Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// ListServicesRequest
type ListServicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *ListServicesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListServicesRequest) Reset() {
	*x = ListServicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest) ProtoMessage() {}

func (x *ListServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest.ProtoReflect.Descriptor instead.
func (*ListServicesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{26}
}

func (x *ListServicesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServicesRequest) GetFilter() *ListServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListServicesResponse
type ListServicesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// services
	Services []*v1.Service `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *ListServicesResponse) Reset() {
	*x = ListServicesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesResponse) ProtoMessage() {}

func (x *ListServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesResponse.ProtoReflect.Descriptor instead.
func (*ListServicesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{27}
}

func (x *ListServicesResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServicesResponse) GetServices() []*v1.Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// UpdateServiceRequest
type UpdateServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// category id
	ServiceCategory *v1.ServiceCategory `protobuf:"bytes,3,opt,name=service_category,json=serviceCategory,proto3" json:"service_category,omitempty"`
	// description
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,5,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// color
	Color *string `protobuf:"bytes,6,opt,name=color,proto3,oneof" json:"color,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,7,opt,name=price,proto3" json:"price,omitempty"`
	// service price unit
	ServicePriceUnit v11.ServicePriceUnit `protobuf:"varint,8,opt,name=service_price_unit,json=servicePriceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"service_price_unit,omitempty"`
	// 万分位税率
	TaxRate *int64 `protobuf:"varint,9,opt,name=tax_rate,json=taxRate,proto3,oneof" json:"tax_rate,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,10,opt,name=duration,proto3" json:"duration,omitempty"`
	// max duration
	MaxDuration *durationpb.Duration `protobuf:"bytes,11,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// limitation
	Limitation *v1.Service_Limitation `protobuf:"bytes,12,opt,name=limitation,proto3" json:"limitation,omitempty"`
	// images
	Images []string `protobuf:"bytes,13,rep,name=images,proto3" json:"images,omitempty"`
	// require_dedicated_staff, only for add-on
	RequireDedicatedStaff *bool `protobuf:"varint,14,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3,oneof" json:"require_dedicated_staff,omitempty"`
	// auto rule
	AutoRule *v1.Service_AutoRule `protobuf:"bytes,15,opt,name=auto_rule,json=autoRule,proto3" json:"auto_rule,omitempty"`
}

func (x *UpdateServiceRequest) Reset() {
	*x = UpdateServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceRequest) ProtoMessage() {}

func (x *UpdateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateServiceRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateServiceRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateServiceRequest) GetServiceCategory() *v1.ServiceCategory {
	if x != nil {
		return x.ServiceCategory
	}
	return nil
}

func (x *UpdateServiceRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateServiceRequest) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *UpdateServiceRequest) GetColor() string {
	if x != nil && x.Color != nil {
		return *x.Color
	}
	return ""
}

func (x *UpdateServiceRequest) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *UpdateServiceRequest) GetServicePriceUnit() v11.ServicePriceUnit {
	if x != nil {
		return x.ServicePriceUnit
	}
	return v11.ServicePriceUnit(0)
}

func (x *UpdateServiceRequest) GetTaxRate() int64 {
	if x != nil && x.TaxRate != nil {
		return *x.TaxRate
	}
	return 0
}

func (x *UpdateServiceRequest) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *UpdateServiceRequest) GetMaxDuration() *durationpb.Duration {
	if x != nil {
		return x.MaxDuration
	}
	return nil
}

func (x *UpdateServiceRequest) GetLimitation() *v1.Service_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

func (x *UpdateServiceRequest) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *UpdateServiceRequest) GetRequireDedicatedStaff() bool {
	if x != nil && x.RequireDedicatedStaff != nil {
		return *x.RequireDedicatedStaff
	}
	return false
}

func (x *UpdateServiceRequest) GetAutoRule() *v1.Service_AutoRule {
	if x != nil {
		return x.AutoRule
	}
	return nil
}

// UpdateServiceResponse
type UpdateServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service
	Service *v1.Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *UpdateServiceResponse) Reset() {
	*x = UpdateServiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceResponse) ProtoMessage() {}

func (x *UpdateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateServiceResponse) GetService() *v1.Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// DeleteServiceRequest
type DeleteServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteServiceRequest) Reset() {
	*x = DeleteServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceRequest) ProtoMessage() {}

func (x *DeleteServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{30}
}

func (x *DeleteServiceRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteServiceResponse
type DeleteServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteServiceResponse) Reset() {
	*x = DeleteServiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceResponse) ProtoMessage() {}

func (x *DeleteServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{31}
}

// SortServicesRequest
type SortServicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sorted services
	ServiceCategorySorts []*SortServicesRequest_ServiceCategorySort `protobuf:"bytes,1,rep,name=service_category_sorts,json=serviceCategorySorts,proto3" json:"service_category_sorts,omitempty"`
}

func (x *SortServicesRequest) Reset() {
	*x = SortServicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortServicesRequest) ProtoMessage() {}

func (x *SortServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortServicesRequest.ProtoReflect.Descriptor instead.
func (*SortServicesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{32}
}

func (x *SortServicesRequest) GetServiceCategorySorts() []*SortServicesRequest_ServiceCategorySort {
	if x != nil {
		return x.ServiceCategorySorts
	}
	return nil
}

// SortServicesResponse
type SortServicesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortServicesResponse) Reset() {
	*x = SortServicesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortServicesResponse) ProtoMessage() {}

func (x *SortServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortServicesResponse.ProtoReflect.Descriptor instead.
func (*SortServicesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{33}
}

// ListServiceChangeHistoriesRequest
type ListServiceChangeHistoriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *ListServiceChangeHistoriesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// order by
	//
	// Deprecated: Do not use.
	OrderBy *ListServiceChangeHistoriesRequest_OrderBy `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// template push change order by
	OrderBys []*v1.TemplatePushChangeHistoryOrderBy `protobuf:"bytes,4,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
}

func (x *ListServiceChangeHistoriesRequest) Reset() {
	*x = ListServiceChangeHistoriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangeHistoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangeHistoriesRequest) ProtoMessage() {}

func (x *ListServiceChangeHistoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangeHistoriesRequest.ProtoReflect.Descriptor instead.
func (*ListServiceChangeHistoriesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{34}
}

func (x *ListServiceChangeHistoriesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceChangeHistoriesRequest) GetFilter() *ListServiceChangeHistoriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// Deprecated: Do not use.
func (x *ListServiceChangeHistoriesRequest) GetOrderBy() *ListServiceChangeHistoriesRequest_OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ListServiceChangeHistoriesRequest) GetOrderBys() []*v1.TemplatePushChangeHistoryOrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

// ListServiceChangeHistoriesResponse
type ListServiceChangeHistoriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// service change histories
	ServiceChangeHistories []*v1.ServiceChangeHistory `protobuf:"bytes,2,rep,name=service_change_histories,json=serviceChangeHistories,proto3" json:"service_change_histories,omitempty"`
}

func (x *ListServiceChangeHistoriesResponse) Reset() {
	*x = ListServiceChangeHistoriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangeHistoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangeHistoriesResponse) ProtoMessage() {}

func (x *ListServiceChangeHistoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangeHistoriesResponse.ProtoReflect.Descriptor instead.
func (*ListServiceChangeHistoriesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{35}
}

func (x *ListServiceChangeHistoriesResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceChangeHistoriesResponse) GetServiceChangeHistories() []*v1.ServiceChangeHistory {
	if x != nil {
		return x.ServiceChangeHistories
	}
	return nil
}

// ListServiceChangesRequest
type ListServiceChangesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *ListServiceChangesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListServiceChangesRequest) Reset() {
	*x = ListServiceChangesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangesRequest) ProtoMessage() {}

func (x *ListServiceChangesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangesRequest.ProtoReflect.Descriptor instead.
func (*ListServiceChangesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{36}
}

func (x *ListServiceChangesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceChangesRequest) GetFilter() *ListServiceChangesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListServiceChangesResponse
type ListServiceChangesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// service changes
	ServiceChanges []*v1.ServiceChange `protobuf:"bytes,2,rep,name=service_changes,json=serviceChanges,proto3" json:"service_changes,omitempty"`
}

func (x *ListServiceChangesResponse) Reset() {
	*x = ListServiceChangesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangesResponse) ProtoMessage() {}

func (x *ListServiceChangesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangesResponse.ProtoReflect.Descriptor instead.
func (*ListServiceChangesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{37}
}

func (x *ListServiceChangesResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceChangesResponse) GetServiceChanges() []*v1.ServiceChange {
	if x != nil {
		return x.ServiceChanges
	}
	return nil
}

// PushServiceChangesRequest
type PushServiceChangesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,3,rep,name=targets,proto3" json:"targets,omitempty"`
	// effective date
	EffectiveDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
	// apply to booked services
	ApplyToBookedServices bool `protobuf:"varint,5,opt,name=apply_to_booked_services,json=applyToBookedServices,proto3" json:"apply_to_booked_services,omitempty"`
}

func (x *PushServiceChangesRequest) Reset() {
	*x = PushServiceChangesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushServiceChangesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushServiceChangesRequest) ProtoMessage() {}

func (x *PushServiceChangesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushServiceChangesRequest.ProtoReflect.Descriptor instead.
func (*PushServiceChangesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{38}
}

func (x *PushServiceChangesRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *PushServiceChangesRequest) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *PushServiceChangesRequest) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *PushServiceChangesRequest) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

func (x *PushServiceChangesRequest) GetApplyToBookedServices() bool {
	if x != nil {
		return x.ApplyToBookedServices
	}
	return false
}

// PushServiceChangesResponse
type PushServiceChangesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushServiceChangesResponse) Reset() {
	*x = PushServiceChangesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushServiceChangesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushServiceChangesResponse) ProtoMessage() {}

func (x *PushServiceChangesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushServiceChangesResponse.ProtoReflect.Descriptor instead.
func (*PushServiceChangesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{39}
}

func (x *PushServiceChangesResponse) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushServiceChangesResponse) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// CreateEvaluationRequest
type CreateEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise_id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// evaluation
	EvaluationDef *v1.CreateEvaluationDef `protobuf:"bytes,2,opt,name=evaluation_def,json=evaluationDef,proto3" json:"evaluation_def,omitempty"`
}

func (x *CreateEvaluationRequest) Reset() {
	*x = CreateEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationRequest) ProtoMessage() {}

func (x *CreateEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationRequest.ProtoReflect.Descriptor instead.
func (*CreateEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{40}
}

func (x *CreateEvaluationRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreateEvaluationRequest) GetEvaluationDef() *v1.CreateEvaluationDef {
	if x != nil {
		return x.EvaluationDef
	}
	return nil
}

// CreateEvaluationResponse
type CreateEvaluationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation
	Evaluation *v1.Evaluation `protobuf:"bytes,1,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
}

func (x *CreateEvaluationResponse) Reset() {
	*x = CreateEvaluationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationResponse) ProtoMessage() {}

func (x *CreateEvaluationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationResponse.ProtoReflect.Descriptor instead.
func (*CreateEvaluationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{41}
}

func (x *CreateEvaluationResponse) GetEvaluation() *v1.Evaluation {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

// ListEvaluationsRequest
type ListEvaluationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListEvaluationsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListEvaluationsRequest) Reset() {
	*x = ListEvaluationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationsRequest) ProtoMessage() {}

func (x *ListEvaluationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationsRequest.ProtoReflect.Descriptor instead.
func (*ListEvaluationsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{42}
}

func (x *ListEvaluationsRequest) GetFilter() *ListEvaluationsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListEvaluationsResponse
type ListEvaluationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluations
	Evaluations []*v1.Evaluation `protobuf:"bytes,1,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *ListEvaluationsResponse) Reset() {
	*x = ListEvaluationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationsResponse) ProtoMessage() {}

func (x *ListEvaluationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationsResponse.ProtoReflect.Descriptor instead.
func (*ListEvaluationsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{43}
}

func (x *ListEvaluationsResponse) GetEvaluations() []*v1.Evaluation {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

// UpdateEvaluationRequest
type UpdateEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// evaluation
	EvaluationDef *v1.UpdateEvaluationDef `protobuf:"bytes,2,opt,name=evaluation_def,json=evaluationDef,proto3" json:"evaluation_def,omitempty"`
}

func (x *UpdateEvaluationRequest) Reset() {
	*x = UpdateEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationRequest) ProtoMessage() {}

func (x *UpdateEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationRequest.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{44}
}

func (x *UpdateEvaluationRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateEvaluationRequest) GetEvaluationDef() *v1.UpdateEvaluationDef {
	if x != nil {
		return x.EvaluationDef
	}
	return nil
}

// UpdateEvaluationResponse
type UpdateEvaluationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation
	Evaluation *v1.Evaluation `protobuf:"bytes,1,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
}

func (x *UpdateEvaluationResponse) Reset() {
	*x = UpdateEvaluationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationResponse) ProtoMessage() {}

func (x *UpdateEvaluationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationResponse.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{45}
}

func (x *UpdateEvaluationResponse) GetEvaluation() *v1.Evaluation {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

// SortEvaluationsRequest
type SortEvaluationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortEvaluationsRequest) Reset() {
	*x = SortEvaluationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortEvaluationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortEvaluationsRequest) ProtoMessage() {}

func (x *SortEvaluationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortEvaluationsRequest.ProtoReflect.Descriptor instead.
func (*SortEvaluationsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{46}
}

func (x *SortEvaluationsRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// SortEvaluationsResponse
type SortEvaluationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortEvaluationsResponse) Reset() {
	*x = SortEvaluationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortEvaluationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortEvaluationsResponse) ProtoMessage() {}

func (x *SortEvaluationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortEvaluationsResponse.ProtoReflect.Descriptor instead.
func (*SortEvaluationsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{47}
}

// PushEvaluationChangesRequest
type PushEvaluationChangesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// evaluation ids
	EvaluationIds []int64 `protobuf:"varint,2,rep,packed,name=evaluation_ids,json=evaluationIds,proto3" json:"evaluation_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,3,rep,name=targets,proto3" json:"targets,omitempty"`
	// effective date
	EffectiveDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
}

func (x *PushEvaluationChangesRequest) Reset() {
	*x = PushEvaluationChangesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushEvaluationChangesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushEvaluationChangesRequest) ProtoMessage() {}

func (x *PushEvaluationChangesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushEvaluationChangesRequest.ProtoReflect.Descriptor instead.
func (*PushEvaluationChangesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{48}
}

func (x *PushEvaluationChangesRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *PushEvaluationChangesRequest) GetEvaluationIds() []int64 {
	if x != nil {
		return x.EvaluationIds
	}
	return nil
}

func (x *PushEvaluationChangesRequest) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *PushEvaluationChangesRequest) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

// PushEvaluationChangesResponse
type PushEvaluationChangesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushEvaluationChangesResponse) Reset() {
	*x = PushEvaluationChangesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushEvaluationChangesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushEvaluationChangesResponse) ProtoMessage() {}

func (x *PushEvaluationChangesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushEvaluationChangesResponse.ProtoReflect.Descriptor instead.
func (*PushEvaluationChangesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{49}
}

func (x *PushEvaluationChangesResponse) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushEvaluationChangesResponse) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// CreatePricingRuleRequest
type CreatePricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// pricing rule
	PricingRule *v1.CreatePricingRuleDef `protobuf:"bytes,2,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *CreatePricingRuleRequest) Reset() {
	*x = CreatePricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePricingRuleRequest) ProtoMessage() {}

func (x *CreatePricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePricingRuleRequest.ProtoReflect.Descriptor instead.
func (*CreatePricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{50}
}

func (x *CreatePricingRuleRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreatePricingRuleRequest) GetPricingRule() *v1.CreatePricingRuleDef {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// CreatePricingRuleResponse
type CreatePricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing rule
	PricingRule *v1.PricingRule `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *CreatePricingRuleResponse) Reset() {
	*x = CreatePricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePricingRuleResponse) ProtoMessage() {}

func (x *CreatePricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePricingRuleResponse.ProtoReflect.Descriptor instead.
func (*CreatePricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{51}
}

func (x *CreatePricingRuleResponse) GetPricingRule() *v1.PricingRule {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// ListPricingRulesRequest
type ListPricingRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListPricingRulesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPricingRulesRequest) Reset() {
	*x = ListPricingRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesRequest) ProtoMessage() {}

func (x *ListPricingRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesRequest.ProtoReflect.Descriptor instead.
func (*ListPricingRulesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{52}
}

func (x *ListPricingRulesRequest) GetFilter() *ListPricingRulesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListPricingRulesResponse
type ListPricingRulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing rules
	PricingRules []*v1.PricingRule `protobuf:"bytes,1,rep,name=pricing_rules,json=pricingRules,proto3" json:"pricing_rules,omitempty"`
}

func (x *ListPricingRulesResponse) Reset() {
	*x = ListPricingRulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesResponse) ProtoMessage() {}

func (x *ListPricingRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesResponse.ProtoReflect.Descriptor instead.
func (*ListPricingRulesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{53}
}

func (x *ListPricingRulesResponse) GetPricingRules() []*v1.PricingRule {
	if x != nil {
		return x.PricingRules
	}
	return nil
}

// UpdatePricingRuleRequest
type UpdatePricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pricing rule
	PricingRule *v1.UpdatePricingRuleDef `protobuf:"bytes,2,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *UpdatePricingRuleRequest) Reset() {
	*x = UpdatePricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePricingRuleRequest) ProtoMessage() {}

func (x *UpdatePricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePricingRuleRequest.ProtoReflect.Descriptor instead.
func (*UpdatePricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{54}
}

func (x *UpdatePricingRuleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePricingRuleRequest) GetPricingRule() *v1.UpdatePricingRuleDef {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// UpdatePricingRuleResponse
type UpdatePricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing rule
	PricingRule *v1.PricingRule `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *UpdatePricingRuleResponse) Reset() {
	*x = UpdatePricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePricingRuleResponse) ProtoMessage() {}

func (x *UpdatePricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePricingRuleResponse.ProtoReflect.Descriptor instead.
func (*UpdatePricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{55}
}

func (x *UpdatePricingRuleResponse) GetPricingRule() *v1.PricingRule {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// SortPricingRulesRequest
type SortPricingRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing rule ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortPricingRulesRequest) Reset() {
	*x = SortPricingRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPricingRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPricingRulesRequest) ProtoMessage() {}

func (x *SortPricingRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPricingRulesRequest.ProtoReflect.Descriptor instead.
func (*SortPricingRulesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{56}
}

func (x *SortPricingRulesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// SortPricingRulesResponse
type SortPricingRulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPricingRulesResponse) Reset() {
	*x = SortPricingRulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPricingRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPricingRulesResponse) ProtoMessage() {}

func (x *SortPricingRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPricingRulesResponse.ProtoReflect.Descriptor instead.
func (*SortPricingRulesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{57}
}

// PushPricingRuleChangesRequest
type PushPricingRuleChangesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// pricing rule ids
	PricingRuleIds []int64 `protobuf:"varint,2,rep,packed,name=pricing_rule_ids,json=pricingRuleIds,proto3" json:"pricing_rule_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,3,rep,name=targets,proto3" json:"targets,omitempty"`
	// effective date
	EffectiveDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
}

func (x *PushPricingRuleChangesRequest) Reset() {
	*x = PushPricingRuleChangesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPricingRuleChangesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPricingRuleChangesRequest) ProtoMessage() {}

func (x *PushPricingRuleChangesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPricingRuleChangesRequest.ProtoReflect.Descriptor instead.
func (*PushPricingRuleChangesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{58}
}

func (x *PushPricingRuleChangesRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *PushPricingRuleChangesRequest) GetPricingRuleIds() []int64 {
	if x != nil {
		return x.PricingRuleIds
	}
	return nil
}

func (x *PushPricingRuleChangesRequest) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *PushPricingRuleChangesRequest) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

// PushPricingRuleChangesResponse
type PushPricingRuleChangesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushPricingRuleChangesResponse) Reset() {
	*x = PushPricingRuleChangesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPricingRuleChangesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPricingRuleChangesResponse) ProtoMessage() {}

func (x *PushPricingRuleChangesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPricingRuleChangesResponse.ProtoReflect.Descriptor instead.
func (*PushPricingRuleChangesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{59}
}

func (x *PushPricingRuleChangesResponse) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushPricingRuleChangesResponse) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// CreateServiceChargeRequest
type CreateServiceChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// service charge
	ServiceCharge *v1.CreateServiceChargeDef `protobuf:"bytes,2,opt,name=service_charge,json=serviceCharge,proto3" json:"service_charge,omitempty"`
}

func (x *CreateServiceChargeRequest) Reset() {
	*x = CreateServiceChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceChargeRequest) ProtoMessage() {}

func (x *CreateServiceChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceChargeRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceChargeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{60}
}

func (x *CreateServiceChargeRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreateServiceChargeRequest) GetServiceCharge() *v1.CreateServiceChargeDef {
	if x != nil {
		return x.ServiceCharge
	}
	return nil
}

// CreateServiceChargeResponse
type CreateServiceChargeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge
	ServiceCharge *v1.ServiceCharge `protobuf:"bytes,1,opt,name=service_charge,json=serviceCharge,proto3" json:"service_charge,omitempty"`
}

func (x *CreateServiceChargeResponse) Reset() {
	*x = CreateServiceChargeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceChargeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceChargeResponse) ProtoMessage() {}

func (x *CreateServiceChargeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceChargeResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceChargeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{61}
}

func (x *CreateServiceChargeResponse) GetServiceCharge() *v1.ServiceCharge {
	if x != nil {
		return x.ServiceCharge
	}
	return nil
}

// ListServiceChargesRequest
type ListServiceChargesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListServiceChargesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListServiceChargesRequest) Reset() {
	*x = ListServiceChargesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChargesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChargesRequest) ProtoMessage() {}

func (x *ListServiceChargesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChargesRequest.ProtoReflect.Descriptor instead.
func (*ListServiceChargesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{62}
}

func (x *ListServiceChargesRequest) GetFilter() *ListServiceChargesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListServiceChargesResponse
type ListServiceChargesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charges
	ServiceCharges []*v1.ServiceCharge `protobuf:"bytes,1,rep,name=service_charges,json=serviceCharges,proto3" json:"service_charges,omitempty"`
}

func (x *ListServiceChargesResponse) Reset() {
	*x = ListServiceChargesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChargesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChargesResponse) ProtoMessage() {}

func (x *ListServiceChargesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChargesResponse.ProtoReflect.Descriptor instead.
func (*ListServiceChargesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{63}
}

func (x *ListServiceChargesResponse) GetServiceCharges() []*v1.ServiceCharge {
	if x != nil {
		return x.ServiceCharges
	}
	return nil
}

// UpdateServiceChargeRequest
type UpdateServiceChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service charge
	ServiceCharge *v1.UpdateServiceChargeDef `protobuf:"bytes,2,opt,name=service_charge,json=serviceCharge,proto3" json:"service_charge,omitempty"`
}

func (x *UpdateServiceChargeRequest) Reset() {
	*x = UpdateServiceChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceChargeRequest) ProtoMessage() {}

func (x *UpdateServiceChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceChargeRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceChargeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{64}
}

func (x *UpdateServiceChargeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateServiceChargeRequest) GetServiceCharge() *v1.UpdateServiceChargeDef {
	if x != nil {
		return x.ServiceCharge
	}
	return nil
}

// UpdateServiceChargeResponse
type UpdateServiceChargeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge
	ServiceCharge *v1.ServiceCharge `protobuf:"bytes,1,opt,name=service_charge,json=serviceCharge,proto3" json:"service_charge,omitempty"`
}

func (x *UpdateServiceChargeResponse) Reset() {
	*x = UpdateServiceChargeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceChargeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceChargeResponse) ProtoMessage() {}

func (x *UpdateServiceChargeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceChargeResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceChargeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{65}
}

func (x *UpdateServiceChargeResponse) GetServiceCharge() *v1.ServiceCharge {
	if x != nil {
		return x.ServiceCharge
	}
	return nil
}

// SortServiceChargesRequest
type SortServiceChargesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortServiceChargesRequest) Reset() {
	*x = SortServiceChargesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortServiceChargesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortServiceChargesRequest) ProtoMessage() {}

func (x *SortServiceChargesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortServiceChargesRequest.ProtoReflect.Descriptor instead.
func (*SortServiceChargesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{66}
}

func (x *SortServiceChargesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// SortServiceChargesResponse
type SortServiceChargesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortServiceChargesResponse) Reset() {
	*x = SortServiceChargesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortServiceChargesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortServiceChargesResponse) ProtoMessage() {}

func (x *SortServiceChargesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortServiceChargesResponse.ProtoReflect.Descriptor instead.
func (*SortServiceChargesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{67}
}

// PushServiceChargeChangesRequest
type PushServiceChargeChangesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// service charge ids
	ServiceChargeIds []int64 `protobuf:"varint,2,rep,packed,name=service_charge_ids,json=serviceChargeIds,proto3" json:"service_charge_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,3,rep,name=targets,proto3" json:"targets,omitempty"`
	// effective date
	EffectiveDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
}

func (x *PushServiceChargeChangesRequest) Reset() {
	*x = PushServiceChargeChangesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushServiceChargeChangesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushServiceChargeChangesRequest) ProtoMessage() {}

func (x *PushServiceChargeChangesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushServiceChargeChangesRequest.ProtoReflect.Descriptor instead.
func (*PushServiceChargeChangesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{68}
}

func (x *PushServiceChargeChangesRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *PushServiceChargeChangesRequest) GetServiceChargeIds() []int64 {
	if x != nil {
		return x.ServiceChargeIds
	}
	return nil
}

func (x *PushServiceChargeChangesRequest) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *PushServiceChargeChangesRequest) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

// PushServiceChargeChangesResponse
type PushServiceChargeChangesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushServiceChargeChangesResponse) Reset() {
	*x = PushServiceChargeChangesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushServiceChargeChangesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushServiceChargeChangesResponse) ProtoMessage() {}

func (x *PushServiceChargeChangesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushServiceChargeChangesResponse.ProtoReflect.Descriptor instead.
func (*PushServiceChargeChangesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{69}
}

func (x *PushServiceChargeChangesResponse) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushServiceChargeChangesResponse) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// filter
type ListPriceBooksRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,2,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
}

func (x *ListPriceBooksRequest_Filter) Reset() {
	*x = ListPriceBooksRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPriceBooksRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPriceBooksRequest_Filter) ProtoMessage() {}

func (x *ListPriceBooksRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPriceBooksRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListPriceBooksRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ListPriceBooksRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListPriceBooksRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

// filter
type ListServiceCategoriesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// service item type
	ServiceItemTypes []v11.ServiceItemType `protobuf:"varint,2,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// price book ids
	PriceBookIds []int64 `protobuf:"varint,3,rep,packed,name=price_book_ids,json=priceBookIds,proto3" json:"price_book_ids,omitempty"`
	// service type
	ServiceTypes []v11.ServiceType `protobuf:"varint,4,rep,packed,name=service_types,json=serviceTypes,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_types,omitempty"`
}

func (x *ListServiceCategoriesRequest_Filter) Reset() {
	*x = ListServiceCategoriesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceCategoriesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceCategoriesRequest_Filter) ProtoMessage() {}

func (x *ListServiceCategoriesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceCategoriesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListServiceCategoriesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ListServiceCategoriesRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *ListServiceCategoriesRequest_Filter) GetServiceItemTypes() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ListServiceCategoriesRequest_Filter) GetPriceBookIds() []int64 {
	if x != nil {
		return x.PriceBookIds
	}
	return nil
}

func (x *ListServiceCategoriesRequest_Filter) GetServiceTypes() []v11.ServiceType {
	if x != nil {
		return x.ServiceTypes
	}
	return nil
}

// filter
type ListServicesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// service item type
	ServiceItemTypes []v11.ServiceItemType `protobuf:"varint,2,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// inactive
	Inactive *bool `protobuf:"varint,3,opt,name=inactive,proto3,oneof" json:"inactive,omitempty"`
	// category ids
	CategoryIds []int64 `protobuf:"varint,4,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	// price book ids
	PriceBookIds []int64 `protobuf:"varint,5,rep,packed,name=price_book_ids,json=priceBookIds,proto3" json:"price_book_ids,omitempty"`
	// service type
	ServiceTypes []v11.ServiceType `protobuf:"varint,6,rep,packed,name=service_types,json=serviceTypes,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_types,omitempty"`
	// ids
	Ids []int64 `protobuf:"varint,7,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListServicesRequest_Filter) Reset() {
	*x = ListServicesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServicesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest_Filter) ProtoMessage() {}

func (x *ListServicesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListServicesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{26, 0}
}

func (x *ListServicesRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetServiceItemTypes() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetInactive() bool {
	if x != nil && x.Inactive != nil {
		return *x.Inactive
	}
	return false
}

func (x *ListServicesRequest_Filter) GetCategoryIds() []int64 {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetPriceBookIds() []int64 {
	if x != nil {
		return x.PriceBookIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetServiceTypes() []v11.ServiceType {
	if x != nil {
		return x.ServiceTypes
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// service category sort
type SortServicesRequest_ServiceCategorySort struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *SortServicesRequest_ServiceCategorySort) Reset() {
	*x = SortServicesRequest_ServiceCategorySort{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortServicesRequest_ServiceCategorySort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortServicesRequest_ServiceCategorySort) ProtoMessage() {}

func (x *SortServicesRequest_ServiceCategorySort) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortServicesRequest_ServiceCategorySort.ProtoReflect.Descriptor instead.
func (*SortServicesRequest_ServiceCategorySort) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{32, 0}
}

func (x *SortServicesRequest_ServiceCategorySort) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SortServicesRequest_ServiceCategorySort) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// filter
type ListServiceChangeHistoriesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *ListServiceChangeHistoriesRequest_Filter) Reset() {
	*x = ListServiceChangeHistoriesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangeHistoriesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangeHistoriesRequest_Filter) ProtoMessage() {}

func (x *ListServiceChangeHistoriesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangeHistoriesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListServiceChangeHistoriesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{34, 0}
}

func (x *ListServiceChangeHistoriesRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *ListServiceChangeHistoriesRequest_Filter) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// order by
type ListServiceChangeHistoriesRequest_OrderBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// field
	Field ListServiceChangeHistoriesRequest_OrderBy_Field `protobuf:"varint,1,opt,name=field,proto3,enum=moego.service.enterprise.v1.ListServiceChangeHistoriesRequest_OrderBy_Field" json:"field,omitempty"`
	// asc
	Asc bool `protobuf:"varint,2,opt,name=asc,proto3" json:"asc,omitempty"`
}

func (x *ListServiceChangeHistoriesRequest_OrderBy) Reset() {
	*x = ListServiceChangeHistoriesRequest_OrderBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangeHistoriesRequest_OrderBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangeHistoriesRequest_OrderBy) ProtoMessage() {}

func (x *ListServiceChangeHistoriesRequest_OrderBy) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangeHistoriesRequest_OrderBy.ProtoReflect.Descriptor instead.
func (*ListServiceChangeHistoriesRequest_OrderBy) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{34, 1}
}

func (x *ListServiceChangeHistoriesRequest_OrderBy) GetField() ListServiceChangeHistoriesRequest_OrderBy_Field {
	if x != nil {
		return x.Field
	}
	return ListServiceChangeHistoriesRequest_OrderBy_FIELD_UNSPECIFIED
}

func (x *ListServiceChangeHistoriesRequest_OrderBy) GetAsc() bool {
	if x != nil {
		return x.Asc
	}
	return false
}

// filter
type ListServiceChangesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// history ids
	HistoryIds []int64 `protobuf:"varint,3,rep,packed,name=history_ids,json=historyIds,proto3" json:"history_ids,omitempty"`
}

func (x *ListServiceChangesRequest_Filter) Reset() {
	*x = ListServiceChangesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangesRequest_Filter) ProtoMessage() {}

func (x *ListServiceChangesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListServiceChangesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{36, 0}
}

func (x *ListServiceChangesRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *ListServiceChangesRequest_Filter) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *ListServiceChangesRequest_Filter) GetHistoryIds() []int64 {
	if x != nil {
		return x.HistoryIds
	}
	return nil
}

// filter
type ListEvaluationsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// is_active
	IsActive *bool `protobuf:"varint,2,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// price book ids
	PriceBookIds []int64 `protobuf:"varint,3,rep,packed,name=price_book_ids,json=priceBookIds,proto3" json:"price_book_ids,omitempty"`
	// ids
	Ids []int64 `protobuf:"varint,4,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListEvaluationsRequest_Filter) Reset() {
	*x = ListEvaluationsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationsRequest_Filter) ProtoMessage() {}

func (x *ListEvaluationsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListEvaluationsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{42, 0}
}

func (x *ListEvaluationsRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *ListEvaluationsRequest_Filter) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *ListEvaluationsRequest_Filter) GetPriceBookIds() []int64 {
	if x != nil {
		return x.PriceBookIds
	}
	return nil
}

func (x *ListEvaluationsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// filter
type ListPricingRulesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// price book ids
	PriceBookIds []int64 `protobuf:"varint,2,rep,packed,name=price_book_ids,json=priceBookIds,proto3" json:"price_book_ids,omitempty"`
	// rule types
	RuleTypes []v21.RuleType `protobuf:"varint,3,rep,packed,name=rule_types,json=ruleTypes,proto3,enum=moego.models.offering.v2.RuleType" json:"rule_types,omitempty"`
	// ids
	Ids []int64 `protobuf:"varint,4,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// is active
	IsActive *bool `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// service item types in limitation.ServiceLimitationDef contain any one in the list
	ServiceItemTypesContainAny []v11.ServiceItemType `protobuf:"varint,6,rep,packed,name=service_item_types_contain_any,json=serviceItemTypesContainAny,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types_contain_any,omitempty"`
}

func (x *ListPricingRulesRequest_Filter) Reset() {
	*x = ListPricingRulesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesRequest_Filter) ProtoMessage() {}

func (x *ListPricingRulesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListPricingRulesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{52, 0}
}

func (x *ListPricingRulesRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *ListPricingRulesRequest_Filter) GetPriceBookIds() []int64 {
	if x != nil {
		return x.PriceBookIds
	}
	return nil
}

func (x *ListPricingRulesRequest_Filter) GetRuleTypes() []v21.RuleType {
	if x != nil {
		return x.RuleTypes
	}
	return nil
}

func (x *ListPricingRulesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListPricingRulesRequest_Filter) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *ListPricingRulesRequest_Filter) GetServiceItemTypesContainAny() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypesContainAny
	}
	return nil
}

// filter
type ListServiceChargesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// price book ids
	PriceBookIds []int64 `protobuf:"varint,2,rep,packed,name=price_book_ids,json=priceBookIds,proto3" json:"price_book_ids,omitempty"`
	// ids
	Ids []int64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// is active
	IsActive *bool `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// types
	SurchargeTypes []v12.SurchargeType `protobuf:"varint,5,rep,packed,name=surcharge_types,json=surchargeTypes,proto3,enum=moego.models.order.v1.SurchargeType" json:"surcharge_types,omitempty"`
	// service item types contain any one in the list
	ServiceItemTypesContainAny []v11.ServiceItemType `protobuf:"varint,6,rep,packed,name=service_item_types_contain_any,json=serviceItemTypesContainAny,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types_contain_any,omitempty"`
}

func (x *ListServiceChargesRequest_Filter) Reset() {
	*x = ListServiceChargesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChargesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChargesRequest_Filter) ProtoMessage() {}

func (x *ListServiceChargesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChargesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListServiceChargesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP(), []int{62, 0}
}

func (x *ListServiceChargesRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *ListServiceChargesRequest_Filter) GetPriceBookIds() []int64 {
	if x != nil {
		return x.PriceBookIds
	}
	return nil
}

func (x *ListServiceChargesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListServiceChargesRequest_Filter) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *ListServiceChargesRequest_Filter) GetSurchargeTypes() []v12.SurchargeType {
	if x != nil {
		return x.SurchargeTypes
	}
	return nil
}

func (x *ListServiceChargesRequest_Filter) GetServiceItemTypesContainAny() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypesContainAny
	}
	return nil
}

var File_moego_service_enterprise_v1_price_book_service_proto protoreflect.FileDescriptor

var file_moego_service_enterprise_v1_price_book_service_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd6, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x27, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x41, 0x0a, 0x06,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x22,
	0x60, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0b, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x73, 0x22, 0x51, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5f, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x22, 0x44, 0x0a, 0x14, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a,
	0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0x5d, 0x0a, 0x15, 0x49,
	0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52,
	0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x22, 0x5d, 0x0a, 0x16, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xc8, 0x01, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5f, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52,
	0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x22, 0x31, 0x0a, 0x16, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x19, 0x0a,
	0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x9c, 0x01, 0x0a, 0x19, 0x44, 0x75, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x28, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xc8,
	0x01, 0x48, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x62, 0x0a, 0x1a, 0x44, 0x75, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x22, 0x5e, 0x0a, 0x17, 0x4d,
	0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x1a, 0x0a, 0x18, 0x4d,
	0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb1, 0x02, 0x0a, 0x1c, 0x53, 0x61, 0x76, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x4b, 0x0a,
	0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0a,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x1f, 0x0a, 0x1d, 0x53,
	0x61, 0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb8, 0x03, 0x0a,
	0x1c, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x58, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xfa, 0x01, 0x0a, 0x06, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x57, 0x0a, 0x12,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x73, 0x12, 0x4a, 0x0a, 0x0d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x7b, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x22, 0x3b, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42,
	0x72, 0x65, 0x65, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49,
	0x64, 0x22, 0x5c, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x70, 0x65,
	0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42,
	0x72, 0x65, 0x65, 0x64, 0x52, 0x09, 0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x22,
	0x3a, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x14, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x70, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xe6, 0x07, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23,
	0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x55, 0x0a,
	0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x58, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x78,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x61, 0x78,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0c, 0x6d,
	0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6d, 0x61,
	0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x0a, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x11, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x12, 0x49, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x56,
	0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x2c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x53, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x89, 0x04, 0x0a, 0x13, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xdd, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x1f, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x73, 0x12, 0x4a, 0x0a, 0x0d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x6e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0x9b, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x22, 0xca, 0x06, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x25, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x19, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x02, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x58, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x10, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12,
	0x1e, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x03, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x17,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52,
	0x15, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x88, 0x01, 0x01, 0x12, 0x49, 0x0a, 0x09, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x61, 0x75, 0x74, 0x6f,
	0x52, 0x75, 0x6c, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0x0a,
	0x06, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x74, 0x61, 0x78, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x22, 0x56, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x26, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd9, 0x01, 0x0a, 0x13, 0x53, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x7a, 0x0a, 0x16, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x6f, 0x72, 0x74, 0x73, 0x1a, 0x46, 0x0a,
	0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x53, 0x6f, 0x72, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8b, 0x05,
	0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5d, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x65, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x59, 0x0a, 0x09,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x1a, 0x50, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x1a, 0xaf, 0x01, 0x0a, 0x07, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x62, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x2e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x73, 0x63,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x61, 0x73, 0x63, 0x22, 0x2e, 0x0a, 0x05, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x01, 0x22, 0xd4, 0x01, 0x0a, 0x22,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6a, 0x0a, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x16, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x22, 0xa8, 0x02, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x71, 0x0a, 0x06, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0a, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x73, 0x22, 0xb4, 0x01,
	0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x52, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x22, 0xa1, 0x02, 0x0a, 0x19, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x41, 0x0a, 0x0e,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x37, 0x0a, 0x18, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x15, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x6f, 0x42, 0x6f, 0x6f, 0x6b, 0x65, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x7a, 0x0a, 0x1a, 0x50, 0x75, 0x73, 0x68,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x56,
	0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x22, 0x62, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x46, 0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x86, 0x02, 0x0a, 0x16, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x52, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x97, 0x01, 0x0a, 0x06, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a,
	0x0e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x49, 0x64, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x03, 0x69, 0x64, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x22, 0x63, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48,
	0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x56, 0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x22, 0x62, 0x0a, 0x18,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x34, 0x0a, 0x16, 0x53, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x03, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08,
	0x01, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x53, 0x6f, 0x72, 0x74, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0xf1, 0x01, 0x0a, 0x1c, 0x50, 0x75, 0x73, 0x68, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x42,
	0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x73, 0x12, 0x41, 0x0a, 0x0e, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x7d, 0x0a, 0x1d, 0x50, 0x75, 0x73, 0x68, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x73, 0x22, 0x94, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0b,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x67, 0x0a, 0x19, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x22, 0xba, 0x03, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x53, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xc9, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0c, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x73, 0x12, 0x41, 0x0a,
	0x0a, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69,
	0x64, 0x73, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x6d, 0x0a, 0x1e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x5f, 0x61, 0x6e, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x41, 0x6e, 0x79, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x22, 0x68, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a,
	0x0d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0c, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x22, 0x7f, 0x0a, 0x18, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x53, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x52,
	0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x67, 0x0a, 0x19,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x35, 0x0a, 0x17, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1a, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x1a, 0x0a, 0x18,
	0x53, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf5, 0x01, 0x0a, 0x1d, 0x50, 0x75, 0x73,
	0x68, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x41, 0x0a,
	0x0e, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x22, 0x7e, 0x0a, 0x1e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73,
	0x22, 0x9c, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x65, 0x66,
	0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x22,
	0x6f, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50,
	0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x22, 0xca, 0x03, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x55,
	0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xd5, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0c, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12,
	0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x4d, 0x0a, 0x0f, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0e, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x6d, 0x0a, 0x1e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x5f, 0x61,
	0x6e, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x41, 0x6e, 0x79, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0x70, 0x0a,
	0x1a, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x22,
	0x87, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x59,
	0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x22, 0x6f, 0x0a, 0x1b, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x0d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x22, 0x37, 0x0a, 0x19, 0x53, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x22, 0x1c, 0x0a, 0x1a, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0xfb, 0x01, 0x0a, 0x1f, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x41, 0x0a, 0x0e,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22,
	0x80, 0x01, 0x0a, 0x20, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x73, 0x32, 0x9b, 0x24, 0x0a, 0x10, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x79, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x7c, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c,
	0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a,
	0x12, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x69, 0x67,
	0x72, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x0d, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x90, 0x01,
	0x0a, 0x15, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x90, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x73, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a,
	0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f,
	0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x75, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x78, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x0c, 0x53, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x9f, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x87, 0x01,
	0x0a, 0x12, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7f, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x53, 0x6f, 0x72, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f,
	0x72, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x15, 0x50, 0x75, 0x73, 0x68, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12,
	0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75,
	0x73, 0x68, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a,
	0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7f, 0x0a, 0x10, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x16, 0x50, 0x75, 0x73, 0x68, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x3a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8a, 0x01,
	0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x12, 0x53,
	0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x99, 0x01, 0x0a, 0x18, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75,
	0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x89, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_enterprise_v1_price_book_service_proto_rawDescOnce sync.Once
	file_moego_service_enterprise_v1_price_book_service_proto_rawDescData = file_moego_service_enterprise_v1_price_book_service_proto_rawDesc
)

func file_moego_service_enterprise_v1_price_book_service_proto_rawDescGZIP() []byte {
	file_moego_service_enterprise_v1_price_book_service_proto_rawDescOnce.Do(func() {
		file_moego_service_enterprise_v1_price_book_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_enterprise_v1_price_book_service_proto_rawDescData)
	})
	return file_moego_service_enterprise_v1_price_book_service_proto_rawDescData
}

var file_moego_service_enterprise_v1_price_book_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_service_enterprise_v1_price_book_service_proto_msgTypes = make([]protoimpl.MessageInfo, 80)
var file_moego_service_enterprise_v1_price_book_service_proto_goTypes = []interface{}{
	(ListServiceChangeHistoriesRequest_OrderBy_Field)(0), // 0: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.OrderBy.Field
	(*ListPriceBooksRequest)(nil),                        // 1: moego.service.enterprise.v1.ListPriceBooksRequest
	(*ListPriceBooksResponse)(nil),                       // 2: moego.service.enterprise.v1.ListPriceBooksResponse
	(*CreatePriceBookRequest)(nil),                       // 3: moego.service.enterprise.v1.CreatePriceBookRequest
	(*CreatePriceBookResponse)(nil),                      // 4: moego.service.enterprise.v1.CreatePriceBookResponse
	(*InitPriceBookRequest)(nil),                         // 5: moego.service.enterprise.v1.InitPriceBookRequest
	(*InitPriceBookResponse)(nil),                        // 6: moego.service.enterprise.v1.InitPriceBookResponse
	(*UpdatePriceBookRequest)(nil),                       // 7: moego.service.enterprise.v1.UpdatePriceBookRequest
	(*UpdatePriceBookResponse)(nil),                      // 8: moego.service.enterprise.v1.UpdatePriceBookResponse
	(*DeletePriceBookRequest)(nil),                       // 9: moego.service.enterprise.v1.DeletePriceBookRequest
	(*DeletePriceBookResponse)(nil),                      // 10: moego.service.enterprise.v1.DeletePriceBookResponse
	(*DuplicatePriceBookRequest)(nil),                    // 11: moego.service.enterprise.v1.DuplicatePriceBookRequest
	(*DuplicatePriceBookResponse)(nil),                   // 12: moego.service.enterprise.v1.DuplicatePriceBookResponse
	(*MigratePriceBookRequest)(nil),                      // 13: moego.service.enterprise.v1.MigratePriceBookRequest
	(*MigratePriceBookResponse)(nil),                     // 14: moego.service.enterprise.v1.MigratePriceBookResponse
	(*SaveServiceCategoriesRequest)(nil),                 // 15: moego.service.enterprise.v1.SaveServiceCategoriesRequest
	(*SaveServiceCategoriesResponse)(nil),                // 16: moego.service.enterprise.v1.SaveServiceCategoriesResponse
	(*ListServiceCategoriesRequest)(nil),                 // 17: moego.service.enterprise.v1.ListServiceCategoriesRequest
	(*ListServiceCategoriesResponse)(nil),                // 18: moego.service.enterprise.v1.ListServiceCategoriesResponse
	(*ListPetBreedsRequest)(nil),                         // 19: moego.service.enterprise.v1.ListPetBreedsRequest
	(*ListPetBreedsResponse)(nil),                        // 20: moego.service.enterprise.v1.ListPetBreedsResponse
	(*ListPetTypesRequest)(nil),                          // 21: moego.service.enterprise.v1.ListPetTypesRequest
	(*ListPetTypesResponse)(nil),                         // 22: moego.service.enterprise.v1.ListPetTypesResponse
	(*CreateServiceRequest)(nil),                         // 23: moego.service.enterprise.v1.CreateServiceRequest
	(*CreateServiceResponse)(nil),                        // 24: moego.service.enterprise.v1.CreateServiceResponse
	(*GetServiceRequest)(nil),                            // 25: moego.service.enterprise.v1.GetServiceRequest
	(*GetServiceResponse)(nil),                           // 26: moego.service.enterprise.v1.GetServiceResponse
	(*ListServicesRequest)(nil),                          // 27: moego.service.enterprise.v1.ListServicesRequest
	(*ListServicesResponse)(nil),                         // 28: moego.service.enterprise.v1.ListServicesResponse
	(*UpdateServiceRequest)(nil),                         // 29: moego.service.enterprise.v1.UpdateServiceRequest
	(*UpdateServiceResponse)(nil),                        // 30: moego.service.enterprise.v1.UpdateServiceResponse
	(*DeleteServiceRequest)(nil),                         // 31: moego.service.enterprise.v1.DeleteServiceRequest
	(*DeleteServiceResponse)(nil),                        // 32: moego.service.enterprise.v1.DeleteServiceResponse
	(*SortServicesRequest)(nil),                          // 33: moego.service.enterprise.v1.SortServicesRequest
	(*SortServicesResponse)(nil),                         // 34: moego.service.enterprise.v1.SortServicesResponse
	(*ListServiceChangeHistoriesRequest)(nil),            // 35: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest
	(*ListServiceChangeHistoriesResponse)(nil),           // 36: moego.service.enterprise.v1.ListServiceChangeHistoriesResponse
	(*ListServiceChangesRequest)(nil),                    // 37: moego.service.enterprise.v1.ListServiceChangesRequest
	(*ListServiceChangesResponse)(nil),                   // 38: moego.service.enterprise.v1.ListServiceChangesResponse
	(*PushServiceChangesRequest)(nil),                    // 39: moego.service.enterprise.v1.PushServiceChangesRequest
	(*PushServiceChangesResponse)(nil),                   // 40: moego.service.enterprise.v1.PushServiceChangesResponse
	(*CreateEvaluationRequest)(nil),                      // 41: moego.service.enterprise.v1.CreateEvaluationRequest
	(*CreateEvaluationResponse)(nil),                     // 42: moego.service.enterprise.v1.CreateEvaluationResponse
	(*ListEvaluationsRequest)(nil),                       // 43: moego.service.enterprise.v1.ListEvaluationsRequest
	(*ListEvaluationsResponse)(nil),                      // 44: moego.service.enterprise.v1.ListEvaluationsResponse
	(*UpdateEvaluationRequest)(nil),                      // 45: moego.service.enterprise.v1.UpdateEvaluationRequest
	(*UpdateEvaluationResponse)(nil),                     // 46: moego.service.enterprise.v1.UpdateEvaluationResponse
	(*SortEvaluationsRequest)(nil),                       // 47: moego.service.enterprise.v1.SortEvaluationsRequest
	(*SortEvaluationsResponse)(nil),                      // 48: moego.service.enterprise.v1.SortEvaluationsResponse
	(*PushEvaluationChangesRequest)(nil),                 // 49: moego.service.enterprise.v1.PushEvaluationChangesRequest
	(*PushEvaluationChangesResponse)(nil),                // 50: moego.service.enterprise.v1.PushEvaluationChangesResponse
	(*CreatePricingRuleRequest)(nil),                     // 51: moego.service.enterprise.v1.CreatePricingRuleRequest
	(*CreatePricingRuleResponse)(nil),                    // 52: moego.service.enterprise.v1.CreatePricingRuleResponse
	(*ListPricingRulesRequest)(nil),                      // 53: moego.service.enterprise.v1.ListPricingRulesRequest
	(*ListPricingRulesResponse)(nil),                     // 54: moego.service.enterprise.v1.ListPricingRulesResponse
	(*UpdatePricingRuleRequest)(nil),                     // 55: moego.service.enterprise.v1.UpdatePricingRuleRequest
	(*UpdatePricingRuleResponse)(nil),                    // 56: moego.service.enterprise.v1.UpdatePricingRuleResponse
	(*SortPricingRulesRequest)(nil),                      // 57: moego.service.enterprise.v1.SortPricingRulesRequest
	(*SortPricingRulesResponse)(nil),                     // 58: moego.service.enterprise.v1.SortPricingRulesResponse
	(*PushPricingRuleChangesRequest)(nil),                // 59: moego.service.enterprise.v1.PushPricingRuleChangesRequest
	(*PushPricingRuleChangesResponse)(nil),               // 60: moego.service.enterprise.v1.PushPricingRuleChangesResponse
	(*CreateServiceChargeRequest)(nil),                   // 61: moego.service.enterprise.v1.CreateServiceChargeRequest
	(*CreateServiceChargeResponse)(nil),                  // 62: moego.service.enterprise.v1.CreateServiceChargeResponse
	(*ListServiceChargesRequest)(nil),                    // 63: moego.service.enterprise.v1.ListServiceChargesRequest
	(*ListServiceChargesResponse)(nil),                   // 64: moego.service.enterprise.v1.ListServiceChargesResponse
	(*UpdateServiceChargeRequest)(nil),                   // 65: moego.service.enterprise.v1.UpdateServiceChargeRequest
	(*UpdateServiceChargeResponse)(nil),                  // 66: moego.service.enterprise.v1.UpdateServiceChargeResponse
	(*SortServiceChargesRequest)(nil),                    // 67: moego.service.enterprise.v1.SortServiceChargesRequest
	(*SortServiceChargesResponse)(nil),                   // 68: moego.service.enterprise.v1.SortServiceChargesResponse
	(*PushServiceChargeChangesRequest)(nil),              // 69: moego.service.enterprise.v1.PushServiceChargeChangesRequest
	(*PushServiceChargeChangesResponse)(nil),             // 70: moego.service.enterprise.v1.PushServiceChargeChangesResponse
	(*ListPriceBooksRequest_Filter)(nil),                 // 71: moego.service.enterprise.v1.ListPriceBooksRequest.Filter
	(*ListServiceCategoriesRequest_Filter)(nil),          // 72: moego.service.enterprise.v1.ListServiceCategoriesRequest.Filter
	(*ListServicesRequest_Filter)(nil),                   // 73: moego.service.enterprise.v1.ListServicesRequest.Filter
	(*SortServicesRequest_ServiceCategorySort)(nil),      // 74: moego.service.enterprise.v1.SortServicesRequest.ServiceCategorySort
	(*ListServiceChangeHistoriesRequest_Filter)(nil),     // 75: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.Filter
	(*ListServiceChangeHistoriesRequest_OrderBy)(nil),    // 76: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.OrderBy
	(*ListServiceChangesRequest_Filter)(nil),             // 77: moego.service.enterprise.v1.ListServiceChangesRequest.Filter
	(*ListEvaluationsRequest_Filter)(nil),                // 78: moego.service.enterprise.v1.ListEvaluationsRequest.Filter
	(*ListPricingRulesRequest_Filter)(nil),               // 79: moego.service.enterprise.v1.ListPricingRulesRequest.Filter
	(*ListServiceChargesRequest_Filter)(nil),             // 80: moego.service.enterprise.v1.ListServiceChargesRequest.Filter
	(*v1.PriceBook)(nil),                                 // 81: moego.models.enterprise.v1.PriceBook
	(*v1.ServiceCategory)(nil),                           // 82: moego.models.enterprise.v1.ServiceCategory
	(v11.ServiceType)(0),                                 // 83: moego.models.offering.v1.ServiceType
	(v11.ServiceItemType)(0),                             // 84: moego.models.offering.v1.ServiceItemType
	(*v2.PaginationRequest)(nil),                         // 85: moego.utils.v2.PaginationRequest
	(*v1.PetBreed)(nil),                                  // 86: moego.models.enterprise.v1.PetBreed
	(*v1.PetType)(nil),                                   // 87: moego.models.enterprise.v1.PetType
	(*money.Money)(nil),                                  // 88: google.type.Money
	(v11.ServicePriceUnit)(0),                            // 89: moego.models.offering.v1.ServicePriceUnit
	(*durationpb.Duration)(nil),                          // 90: google.protobuf.Duration
	(*v1.Service_Limitation)(nil),                        // 91: moego.models.enterprise.v1.Service.Limitation
	(*v1.Service_AutoRule)(nil),                          // 92: moego.models.enterprise.v1.Service.AutoRule
	(*v1.Service)(nil),                                   // 93: moego.models.enterprise.v1.Service
	(*v2.PaginationResponse)(nil),                        // 94: moego.utils.v2.PaginationResponse
	(*v1.TemplatePushChangeHistoryOrderBy)(nil),          // 95: moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy
	(*v1.ServiceChangeHistory)(nil),                      // 96: moego.models.enterprise.v1.ServiceChangeHistory
	(*v1.ServiceChange)(nil),                             // 97: moego.models.enterprise.v1.ServiceChange
	(*v1.TenantObject)(nil),                              // 98: moego.models.enterprise.v1.TenantObject
	(*timestamppb.Timestamp)(nil),                        // 99: google.protobuf.Timestamp
	(*v1.CreateEvaluationDef)(nil),                       // 100: moego.models.enterprise.v1.CreateEvaluationDef
	(*v1.Evaluation)(nil),                                // 101: moego.models.enterprise.v1.Evaluation
	(*v1.UpdateEvaluationDef)(nil),                       // 102: moego.models.enterprise.v1.UpdateEvaluationDef
	(*v1.CreatePricingRuleDef)(nil),                      // 103: moego.models.enterprise.v1.CreatePricingRuleDef
	(*v1.PricingRule)(nil),                               // 104: moego.models.enterprise.v1.PricingRule
	(*v1.UpdatePricingRuleDef)(nil),                      // 105: moego.models.enterprise.v1.UpdatePricingRuleDef
	(*v1.CreateServiceChargeDef)(nil),                    // 106: moego.models.enterprise.v1.CreateServiceChargeDef
	(*v1.ServiceCharge)(nil),                             // 107: moego.models.enterprise.v1.ServiceCharge
	(*v1.UpdateServiceChargeDef)(nil),                    // 108: moego.models.enterprise.v1.UpdateServiceChargeDef
	(v21.RuleType)(0),                                    // 109: moego.models.offering.v2.RuleType
	(v12.SurchargeType)(0),                               // 110: moego.models.order.v1.SurchargeType
}
var file_moego_service_enterprise_v1_price_book_service_proto_depIdxs = []int32{
	71,  // 0: moego.service.enterprise.v1.ListPriceBooksRequest.filter:type_name -> moego.service.enterprise.v1.ListPriceBooksRequest.Filter
	81,  // 1: moego.service.enterprise.v1.ListPriceBooksResponse.price_books:type_name -> moego.models.enterprise.v1.PriceBook
	81,  // 2: moego.service.enterprise.v1.CreatePriceBookResponse.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	81,  // 3: moego.service.enterprise.v1.InitPriceBookResponse.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	81,  // 4: moego.service.enterprise.v1.UpdatePriceBookResponse.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	81,  // 5: moego.service.enterprise.v1.DuplicatePriceBookResponse.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	82,  // 6: moego.service.enterprise.v1.SaveServiceCategoriesRequest.categories:type_name -> moego.models.enterprise.v1.ServiceCategory
	83,  // 7: moego.service.enterprise.v1.SaveServiceCategoriesRequest.service_type:type_name -> moego.models.offering.v1.ServiceType
	84,  // 8: moego.service.enterprise.v1.SaveServiceCategoriesRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	85,  // 9: moego.service.enterprise.v1.ListServiceCategoriesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	72,  // 10: moego.service.enterprise.v1.ListServiceCategoriesRequest.filter:type_name -> moego.service.enterprise.v1.ListServiceCategoriesRequest.Filter
	82,  // 11: moego.service.enterprise.v1.ListServiceCategoriesResponse.service_categories:type_name -> moego.models.enterprise.v1.ServiceCategory
	86,  // 12: moego.service.enterprise.v1.ListPetBreedsResponse.pet_breeds:type_name -> moego.models.enterprise.v1.PetBreed
	87,  // 13: moego.service.enterprise.v1.ListPetTypesResponse.pet_types:type_name -> moego.models.enterprise.v1.PetType
	81,  // 14: moego.service.enterprise.v1.CreateServiceRequest.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	84,  // 15: moego.service.enterprise.v1.CreateServiceRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	82,  // 16: moego.service.enterprise.v1.CreateServiceRequest.category:type_name -> moego.models.enterprise.v1.ServiceCategory
	88,  // 17: moego.service.enterprise.v1.CreateServiceRequest.price:type_name -> google.type.Money
	89,  // 18: moego.service.enterprise.v1.CreateServiceRequest.service_price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	90,  // 19: moego.service.enterprise.v1.CreateServiceRequest.duration:type_name -> google.protobuf.Duration
	90,  // 20: moego.service.enterprise.v1.CreateServiceRequest.max_duration:type_name -> google.protobuf.Duration
	91,  // 21: moego.service.enterprise.v1.CreateServiceRequest.limitation:type_name -> moego.models.enterprise.v1.Service.Limitation
	83,  // 22: moego.service.enterprise.v1.CreateServiceRequest.service_type:type_name -> moego.models.offering.v1.ServiceType
	92,  // 23: moego.service.enterprise.v1.CreateServiceRequest.auto_rule:type_name -> moego.models.enterprise.v1.Service.AutoRule
	93,  // 24: moego.service.enterprise.v1.CreateServiceResponse.service:type_name -> moego.models.enterprise.v1.Service
	93,  // 25: moego.service.enterprise.v1.GetServiceResponse.service:type_name -> moego.models.enterprise.v1.Service
	85,  // 26: moego.service.enterprise.v1.ListServicesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	73,  // 27: moego.service.enterprise.v1.ListServicesRequest.filter:type_name -> moego.service.enterprise.v1.ListServicesRequest.Filter
	94,  // 28: moego.service.enterprise.v1.ListServicesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	93,  // 29: moego.service.enterprise.v1.ListServicesResponse.services:type_name -> moego.models.enterprise.v1.Service
	82,  // 30: moego.service.enterprise.v1.UpdateServiceRequest.service_category:type_name -> moego.models.enterprise.v1.ServiceCategory
	88,  // 31: moego.service.enterprise.v1.UpdateServiceRequest.price:type_name -> google.type.Money
	89,  // 32: moego.service.enterprise.v1.UpdateServiceRequest.service_price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	90,  // 33: moego.service.enterprise.v1.UpdateServiceRequest.duration:type_name -> google.protobuf.Duration
	90,  // 34: moego.service.enterprise.v1.UpdateServiceRequest.max_duration:type_name -> google.protobuf.Duration
	91,  // 35: moego.service.enterprise.v1.UpdateServiceRequest.limitation:type_name -> moego.models.enterprise.v1.Service.Limitation
	92,  // 36: moego.service.enterprise.v1.UpdateServiceRequest.auto_rule:type_name -> moego.models.enterprise.v1.Service.AutoRule
	93,  // 37: moego.service.enterprise.v1.UpdateServiceResponse.service:type_name -> moego.models.enterprise.v1.Service
	74,  // 38: moego.service.enterprise.v1.SortServicesRequest.service_category_sorts:type_name -> moego.service.enterprise.v1.SortServicesRequest.ServiceCategorySort
	85,  // 39: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	75,  // 40: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.filter:type_name -> moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.Filter
	76,  // 41: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.order_by:type_name -> moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.OrderBy
	95,  // 42: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.order_bys:type_name -> moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy
	94,  // 43: moego.service.enterprise.v1.ListServiceChangeHistoriesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	96,  // 44: moego.service.enterprise.v1.ListServiceChangeHistoriesResponse.service_change_histories:type_name -> moego.models.enterprise.v1.ServiceChangeHistory
	85,  // 45: moego.service.enterprise.v1.ListServiceChangesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	77,  // 46: moego.service.enterprise.v1.ListServiceChangesRequest.filter:type_name -> moego.service.enterprise.v1.ListServiceChangesRequest.Filter
	94,  // 47: moego.service.enterprise.v1.ListServiceChangesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	97,  // 48: moego.service.enterprise.v1.ListServiceChangesResponse.service_changes:type_name -> moego.models.enterprise.v1.ServiceChange
	98,  // 49: moego.service.enterprise.v1.PushServiceChangesRequest.targets:type_name -> moego.models.enterprise.v1.TenantObject
	99,  // 50: moego.service.enterprise.v1.PushServiceChangesRequest.effective_date:type_name -> google.protobuf.Timestamp
	100, // 51: moego.service.enterprise.v1.CreateEvaluationRequest.evaluation_def:type_name -> moego.models.enterprise.v1.CreateEvaluationDef
	101, // 52: moego.service.enterprise.v1.CreateEvaluationResponse.evaluation:type_name -> moego.models.enterprise.v1.Evaluation
	78,  // 53: moego.service.enterprise.v1.ListEvaluationsRequest.filter:type_name -> moego.service.enterprise.v1.ListEvaluationsRequest.Filter
	101, // 54: moego.service.enterprise.v1.ListEvaluationsResponse.evaluations:type_name -> moego.models.enterprise.v1.Evaluation
	102, // 55: moego.service.enterprise.v1.UpdateEvaluationRequest.evaluation_def:type_name -> moego.models.enterprise.v1.UpdateEvaluationDef
	101, // 56: moego.service.enterprise.v1.UpdateEvaluationResponse.evaluation:type_name -> moego.models.enterprise.v1.Evaluation
	98,  // 57: moego.service.enterprise.v1.PushEvaluationChangesRequest.targets:type_name -> moego.models.enterprise.v1.TenantObject
	99,  // 58: moego.service.enterprise.v1.PushEvaluationChangesRequest.effective_date:type_name -> google.protobuf.Timestamp
	103, // 59: moego.service.enterprise.v1.CreatePricingRuleRequest.pricing_rule:type_name -> moego.models.enterprise.v1.CreatePricingRuleDef
	104, // 60: moego.service.enterprise.v1.CreatePricingRuleResponse.pricing_rule:type_name -> moego.models.enterprise.v1.PricingRule
	79,  // 61: moego.service.enterprise.v1.ListPricingRulesRequest.filter:type_name -> moego.service.enterprise.v1.ListPricingRulesRequest.Filter
	104, // 62: moego.service.enterprise.v1.ListPricingRulesResponse.pricing_rules:type_name -> moego.models.enterprise.v1.PricingRule
	105, // 63: moego.service.enterprise.v1.UpdatePricingRuleRequest.pricing_rule:type_name -> moego.models.enterprise.v1.UpdatePricingRuleDef
	104, // 64: moego.service.enterprise.v1.UpdatePricingRuleResponse.pricing_rule:type_name -> moego.models.enterprise.v1.PricingRule
	98,  // 65: moego.service.enterprise.v1.PushPricingRuleChangesRequest.targets:type_name -> moego.models.enterprise.v1.TenantObject
	99,  // 66: moego.service.enterprise.v1.PushPricingRuleChangesRequest.effective_date:type_name -> google.protobuf.Timestamp
	106, // 67: moego.service.enterprise.v1.CreateServiceChargeRequest.service_charge:type_name -> moego.models.enterprise.v1.CreateServiceChargeDef
	107, // 68: moego.service.enterprise.v1.CreateServiceChargeResponse.service_charge:type_name -> moego.models.enterprise.v1.ServiceCharge
	80,  // 69: moego.service.enterprise.v1.ListServiceChargesRequest.filter:type_name -> moego.service.enterprise.v1.ListServiceChargesRequest.Filter
	107, // 70: moego.service.enterprise.v1.ListServiceChargesResponse.service_charges:type_name -> moego.models.enterprise.v1.ServiceCharge
	108, // 71: moego.service.enterprise.v1.UpdateServiceChargeRequest.service_charge:type_name -> moego.models.enterprise.v1.UpdateServiceChargeDef
	107, // 72: moego.service.enterprise.v1.UpdateServiceChargeResponse.service_charge:type_name -> moego.models.enterprise.v1.ServiceCharge
	98,  // 73: moego.service.enterprise.v1.PushServiceChargeChangesRequest.targets:type_name -> moego.models.enterprise.v1.TenantObject
	99,  // 74: moego.service.enterprise.v1.PushServiceChargeChangesRequest.effective_date:type_name -> google.protobuf.Timestamp
	84,  // 75: moego.service.enterprise.v1.ListServiceCategoriesRequest.Filter.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	83,  // 76: moego.service.enterprise.v1.ListServiceCategoriesRequest.Filter.service_types:type_name -> moego.models.offering.v1.ServiceType
	84,  // 77: moego.service.enterprise.v1.ListServicesRequest.Filter.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	83,  // 78: moego.service.enterprise.v1.ListServicesRequest.Filter.service_types:type_name -> moego.models.offering.v1.ServiceType
	0,   // 79: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.OrderBy.field:type_name -> moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.OrderBy.Field
	109, // 80: moego.service.enterprise.v1.ListPricingRulesRequest.Filter.rule_types:type_name -> moego.models.offering.v2.RuleType
	84,  // 81: moego.service.enterprise.v1.ListPricingRulesRequest.Filter.service_item_types_contain_any:type_name -> moego.models.offering.v1.ServiceItemType
	110, // 82: moego.service.enterprise.v1.ListServiceChargesRequest.Filter.surcharge_types:type_name -> moego.models.order.v1.SurchargeType
	84,  // 83: moego.service.enterprise.v1.ListServiceChargesRequest.Filter.service_item_types_contain_any:type_name -> moego.models.offering.v1.ServiceItemType
	1,   // 84: moego.service.enterprise.v1.PriceBookService.ListPriceBooks:input_type -> moego.service.enterprise.v1.ListPriceBooksRequest
	3,   // 85: moego.service.enterprise.v1.PriceBookService.CreatePriceBook:input_type -> moego.service.enterprise.v1.CreatePriceBookRequest
	7,   // 86: moego.service.enterprise.v1.PriceBookService.UpdatePriceBook:input_type -> moego.service.enterprise.v1.UpdatePriceBookRequest
	9,   // 87: moego.service.enterprise.v1.PriceBookService.DeletePriceBook:input_type -> moego.service.enterprise.v1.DeletePriceBookRequest
	11,  // 88: moego.service.enterprise.v1.PriceBookService.DuplicatePriceBook:input_type -> moego.service.enterprise.v1.DuplicatePriceBookRequest
	13,  // 89: moego.service.enterprise.v1.PriceBookService.MigratePriceBook:input_type -> moego.service.enterprise.v1.MigratePriceBookRequest
	5,   // 90: moego.service.enterprise.v1.PriceBookService.InitPriceBook:input_type -> moego.service.enterprise.v1.InitPriceBookRequest
	15,  // 91: moego.service.enterprise.v1.PriceBookService.SaveServiceCategories:input_type -> moego.service.enterprise.v1.SaveServiceCategoriesRequest
	17,  // 92: moego.service.enterprise.v1.PriceBookService.ListServiceCategories:input_type -> moego.service.enterprise.v1.ListServiceCategoriesRequest
	19,  // 93: moego.service.enterprise.v1.PriceBookService.ListPetBreeds:input_type -> moego.service.enterprise.v1.ListPetBreedsRequest
	21,  // 94: moego.service.enterprise.v1.PriceBookService.ListPetTypes:input_type -> moego.service.enterprise.v1.ListPetTypesRequest
	23,  // 95: moego.service.enterprise.v1.PriceBookService.CreateService:input_type -> moego.service.enterprise.v1.CreateServiceRequest
	25,  // 96: moego.service.enterprise.v1.PriceBookService.GetService:input_type -> moego.service.enterprise.v1.GetServiceRequest
	27,  // 97: moego.service.enterprise.v1.PriceBookService.ListServices:input_type -> moego.service.enterprise.v1.ListServicesRequest
	29,  // 98: moego.service.enterprise.v1.PriceBookService.UpdateService:input_type -> moego.service.enterprise.v1.UpdateServiceRequest
	31,  // 99: moego.service.enterprise.v1.PriceBookService.DeleteService:input_type -> moego.service.enterprise.v1.DeleteServiceRequest
	33,  // 100: moego.service.enterprise.v1.PriceBookService.SortServices:input_type -> moego.service.enterprise.v1.SortServicesRequest
	35,  // 101: moego.service.enterprise.v1.PriceBookService.ListServiceChangeHistories:input_type -> moego.service.enterprise.v1.ListServiceChangeHistoriesRequest
	37,  // 102: moego.service.enterprise.v1.PriceBookService.ListServiceChanges:input_type -> moego.service.enterprise.v1.ListServiceChangesRequest
	39,  // 103: moego.service.enterprise.v1.PriceBookService.PushServiceChanges:input_type -> moego.service.enterprise.v1.PushServiceChangesRequest
	41,  // 104: moego.service.enterprise.v1.PriceBookService.CreateEvaluation:input_type -> moego.service.enterprise.v1.CreateEvaluationRequest
	43,  // 105: moego.service.enterprise.v1.PriceBookService.ListEvaluations:input_type -> moego.service.enterprise.v1.ListEvaluationsRequest
	45,  // 106: moego.service.enterprise.v1.PriceBookService.UpdateEvaluation:input_type -> moego.service.enterprise.v1.UpdateEvaluationRequest
	47,  // 107: moego.service.enterprise.v1.PriceBookService.SortEvaluations:input_type -> moego.service.enterprise.v1.SortEvaluationsRequest
	49,  // 108: moego.service.enterprise.v1.PriceBookService.PushEvaluationChanges:input_type -> moego.service.enterprise.v1.PushEvaluationChangesRequest
	51,  // 109: moego.service.enterprise.v1.PriceBookService.CreatePricingRule:input_type -> moego.service.enterprise.v1.CreatePricingRuleRequest
	53,  // 110: moego.service.enterprise.v1.PriceBookService.ListPricingRules:input_type -> moego.service.enterprise.v1.ListPricingRulesRequest
	55,  // 111: moego.service.enterprise.v1.PriceBookService.UpdatePricingRule:input_type -> moego.service.enterprise.v1.UpdatePricingRuleRequest
	57,  // 112: moego.service.enterprise.v1.PriceBookService.SortPricingRules:input_type -> moego.service.enterprise.v1.SortPricingRulesRequest
	59,  // 113: moego.service.enterprise.v1.PriceBookService.PushPricingRuleChanges:input_type -> moego.service.enterprise.v1.PushPricingRuleChangesRequest
	61,  // 114: moego.service.enterprise.v1.PriceBookService.CreateServiceCharge:input_type -> moego.service.enterprise.v1.CreateServiceChargeRequest
	63,  // 115: moego.service.enterprise.v1.PriceBookService.ListServiceCharges:input_type -> moego.service.enterprise.v1.ListServiceChargesRequest
	65,  // 116: moego.service.enterprise.v1.PriceBookService.UpdateServiceCharge:input_type -> moego.service.enterprise.v1.UpdateServiceChargeRequest
	67,  // 117: moego.service.enterprise.v1.PriceBookService.SortServiceCharges:input_type -> moego.service.enterprise.v1.SortServiceChargesRequest
	69,  // 118: moego.service.enterprise.v1.PriceBookService.PushServiceChargeChanges:input_type -> moego.service.enterprise.v1.PushServiceChargeChangesRequest
	2,   // 119: moego.service.enterprise.v1.PriceBookService.ListPriceBooks:output_type -> moego.service.enterprise.v1.ListPriceBooksResponse
	4,   // 120: moego.service.enterprise.v1.PriceBookService.CreatePriceBook:output_type -> moego.service.enterprise.v1.CreatePriceBookResponse
	8,   // 121: moego.service.enterprise.v1.PriceBookService.UpdatePriceBook:output_type -> moego.service.enterprise.v1.UpdatePriceBookResponse
	10,  // 122: moego.service.enterprise.v1.PriceBookService.DeletePriceBook:output_type -> moego.service.enterprise.v1.DeletePriceBookResponse
	12,  // 123: moego.service.enterprise.v1.PriceBookService.DuplicatePriceBook:output_type -> moego.service.enterprise.v1.DuplicatePriceBookResponse
	14,  // 124: moego.service.enterprise.v1.PriceBookService.MigratePriceBook:output_type -> moego.service.enterprise.v1.MigratePriceBookResponse
	6,   // 125: moego.service.enterprise.v1.PriceBookService.InitPriceBook:output_type -> moego.service.enterprise.v1.InitPriceBookResponse
	16,  // 126: moego.service.enterprise.v1.PriceBookService.SaveServiceCategories:output_type -> moego.service.enterprise.v1.SaveServiceCategoriesResponse
	18,  // 127: moego.service.enterprise.v1.PriceBookService.ListServiceCategories:output_type -> moego.service.enterprise.v1.ListServiceCategoriesResponse
	20,  // 128: moego.service.enterprise.v1.PriceBookService.ListPetBreeds:output_type -> moego.service.enterprise.v1.ListPetBreedsResponse
	22,  // 129: moego.service.enterprise.v1.PriceBookService.ListPetTypes:output_type -> moego.service.enterprise.v1.ListPetTypesResponse
	24,  // 130: moego.service.enterprise.v1.PriceBookService.CreateService:output_type -> moego.service.enterprise.v1.CreateServiceResponse
	26,  // 131: moego.service.enterprise.v1.PriceBookService.GetService:output_type -> moego.service.enterprise.v1.GetServiceResponse
	28,  // 132: moego.service.enterprise.v1.PriceBookService.ListServices:output_type -> moego.service.enterprise.v1.ListServicesResponse
	30,  // 133: moego.service.enterprise.v1.PriceBookService.UpdateService:output_type -> moego.service.enterprise.v1.UpdateServiceResponse
	32,  // 134: moego.service.enterprise.v1.PriceBookService.DeleteService:output_type -> moego.service.enterprise.v1.DeleteServiceResponse
	34,  // 135: moego.service.enterprise.v1.PriceBookService.SortServices:output_type -> moego.service.enterprise.v1.SortServicesResponse
	36,  // 136: moego.service.enterprise.v1.PriceBookService.ListServiceChangeHistories:output_type -> moego.service.enterprise.v1.ListServiceChangeHistoriesResponse
	38,  // 137: moego.service.enterprise.v1.PriceBookService.ListServiceChanges:output_type -> moego.service.enterprise.v1.ListServiceChangesResponse
	40,  // 138: moego.service.enterprise.v1.PriceBookService.PushServiceChanges:output_type -> moego.service.enterprise.v1.PushServiceChangesResponse
	42,  // 139: moego.service.enterprise.v1.PriceBookService.CreateEvaluation:output_type -> moego.service.enterprise.v1.CreateEvaluationResponse
	44,  // 140: moego.service.enterprise.v1.PriceBookService.ListEvaluations:output_type -> moego.service.enterprise.v1.ListEvaluationsResponse
	46,  // 141: moego.service.enterprise.v1.PriceBookService.UpdateEvaluation:output_type -> moego.service.enterprise.v1.UpdateEvaluationResponse
	48,  // 142: moego.service.enterprise.v1.PriceBookService.SortEvaluations:output_type -> moego.service.enterprise.v1.SortEvaluationsResponse
	50,  // 143: moego.service.enterprise.v1.PriceBookService.PushEvaluationChanges:output_type -> moego.service.enterprise.v1.PushEvaluationChangesResponse
	52,  // 144: moego.service.enterprise.v1.PriceBookService.CreatePricingRule:output_type -> moego.service.enterprise.v1.CreatePricingRuleResponse
	54,  // 145: moego.service.enterprise.v1.PriceBookService.ListPricingRules:output_type -> moego.service.enterprise.v1.ListPricingRulesResponse
	56,  // 146: moego.service.enterprise.v1.PriceBookService.UpdatePricingRule:output_type -> moego.service.enterprise.v1.UpdatePricingRuleResponse
	58,  // 147: moego.service.enterprise.v1.PriceBookService.SortPricingRules:output_type -> moego.service.enterprise.v1.SortPricingRulesResponse
	60,  // 148: moego.service.enterprise.v1.PriceBookService.PushPricingRuleChanges:output_type -> moego.service.enterprise.v1.PushPricingRuleChangesResponse
	62,  // 149: moego.service.enterprise.v1.PriceBookService.CreateServiceCharge:output_type -> moego.service.enterprise.v1.CreateServiceChargeResponse
	64,  // 150: moego.service.enterprise.v1.PriceBookService.ListServiceCharges:output_type -> moego.service.enterprise.v1.ListServiceChargesResponse
	66,  // 151: moego.service.enterprise.v1.PriceBookService.UpdateServiceCharge:output_type -> moego.service.enterprise.v1.UpdateServiceChargeResponse
	68,  // 152: moego.service.enterprise.v1.PriceBookService.SortServiceCharges:output_type -> moego.service.enterprise.v1.SortServiceChargesResponse
	70,  // 153: moego.service.enterprise.v1.PriceBookService.PushServiceChargeChanges:output_type -> moego.service.enterprise.v1.PushServiceChargeChangesResponse
	119, // [119:154] is the sub-list for method output_type
	84,  // [84:119] is the sub-list for method input_type
	84,  // [84:84] is the sub-list for extension type_name
	84,  // [84:84] is the sub-list for extension extendee
	0,   // [0:84] is the sub-list for field type_name
}

func init() { file_moego_service_enterprise_v1_price_book_service_proto_init() }
func file_moego_service_enterprise_v1_price_book_service_proto_init() {
	if File_moego_service_enterprise_v1_price_book_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPriceBooksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPriceBooksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePriceBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePriceBookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitPriceBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitPriceBookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePriceBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePriceBookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePriceBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePriceBookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicatePriceBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicatePriceBookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigratePriceBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigratePriceBookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveServiceCategoriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveServiceCategoriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceCategoriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceCategoriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBreedsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBreedsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetTypesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetTypesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServicesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteServiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortServicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortServicesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangeHistoriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangeHistoriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushServiceChangesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushServiceChangesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortEvaluationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortEvaluationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushEvaluationChangesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushEvaluationChangesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPricingRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPricingRulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPricingRuleChangesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPricingRuleChangesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceChargeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChargesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChargesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceChargeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortServiceChargesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortServiceChargesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushServiceChargeChangesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushServiceChargeChangesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPriceBooksRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceCategoriesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServicesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortServicesRequest_ServiceCategorySort); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangeHistoriesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangeHistoriesRequest_OrderBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChargesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[28].OneofWrappers = []interface{}{}
	file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[72].OneofWrappers = []interface{}{}
	file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[77].OneofWrappers = []interface{}{}
	file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[78].OneofWrappers = []interface{}{}
	file_moego_service_enterprise_v1_price_book_service_proto_msgTypes[79].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_enterprise_v1_price_book_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   80,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_enterprise_v1_price_book_service_proto_goTypes,
		DependencyIndexes: file_moego_service_enterprise_v1_price_book_service_proto_depIdxs,
		EnumInfos:         file_moego_service_enterprise_v1_price_book_service_proto_enumTypes,
		MessageInfos:      file_moego_service_enterprise_v1_price_book_service_proto_msgTypes,
	}.Build()
	File_moego_service_enterprise_v1_price_book_service_proto = out.File
	file_moego_service_enterprise_v1_price_book_service_proto_rawDesc = nil
	file_moego_service_enterprise_v1_price_book_service_proto_goTypes = nil
	file_moego_service_enterprise_v1_price_book_service_proto_depIdxs = nil
}
