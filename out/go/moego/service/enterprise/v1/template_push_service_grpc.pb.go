// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/enterprise/v1/template_push_service.proto

package enterprisesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TemplatePushServiceClient is the client API for TemplatePushService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TemplatePushServiceClient interface {
	// ListTemplatePushMappings
	ListTemplatePushMappings(ctx context.Context, in *ListTemplatePushMappingsRequest, opts ...grpc.CallOption) (*ListTemplatePushMappingsResponse, error)
	// UpsertTemplatePushMapping
	UpsertTemplatePushMapping(ctx context.Context, in *UpsertTemplatePushMappingRequest, opts ...grpc.CallOption) (*UpsertTemplatePushMappingResponse, error)
	// list template push setting
	ListTemplatePushSettings(ctx context.Context, in *ListTemplatePushSettingsRequest, opts ...grpc.CallOption) (*ListTemplatePushSettingsResponse, error)
	// UpsertTemplatePushSetting
	UpsertTemplatePushSetting(ctx context.Context, in *UpsertTemplatePushSettingRequest, opts ...grpc.CallOption) (*UpsertTemplatePushSettingResponse, error)
	// list template push changes
	ListTemplatePushChanges(ctx context.Context, in *ListTemplatePushChangesRequest, opts ...grpc.CallOption) (*ListTemplatePushChangesResponse, error)
	// list template push histories
	ListTemplatePushHistories(ctx context.Context, in *ListTemplatePushHistoriesRequest, opts ...grpc.CallOption) (*ListTemplatePushHistoriesResponse, error)
	// check template push
	CheckTemplatePush(ctx context.Context, in *CheckTemplatePushRequest, opts ...grpc.CallOption) (*CheckTemplatePushResponse, error)
}

type templatePushServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTemplatePushServiceClient(cc grpc.ClientConnInterface) TemplatePushServiceClient {
	return &templatePushServiceClient{cc}
}

func (c *templatePushServiceClient) ListTemplatePushMappings(ctx context.Context, in *ListTemplatePushMappingsRequest, opts ...grpc.CallOption) (*ListTemplatePushMappingsResponse, error) {
	out := new(ListTemplatePushMappingsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TemplatePushService/ListTemplatePushMappings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templatePushServiceClient) UpsertTemplatePushMapping(ctx context.Context, in *UpsertTemplatePushMappingRequest, opts ...grpc.CallOption) (*UpsertTemplatePushMappingResponse, error) {
	out := new(UpsertTemplatePushMappingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TemplatePushService/UpsertTemplatePushMapping", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templatePushServiceClient) ListTemplatePushSettings(ctx context.Context, in *ListTemplatePushSettingsRequest, opts ...grpc.CallOption) (*ListTemplatePushSettingsResponse, error) {
	out := new(ListTemplatePushSettingsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TemplatePushService/ListTemplatePushSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templatePushServiceClient) UpsertTemplatePushSetting(ctx context.Context, in *UpsertTemplatePushSettingRequest, opts ...grpc.CallOption) (*UpsertTemplatePushSettingResponse, error) {
	out := new(UpsertTemplatePushSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TemplatePushService/UpsertTemplatePushSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templatePushServiceClient) ListTemplatePushChanges(ctx context.Context, in *ListTemplatePushChangesRequest, opts ...grpc.CallOption) (*ListTemplatePushChangesResponse, error) {
	out := new(ListTemplatePushChangesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TemplatePushService/ListTemplatePushChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templatePushServiceClient) ListTemplatePushHistories(ctx context.Context, in *ListTemplatePushHistoriesRequest, opts ...grpc.CallOption) (*ListTemplatePushHistoriesResponse, error) {
	out := new(ListTemplatePushHistoriesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TemplatePushService/ListTemplatePushHistories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templatePushServiceClient) CheckTemplatePush(ctx context.Context, in *CheckTemplatePushRequest, opts ...grpc.CallOption) (*CheckTemplatePushResponse, error) {
	out := new(CheckTemplatePushResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TemplatePushService/CheckTemplatePush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TemplatePushServiceServer is the server API for TemplatePushService service.
// All implementations must embed UnimplementedTemplatePushServiceServer
// for forward compatibility
type TemplatePushServiceServer interface {
	// ListTemplatePushMappings
	ListTemplatePushMappings(context.Context, *ListTemplatePushMappingsRequest) (*ListTemplatePushMappingsResponse, error)
	// UpsertTemplatePushMapping
	UpsertTemplatePushMapping(context.Context, *UpsertTemplatePushMappingRequest) (*UpsertTemplatePushMappingResponse, error)
	// list template push setting
	ListTemplatePushSettings(context.Context, *ListTemplatePushSettingsRequest) (*ListTemplatePushSettingsResponse, error)
	// UpsertTemplatePushSetting
	UpsertTemplatePushSetting(context.Context, *UpsertTemplatePushSettingRequest) (*UpsertTemplatePushSettingResponse, error)
	// list template push changes
	ListTemplatePushChanges(context.Context, *ListTemplatePushChangesRequest) (*ListTemplatePushChangesResponse, error)
	// list template push histories
	ListTemplatePushHistories(context.Context, *ListTemplatePushHistoriesRequest) (*ListTemplatePushHistoriesResponse, error)
	// check template push
	CheckTemplatePush(context.Context, *CheckTemplatePushRequest) (*CheckTemplatePushResponse, error)
	mustEmbedUnimplementedTemplatePushServiceServer()
}

// UnimplementedTemplatePushServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTemplatePushServiceServer struct {
}

func (UnimplementedTemplatePushServiceServer) ListTemplatePushMappings(context.Context, *ListTemplatePushMappingsRequest) (*ListTemplatePushMappingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplatePushMappings not implemented")
}
func (UnimplementedTemplatePushServiceServer) UpsertTemplatePushMapping(context.Context, *UpsertTemplatePushMappingRequest) (*UpsertTemplatePushMappingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertTemplatePushMapping not implemented")
}
func (UnimplementedTemplatePushServiceServer) ListTemplatePushSettings(context.Context, *ListTemplatePushSettingsRequest) (*ListTemplatePushSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplatePushSettings not implemented")
}
func (UnimplementedTemplatePushServiceServer) UpsertTemplatePushSetting(context.Context, *UpsertTemplatePushSettingRequest) (*UpsertTemplatePushSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertTemplatePushSetting not implemented")
}
func (UnimplementedTemplatePushServiceServer) ListTemplatePushChanges(context.Context, *ListTemplatePushChangesRequest) (*ListTemplatePushChangesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplatePushChanges not implemented")
}
func (UnimplementedTemplatePushServiceServer) ListTemplatePushHistories(context.Context, *ListTemplatePushHistoriesRequest) (*ListTemplatePushHistoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplatePushHistories not implemented")
}
func (UnimplementedTemplatePushServiceServer) CheckTemplatePush(context.Context, *CheckTemplatePushRequest) (*CheckTemplatePushResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTemplatePush not implemented")
}
func (UnimplementedTemplatePushServiceServer) mustEmbedUnimplementedTemplatePushServiceServer() {}

// UnsafeTemplatePushServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TemplatePushServiceServer will
// result in compilation errors.
type UnsafeTemplatePushServiceServer interface {
	mustEmbedUnimplementedTemplatePushServiceServer()
}

func RegisterTemplatePushServiceServer(s grpc.ServiceRegistrar, srv TemplatePushServiceServer) {
	s.RegisterService(&TemplatePushService_ServiceDesc, srv)
}

func _TemplatePushService_ListTemplatePushMappings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatePushMappingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplatePushServiceServer).ListTemplatePushMappings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TemplatePushService/ListTemplatePushMappings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplatePushServiceServer).ListTemplatePushMappings(ctx, req.(*ListTemplatePushMappingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplatePushService_UpsertTemplatePushMapping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertTemplatePushMappingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplatePushServiceServer).UpsertTemplatePushMapping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TemplatePushService/UpsertTemplatePushMapping",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplatePushServiceServer).UpsertTemplatePushMapping(ctx, req.(*UpsertTemplatePushMappingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplatePushService_ListTemplatePushSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatePushSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplatePushServiceServer).ListTemplatePushSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TemplatePushService/ListTemplatePushSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplatePushServiceServer).ListTemplatePushSettings(ctx, req.(*ListTemplatePushSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplatePushService_UpsertTemplatePushSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertTemplatePushSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplatePushServiceServer).UpsertTemplatePushSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TemplatePushService/UpsertTemplatePushSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplatePushServiceServer).UpsertTemplatePushSetting(ctx, req.(*UpsertTemplatePushSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplatePushService_ListTemplatePushChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatePushChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplatePushServiceServer).ListTemplatePushChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TemplatePushService/ListTemplatePushChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplatePushServiceServer).ListTemplatePushChanges(ctx, req.(*ListTemplatePushChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplatePushService_ListTemplatePushHistories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatePushHistoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplatePushServiceServer).ListTemplatePushHistories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TemplatePushService/ListTemplatePushHistories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplatePushServiceServer).ListTemplatePushHistories(ctx, req.(*ListTemplatePushHistoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplatePushService_CheckTemplatePush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTemplatePushRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplatePushServiceServer).CheckTemplatePush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TemplatePushService/CheckTemplatePush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplatePushServiceServer).CheckTemplatePush(ctx, req.(*CheckTemplatePushRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TemplatePushService_ServiceDesc is the grpc.ServiceDesc for TemplatePushService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TemplatePushService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.enterprise.v1.TemplatePushService",
	HandlerType: (*TemplatePushServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListTemplatePushMappings",
			Handler:    _TemplatePushService_ListTemplatePushMappings_Handler,
		},
		{
			MethodName: "UpsertTemplatePushMapping",
			Handler:    _TemplatePushService_UpsertTemplatePushMapping_Handler,
		},
		{
			MethodName: "ListTemplatePushSettings",
			Handler:    _TemplatePushService_ListTemplatePushSettings_Handler,
		},
		{
			MethodName: "UpsertTemplatePushSetting",
			Handler:    _TemplatePushService_UpsertTemplatePushSetting_Handler,
		},
		{
			MethodName: "ListTemplatePushChanges",
			Handler:    _TemplatePushService_ListTemplatePushChanges_Handler,
		},
		{
			MethodName: "ListTemplatePushHistories",
			Handler:    _TemplatePushService_ListTemplatePushHistories_Handler,
		},
		{
			MethodName: "CheckTemplatePush",
			Handler:    _TemplatePushService_CheckTemplatePush_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/enterprise/v1/template_push_service.proto",
}
