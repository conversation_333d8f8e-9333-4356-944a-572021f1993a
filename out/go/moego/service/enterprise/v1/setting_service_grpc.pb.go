// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/enterprise/v1/setting_service.proto

package enterprisesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SettingServiceClient is the client API for SettingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SettingServiceClient interface {
	// copy intake form
	CopyIntakeForms(ctx context.Context, in *CopyIntakeFormsRequest, opts ...grpc.CallOption) (*CopyIntakeFormsResponse, error)
	// copy discount code
	CopyDiscountCodes(ctx context.Context, in *CopyDiscountCodesRequest, opts ...grpc.CallOption) (*CopyDiscountCodesResponse, error)
	// copy roles
	CopyRoles(ctx context.Context, in *CopyRolesRequest, opts ...grpc.CallOption) (*CopyRolesResponse, error)
	// copy packages
	CopyPackages(ctx context.Context, in *CopyPackagesRequest, opts ...grpc.CallOption) (*CopyPackagesResponse, error)
	// copy membership
	CopyMemberships(ctx context.Context, in *CopyMembershipsRequest, opts ...grpc.CallOption) (*CopyMembershipsResponse, error)
	// copy product
	CopyProducts(ctx context.Context, in *CopyProductsRequest, opts ...grpc.CallOption) (*CopyProductsResponse, error)
	// create lodging type
	CreateLodgingType(ctx context.Context, in *CreateLodgingTypeRequest, opts ...grpc.CallOption) (*CreateLodgingTypeResponse, error)
	// update lodging type
	UpdateLodgingType(ctx context.Context, in *UpdateLodgingTypeRequest, opts ...grpc.CallOption) (*UpdateLodgingTypeResponse, error)
	// delete lodging type
	DeleteLodgingType(ctx context.Context, in *DeleteLodgingTypeRequest, opts ...grpc.CallOption) (*DeleteLodgingTypeResponse, error)
	// sort lodging types
	SortLodgingTypes(ctx context.Context, in *SortLodgingTypesRequest, opts ...grpc.CallOption) (*SortLodgingTypesResponse, error)
	// list lodging types
	ListLodgingTypes(ctx context.Context, in *ListLodgingTypesRequest, opts ...grpc.CallOption) (*ListLodgingTypesResponse, error)
	// push lodging types
	PushLodgingTypes(ctx context.Context, in *PushLodgingTypesRequest, opts ...grpc.CallOption) (*PushLodgingTypesResponse, error)
	// init lodging types
	InitLodgingTypes(ctx context.Context, in *InitLodgingTypesRequest, opts ...grpc.CallOption) (*InitLodgingTypesResponse, error)
	// List pet codes.
	ListPetCodes(ctx context.Context, in *ListPetCodesRequest, opts ...grpc.CallOption) (*ListPetCodesResponse, error)
	// Create a pet code.
	CreatePetCode(ctx context.Context, in *CreatePetCodeRequest, opts ...grpc.CallOption) (*CreatePetCodeResponse, error)
	// Update a pet code.
	UpdatePetCode(ctx context.Context, in *UpdatePetCodeRequest, opts ...grpc.CallOption) (*UpdatePetCodeResponse, error)
	// Sort pet codes
	SortPetCodes(ctx context.Context, in *SortPetCodesRequest, opts ...grpc.CallOption) (*SortPetCodesResponse, error)
	// Delete a pet code.
	DeletePetCode(ctx context.Context, in *DeletePetCodeRequest, opts ...grpc.CallOption) (*DeletePetCodeResponse, error)
	// push pet codes
	PushPetCodes(ctx context.Context, in *PushPetCodesRequest, opts ...grpc.CallOption) (*PushPetCodesResponse, error)
	// init pet codes
	InitPetCodes(ctx context.Context, in *InitPetCodesRequest, opts ...grpc.CallOption) (*InitPetCodesResponse, error)
	// create pet metadata
	CreatePetMetadata(ctx context.Context, in *CreatePetMetadataRequest, opts ...grpc.CallOption) (*CreatePetMetadataResponse, error)
	// update pet metadata
	UpdatePetMetadata(ctx context.Context, in *UpdatePetMetadataRequest, opts ...grpc.CallOption) (*UpdatePetMetadataResponse, error)
	// delete pet metadata
	DeletePetMetadata(ctx context.Context, in *DeletePetMetadataRequest, opts ...grpc.CallOption) (*DeletePetMetadataResponse, error)
	// list pet metadata
	ListPetMetadata(ctx context.Context, in *ListPetMetadataRequest, opts ...grpc.CallOption) (*ListPetMetadataResponse, error)
	// push pet metadata
	PushPetMetadata(ctx context.Context, in *PushPetMetadataRequest, opts ...grpc.CallOption) (*PushPetMetadataResponse, error)
	// init pet metadata
	InitPetMetadata(ctx context.Context, in *InitPetMetadataRequest, opts ...grpc.CallOption) (*InitPetMetadataResponse, error)
	// sort pet metadata
	SortPetMetadata(ctx context.Context, in *SortPetMetadataRequest, opts ...grpc.CallOption) (*SortPetMetadataResponse, error)
	// list associated food source
	ListSurchargeAssociatedFoodSource(ctx context.Context, in *ListSurchargeAssociatedFoodSourceRequest, opts ...grpc.CallOption) (*ListSurchargeAssociatedFoodSourceResponse, error)
}

type settingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSettingServiceClient(cc grpc.ClientConnInterface) SettingServiceClient {
	return &settingServiceClient{cc}
}

func (c *settingServiceClient) CopyIntakeForms(ctx context.Context, in *CopyIntakeFormsRequest, opts ...grpc.CallOption) (*CopyIntakeFormsResponse, error) {
	out := new(CopyIntakeFormsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyIntakeForms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CopyDiscountCodes(ctx context.Context, in *CopyDiscountCodesRequest, opts ...grpc.CallOption) (*CopyDiscountCodesResponse, error) {
	out := new(CopyDiscountCodesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyDiscountCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CopyRoles(ctx context.Context, in *CopyRolesRequest, opts ...grpc.CallOption) (*CopyRolesResponse, error) {
	out := new(CopyRolesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyRoles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CopyPackages(ctx context.Context, in *CopyPackagesRequest, opts ...grpc.CallOption) (*CopyPackagesResponse, error) {
	out := new(CopyPackagesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyPackages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CopyMemberships(ctx context.Context, in *CopyMembershipsRequest, opts ...grpc.CallOption) (*CopyMembershipsResponse, error) {
	out := new(CopyMembershipsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CopyProducts(ctx context.Context, in *CopyProductsRequest, opts ...grpc.CallOption) (*CopyProductsResponse, error) {
	out := new(CopyProductsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyProducts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CreateLodgingType(ctx context.Context, in *CreateLodgingTypeRequest, opts ...grpc.CallOption) (*CreateLodgingTypeResponse, error) {
	out := new(CreateLodgingTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CreateLodgingType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) UpdateLodgingType(ctx context.Context, in *UpdateLodgingTypeRequest, opts ...grpc.CallOption) (*UpdateLodgingTypeResponse, error) {
	out := new(UpdateLodgingTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/UpdateLodgingType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) DeleteLodgingType(ctx context.Context, in *DeleteLodgingTypeRequest, opts ...grpc.CallOption) (*DeleteLodgingTypeResponse, error) {
	out := new(DeleteLodgingTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/DeleteLodgingType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) SortLodgingTypes(ctx context.Context, in *SortLodgingTypesRequest, opts ...grpc.CallOption) (*SortLodgingTypesResponse, error) {
	out := new(SortLodgingTypesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/SortLodgingTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) ListLodgingTypes(ctx context.Context, in *ListLodgingTypesRequest, opts ...grpc.CallOption) (*ListLodgingTypesResponse, error) {
	out := new(ListLodgingTypesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/ListLodgingTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) PushLodgingTypes(ctx context.Context, in *PushLodgingTypesRequest, opts ...grpc.CallOption) (*PushLodgingTypesResponse, error) {
	out := new(PushLodgingTypesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/PushLodgingTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) InitLodgingTypes(ctx context.Context, in *InitLodgingTypesRequest, opts ...grpc.CallOption) (*InitLodgingTypesResponse, error) {
	out := new(InitLodgingTypesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/InitLodgingTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) ListPetCodes(ctx context.Context, in *ListPetCodesRequest, opts ...grpc.CallOption) (*ListPetCodesResponse, error) {
	out := new(ListPetCodesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/ListPetCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CreatePetCode(ctx context.Context, in *CreatePetCodeRequest, opts ...grpc.CallOption) (*CreatePetCodeResponse, error) {
	out := new(CreatePetCodeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CreatePetCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) UpdatePetCode(ctx context.Context, in *UpdatePetCodeRequest, opts ...grpc.CallOption) (*UpdatePetCodeResponse, error) {
	out := new(UpdatePetCodeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/UpdatePetCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) SortPetCodes(ctx context.Context, in *SortPetCodesRequest, opts ...grpc.CallOption) (*SortPetCodesResponse, error) {
	out := new(SortPetCodesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/SortPetCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) DeletePetCode(ctx context.Context, in *DeletePetCodeRequest, opts ...grpc.CallOption) (*DeletePetCodeResponse, error) {
	out := new(DeletePetCodeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/DeletePetCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) PushPetCodes(ctx context.Context, in *PushPetCodesRequest, opts ...grpc.CallOption) (*PushPetCodesResponse, error) {
	out := new(PushPetCodesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/PushPetCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) InitPetCodes(ctx context.Context, in *InitPetCodesRequest, opts ...grpc.CallOption) (*InitPetCodesResponse, error) {
	out := new(InitPetCodesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/InitPetCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CreatePetMetadata(ctx context.Context, in *CreatePetMetadataRequest, opts ...grpc.CallOption) (*CreatePetMetadataResponse, error) {
	out := new(CreatePetMetadataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CreatePetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) UpdatePetMetadata(ctx context.Context, in *UpdatePetMetadataRequest, opts ...grpc.CallOption) (*UpdatePetMetadataResponse, error) {
	out := new(UpdatePetMetadataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/UpdatePetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) DeletePetMetadata(ctx context.Context, in *DeletePetMetadataRequest, opts ...grpc.CallOption) (*DeletePetMetadataResponse, error) {
	out := new(DeletePetMetadataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/DeletePetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) ListPetMetadata(ctx context.Context, in *ListPetMetadataRequest, opts ...grpc.CallOption) (*ListPetMetadataResponse, error) {
	out := new(ListPetMetadataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/ListPetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) PushPetMetadata(ctx context.Context, in *PushPetMetadataRequest, opts ...grpc.CallOption) (*PushPetMetadataResponse, error) {
	out := new(PushPetMetadataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/PushPetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) InitPetMetadata(ctx context.Context, in *InitPetMetadataRequest, opts ...grpc.CallOption) (*InitPetMetadataResponse, error) {
	out := new(InitPetMetadataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/InitPetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) SortPetMetadata(ctx context.Context, in *SortPetMetadataRequest, opts ...grpc.CallOption) (*SortPetMetadataResponse, error) {
	out := new(SortPetMetadataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/SortPetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) ListSurchargeAssociatedFoodSource(ctx context.Context, in *ListSurchargeAssociatedFoodSourceRequest, opts ...grpc.CallOption) (*ListSurchargeAssociatedFoodSourceResponse, error) {
	out := new(ListSurchargeAssociatedFoodSourceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/ListSurchargeAssociatedFoodSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SettingServiceServer is the server API for SettingService service.
// All implementations must embed UnimplementedSettingServiceServer
// for forward compatibility
type SettingServiceServer interface {
	// copy intake form
	CopyIntakeForms(context.Context, *CopyIntakeFormsRequest) (*CopyIntakeFormsResponse, error)
	// copy discount code
	CopyDiscountCodes(context.Context, *CopyDiscountCodesRequest) (*CopyDiscountCodesResponse, error)
	// copy roles
	CopyRoles(context.Context, *CopyRolesRequest) (*CopyRolesResponse, error)
	// copy packages
	CopyPackages(context.Context, *CopyPackagesRequest) (*CopyPackagesResponse, error)
	// copy membership
	CopyMemberships(context.Context, *CopyMembershipsRequest) (*CopyMembershipsResponse, error)
	// copy product
	CopyProducts(context.Context, *CopyProductsRequest) (*CopyProductsResponse, error)
	// create lodging type
	CreateLodgingType(context.Context, *CreateLodgingTypeRequest) (*CreateLodgingTypeResponse, error)
	// update lodging type
	UpdateLodgingType(context.Context, *UpdateLodgingTypeRequest) (*UpdateLodgingTypeResponse, error)
	// delete lodging type
	DeleteLodgingType(context.Context, *DeleteLodgingTypeRequest) (*DeleteLodgingTypeResponse, error)
	// sort lodging types
	SortLodgingTypes(context.Context, *SortLodgingTypesRequest) (*SortLodgingTypesResponse, error)
	// list lodging types
	ListLodgingTypes(context.Context, *ListLodgingTypesRequest) (*ListLodgingTypesResponse, error)
	// push lodging types
	PushLodgingTypes(context.Context, *PushLodgingTypesRequest) (*PushLodgingTypesResponse, error)
	// init lodging types
	InitLodgingTypes(context.Context, *InitLodgingTypesRequest) (*InitLodgingTypesResponse, error)
	// List pet codes.
	ListPetCodes(context.Context, *ListPetCodesRequest) (*ListPetCodesResponse, error)
	// Create a pet code.
	CreatePetCode(context.Context, *CreatePetCodeRequest) (*CreatePetCodeResponse, error)
	// Update a pet code.
	UpdatePetCode(context.Context, *UpdatePetCodeRequest) (*UpdatePetCodeResponse, error)
	// Sort pet codes
	SortPetCodes(context.Context, *SortPetCodesRequest) (*SortPetCodesResponse, error)
	// Delete a pet code.
	DeletePetCode(context.Context, *DeletePetCodeRequest) (*DeletePetCodeResponse, error)
	// push pet codes
	PushPetCodes(context.Context, *PushPetCodesRequest) (*PushPetCodesResponse, error)
	// init pet codes
	InitPetCodes(context.Context, *InitPetCodesRequest) (*InitPetCodesResponse, error)
	// create pet metadata
	CreatePetMetadata(context.Context, *CreatePetMetadataRequest) (*CreatePetMetadataResponse, error)
	// update pet metadata
	UpdatePetMetadata(context.Context, *UpdatePetMetadataRequest) (*UpdatePetMetadataResponse, error)
	// delete pet metadata
	DeletePetMetadata(context.Context, *DeletePetMetadataRequest) (*DeletePetMetadataResponse, error)
	// list pet metadata
	ListPetMetadata(context.Context, *ListPetMetadataRequest) (*ListPetMetadataResponse, error)
	// push pet metadata
	PushPetMetadata(context.Context, *PushPetMetadataRequest) (*PushPetMetadataResponse, error)
	// init pet metadata
	InitPetMetadata(context.Context, *InitPetMetadataRequest) (*InitPetMetadataResponse, error)
	// sort pet metadata
	SortPetMetadata(context.Context, *SortPetMetadataRequest) (*SortPetMetadataResponse, error)
	// list associated food source
	ListSurchargeAssociatedFoodSource(context.Context, *ListSurchargeAssociatedFoodSourceRequest) (*ListSurchargeAssociatedFoodSourceResponse, error)
	mustEmbedUnimplementedSettingServiceServer()
}

// UnimplementedSettingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSettingServiceServer struct {
}

func (UnimplementedSettingServiceServer) CopyIntakeForms(context.Context, *CopyIntakeFormsRequest) (*CopyIntakeFormsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyIntakeForms not implemented")
}
func (UnimplementedSettingServiceServer) CopyDiscountCodes(context.Context, *CopyDiscountCodesRequest) (*CopyDiscountCodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyDiscountCodes not implemented")
}
func (UnimplementedSettingServiceServer) CopyRoles(context.Context, *CopyRolesRequest) (*CopyRolesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyRoles not implemented")
}
func (UnimplementedSettingServiceServer) CopyPackages(context.Context, *CopyPackagesRequest) (*CopyPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyPackages not implemented")
}
func (UnimplementedSettingServiceServer) CopyMemberships(context.Context, *CopyMembershipsRequest) (*CopyMembershipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyMemberships not implemented")
}
func (UnimplementedSettingServiceServer) CopyProducts(context.Context, *CopyProductsRequest) (*CopyProductsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyProducts not implemented")
}
func (UnimplementedSettingServiceServer) CreateLodgingType(context.Context, *CreateLodgingTypeRequest) (*CreateLodgingTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLodgingType not implemented")
}
func (UnimplementedSettingServiceServer) UpdateLodgingType(context.Context, *UpdateLodgingTypeRequest) (*UpdateLodgingTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLodgingType not implemented")
}
func (UnimplementedSettingServiceServer) DeleteLodgingType(context.Context, *DeleteLodgingTypeRequest) (*DeleteLodgingTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLodgingType not implemented")
}
func (UnimplementedSettingServiceServer) SortLodgingTypes(context.Context, *SortLodgingTypesRequest) (*SortLodgingTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortLodgingTypes not implemented")
}
func (UnimplementedSettingServiceServer) ListLodgingTypes(context.Context, *ListLodgingTypesRequest) (*ListLodgingTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLodgingTypes not implemented")
}
func (UnimplementedSettingServiceServer) PushLodgingTypes(context.Context, *PushLodgingTypesRequest) (*PushLodgingTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushLodgingTypes not implemented")
}
func (UnimplementedSettingServiceServer) InitLodgingTypes(context.Context, *InitLodgingTypesRequest) (*InitLodgingTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitLodgingTypes not implemented")
}
func (UnimplementedSettingServiceServer) ListPetCodes(context.Context, *ListPetCodesRequest) (*ListPetCodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetCodes not implemented")
}
func (UnimplementedSettingServiceServer) CreatePetCode(context.Context, *CreatePetCodeRequest) (*CreatePetCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetCode not implemented")
}
func (UnimplementedSettingServiceServer) UpdatePetCode(context.Context, *UpdatePetCodeRequest) (*UpdatePetCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetCode not implemented")
}
func (UnimplementedSettingServiceServer) SortPetCodes(context.Context, *SortPetCodesRequest) (*SortPetCodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPetCodes not implemented")
}
func (UnimplementedSettingServiceServer) DeletePetCode(context.Context, *DeletePetCodeRequest) (*DeletePetCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetCode not implemented")
}
func (UnimplementedSettingServiceServer) PushPetCodes(context.Context, *PushPetCodesRequest) (*PushPetCodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushPetCodes not implemented")
}
func (UnimplementedSettingServiceServer) InitPetCodes(context.Context, *InitPetCodesRequest) (*InitPetCodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitPetCodes not implemented")
}
func (UnimplementedSettingServiceServer) CreatePetMetadata(context.Context, *CreatePetMetadataRequest) (*CreatePetMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetMetadata not implemented")
}
func (UnimplementedSettingServiceServer) UpdatePetMetadata(context.Context, *UpdatePetMetadataRequest) (*UpdatePetMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetMetadata not implemented")
}
func (UnimplementedSettingServiceServer) DeletePetMetadata(context.Context, *DeletePetMetadataRequest) (*DeletePetMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetMetadata not implemented")
}
func (UnimplementedSettingServiceServer) ListPetMetadata(context.Context, *ListPetMetadataRequest) (*ListPetMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetMetadata not implemented")
}
func (UnimplementedSettingServiceServer) PushPetMetadata(context.Context, *PushPetMetadataRequest) (*PushPetMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushPetMetadata not implemented")
}
func (UnimplementedSettingServiceServer) InitPetMetadata(context.Context, *InitPetMetadataRequest) (*InitPetMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitPetMetadata not implemented")
}
func (UnimplementedSettingServiceServer) SortPetMetadata(context.Context, *SortPetMetadataRequest) (*SortPetMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPetMetadata not implemented")
}
func (UnimplementedSettingServiceServer) ListSurchargeAssociatedFoodSource(context.Context, *ListSurchargeAssociatedFoodSourceRequest) (*ListSurchargeAssociatedFoodSourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSurchargeAssociatedFoodSource not implemented")
}
func (UnimplementedSettingServiceServer) mustEmbedUnimplementedSettingServiceServer() {}

// UnsafeSettingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SettingServiceServer will
// result in compilation errors.
type UnsafeSettingServiceServer interface {
	mustEmbedUnimplementedSettingServiceServer()
}

func RegisterSettingServiceServer(s grpc.ServiceRegistrar, srv SettingServiceServer) {
	s.RegisterService(&SettingService_ServiceDesc, srv)
}

func _SettingService_CopyIntakeForms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyIntakeFormsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyIntakeForms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyIntakeForms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyIntakeForms(ctx, req.(*CopyIntakeFormsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CopyDiscountCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyDiscountCodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyDiscountCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyDiscountCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyDiscountCodes(ctx, req.(*CopyDiscountCodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CopyRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyRolesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyRoles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyRoles(ctx, req.(*CopyRolesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CopyPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyPackages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyPackages(ctx, req.(*CopyPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CopyMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyMembershipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyMemberships(ctx, req.(*CopyMembershipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CopyProducts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyProductsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyProducts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyProducts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyProducts(ctx, req.(*CopyProductsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CreateLodgingType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLodgingTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CreateLodgingType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CreateLodgingType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CreateLodgingType(ctx, req.(*CreateLodgingTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_UpdateLodgingType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLodgingTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).UpdateLodgingType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/UpdateLodgingType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).UpdateLodgingType(ctx, req.(*UpdateLodgingTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_DeleteLodgingType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLodgingTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).DeleteLodgingType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/DeleteLodgingType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).DeleteLodgingType(ctx, req.(*DeleteLodgingTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_SortLodgingTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortLodgingTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).SortLodgingTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/SortLodgingTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).SortLodgingTypes(ctx, req.(*SortLodgingTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_ListLodgingTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLodgingTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).ListLodgingTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/ListLodgingTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).ListLodgingTypes(ctx, req.(*ListLodgingTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_PushLodgingTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushLodgingTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).PushLodgingTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/PushLodgingTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).PushLodgingTypes(ctx, req.(*PushLodgingTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_InitLodgingTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitLodgingTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).InitLodgingTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/InitLodgingTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).InitLodgingTypes(ctx, req.(*InitLodgingTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_ListPetCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetCodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).ListPetCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/ListPetCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).ListPetCodes(ctx, req.(*ListPetCodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CreatePetCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CreatePetCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CreatePetCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CreatePetCode(ctx, req.(*CreatePetCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_UpdatePetCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).UpdatePetCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/UpdatePetCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).UpdatePetCode(ctx, req.(*UpdatePetCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_SortPetCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPetCodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).SortPetCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/SortPetCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).SortPetCodes(ctx, req.(*SortPetCodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_DeletePetCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).DeletePetCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/DeletePetCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).DeletePetCode(ctx, req.(*DeletePetCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_PushPetCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPetCodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).PushPetCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/PushPetCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).PushPetCodes(ctx, req.(*PushPetCodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_InitPetCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitPetCodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).InitPetCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/InitPetCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).InitPetCodes(ctx, req.(*InitPetCodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CreatePetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CreatePetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CreatePetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CreatePetMetadata(ctx, req.(*CreatePetMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_UpdatePetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).UpdatePetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/UpdatePetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).UpdatePetMetadata(ctx, req.(*UpdatePetMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_DeletePetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).DeletePetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/DeletePetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).DeletePetMetadata(ctx, req.(*DeletePetMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_ListPetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).ListPetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/ListPetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).ListPetMetadata(ctx, req.(*ListPetMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_PushPetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPetMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).PushPetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/PushPetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).PushPetMetadata(ctx, req.(*PushPetMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_InitPetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitPetMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).InitPetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/InitPetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).InitPetMetadata(ctx, req.(*InitPetMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_SortPetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPetMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).SortPetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/SortPetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).SortPetMetadata(ctx, req.(*SortPetMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_ListSurchargeAssociatedFoodSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSurchargeAssociatedFoodSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).ListSurchargeAssociatedFoodSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/ListSurchargeAssociatedFoodSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).ListSurchargeAssociatedFoodSource(ctx, req.(*ListSurchargeAssociatedFoodSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SettingService_ServiceDesc is the grpc.ServiceDesc for SettingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SettingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.enterprise.v1.SettingService",
	HandlerType: (*SettingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CopyIntakeForms",
			Handler:    _SettingService_CopyIntakeForms_Handler,
		},
		{
			MethodName: "CopyDiscountCodes",
			Handler:    _SettingService_CopyDiscountCodes_Handler,
		},
		{
			MethodName: "CopyRoles",
			Handler:    _SettingService_CopyRoles_Handler,
		},
		{
			MethodName: "CopyPackages",
			Handler:    _SettingService_CopyPackages_Handler,
		},
		{
			MethodName: "CopyMemberships",
			Handler:    _SettingService_CopyMemberships_Handler,
		},
		{
			MethodName: "CopyProducts",
			Handler:    _SettingService_CopyProducts_Handler,
		},
		{
			MethodName: "CreateLodgingType",
			Handler:    _SettingService_CreateLodgingType_Handler,
		},
		{
			MethodName: "UpdateLodgingType",
			Handler:    _SettingService_UpdateLodgingType_Handler,
		},
		{
			MethodName: "DeleteLodgingType",
			Handler:    _SettingService_DeleteLodgingType_Handler,
		},
		{
			MethodName: "SortLodgingTypes",
			Handler:    _SettingService_SortLodgingTypes_Handler,
		},
		{
			MethodName: "ListLodgingTypes",
			Handler:    _SettingService_ListLodgingTypes_Handler,
		},
		{
			MethodName: "PushLodgingTypes",
			Handler:    _SettingService_PushLodgingTypes_Handler,
		},
		{
			MethodName: "InitLodgingTypes",
			Handler:    _SettingService_InitLodgingTypes_Handler,
		},
		{
			MethodName: "ListPetCodes",
			Handler:    _SettingService_ListPetCodes_Handler,
		},
		{
			MethodName: "CreatePetCode",
			Handler:    _SettingService_CreatePetCode_Handler,
		},
		{
			MethodName: "UpdatePetCode",
			Handler:    _SettingService_UpdatePetCode_Handler,
		},
		{
			MethodName: "SortPetCodes",
			Handler:    _SettingService_SortPetCodes_Handler,
		},
		{
			MethodName: "DeletePetCode",
			Handler:    _SettingService_DeletePetCode_Handler,
		},
		{
			MethodName: "PushPetCodes",
			Handler:    _SettingService_PushPetCodes_Handler,
		},
		{
			MethodName: "InitPetCodes",
			Handler:    _SettingService_InitPetCodes_Handler,
		},
		{
			MethodName: "CreatePetMetadata",
			Handler:    _SettingService_CreatePetMetadata_Handler,
		},
		{
			MethodName: "UpdatePetMetadata",
			Handler:    _SettingService_UpdatePetMetadata_Handler,
		},
		{
			MethodName: "DeletePetMetadata",
			Handler:    _SettingService_DeletePetMetadata_Handler,
		},
		{
			MethodName: "ListPetMetadata",
			Handler:    _SettingService_ListPetMetadata_Handler,
		},
		{
			MethodName: "PushPetMetadata",
			Handler:    _SettingService_PushPetMetadata_Handler,
		},
		{
			MethodName: "InitPetMetadata",
			Handler:    _SettingService_InitPetMetadata_Handler,
		},
		{
			MethodName: "SortPetMetadata",
			Handler:    _SettingService_SortPetMetadata_Handler,
		},
		{
			MethodName: "ListSurchargeAssociatedFoodSource",
			Handler:    _SettingService_ListSurchargeAssociatedFoodSource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/enterprise/v1/setting_service.proto",
}
