// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/enterprise/v1/template_push_service.proto

package enterprisesvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// enum group
type ListTemplatePushHistoriesRequest_Group int32

const (
	// 未指定
	ListTemplatePushHistoriesRequest_GROUP_UNSPECIFIED ListTemplatePushHistoriesRequest_Group = 0
	// pet code
	ListTemplatePushHistoriesRequest_GROUP_PET_CODE ListTemplatePushHistoriesRequest_Group = 1
	// pet feeding, feeding Schedule, Unit, Type, Source, Instruction
	ListTemplatePushHistoriesRequest_GROUP_PET_FEEDING ListTemplatePushHistoriesRequest_Group = 2
	// service charge exceed 24 hours
	ListTemplatePushHistoriesRequest_GROUP_SERVICE_CHARGE_EXCEED_24_HOURS ListTemplatePushHistoriesRequest_Group = 3
	// service charge medication charge
	ListTemplatePushHistoriesRequest_GROUP_SERVICE_CHARGE_MEDICATION ListTemplatePushHistoriesRequest_Group = 4
)

// Enum value maps for ListTemplatePushHistoriesRequest_Group.
var (
	ListTemplatePushHistoriesRequest_Group_name = map[int32]string{
		0: "GROUP_UNSPECIFIED",
		1: "GROUP_PET_CODE",
		2: "GROUP_PET_FEEDING",
		3: "GROUP_SERVICE_CHARGE_EXCEED_24_HOURS",
		4: "GROUP_SERVICE_CHARGE_MEDICATION",
	}
	ListTemplatePushHistoriesRequest_Group_value = map[string]int32{
		"GROUP_UNSPECIFIED":                    0,
		"GROUP_PET_CODE":                       1,
		"GROUP_PET_FEEDING":                    2,
		"GROUP_SERVICE_CHARGE_EXCEED_24_HOURS": 3,
		"GROUP_SERVICE_CHARGE_MEDICATION":      4,
	}
)

func (x ListTemplatePushHistoriesRequest_Group) Enum() *ListTemplatePushHistoriesRequest_Group {
	p := new(ListTemplatePushHistoriesRequest_Group)
	*p = x
	return p
}

func (x ListTemplatePushHistoriesRequest_Group) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListTemplatePushHistoriesRequest_Group) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_enterprise_v1_template_push_service_proto_enumTypes[0].Descriptor()
}

func (ListTemplatePushHistoriesRequest_Group) Type() protoreflect.EnumType {
	return &file_moego_service_enterprise_v1_template_push_service_proto_enumTypes[0]
}

func (x ListTemplatePushHistoriesRequest_Group) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListTemplatePushHistoriesRequest_Group.Descriptor instead.
func (ListTemplatePushHistoriesRequest_Group) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{10, 0}
}

// ListTemplatePushMappingsRequest
type ListTemplatePushMappingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListTemplatePushMappingsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListTemplatePushMappingsRequest) Reset() {
	*x = ListTemplatePushMappingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushMappingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushMappingsRequest) ProtoMessage() {}

func (x *ListTemplatePushMappingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushMappingsRequest.ProtoReflect.Descriptor instead.
func (*ListTemplatePushMappingsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListTemplatePushMappingsRequest) GetFilter() *ListTemplatePushMappingsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListTemplatePushMappingsResponse
type ListTemplatePushMappingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template push mappings
	TemplatePushMappings []*v1.TemplatePushMapping `protobuf:"bytes,1,rep,name=template_push_mappings,json=templatePushMappings,proto3" json:"template_push_mappings,omitempty"`
}

func (x *ListTemplatePushMappingsResponse) Reset() {
	*x = ListTemplatePushMappingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushMappingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushMappingsResponse) ProtoMessage() {}

func (x *ListTemplatePushMappingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushMappingsResponse.ProtoReflect.Descriptor instead.
func (*ListTemplatePushMappingsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListTemplatePushMappingsResponse) GetTemplatePushMappings() []*v1.TemplatePushMapping {
	if x != nil {
		return x.TemplatePushMappings
	}
	return nil
}

// UpsertTemplatePushMappingRequest
type UpsertTemplatePushMappingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template_type, template_id and target_company_id are used to identify the mapping
	// if the mapping exists, target_id will be updated, otherwise a new mapping will be created
	// template type
	TemplateType v1.TemplateType `protobuf:"varint,1,opt,name=template_type,json=templateType,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_type,omitempty"`
	// template id
	TemplateId int64 `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// target organization type
	TargetOrganizationType v11.OrganizationType `protobuf:"varint,3,opt,name=target_organization_type,json=targetOrganizationType,proto3,enum=moego.models.organization.v1.OrganizationType" json:"target_organization_type,omitempty"`
	// target organization id
	TargetOrganizationId int64 `protobuf:"varint,4,opt,name=target_organization_id,json=targetOrganizationId,proto3" json:"target_organization_id,omitempty"`
	// target id
	TargetId int64 `protobuf:"varint,5,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
}

func (x *UpsertTemplatePushMappingRequest) Reset() {
	*x = UpsertTemplatePushMappingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertTemplatePushMappingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertTemplatePushMappingRequest) ProtoMessage() {}

func (x *UpsertTemplatePushMappingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertTemplatePushMappingRequest.ProtoReflect.Descriptor instead.
func (*UpsertTemplatePushMappingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpsertTemplatePushMappingRequest) GetTemplateType() v1.TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return v1.TemplateType(0)
}

func (x *UpsertTemplatePushMappingRequest) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *UpsertTemplatePushMappingRequest) GetTargetOrganizationType() v11.OrganizationType {
	if x != nil {
		return x.TargetOrganizationType
	}
	return v11.OrganizationType(0)
}

func (x *UpsertTemplatePushMappingRequest) GetTargetOrganizationId() int64 {
	if x != nil {
		return x.TargetOrganizationId
	}
	return 0
}

func (x *UpsertTemplatePushMappingRequest) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

// UpsertTemplatePushMappingResponse
type UpsertTemplatePushMappingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template push mapping
	TemplatePushMapping *v1.TemplatePushMapping `protobuf:"bytes,1,opt,name=template_push_mapping,json=templatePushMapping,proto3" json:"template_push_mapping,omitempty"`
}

func (x *UpsertTemplatePushMappingResponse) Reset() {
	*x = UpsertTemplatePushMappingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertTemplatePushMappingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertTemplatePushMappingResponse) ProtoMessage() {}

func (x *UpsertTemplatePushMappingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertTemplatePushMappingResponse.ProtoReflect.Descriptor instead.
func (*UpsertTemplatePushMappingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpsertTemplatePushMappingResponse) GetTemplatePushMapping() *v1.TemplatePushMapping {
	if x != nil {
		return x.TemplatePushMapping
	}
	return nil
}

// ListTemplatePushSettingsRequest
type ListTemplatePushSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListTemplatePushSettingsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListTemplatePushSettingsRequest) Reset() {
	*x = ListTemplatePushSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushSettingsRequest) ProtoMessage() {}

func (x *ListTemplatePushSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushSettingsRequest.ProtoReflect.Descriptor instead.
func (*ListTemplatePushSettingsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListTemplatePushSettingsRequest) GetFilter() *ListTemplatePushSettingsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListTemplatePushSettingsResponse
type ListTemplatePushSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template push settings
	TemplatePushSettings []*v1.TemplatePushSetting `protobuf:"bytes,1,rep,name=template_push_settings,json=templatePushSettings,proto3" json:"template_push_settings,omitempty"`
}

func (x *ListTemplatePushSettingsResponse) Reset() {
	*x = ListTemplatePushSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushSettingsResponse) ProtoMessage() {}

func (x *ListTemplatePushSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushSettingsResponse.ProtoReflect.Descriptor instead.
func (*ListTemplatePushSettingsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListTemplatePushSettingsResponse) GetTemplatePushSettings() []*v1.TemplatePushSetting {
	if x != nil {
		return x.TemplatePushSettings
	}
	return nil
}

// UpsertTemplatePushSettingRequest
type UpsertTemplatePushSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template_type, template_id are used to identify the setting
	// template type
	TemplateType v1.TemplateType `protobuf:"varint,1,opt,name=template_type,json=templateType,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_type,omitempty"`
	// template id
	TemplateId int64 `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// attributes
	Attributes []*v1.TemplatePushSetting_Attribute `protobuf:"bytes,3,rep,name=attributes,proto3" json:"attributes,omitempty"`
}

func (x *UpsertTemplatePushSettingRequest) Reset() {
	*x = UpsertTemplatePushSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertTemplatePushSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertTemplatePushSettingRequest) ProtoMessage() {}

func (x *UpsertTemplatePushSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertTemplatePushSettingRequest.ProtoReflect.Descriptor instead.
func (*UpsertTemplatePushSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpsertTemplatePushSettingRequest) GetTemplateType() v1.TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return v1.TemplateType(0)
}

func (x *UpsertTemplatePushSettingRequest) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *UpsertTemplatePushSettingRequest) GetAttributes() []*v1.TemplatePushSetting_Attribute {
	if x != nil {
		return x.Attributes
	}
	return nil
}

// UpsertTemplatePushSettingResponse
type UpsertTemplatePushSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template push setting
	TemplatePushSetting *v1.TemplatePushSetting `protobuf:"bytes,1,opt,name=template_push_setting,json=templatePushSetting,proto3" json:"template_push_setting,omitempty"`
}

func (x *UpsertTemplatePushSettingResponse) Reset() {
	*x = UpsertTemplatePushSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertTemplatePushSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertTemplatePushSettingResponse) ProtoMessage() {}

func (x *UpsertTemplatePushSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertTemplatePushSettingResponse.ProtoReflect.Descriptor instead.
func (*UpsertTemplatePushSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpsertTemplatePushSettingResponse) GetTemplatePushSetting() *v1.TemplatePushSetting {
	if x != nil {
		return x.TemplatePushSetting
	}
	return nil
}

// ListTemplatePushChangesRequest
type ListTemplatePushChangesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListTemplatePushChangesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListTemplatePushChangesRequest) Reset() {
	*x = ListTemplatePushChangesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushChangesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushChangesRequest) ProtoMessage() {}

func (x *ListTemplatePushChangesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushChangesRequest.ProtoReflect.Descriptor instead.
func (*ListTemplatePushChangesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{8}
}

func (x *ListTemplatePushChangesRequest) GetFilter() *ListTemplatePushChangesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListTemplatePushChangesResponse
type ListTemplatePushChangesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template push changes
	TemplatePushChanges []*v1.TemplatePushChange `protobuf:"bytes,1,rep,name=template_push_changes,json=templatePushChanges,proto3" json:"template_push_changes,omitempty"`
}

func (x *ListTemplatePushChangesResponse) Reset() {
	*x = ListTemplatePushChangesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushChangesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushChangesResponse) ProtoMessage() {}

func (x *ListTemplatePushChangesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushChangesResponse.ProtoReflect.Descriptor instead.
func (*ListTemplatePushChangesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListTemplatePushChangesResponse) GetTemplatePushChanges() []*v1.TemplatePushChange {
	if x != nil {
		return x.TemplatePushChanges
	}
	return nil
}

// ListTemplatePushHistoriesRequest
type ListTemplatePushHistoriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListTemplatePushHistoriesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// order by
	OrderBys []*v1.TemplatePushChangeHistoryOrderBy `protobuf:"bytes,2,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
}

func (x *ListTemplatePushHistoriesRequest) Reset() {
	*x = ListTemplatePushHistoriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushHistoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushHistoriesRequest) ProtoMessage() {}

func (x *ListTemplatePushHistoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushHistoriesRequest.ProtoReflect.Descriptor instead.
func (*ListTemplatePushHistoriesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListTemplatePushHistoriesRequest) GetFilter() *ListTemplatePushHistoriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListTemplatePushHistoriesRequest) GetOrderBys() []*v1.TemplatePushChangeHistoryOrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

// ListTemplatePushHistoriesResponse
type ListTemplatePushHistoriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template push histories
	TemplatePushHistories []*v1.TemplatePushHistory `protobuf:"bytes,1,rep,name=template_push_histories,json=templatePushHistories,proto3" json:"template_push_histories,omitempty"`
}

func (x *ListTemplatePushHistoriesResponse) Reset() {
	*x = ListTemplatePushHistoriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushHistoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushHistoriesResponse) ProtoMessage() {}

func (x *ListTemplatePushHistoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushHistoriesResponse.ProtoReflect.Descriptor instead.
func (*ListTemplatePushHistoriesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListTemplatePushHistoriesResponse) GetTemplatePushHistories() []*v1.TemplatePushHistory {
	if x != nil {
		return x.TemplatePushHistories
	}
	return nil
}

// CheckTemplatePushRequest
type CheckTemplatePushRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// template type
	TemplateType v1.TemplateType `protobuf:"varint,2,opt,name=template_type,json=templateType,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_type,omitempty"`
	// template ids
	TemplateIds []int64 `protobuf:"varint,3,rep,packed,name=template_ids,json=templateIds,proto3" json:"template_ids,omitempty"`
	// target organizations
	Targets []*v1.TenantObject `protobuf:"bytes,4,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *CheckTemplatePushRequest) Reset() {
	*x = CheckTemplatePushRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckTemplatePushRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTemplatePushRequest) ProtoMessage() {}

func (x *CheckTemplatePushRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTemplatePushRequest.ProtoReflect.Descriptor instead.
func (*CheckTemplatePushRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{12}
}

func (x *CheckTemplatePushRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CheckTemplatePushRequest) GetTemplateType() v1.TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return v1.TemplateType(0)
}

func (x *CheckTemplatePushRequest) GetTemplateIds() []int64 {
	if x != nil {
		return x.TemplateIds
	}
	return nil
}

func (x *CheckTemplatePushRequest) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// CheckTemplatePushResponse
type CheckTemplatePushResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conflicts
	Conflicts []*v1.TemplatePushConflict `protobuf:"bytes,1,rep,name=conflicts,proto3" json:"conflicts,omitempty"`
}

func (x *CheckTemplatePushResponse) Reset() {
	*x = CheckTemplatePushResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckTemplatePushResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTemplatePushResponse) ProtoMessage() {}

func (x *CheckTemplatePushResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTemplatePushResponse.ProtoReflect.Descriptor instead.
func (*CheckTemplatePushResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{13}
}

func (x *CheckTemplatePushResponse) GetConflicts() []*v1.TemplatePushConflict {
	if x != nil {
		return x.Conflicts
	}
	return nil
}

// filter
type ListTemplatePushMappingsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// target organization type
	TargetOrganizationTypes []v11.OrganizationType `protobuf:"varint,1,rep,packed,name=target_organization_types,json=targetOrganizationTypes,proto3,enum=moego.models.organization.v1.OrganizationType" json:"target_organization_types,omitempty"`
	// target organization ids
	TargetOrganizationIds []int64 `protobuf:"varint,2,rep,packed,name=target_organization_ids,json=targetOrganizationIds,proto3" json:"target_organization_ids,omitempty"`
	// template types
	TemplateTypes []v1.TemplateType `protobuf:"varint,3,rep,packed,name=template_types,json=templateTypes,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_types,omitempty"`
	// template ids
	TemplateIds []int64 `protobuf:"varint,4,rep,packed,name=template_ids,json=templateIds,proto3" json:"template_ids,omitempty"`
}

func (x *ListTemplatePushMappingsRequest_Filter) Reset() {
	*x = ListTemplatePushMappingsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushMappingsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushMappingsRequest_Filter) ProtoMessage() {}

func (x *ListTemplatePushMappingsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushMappingsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListTemplatePushMappingsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ListTemplatePushMappingsRequest_Filter) GetTargetOrganizationTypes() []v11.OrganizationType {
	if x != nil {
		return x.TargetOrganizationTypes
	}
	return nil
}

func (x *ListTemplatePushMappingsRequest_Filter) GetTargetOrganizationIds() []int64 {
	if x != nil {
		return x.TargetOrganizationIds
	}
	return nil
}

func (x *ListTemplatePushMappingsRequest_Filter) GetTemplateTypes() []v1.TemplateType {
	if x != nil {
		return x.TemplateTypes
	}
	return nil
}

func (x *ListTemplatePushMappingsRequest_Filter) GetTemplateIds() []int64 {
	if x != nil {
		return x.TemplateIds
	}
	return nil
}

// filter
type ListTemplatePushSettingsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
	// template types
	TemplateTypes []v1.TemplateType `protobuf:"varint,2,rep,packed,name=template_types,json=templateTypes,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_types,omitempty"`
	// template ids
	TemplateIds []int64 `protobuf:"varint,3,rep,packed,name=template_ids,json=templateIds,proto3" json:"template_ids,omitempty"`
}

func (x *ListTemplatePushSettingsRequest_Filter) Reset() {
	*x = ListTemplatePushSettingsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushSettingsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushSettingsRequest_Filter) ProtoMessage() {}

func (x *ListTemplatePushSettingsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushSettingsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListTemplatePushSettingsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListTemplatePushSettingsRequest_Filter) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

func (x *ListTemplatePushSettingsRequest_Filter) GetTemplateTypes() []v1.TemplateType {
	if x != nil {
		return x.TemplateTypes
	}
	return nil
}

func (x *ListTemplatePushSettingsRequest_Filter) GetTemplateIds() []int64 {
	if x != nil {
		return x.TemplateIds
	}
	return nil
}

// filter
type ListTemplatePushChangesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// history ids
	HistoryIds []int64 `protobuf:"varint,1,rep,packed,name=history_ids,json=historyIds,proto3" json:"history_ids,omitempty"`
	// template types
	TemplateTypes []v1.TemplateType `protobuf:"varint,2,rep,packed,name=template_types,json=templateTypes,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_types,omitempty"`
	// template ids
	TemplateIds []int64 `protobuf:"varint,3,rep,packed,name=template_ids,json=templateIds,proto3" json:"template_ids,omitempty"`
	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,4,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
}

func (x *ListTemplatePushChangesRequest_Filter) Reset() {
	*x = ListTemplatePushChangesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushChangesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushChangesRequest_Filter) ProtoMessage() {}

func (x *ListTemplatePushChangesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushChangesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListTemplatePushChangesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListTemplatePushChangesRequest_Filter) GetHistoryIds() []int64 {
	if x != nil {
		return x.HistoryIds
	}
	return nil
}

func (x *ListTemplatePushChangesRequest_Filter) GetTemplateTypes() []v1.TemplateType {
	if x != nil {
		return x.TemplateTypes
	}
	return nil
}

func (x *ListTemplatePushChangesRequest_Filter) GetTemplateIds() []int64 {
	if x != nil {
		return x.TemplateIds
	}
	return nil
}

func (x *ListTemplatePushChangesRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

// filter
type ListTemplatePushHistoriesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
	// template types
	TemplateTypes []v1.TemplateType `protobuf:"varint,2,rep,packed,name=template_types,json=templateTypes,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_types,omitempty"`
	// template ids
	TemplateIds []int64 `protobuf:"varint,3,rep,packed,name=template_ids,json=templateIds,proto3" json:"template_ids,omitempty"`
	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,4,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// group, 表示查询特殊的 group,聚合多个 template id, 传了这个会忽略 template_ids 和 template type
	Group *ListTemplatePushHistoriesRequest_Group `protobuf:"varint,5,opt,name=group,proto3,enum=moego.service.enterprise.v1.ListTemplatePushHistoriesRequest_Group,oneof" json:"group,omitempty"`
}

func (x *ListTemplatePushHistoriesRequest_Filter) Reset() {
	*x = ListTemplatePushHistoriesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushHistoriesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushHistoriesRequest_Filter) ProtoMessage() {}

func (x *ListTemplatePushHistoriesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushHistoriesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListTemplatePushHistoriesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ListTemplatePushHistoriesRequest_Filter) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

func (x *ListTemplatePushHistoriesRequest_Filter) GetTemplateTypes() []v1.TemplateType {
	if x != nil {
		return x.TemplateTypes
	}
	return nil
}

func (x *ListTemplatePushHistoriesRequest_Filter) GetTemplateIds() []int64 {
	if x != nil {
		return x.TemplateIds
	}
	return nil
}

func (x *ListTemplatePushHistoriesRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *ListTemplatePushHistoriesRequest_Filter) GetGroup() ListTemplatePushHistoriesRequest_Group {
	if x != nil && x.Group != nil {
		return *x.Group
	}
	return ListTemplatePushHistoriesRequest_GROUP_UNSPECIFIED
}

var File_moego_service_enterprise_v1_template_push_service_proto protoreflect.FileDescriptor

var file_moego_service_enterprise_v1_template_push_service_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcd, 0x03, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xcc, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x75, 0x0a, 0x19, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8,
	0x07, 0x52, 0x17, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x41, 0x0a, 0x17, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x15, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x5a, 0x0a,
	0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0d, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x0c, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x73, 0x22, 0x89, 0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x16,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x6d, 0x61,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x14, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x61, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x73, 0x22, 0xf4, 0x02, 0x0a, 0x20, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4d, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x72, 0x0a, 0x18, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x16, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x16, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x14,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x22, 0x88, 0x01, 0x0a, 0x21, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x63, 0x0a, 0x15, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73,
	0x68, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x52, 0x13, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x61,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x22, 0xcc, 0x02, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xcb, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x37, 0x0a, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x5a, 0x0a, 0x0e, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x73, 0x22, 0x89, 0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x16, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x14, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x22, 0xed, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4d, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x22, 0x88, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x15, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x13, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xef, 0x02, 0x0a, 0x1e,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5a,
	0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xf0, 0x01, 0x0a, 0x06, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x0b, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92,
	0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0a, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64,
	0x73, 0x12, 0x5a, 0x0a, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0d,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2c, 0x0a,
	0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0b,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x0e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x22, 0x85, 0x01,
	0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x62, 0x0a, 0x15, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75,
	0x73, 0x68, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x13, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x22, 0xf4, 0x05, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5c, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x63, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x10, 0x0a, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x1a, 0xf1, 0x02,
	0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x12, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52,
	0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x73, 0x12, 0x5a, 0x0a, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0d,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2c, 0x0a,
	0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0b,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x0e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x68, 0x0a,
	0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x05, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x22, 0x98, 0x01, 0x0a, 0x05, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x15, 0x0a, 0x11, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x50, 0x45, 0x54, 0x5f,
	0x43, 0x4f, 0x44, 0x45, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f,
	0x50, 0x45, 0x54, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x28, 0x0a,
	0x24, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x43,
	0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x5f, 0x32, 0x34, 0x5f,
	0x48, 0x4f, 0x55, 0x52, 0x53, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x47, 0x52, 0x4f, 0x55, 0x50,
	0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f,
	0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x22, 0x8c, 0x01, 0x0a,
	0x21, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x67, 0x0a, 0x17, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70,
	0x75, 0x73, 0x68, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x52, 0x15, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0xa4, 0x02, 0x0a, 0x18,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x31, 0x0a, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x08,
	0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x4c, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x73, 0x22, 0x6b, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4e, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66,
	0x6c, 0x69, 0x63, 0x74, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x73, 0x32,
	0xca, 0x08, 0x0a, 0x13, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x75, 0x73, 0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x9c, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x99, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9c,
	0x01, 0x0a, 0x19, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x96, 0x01,
	0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9c, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x12, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x89, 0x01, 0x0a,
	0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_enterprise_v1_template_push_service_proto_rawDescOnce sync.Once
	file_moego_service_enterprise_v1_template_push_service_proto_rawDescData = file_moego_service_enterprise_v1_template_push_service_proto_rawDesc
)

func file_moego_service_enterprise_v1_template_push_service_proto_rawDescGZIP() []byte {
	file_moego_service_enterprise_v1_template_push_service_proto_rawDescOnce.Do(func() {
		file_moego_service_enterprise_v1_template_push_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_enterprise_v1_template_push_service_proto_rawDescData)
	})
	return file_moego_service_enterprise_v1_template_push_service_proto_rawDescData
}

var file_moego_service_enterprise_v1_template_push_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_service_enterprise_v1_template_push_service_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_moego_service_enterprise_v1_template_push_service_proto_goTypes = []interface{}{
	(ListTemplatePushHistoriesRequest_Group)(0),     // 0: moego.service.enterprise.v1.ListTemplatePushHistoriesRequest.Group
	(*ListTemplatePushMappingsRequest)(nil),         // 1: moego.service.enterprise.v1.ListTemplatePushMappingsRequest
	(*ListTemplatePushMappingsResponse)(nil),        // 2: moego.service.enterprise.v1.ListTemplatePushMappingsResponse
	(*UpsertTemplatePushMappingRequest)(nil),        // 3: moego.service.enterprise.v1.UpsertTemplatePushMappingRequest
	(*UpsertTemplatePushMappingResponse)(nil),       // 4: moego.service.enterprise.v1.UpsertTemplatePushMappingResponse
	(*ListTemplatePushSettingsRequest)(nil),         // 5: moego.service.enterprise.v1.ListTemplatePushSettingsRequest
	(*ListTemplatePushSettingsResponse)(nil),        // 6: moego.service.enterprise.v1.ListTemplatePushSettingsResponse
	(*UpsertTemplatePushSettingRequest)(nil),        // 7: moego.service.enterprise.v1.UpsertTemplatePushSettingRequest
	(*UpsertTemplatePushSettingResponse)(nil),       // 8: moego.service.enterprise.v1.UpsertTemplatePushSettingResponse
	(*ListTemplatePushChangesRequest)(nil),          // 9: moego.service.enterprise.v1.ListTemplatePushChangesRequest
	(*ListTemplatePushChangesResponse)(nil),         // 10: moego.service.enterprise.v1.ListTemplatePushChangesResponse
	(*ListTemplatePushHistoriesRequest)(nil),        // 11: moego.service.enterprise.v1.ListTemplatePushHistoriesRequest
	(*ListTemplatePushHistoriesResponse)(nil),       // 12: moego.service.enterprise.v1.ListTemplatePushHistoriesResponse
	(*CheckTemplatePushRequest)(nil),                // 13: moego.service.enterprise.v1.CheckTemplatePushRequest
	(*CheckTemplatePushResponse)(nil),               // 14: moego.service.enterprise.v1.CheckTemplatePushResponse
	(*ListTemplatePushMappingsRequest_Filter)(nil),  // 15: moego.service.enterprise.v1.ListTemplatePushMappingsRequest.Filter
	(*ListTemplatePushSettingsRequest_Filter)(nil),  // 16: moego.service.enterprise.v1.ListTemplatePushSettingsRequest.Filter
	(*ListTemplatePushChangesRequest_Filter)(nil),   // 17: moego.service.enterprise.v1.ListTemplatePushChangesRequest.Filter
	(*ListTemplatePushHistoriesRequest_Filter)(nil), // 18: moego.service.enterprise.v1.ListTemplatePushHistoriesRequest.Filter
	(*v1.TemplatePushMapping)(nil),                  // 19: moego.models.enterprise.v1.TemplatePushMapping
	(v1.TemplateType)(0),                            // 20: moego.models.enterprise.v1.TemplateType
	(v11.OrganizationType)(0),                       // 21: moego.models.organization.v1.OrganizationType
	(*v1.TemplatePushSetting)(nil),                  // 22: moego.models.enterprise.v1.TemplatePushSetting
	(*v1.TemplatePushSetting_Attribute)(nil),        // 23: moego.models.enterprise.v1.TemplatePushSetting.Attribute
	(*v1.TemplatePushChange)(nil),                   // 24: moego.models.enterprise.v1.TemplatePushChange
	(*v1.TemplatePushChangeHistoryOrderBy)(nil),     // 25: moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy
	(*v1.TemplatePushHistory)(nil),                  // 26: moego.models.enterprise.v1.TemplatePushHistory
	(*v1.TenantObject)(nil),                         // 27: moego.models.enterprise.v1.TenantObject
	(*v1.TemplatePushConflict)(nil),                 // 28: moego.models.enterprise.v1.TemplatePushConflict
}
var file_moego_service_enterprise_v1_template_push_service_proto_depIdxs = []int32{
	15, // 0: moego.service.enterprise.v1.ListTemplatePushMappingsRequest.filter:type_name -> moego.service.enterprise.v1.ListTemplatePushMappingsRequest.Filter
	19, // 1: moego.service.enterprise.v1.ListTemplatePushMappingsResponse.template_push_mappings:type_name -> moego.models.enterprise.v1.TemplatePushMapping
	20, // 2: moego.service.enterprise.v1.UpsertTemplatePushMappingRequest.template_type:type_name -> moego.models.enterprise.v1.TemplateType
	21, // 3: moego.service.enterprise.v1.UpsertTemplatePushMappingRequest.target_organization_type:type_name -> moego.models.organization.v1.OrganizationType
	19, // 4: moego.service.enterprise.v1.UpsertTemplatePushMappingResponse.template_push_mapping:type_name -> moego.models.enterprise.v1.TemplatePushMapping
	16, // 5: moego.service.enterprise.v1.ListTemplatePushSettingsRequest.filter:type_name -> moego.service.enterprise.v1.ListTemplatePushSettingsRequest.Filter
	22, // 6: moego.service.enterprise.v1.ListTemplatePushSettingsResponse.template_push_settings:type_name -> moego.models.enterprise.v1.TemplatePushSetting
	20, // 7: moego.service.enterprise.v1.UpsertTemplatePushSettingRequest.template_type:type_name -> moego.models.enterprise.v1.TemplateType
	23, // 8: moego.service.enterprise.v1.UpsertTemplatePushSettingRequest.attributes:type_name -> moego.models.enterprise.v1.TemplatePushSetting.Attribute
	22, // 9: moego.service.enterprise.v1.UpsertTemplatePushSettingResponse.template_push_setting:type_name -> moego.models.enterprise.v1.TemplatePushSetting
	17, // 10: moego.service.enterprise.v1.ListTemplatePushChangesRequest.filter:type_name -> moego.service.enterprise.v1.ListTemplatePushChangesRequest.Filter
	24, // 11: moego.service.enterprise.v1.ListTemplatePushChangesResponse.template_push_changes:type_name -> moego.models.enterprise.v1.TemplatePushChange
	18, // 12: moego.service.enterprise.v1.ListTemplatePushHistoriesRequest.filter:type_name -> moego.service.enterprise.v1.ListTemplatePushHistoriesRequest.Filter
	25, // 13: moego.service.enterprise.v1.ListTemplatePushHistoriesRequest.order_bys:type_name -> moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy
	26, // 14: moego.service.enterprise.v1.ListTemplatePushHistoriesResponse.template_push_histories:type_name -> moego.models.enterprise.v1.TemplatePushHistory
	20, // 15: moego.service.enterprise.v1.CheckTemplatePushRequest.template_type:type_name -> moego.models.enterprise.v1.TemplateType
	27, // 16: moego.service.enterprise.v1.CheckTemplatePushRequest.targets:type_name -> moego.models.enterprise.v1.TenantObject
	28, // 17: moego.service.enterprise.v1.CheckTemplatePushResponse.conflicts:type_name -> moego.models.enterprise.v1.TemplatePushConflict
	21, // 18: moego.service.enterprise.v1.ListTemplatePushMappingsRequest.Filter.target_organization_types:type_name -> moego.models.organization.v1.OrganizationType
	20, // 19: moego.service.enterprise.v1.ListTemplatePushMappingsRequest.Filter.template_types:type_name -> moego.models.enterprise.v1.TemplateType
	20, // 20: moego.service.enterprise.v1.ListTemplatePushSettingsRequest.Filter.template_types:type_name -> moego.models.enterprise.v1.TemplateType
	20, // 21: moego.service.enterprise.v1.ListTemplatePushChangesRequest.Filter.template_types:type_name -> moego.models.enterprise.v1.TemplateType
	20, // 22: moego.service.enterprise.v1.ListTemplatePushHistoriesRequest.Filter.template_types:type_name -> moego.models.enterprise.v1.TemplateType
	0,  // 23: moego.service.enterprise.v1.ListTemplatePushHistoriesRequest.Filter.group:type_name -> moego.service.enterprise.v1.ListTemplatePushHistoriesRequest.Group
	1,  // 24: moego.service.enterprise.v1.TemplatePushService.ListTemplatePushMappings:input_type -> moego.service.enterprise.v1.ListTemplatePushMappingsRequest
	3,  // 25: moego.service.enterprise.v1.TemplatePushService.UpsertTemplatePushMapping:input_type -> moego.service.enterprise.v1.UpsertTemplatePushMappingRequest
	5,  // 26: moego.service.enterprise.v1.TemplatePushService.ListTemplatePushSettings:input_type -> moego.service.enterprise.v1.ListTemplatePushSettingsRequest
	7,  // 27: moego.service.enterprise.v1.TemplatePushService.UpsertTemplatePushSetting:input_type -> moego.service.enterprise.v1.UpsertTemplatePushSettingRequest
	9,  // 28: moego.service.enterprise.v1.TemplatePushService.ListTemplatePushChanges:input_type -> moego.service.enterprise.v1.ListTemplatePushChangesRequest
	11, // 29: moego.service.enterprise.v1.TemplatePushService.ListTemplatePushHistories:input_type -> moego.service.enterprise.v1.ListTemplatePushHistoriesRequest
	13, // 30: moego.service.enterprise.v1.TemplatePushService.CheckTemplatePush:input_type -> moego.service.enterprise.v1.CheckTemplatePushRequest
	2,  // 31: moego.service.enterprise.v1.TemplatePushService.ListTemplatePushMappings:output_type -> moego.service.enterprise.v1.ListTemplatePushMappingsResponse
	4,  // 32: moego.service.enterprise.v1.TemplatePushService.UpsertTemplatePushMapping:output_type -> moego.service.enterprise.v1.UpsertTemplatePushMappingResponse
	6,  // 33: moego.service.enterprise.v1.TemplatePushService.ListTemplatePushSettings:output_type -> moego.service.enterprise.v1.ListTemplatePushSettingsResponse
	8,  // 34: moego.service.enterprise.v1.TemplatePushService.UpsertTemplatePushSetting:output_type -> moego.service.enterprise.v1.UpsertTemplatePushSettingResponse
	10, // 35: moego.service.enterprise.v1.TemplatePushService.ListTemplatePushChanges:output_type -> moego.service.enterprise.v1.ListTemplatePushChangesResponse
	12, // 36: moego.service.enterprise.v1.TemplatePushService.ListTemplatePushHistories:output_type -> moego.service.enterprise.v1.ListTemplatePushHistoriesResponse
	14, // 37: moego.service.enterprise.v1.TemplatePushService.CheckTemplatePush:output_type -> moego.service.enterprise.v1.CheckTemplatePushResponse
	31, // [31:38] is the sub-list for method output_type
	24, // [24:31] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_moego_service_enterprise_v1_template_push_service_proto_init() }
func file_moego_service_enterprise_v1_template_push_service_proto_init() {
	if File_moego_service_enterprise_v1_template_push_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushMappingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushMappingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertTemplatePushMappingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertTemplatePushMappingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertTemplatePushSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertTemplatePushSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushChangesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushChangesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushHistoriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushHistoriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckTemplatePushRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckTemplatePushResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushMappingsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushSettingsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushChangesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushHistoriesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_enterprise_v1_template_push_service_proto_msgTypes[17].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_enterprise_v1_template_push_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_enterprise_v1_template_push_service_proto_goTypes,
		DependencyIndexes: file_moego_service_enterprise_v1_template_push_service_proto_depIdxs,
		EnumInfos:         file_moego_service_enterprise_v1_template_push_service_proto_enumTypes,
		MessageInfos:      file_moego_service_enterprise_v1_template_push_service_proto_msgTypes,
	}.Build()
	File_moego_service_enterprise_v1_template_push_service_proto = out.File
	file_moego_service_enterprise_v1_template_push_service_proto_rawDesc = nil
	file_moego_service_enterprise_v1_template_push_service_proto_goTypes = nil
	file_moego_service_enterprise_v1_template_push_service_proto_depIdxs = nil
}
