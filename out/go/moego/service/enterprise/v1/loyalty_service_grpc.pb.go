// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/enterprise/v1/loyalty_service.proto

package enterprisesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// LoyaltyServiceClient is the client API for LoyaltyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LoyaltyServiceClient interface {
	// create membership
	CreateMembership(ctx context.Context, in *CreateMembershipRequest, opts ...grpc.CallOption) (*CreateMembershipResponse, error)
	// list memberships
	ListMemberships(ctx context.Context, in *ListMembershipsRequest, opts ...grpc.CallOption) (*ListMembershipsResponse, error)
	// update membership
	UpdateMembership(ctx context.Context, in *UpdateMembershipRequest, opts ...grpc.CallOption) (*UpdateMembershipResponse, error)
	// delete membership
	DeleteMembership(ctx context.Context, in *DeleteMembershipRequest, opts ...grpc.CallOption) (*DeleteMembershipResponse, error)
	// push membership changes
	PushMembershipChanges(ctx context.Context, in *PushMembershipChangesRequest, opts ...grpc.CallOption) (*PushMembershipChangesResponse, error)
	// create package
	CreatePackage(ctx context.Context, in *CreatePackageRequest, opts ...grpc.CallOption) (*CreatePackageResponse, error)
	// list packages
	ListPackages(ctx context.Context, in *ListPackagesRequest, opts ...grpc.CallOption) (*ListPackagesResponse, error)
	// update package
	UpdatePackage(ctx context.Context, in *UpdatePackageRequest, opts ...grpc.CallOption) (*UpdatePackageResponse, error)
	// delete package
	DeletePackage(ctx context.Context, in *DeletePackageRequest, opts ...grpc.CallOption) (*DeletePackageResponse, error)
	// push package changes
	PushPackageChanges(ctx context.Context, in *PushPackageChangesRequest, opts ...grpc.CallOption) (*PushPackageChangesResponse, error)
	// create discount
	CreateDiscount(ctx context.Context, in *CreateDiscountRequest, opts ...grpc.CallOption) (*CreateDiscountResponse, error)
	// list discounts
	ListDiscounts(ctx context.Context, in *ListDiscountsRequest, opts ...grpc.CallOption) (*ListDiscountsResponse, error)
	// update discount
	UpdateDiscount(ctx context.Context, in *UpdateDiscountRequest, opts ...grpc.CallOption) (*UpdateDiscountResponse, error)
	// delete discount
	DeleteDiscount(ctx context.Context, in *DeleteDiscountRequest, opts ...grpc.CallOption) (*DeleteDiscountResponse, error)
	// generate discount
	GenerateDiscountCode(ctx context.Context, in *GenerateDiscountCodeRequest, opts ...grpc.CallOption) (*GenerateDiscountCodeResponse, error)
	// push discount changes
	PushDiscountChanges(ctx context.Context, in *PushDiscountChangesRequest, opts ...grpc.CallOption) (*PushDiscountChangesResponse, error)
}

type loyaltyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLoyaltyServiceClient(cc grpc.ClientConnInterface) LoyaltyServiceClient {
	return &loyaltyServiceClient{cc}
}

func (c *loyaltyServiceClient) CreateMembership(ctx context.Context, in *CreateMembershipRequest, opts ...grpc.CallOption) (*CreateMembershipResponse, error) {
	out := new(CreateMembershipResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/CreateMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) ListMemberships(ctx context.Context, in *ListMembershipsRequest, opts ...grpc.CallOption) (*ListMembershipsResponse, error) {
	out := new(ListMembershipsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/ListMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) UpdateMembership(ctx context.Context, in *UpdateMembershipRequest, opts ...grpc.CallOption) (*UpdateMembershipResponse, error) {
	out := new(UpdateMembershipResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/UpdateMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) DeleteMembership(ctx context.Context, in *DeleteMembershipRequest, opts ...grpc.CallOption) (*DeleteMembershipResponse, error) {
	out := new(DeleteMembershipResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/DeleteMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) PushMembershipChanges(ctx context.Context, in *PushMembershipChangesRequest, opts ...grpc.CallOption) (*PushMembershipChangesResponse, error) {
	out := new(PushMembershipChangesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/PushMembershipChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) CreatePackage(ctx context.Context, in *CreatePackageRequest, opts ...grpc.CallOption) (*CreatePackageResponse, error) {
	out := new(CreatePackageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/CreatePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) ListPackages(ctx context.Context, in *ListPackagesRequest, opts ...grpc.CallOption) (*ListPackagesResponse, error) {
	out := new(ListPackagesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/ListPackages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) UpdatePackage(ctx context.Context, in *UpdatePackageRequest, opts ...grpc.CallOption) (*UpdatePackageResponse, error) {
	out := new(UpdatePackageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/UpdatePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) DeletePackage(ctx context.Context, in *DeletePackageRequest, opts ...grpc.CallOption) (*DeletePackageResponse, error) {
	out := new(DeletePackageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/DeletePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) PushPackageChanges(ctx context.Context, in *PushPackageChangesRequest, opts ...grpc.CallOption) (*PushPackageChangesResponse, error) {
	out := new(PushPackageChangesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/PushPackageChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) CreateDiscount(ctx context.Context, in *CreateDiscountRequest, opts ...grpc.CallOption) (*CreateDiscountResponse, error) {
	out := new(CreateDiscountResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/CreateDiscount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) ListDiscounts(ctx context.Context, in *ListDiscountsRequest, opts ...grpc.CallOption) (*ListDiscountsResponse, error) {
	out := new(ListDiscountsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/ListDiscounts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) UpdateDiscount(ctx context.Context, in *UpdateDiscountRequest, opts ...grpc.CallOption) (*UpdateDiscountResponse, error) {
	out := new(UpdateDiscountResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/UpdateDiscount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) DeleteDiscount(ctx context.Context, in *DeleteDiscountRequest, opts ...grpc.CallOption) (*DeleteDiscountResponse, error) {
	out := new(DeleteDiscountResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/DeleteDiscount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) GenerateDiscountCode(ctx context.Context, in *GenerateDiscountCodeRequest, opts ...grpc.CallOption) (*GenerateDiscountCodeResponse, error) {
	out := new(GenerateDiscountCodeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/GenerateDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) PushDiscountChanges(ctx context.Context, in *PushDiscountChangesRequest, opts ...grpc.CallOption) (*PushDiscountChangesResponse, error) {
	out := new(PushDiscountChangesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.LoyaltyService/PushDiscountChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LoyaltyServiceServer is the server API for LoyaltyService service.
// All implementations must embed UnimplementedLoyaltyServiceServer
// for forward compatibility
type LoyaltyServiceServer interface {
	// create membership
	CreateMembership(context.Context, *CreateMembershipRequest) (*CreateMembershipResponse, error)
	// list memberships
	ListMemberships(context.Context, *ListMembershipsRequest) (*ListMembershipsResponse, error)
	// update membership
	UpdateMembership(context.Context, *UpdateMembershipRequest) (*UpdateMembershipResponse, error)
	// delete membership
	DeleteMembership(context.Context, *DeleteMembershipRequest) (*DeleteMembershipResponse, error)
	// push membership changes
	PushMembershipChanges(context.Context, *PushMembershipChangesRequest) (*PushMembershipChangesResponse, error)
	// create package
	CreatePackage(context.Context, *CreatePackageRequest) (*CreatePackageResponse, error)
	// list packages
	ListPackages(context.Context, *ListPackagesRequest) (*ListPackagesResponse, error)
	// update package
	UpdatePackage(context.Context, *UpdatePackageRequest) (*UpdatePackageResponse, error)
	// delete package
	DeletePackage(context.Context, *DeletePackageRequest) (*DeletePackageResponse, error)
	// push package changes
	PushPackageChanges(context.Context, *PushPackageChangesRequest) (*PushPackageChangesResponse, error)
	// create discount
	CreateDiscount(context.Context, *CreateDiscountRequest) (*CreateDiscountResponse, error)
	// list discounts
	ListDiscounts(context.Context, *ListDiscountsRequest) (*ListDiscountsResponse, error)
	// update discount
	UpdateDiscount(context.Context, *UpdateDiscountRequest) (*UpdateDiscountResponse, error)
	// delete discount
	DeleteDiscount(context.Context, *DeleteDiscountRequest) (*DeleteDiscountResponse, error)
	// generate discount
	GenerateDiscountCode(context.Context, *GenerateDiscountCodeRequest) (*GenerateDiscountCodeResponse, error)
	// push discount changes
	PushDiscountChanges(context.Context, *PushDiscountChangesRequest) (*PushDiscountChangesResponse, error)
	mustEmbedUnimplementedLoyaltyServiceServer()
}

// UnimplementedLoyaltyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedLoyaltyServiceServer struct {
}

func (UnimplementedLoyaltyServiceServer) CreateMembership(context.Context, *CreateMembershipRequest) (*CreateMembershipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMembership not implemented")
}
func (UnimplementedLoyaltyServiceServer) ListMemberships(context.Context, *ListMembershipsRequest) (*ListMembershipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMemberships not implemented")
}
func (UnimplementedLoyaltyServiceServer) UpdateMembership(context.Context, *UpdateMembershipRequest) (*UpdateMembershipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMembership not implemented")
}
func (UnimplementedLoyaltyServiceServer) DeleteMembership(context.Context, *DeleteMembershipRequest) (*DeleteMembershipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMembership not implemented")
}
func (UnimplementedLoyaltyServiceServer) PushMembershipChanges(context.Context, *PushMembershipChangesRequest) (*PushMembershipChangesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushMembershipChanges not implemented")
}
func (UnimplementedLoyaltyServiceServer) CreatePackage(context.Context, *CreatePackageRequest) (*CreatePackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePackage not implemented")
}
func (UnimplementedLoyaltyServiceServer) ListPackages(context.Context, *ListPackagesRequest) (*ListPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPackages not implemented")
}
func (UnimplementedLoyaltyServiceServer) UpdatePackage(context.Context, *UpdatePackageRequest) (*UpdatePackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePackage not implemented")
}
func (UnimplementedLoyaltyServiceServer) DeletePackage(context.Context, *DeletePackageRequest) (*DeletePackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePackage not implemented")
}
func (UnimplementedLoyaltyServiceServer) PushPackageChanges(context.Context, *PushPackageChangesRequest) (*PushPackageChangesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushPackageChanges not implemented")
}
func (UnimplementedLoyaltyServiceServer) CreateDiscount(context.Context, *CreateDiscountRequest) (*CreateDiscountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDiscount not implemented")
}
func (UnimplementedLoyaltyServiceServer) ListDiscounts(context.Context, *ListDiscountsRequest) (*ListDiscountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDiscounts not implemented")
}
func (UnimplementedLoyaltyServiceServer) UpdateDiscount(context.Context, *UpdateDiscountRequest) (*UpdateDiscountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDiscount not implemented")
}
func (UnimplementedLoyaltyServiceServer) DeleteDiscount(context.Context, *DeleteDiscountRequest) (*DeleteDiscountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDiscount not implemented")
}
func (UnimplementedLoyaltyServiceServer) GenerateDiscountCode(context.Context, *GenerateDiscountCodeRequest) (*GenerateDiscountCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateDiscountCode not implemented")
}
func (UnimplementedLoyaltyServiceServer) PushDiscountChanges(context.Context, *PushDiscountChangesRequest) (*PushDiscountChangesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushDiscountChanges not implemented")
}
func (UnimplementedLoyaltyServiceServer) mustEmbedUnimplementedLoyaltyServiceServer() {}

// UnsafeLoyaltyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LoyaltyServiceServer will
// result in compilation errors.
type UnsafeLoyaltyServiceServer interface {
	mustEmbedUnimplementedLoyaltyServiceServer()
}

func RegisterLoyaltyServiceServer(s grpc.ServiceRegistrar, srv LoyaltyServiceServer) {
	s.RegisterService(&LoyaltyService_ServiceDesc, srv)
}

func _LoyaltyService_CreateMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMembershipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).CreateMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/CreateMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).CreateMembership(ctx, req.(*CreateMembershipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_ListMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).ListMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/ListMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).ListMemberships(ctx, req.(*ListMembershipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_UpdateMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMembershipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).UpdateMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/UpdateMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).UpdateMembership(ctx, req.(*UpdateMembershipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_DeleteMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMembershipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).DeleteMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/DeleteMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).DeleteMembership(ctx, req.(*DeleteMembershipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_PushMembershipChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushMembershipChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).PushMembershipChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/PushMembershipChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).PushMembershipChanges(ctx, req.(*PushMembershipChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_CreatePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).CreatePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/CreatePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).CreatePackage(ctx, req.(*CreatePackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_ListPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).ListPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/ListPackages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).ListPackages(ctx, req.(*ListPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_UpdatePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).UpdatePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/UpdatePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).UpdatePackage(ctx, req.(*UpdatePackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_DeletePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).DeletePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/DeletePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).DeletePackage(ctx, req.(*DeletePackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_PushPackageChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPackageChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).PushPackageChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/PushPackageChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).PushPackageChanges(ctx, req.(*PushPackageChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_CreateDiscount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDiscountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).CreateDiscount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/CreateDiscount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).CreateDiscount(ctx, req.(*CreateDiscountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_ListDiscounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDiscountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).ListDiscounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/ListDiscounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).ListDiscounts(ctx, req.(*ListDiscountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_UpdateDiscount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDiscountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).UpdateDiscount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/UpdateDiscount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).UpdateDiscount(ctx, req.(*UpdateDiscountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_DeleteDiscount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDiscountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).DeleteDiscount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/DeleteDiscount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).DeleteDiscount(ctx, req.(*DeleteDiscountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_GenerateDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateDiscountCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).GenerateDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/GenerateDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).GenerateDiscountCode(ctx, req.(*GenerateDiscountCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_PushDiscountChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushDiscountChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).PushDiscountChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.LoyaltyService/PushDiscountChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).PushDiscountChanges(ctx, req.(*PushDiscountChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// LoyaltyService_ServiceDesc is the grpc.ServiceDesc for LoyaltyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LoyaltyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.enterprise.v1.LoyaltyService",
	HandlerType: (*LoyaltyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateMembership",
			Handler:    _LoyaltyService_CreateMembership_Handler,
		},
		{
			MethodName: "ListMemberships",
			Handler:    _LoyaltyService_ListMemberships_Handler,
		},
		{
			MethodName: "UpdateMembership",
			Handler:    _LoyaltyService_UpdateMembership_Handler,
		},
		{
			MethodName: "DeleteMembership",
			Handler:    _LoyaltyService_DeleteMembership_Handler,
		},
		{
			MethodName: "PushMembershipChanges",
			Handler:    _LoyaltyService_PushMembershipChanges_Handler,
		},
		{
			MethodName: "CreatePackage",
			Handler:    _LoyaltyService_CreatePackage_Handler,
		},
		{
			MethodName: "ListPackages",
			Handler:    _LoyaltyService_ListPackages_Handler,
		},
		{
			MethodName: "UpdatePackage",
			Handler:    _LoyaltyService_UpdatePackage_Handler,
		},
		{
			MethodName: "DeletePackage",
			Handler:    _LoyaltyService_DeletePackage_Handler,
		},
		{
			MethodName: "PushPackageChanges",
			Handler:    _LoyaltyService_PushPackageChanges_Handler,
		},
		{
			MethodName: "CreateDiscount",
			Handler:    _LoyaltyService_CreateDiscount_Handler,
		},
		{
			MethodName: "ListDiscounts",
			Handler:    _LoyaltyService_ListDiscounts_Handler,
		},
		{
			MethodName: "UpdateDiscount",
			Handler:    _LoyaltyService_UpdateDiscount_Handler,
		},
		{
			MethodName: "DeleteDiscount",
			Handler:    _LoyaltyService_DeleteDiscount_Handler,
		},
		{
			MethodName: "GenerateDiscountCode",
			Handler:    _LoyaltyService_GenerateDiscountCode_Handler,
		},
		{
			MethodName: "PushDiscountChanges",
			Handler:    _LoyaltyService_PushDiscountChanges_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/enterprise/v1/loyalty_service.proto",
}
