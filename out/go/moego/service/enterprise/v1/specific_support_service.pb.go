// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/enterprise/v1/specific_support_service.proto

package enterprisesvcpb

import (
	v14 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Evaluation Requirement
type CheckSpecialEvaluationResponse_EvaluationRequirement int32

const (
	// unspecified
	CheckSpecialEvaluationResponse_REQUIREMENT_UNSPECIFIED CheckSpecialEvaluationResponse_EvaluationRequirement = 0
	// none
	CheckSpecialEvaluationResponse_NONE CheckSpecialEvaluationResponse_EvaluationRequirement = 1
	// basic
	CheckSpecialEvaluationResponse_BASIC CheckSpecialEvaluationResponse_EvaluationRequirement = 2
	// special
	CheckSpecialEvaluationResponse_SPECIAL CheckSpecialEvaluationResponse_EvaluationRequirement = 3
)

// Enum value maps for CheckSpecialEvaluationResponse_EvaluationRequirement.
var (
	CheckSpecialEvaluationResponse_EvaluationRequirement_name = map[int32]string{
		0: "REQUIREMENT_UNSPECIFIED",
		1: "NONE",
		2: "BASIC",
		3: "SPECIAL",
	}
	CheckSpecialEvaluationResponse_EvaluationRequirement_value = map[string]int32{
		"REQUIREMENT_UNSPECIFIED": 0,
		"NONE":                    1,
		"BASIC":                   2,
		"SPECIAL":                 3,
	}
)

func (x CheckSpecialEvaluationResponse_EvaluationRequirement) Enum() *CheckSpecialEvaluationResponse_EvaluationRequirement {
	p := new(CheckSpecialEvaluationResponse_EvaluationRequirement)
	*p = x
	return p
}

func (x CheckSpecialEvaluationResponse_EvaluationRequirement) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckSpecialEvaluationResponse_EvaluationRequirement) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_enterprise_v1_specific_support_service_proto_enumTypes[0].Descriptor()
}

func (CheckSpecialEvaluationResponse_EvaluationRequirement) Type() protoreflect.EnumType {
	return &file_moego_service_enterprise_v1_specific_support_service_proto_enumTypes[0]
}

func (x CheckSpecialEvaluationResponse_EvaluationRequirement) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckSpecialEvaluationResponse_EvaluationRequirement.Descriptor instead.
func (CheckSpecialEvaluationResponse_EvaluationRequirement) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{11, 0}
}

// appt reminder type
type EnableAllCustomersMarketingEmailRequest_ApptReminderType int32

const (
	// unspecified
	EnableAllCustomersMarketingEmailRequest_APPT_REMINDER_TYPE_UNSPECIFIED EnableAllCustomersMarketingEmailRequest_ApptReminderType = 0
	// message
	EnableAllCustomersMarketingEmailRequest_MESSAGE EnableAllCustomersMarketingEmailRequest_ApptReminderType = 1
	// email
	EnableAllCustomersMarketingEmailRequest_EMAIL EnableAllCustomersMarketingEmailRequest_ApptReminderType = 2
	// call
	EnableAllCustomersMarketingEmailRequest_CALL EnableAllCustomersMarketingEmailRequest_ApptReminderType = 3
)

// Enum value maps for EnableAllCustomersMarketingEmailRequest_ApptReminderType.
var (
	EnableAllCustomersMarketingEmailRequest_ApptReminderType_name = map[int32]string{
		0: "APPT_REMINDER_TYPE_UNSPECIFIED",
		1: "MESSAGE",
		2: "EMAIL",
		3: "CALL",
	}
	EnableAllCustomersMarketingEmailRequest_ApptReminderType_value = map[string]int32{
		"APPT_REMINDER_TYPE_UNSPECIFIED": 0,
		"MESSAGE":                        1,
		"EMAIL":                          2,
		"CALL":                           3,
	}
)

func (x EnableAllCustomersMarketingEmailRequest_ApptReminderType) Enum() *EnableAllCustomersMarketingEmailRequest_ApptReminderType {
	p := new(EnableAllCustomersMarketingEmailRequest_ApptReminderType)
	*p = x
	return p
}

func (x EnableAllCustomersMarketingEmailRequest_ApptReminderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnableAllCustomersMarketingEmailRequest_ApptReminderType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_enterprise_v1_specific_support_service_proto_enumTypes[1].Descriptor()
}

func (EnableAllCustomersMarketingEmailRequest_ApptReminderType) Type() protoreflect.EnumType {
	return &file_moego_service_enterprise_v1_specific_support_service_proto_enumTypes[1]
}

func (x EnableAllCustomersMarketingEmailRequest_ApptReminderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnableAllCustomersMarketingEmailRequest_ApptReminderType.Descriptor instead.
func (EnableAllCustomersMarketingEmailRequest_ApptReminderType) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{16, 0}
}

// GetCompanyMessageCyclesRequest
type GetCompanyMessageCyclesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyIds []int64 `protobuf:"varint,1,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
}

func (x *GetCompanyMessageCyclesRequest) Reset() {
	*x = GetCompanyMessageCyclesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyMessageCyclesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyMessageCyclesRequest) ProtoMessage() {}

func (x *GetCompanyMessageCyclesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyMessageCyclesRequest.ProtoReflect.Descriptor instead.
func (*GetCompanyMessageCyclesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCompanyMessageCyclesRequest) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

// GetCompanyMessageCyclesResponse
type GetCompanyMessageCyclesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company message cycles
	MessageCycles []*v1.MessageCycle `protobuf:"bytes,1,rep,name=message_cycles,json=messageCycles,proto3" json:"message_cycles,omitempty"`
}

func (x *GetCompanyMessageCyclesResponse) Reset() {
	*x = GetCompanyMessageCyclesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyMessageCyclesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyMessageCyclesResponse) ProtoMessage() {}

func (x *GetCompanyMessageCyclesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyMessageCyclesResponse.ProtoReflect.Descriptor instead.
func (*GetCompanyMessageCyclesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCompanyMessageCyclesResponse) GetMessageCycles() []*v1.MessageCycle {
	if x != nil {
		return x.MessageCycles
	}
	return nil
}

// CheckCustomersInMessageListRequest
type CheckCustomersInMessageListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the customer id
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
}

func (x *CheckCustomersInMessageListRequest) Reset() {
	*x = CheckCustomersInMessageListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCustomersInMessageListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCustomersInMessageListRequest) ProtoMessage() {}

func (x *CheckCustomersInMessageListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCustomersInMessageListRequest.ProtoReflect.Descriptor instead.
func (*CheckCustomersInMessageListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{2}
}

func (x *CheckCustomersInMessageListRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// CheckCustomersInMessageListResponse
type CheckCustomersInMessageListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the customers in message list
	InMessageList map[int64]bool `protobuf:"bytes,1,rep,name=in_message_list,json=inMessageList,proto3" json:"in_message_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *CheckCustomersInMessageListResponse) Reset() {
	*x = CheckCustomersInMessageListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCustomersInMessageListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCustomersInMessageListResponse) ProtoMessage() {}

func (x *CheckCustomersInMessageListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCustomersInMessageListResponse.ProtoReflect.Descriptor instead.
func (*CheckCustomersInMessageListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{3}
}

func (x *CheckCustomersInMessageListResponse) GetInMessageList() map[int64]bool {
	if x != nil {
		return x.InMessageList
	}
	return nil
}

// GetCustomersInMessageListRequest
type GetCustomersInMessageListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company ids
	CompanyIds []int64 `protobuf:"varint,1,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
}

func (x *GetCustomersInMessageListRequest) Reset() {
	*x = GetCustomersInMessageListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomersInMessageListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomersInMessageListRequest) ProtoMessage() {}

func (x *GetCustomersInMessageListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomersInMessageListRequest.ProtoReflect.Descriptor instead.
func (*GetCustomersInMessageListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetCustomersInMessageListRequest) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

// GetCustomersInMessageListResponse
type GetCustomersInMessageListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the customers in message list
	Bindings []*GetCustomersInMessageListResponse_Binding `protobuf:"bytes,1,rep,name=bindings,proto3" json:"bindings,omitempty"`
}

func (x *GetCustomersInMessageListResponse) Reset() {
	*x = GetCustomersInMessageListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomersInMessageListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomersInMessageListResponse) ProtoMessage() {}

func (x *GetCustomersInMessageListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomersInMessageListResponse.ProtoReflect.Descriptor instead.
func (*GetCustomersInMessageListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetCustomersInMessageListResponse) GetBindings() []*GetCustomersInMessageListResponse_Binding {
	if x != nil {
		return x.Bindings
	}
	return nil
}

// ExportNonStandardPetBreedRequest
type ExportNonStandardPetBreedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// the pet type
	PetType v11.PetType `protobuf:"varint,2,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// standard file url
	StandardFileUrl string `protobuf:"bytes,3,opt,name=standard_file_url,json=standardFileUrl,proto3" json:"standard_file_url,omitempty"`
}

func (x *ExportNonStandardPetBreedRequest) Reset() {
	*x = ExportNonStandardPetBreedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportNonStandardPetBreedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportNonStandardPetBreedRequest) ProtoMessage() {}

func (x *ExportNonStandardPetBreedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportNonStandardPetBreedRequest.ProtoReflect.Descriptor instead.
func (*ExportNonStandardPetBreedRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{6}
}

func (x *ExportNonStandardPetBreedRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ExportNonStandardPetBreedRequest) GetPetType() v11.PetType {
	if x != nil {
		return x.PetType
	}
	return v11.PetType(0)
}

func (x *ExportNonStandardPetBreedRequest) GetStandardFileUrl() string {
	if x != nil {
		return x.StandardFileUrl
	}
	return ""
}

// ExportNonStandardPetBreedResponse
type ExportNonStandardPetBreedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the export result
	FileUrl string `protobuf:"bytes,1,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
}

func (x *ExportNonStandardPetBreedResponse) Reset() {
	*x = ExportNonStandardPetBreedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportNonStandardPetBreedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportNonStandardPetBreedResponse) ProtoMessage() {}

func (x *ExportNonStandardPetBreedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportNonStandardPetBreedResponse.ProtoReflect.Descriptor instead.
func (*ExportNonStandardPetBreedResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{7}
}

func (x *ExportNonStandardPetBreedResponse) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

// ImportPetCodeBindingRequest
type ImportPetCodeBindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// the file url
	FileUrl string `protobuf:"bytes,2,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
}

func (x *ImportPetCodeBindingRequest) Reset() {
	*x = ImportPetCodeBindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportPetCodeBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportPetCodeBindingRequest) ProtoMessage() {}

func (x *ImportPetCodeBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportPetCodeBindingRequest.ProtoReflect.Descriptor instead.
func (*ImportPetCodeBindingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{8}
}

func (x *ImportPetCodeBindingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ImportPetCodeBindingRequest) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

// ImportPetCodeBindingResponse
type ImportPetCodeBindingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the import result
	Skips []*ImportPetCodeBindingResponse_Skip `protobuf:"bytes,1,rep,name=skips,proto3" json:"skips,omitempty"`
}

func (x *ImportPetCodeBindingResponse) Reset() {
	*x = ImportPetCodeBindingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportPetCodeBindingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportPetCodeBindingResponse) ProtoMessage() {}

func (x *ImportPetCodeBindingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportPetCodeBindingResponse.ProtoReflect.Descriptor instead.
func (*ImportPetCodeBindingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{9}
}

func (x *ImportPetCodeBindingResponse) GetSkips() []*ImportPetCodeBindingResponse_Skip {
	if x != nil {
		return x.Skips
	}
	return nil
}

// CheckSpecialEvaluationRequest
type CheckSpecialEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// the pet service ids
	PetServiceIds map[int64]*CheckSpecialEvaluationRequest_ServiceIDs `protobuf:"bytes,2,rep,name=pet_service_ids,json=petServiceIds,proto3" json:"pet_service_ids,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CheckSpecialEvaluationRequest) Reset() {
	*x = CheckSpecialEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSpecialEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSpecialEvaluationRequest) ProtoMessage() {}

func (x *CheckSpecialEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSpecialEvaluationRequest.ProtoReflect.Descriptor instead.
func (*CheckSpecialEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{10}
}

func (x *CheckSpecialEvaluationRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CheckSpecialEvaluationRequest) GetPetServiceIds() map[int64]*CheckSpecialEvaluationRequest_ServiceIDs {
	if x != nil {
		return x.PetServiceIds
	}
	return nil
}

// CheckSpecialEvaluationResponse
type CheckSpecialEvaluationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet service evaluation check results
	Results []*CheckSpecialEvaluationResponse_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *CheckSpecialEvaluationResponse) Reset() {
	*x = CheckSpecialEvaluationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSpecialEvaluationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSpecialEvaluationResponse) ProtoMessage() {}

func (x *CheckSpecialEvaluationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSpecialEvaluationResponse.ProtoReflect.Descriptor instead.
func (*CheckSpecialEvaluationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{11}
}

func (x *CheckSpecialEvaluationResponse) GetResults() []*CheckSpecialEvaluationResponse_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

// ListApplicableLineItemsRequest
type ListApplicableLineItemsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// discount id
	DiscountId int64 `protobuf:"varint,2,opt,name=discount_id,json=discountId,proto3" json:"discount_id,omitempty"`
	// order id
	OrderId int64 `protobuf:"varint,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *ListApplicableLineItemsRequest) Reset() {
	*x = ListApplicableLineItemsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListApplicableLineItemsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApplicableLineItemsRequest) ProtoMessage() {}

func (x *ListApplicableLineItemsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApplicableLineItemsRequest.ProtoReflect.Descriptor instead.
func (*ListApplicableLineItemsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{12}
}

func (x *ListApplicableLineItemsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListApplicableLineItemsRequest) GetDiscountId() int64 {
	if x != nil {
		return x.DiscountId
	}
	return 0
}

func (x *ListApplicableLineItemsRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

// ListApplicableLineItemsResponse
type ListApplicableLineItemsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order Line Item Model
	OrderLineItems []*v12.OrderLineItemModel `protobuf:"bytes,1,rep,name=order_line_items,json=orderLineItems,proto3" json:"order_line_items,omitempty"`
	// Business Customer Pet Info Model
	Pets []*v13.BusinessCustomerPetInfoModel `protobuf:"bytes,2,rep,name=pets,proto3" json:"pets,omitempty"`
}

func (x *ListApplicableLineItemsResponse) Reset() {
	*x = ListApplicableLineItemsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListApplicableLineItemsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApplicableLineItemsResponse) ProtoMessage() {}

func (x *ListApplicableLineItemsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApplicableLineItemsResponse.ProtoReflect.Descriptor instead.
func (*ListApplicableLineItemsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{13}
}

func (x *ListApplicableLineItemsResponse) GetOrderLineItems() []*v12.OrderLineItemModel {
	if x != nil {
		return x.OrderLineItems
	}
	return nil
}

func (x *ListApplicableLineItemsResponse) GetPets() []*v13.BusinessCustomerPetInfoModel {
	if x != nil {
		return x.Pets
	}
	return nil
}

// start vet verify task job request
type StartVetVerifyTaskJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// vet verify id
	VetVerifyId string `protobuf:"bytes,2,opt,name=vet_verify_id,json=vetVerifyId,proto3" json:"vet_verify_id,omitempty"`
}

func (x *StartVetVerifyTaskJobRequest) Reset() {
	*x = StartVetVerifyTaskJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartVetVerifyTaskJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartVetVerifyTaskJobRequest) ProtoMessage() {}

func (x *StartVetVerifyTaskJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartVetVerifyTaskJobRequest.ProtoReflect.Descriptor instead.
func (*StartVetVerifyTaskJobRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{14}
}

func (x *StartVetVerifyTaskJobRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *StartVetVerifyTaskJobRequest) GetVetVerifyId() string {
	if x != nil {
		return x.VetVerifyId
	}
	return ""
}

// start vet verify task job response
type StartVetVerifyTaskJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// task id
	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// task name
	TaskName string `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
}

func (x *StartVetVerifyTaskJobResponse) Reset() {
	*x = StartVetVerifyTaskJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartVetVerifyTaskJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartVetVerifyTaskJobResponse) ProtoMessage() {}

func (x *StartVetVerifyTaskJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartVetVerifyTaskJobResponse.ProtoReflect.Descriptor instead.
func (*StartVetVerifyTaskJobResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{15}
}

func (x *StartVetVerifyTaskJobResponse) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *StartVetVerifyTaskJobResponse) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

// enable all customers marketing email request
type EnableAllCustomersMarketingEmailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// send auto message
	SendAutoMessage *bool `protobuf:"varint,2,opt,name=send_auto_message,json=sendAutoMessage,proto3,oneof" json:"send_auto_message,omitempty"`
	// send auto email
	SendAutoEmail *bool `protobuf:"varint,3,opt,name=send_auto_email,json=sendAutoEmail,proto3,oneof" json:"send_auto_email,omitempty"`
	// is unsubscribed marketing email
	IsUnsubscribed *bool `protobuf:"varint,4,opt,name=is_unsubscribed,json=isUnsubscribed,proto3,oneof" json:"is_unsubscribed,omitempty"`
	// appt reminders
	ApptReminders []EnableAllCustomersMarketingEmailRequest_ApptReminderType `protobuf:"varint,5,rep,packed,name=appt_reminders,json=apptReminders,proto3,enum=moego.service.enterprise.v1.EnableAllCustomersMarketingEmailRequest_ApptReminderType" json:"appt_reminders,omitempty"`
}

func (x *EnableAllCustomersMarketingEmailRequest) Reset() {
	*x = EnableAllCustomersMarketingEmailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnableAllCustomersMarketingEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableAllCustomersMarketingEmailRequest) ProtoMessage() {}

func (x *EnableAllCustomersMarketingEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableAllCustomersMarketingEmailRequest.ProtoReflect.Descriptor instead.
func (*EnableAllCustomersMarketingEmailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{16}
}

func (x *EnableAllCustomersMarketingEmailRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *EnableAllCustomersMarketingEmailRequest) GetSendAutoMessage() bool {
	if x != nil && x.SendAutoMessage != nil {
		return *x.SendAutoMessage
	}
	return false
}

func (x *EnableAllCustomersMarketingEmailRequest) GetSendAutoEmail() bool {
	if x != nil && x.SendAutoEmail != nil {
		return *x.SendAutoEmail
	}
	return false
}

func (x *EnableAllCustomersMarketingEmailRequest) GetIsUnsubscribed() bool {
	if x != nil && x.IsUnsubscribed != nil {
		return *x.IsUnsubscribed
	}
	return false
}

func (x *EnableAllCustomersMarketingEmailRequest) GetApptReminders() []EnableAllCustomersMarketingEmailRequest_ApptReminderType {
	if x != nil {
		return x.ApptReminders
	}
	return nil
}

// enable all customers marketing email response
type EnableAllCustomersMarketingEmailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EnableAllCustomersMarketingEmailResponse) Reset() {
	*x = EnableAllCustomersMarketingEmailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnableAllCustomersMarketingEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableAllCustomersMarketingEmailResponse) ProtoMessage() {}

func (x *EnableAllCustomersMarketingEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableAllCustomersMarketingEmailResponse.ProtoReflect.Descriptor instead.
func (*EnableAllCustomersMarketingEmailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{17}
}

// binding
type GetCustomersInMessageListResponse_Binding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the customer
	Customer *v13.BusinessCustomerModel `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	// the tags
	Tags []string `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
	// the last appointment
	LastAppointment *v14.AppointmentModel `protobuf:"bytes,3,opt,name=last_appointment,json=lastAppointment,proto3" json:"last_appointment,omitempty"`
}

func (x *GetCustomersInMessageListResponse_Binding) Reset() {
	*x = GetCustomersInMessageListResponse_Binding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomersInMessageListResponse_Binding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomersInMessageListResponse_Binding) ProtoMessage() {}

func (x *GetCustomersInMessageListResponse_Binding) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomersInMessageListResponse_Binding.ProtoReflect.Descriptor instead.
func (*GetCustomersInMessageListResponse_Binding) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{5, 0}
}

func (x *GetCustomersInMessageListResponse_Binding) GetCustomer() *v13.BusinessCustomerModel {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *GetCustomersInMessageListResponse_Binding) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *GetCustomersInMessageListResponse_Binding) GetLastAppointment() *v14.AppointmentModel {
	if x != nil {
		return x.LastAppointment
	}
	return nil
}

// skip pet name, client name, pet breed
type ImportPetCodeBindingResponse_Skip struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pet name
	PetName string `protobuf:"bytes,1,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// the client name
	ClientName string `protobuf:"bytes,2,opt,name=client_name,json=clientName,proto3" json:"client_name,omitempty"`
	// the pet breed
	PetBreed string `protobuf:"bytes,3,opt,name=pet_breed,json=petBreed,proto3" json:"pet_breed,omitempty"`
}

func (x *ImportPetCodeBindingResponse_Skip) Reset() {
	*x = ImportPetCodeBindingResponse_Skip{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportPetCodeBindingResponse_Skip) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportPetCodeBindingResponse_Skip) ProtoMessage() {}

func (x *ImportPetCodeBindingResponse_Skip) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportPetCodeBindingResponse_Skip.ProtoReflect.Descriptor instead.
func (*ImportPetCodeBindingResponse_Skip) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ImportPetCodeBindingResponse_Skip) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *ImportPetCodeBindingResponse_Skip) GetClientName() string {
	if x != nil {
		return x.ClientName
	}
	return ""
}

func (x *ImportPetCodeBindingResponse_Skip) GetPetBreed() string {
	if x != nil {
		return x.PetBreed
	}
	return ""
}

// service ids
type CheckSpecialEvaluationRequest_ServiceIDs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service ids
	ServiceIds []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *CheckSpecialEvaluationRequest_ServiceIDs) Reset() {
	*x = CheckSpecialEvaluationRequest_ServiceIDs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSpecialEvaluationRequest_ServiceIDs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSpecialEvaluationRequest_ServiceIDs) ProtoMessage() {}

func (x *CheckSpecialEvaluationRequest_ServiceIDs) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSpecialEvaluationRequest_ServiceIDs.ProtoReflect.Descriptor instead.
func (*CheckSpecialEvaluationRequest_ServiceIDs) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *CheckSpecialEvaluationRequest_ServiceIDs) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// Result
type CheckSpecialEvaluationResponse_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// evaluation requirement
	EvaluationRequirement CheckSpecialEvaluationResponse_EvaluationRequirement `protobuf:"varint,3,opt,name=evaluation_requirement,json=evaluationRequirement,proto3,enum=moego.service.enterprise.v1.CheckSpecialEvaluationResponse_EvaluationRequirement" json:"evaluation_requirement,omitempty"`
	// need evaluation
	NeedEvaluation bool `protobuf:"varint,4,opt,name=need_evaluation,json=needEvaluation,proto3" json:"need_evaluation,omitempty"`
}

func (x *CheckSpecialEvaluationResponse_Result) Reset() {
	*x = CheckSpecialEvaluationResponse_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSpecialEvaluationResponse_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSpecialEvaluationResponse_Result) ProtoMessage() {}

func (x *CheckSpecialEvaluationResponse_Result) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSpecialEvaluationResponse_Result.ProtoReflect.Descriptor instead.
func (*CheckSpecialEvaluationResponse_Result) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP(), []int{11, 0}
}

func (x *CheckSpecialEvaluationResponse_Result) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *CheckSpecialEvaluationResponse_Result) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *CheckSpecialEvaluationResponse_Result) GetEvaluationRequirement() CheckSpecialEvaluationResponse_EvaluationRequirement {
	if x != nil {
		return x.EvaluationRequirement
	}
	return CheckSpecialEvaluationResponse_REQUIREMENT_UNSPECIFIED
}

func (x *CheckSpecialEvaluationResponse_Result) GetNeedEvaluation() bool {
	if x != nil {
		return x.NeedEvaluation
	}
	return false
}

var File_moego_service_enterprise_v1_specific_support_service_proto protoreflect.FileDescriptor

var file_moego_service_enterprise_v1_specific_support_service_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x41, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x73, 0x22, 0x72, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x0d, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x73, 0x22, 0x47, 0x0a, 0x22, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x22, 0xe4, 0x01, 0x0a, 0x23, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x0f, 0x69, 0x6e, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x53, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x49,
	0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x69, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x40, 0x0a, 0x12, 0x49, 0x6e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x43, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0xd7, 0x02,
	0x0a, 0x21, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x49, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x08, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73,
	0x49, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x62,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x1a, 0xcd, 0x01, 0x0a, 0x07, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x54, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x58, 0x0a,
	0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xba, 0x01, 0x0a, 0x20, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x4e, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74,
	0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x6e,
	0x64, 0x61, 0x72, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x46, 0x69, 0x6c,
	0x65, 0x55, 0x72, 0x6c, 0x22, 0x3e, 0x0a, 0x21, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c,
	0x65, 0x55, 0x72, 0x6c, 0x22, 0x60, 0x0a, 0x1b, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66,
	0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x22, 0xd5, 0x01, 0x0a, 0x1c, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x05, 0x73, 0x6b, 0x69, 0x70, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x53, 0x6b, 0x69, 0x70, 0x52, 0x05, 0x73, 0x6b, 0x69, 0x70, 0x73, 0x1a, 0x5f, 0x0a,
	0x04, 0x53, 0x6b, 0x69, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x22, 0xf7,
	0x02, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x75, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x1a,
	0x2d, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x1a, 0x87,
	0x01, 0x0a, 0x12, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x5b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x73, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xcb, 0x03, 0x0a, 0x1e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x07, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0xf2, 0x01, 0x0a, 0x06, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x88, 0x01, 0x0a, 0x16, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x51, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x15,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e,
	0x6e, 0x65, 0x65, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x56,
	0x0a, 0x15, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x51, 0x55, 0x49,
	0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x09,
	0x0a, 0x05, 0x42, 0x41, 0x53, 0x49, 0x43, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x41, 0x4c, 0x10, 0x03, 0x22, 0x84, 0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0xcb, 0x01,
	0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65,
	0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x53, 0x0a, 0x10, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x53, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x22, 0x73, 0x0a, 0x1c, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x56, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x61, 0x73,
	0x6b, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0d, 0x76, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0b, 0x76, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x49, 0x64,
	0x22, 0x55, 0x0a, 0x1d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x56, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xf3, 0x03, 0x0a, 0x27, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x4d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x11, 0x73,
	0x65, 0x6e, 0x64, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x6e, 0x64, 0x41, 0x75,
	0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f,
	0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x0d, 0x73, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74,
	0x6f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x69, 0x73, 0x5f,
	0x75, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x02, 0x52, 0x0e, 0x69, 0x73, 0x55, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x7c, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x74, 0x5f,
	0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x55, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73,
	0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x74, 0x52, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x73, 0x22, 0x58, 0x0a, 0x10, 0x41, 0x70, 0x70, 0x74, 0x52, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x50, 0x50,
	0x54, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x03, 0x42,
	0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x73,
	0x5f, 0x75, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x64, 0x22, 0x2a, 0x0a,
	0x28, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x73, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x88, 0x0b, 0x0a, 0x16, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x73,
	0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x43, 0x79, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x79, 0x63,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa0, 0x01, 0x0a, 0x1b,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x49, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9a,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x49,
	0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x19,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72,
	0x64, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x14, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x17, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6e,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65,
	0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x8e, 0x01, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x72, 0x74, 0x56, 0x65, 0x74, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x4a, 0x6f, 0x62, 0x12, 0x39, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x56,
	0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x4a, 0x6f, 0x62, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x56, 0x65, 0x74, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0xaf, 0x01, 0x0a, 0x20, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x6c,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e,
	0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x4d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x89, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31,
	0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x73, 0x76, 0x63, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_enterprise_v1_specific_support_service_proto_rawDescOnce sync.Once
	file_moego_service_enterprise_v1_specific_support_service_proto_rawDescData = file_moego_service_enterprise_v1_specific_support_service_proto_rawDesc
)

func file_moego_service_enterprise_v1_specific_support_service_proto_rawDescGZIP() []byte {
	file_moego_service_enterprise_v1_specific_support_service_proto_rawDescOnce.Do(func() {
		file_moego_service_enterprise_v1_specific_support_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_enterprise_v1_specific_support_service_proto_rawDescData)
	})
	return file_moego_service_enterprise_v1_specific_support_service_proto_rawDescData
}

var file_moego_service_enterprise_v1_specific_support_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_moego_service_enterprise_v1_specific_support_service_proto_goTypes = []interface{}{
	(CheckSpecialEvaluationResponse_EvaluationRequirement)(0),     // 0: moego.service.enterprise.v1.CheckSpecialEvaluationResponse.EvaluationRequirement
	(EnableAllCustomersMarketingEmailRequest_ApptReminderType)(0), // 1: moego.service.enterprise.v1.EnableAllCustomersMarketingEmailRequest.ApptReminderType
	(*GetCompanyMessageCyclesRequest)(nil),                        // 2: moego.service.enterprise.v1.GetCompanyMessageCyclesRequest
	(*GetCompanyMessageCyclesResponse)(nil),                       // 3: moego.service.enterprise.v1.GetCompanyMessageCyclesResponse
	(*CheckCustomersInMessageListRequest)(nil),                    // 4: moego.service.enterprise.v1.CheckCustomersInMessageListRequest
	(*CheckCustomersInMessageListResponse)(nil),                   // 5: moego.service.enterprise.v1.CheckCustomersInMessageListResponse
	(*GetCustomersInMessageListRequest)(nil),                      // 6: moego.service.enterprise.v1.GetCustomersInMessageListRequest
	(*GetCustomersInMessageListResponse)(nil),                     // 7: moego.service.enterprise.v1.GetCustomersInMessageListResponse
	(*ExportNonStandardPetBreedRequest)(nil),                      // 8: moego.service.enterprise.v1.ExportNonStandardPetBreedRequest
	(*ExportNonStandardPetBreedResponse)(nil),                     // 9: moego.service.enterprise.v1.ExportNonStandardPetBreedResponse
	(*ImportPetCodeBindingRequest)(nil),                           // 10: moego.service.enterprise.v1.ImportPetCodeBindingRequest
	(*ImportPetCodeBindingResponse)(nil),                          // 11: moego.service.enterprise.v1.ImportPetCodeBindingResponse
	(*CheckSpecialEvaluationRequest)(nil),                         // 12: moego.service.enterprise.v1.CheckSpecialEvaluationRequest
	(*CheckSpecialEvaluationResponse)(nil),                        // 13: moego.service.enterprise.v1.CheckSpecialEvaluationResponse
	(*ListApplicableLineItemsRequest)(nil),                        // 14: moego.service.enterprise.v1.ListApplicableLineItemsRequest
	(*ListApplicableLineItemsResponse)(nil),                       // 15: moego.service.enterprise.v1.ListApplicableLineItemsResponse
	(*StartVetVerifyTaskJobRequest)(nil),                          // 16: moego.service.enterprise.v1.StartVetVerifyTaskJobRequest
	(*StartVetVerifyTaskJobResponse)(nil),                         // 17: moego.service.enterprise.v1.StartVetVerifyTaskJobResponse
	(*EnableAllCustomersMarketingEmailRequest)(nil),               // 18: moego.service.enterprise.v1.EnableAllCustomersMarketingEmailRequest
	(*EnableAllCustomersMarketingEmailResponse)(nil),              // 19: moego.service.enterprise.v1.EnableAllCustomersMarketingEmailResponse
	nil, // 20: moego.service.enterprise.v1.CheckCustomersInMessageListResponse.InMessageListEntry
	(*GetCustomersInMessageListResponse_Binding)(nil), // 21: moego.service.enterprise.v1.GetCustomersInMessageListResponse.Binding
	(*ImportPetCodeBindingResponse_Skip)(nil),         // 22: moego.service.enterprise.v1.ImportPetCodeBindingResponse.Skip
	(*CheckSpecialEvaluationRequest_ServiceIDs)(nil),  // 23: moego.service.enterprise.v1.CheckSpecialEvaluationRequest.ServiceIDs
	nil, // 24: moego.service.enterprise.v1.CheckSpecialEvaluationRequest.PetServiceIdsEntry
	(*CheckSpecialEvaluationResponse_Result)(nil), // 25: moego.service.enterprise.v1.CheckSpecialEvaluationResponse.Result
	(*v1.MessageCycle)(nil),                       // 26: moego.models.enterprise.v1.MessageCycle
	(v11.PetType)(0),                              // 27: moego.models.customer.v1.PetType
	(*v12.OrderLineItemModel)(nil),                // 28: moego.models.order.v1.OrderLineItemModel
	(*v13.BusinessCustomerPetInfoModel)(nil),      // 29: moego.models.business_customer.v1.BusinessCustomerPetInfoModel
	(*v13.BusinessCustomerModel)(nil),             // 30: moego.models.business_customer.v1.BusinessCustomerModel
	(*v14.AppointmentModel)(nil),                  // 31: moego.models.appointment.v1.AppointmentModel
}
var file_moego_service_enterprise_v1_specific_support_service_proto_depIdxs = []int32{
	26, // 0: moego.service.enterprise.v1.GetCompanyMessageCyclesResponse.message_cycles:type_name -> moego.models.enterprise.v1.MessageCycle
	20, // 1: moego.service.enterprise.v1.CheckCustomersInMessageListResponse.in_message_list:type_name -> moego.service.enterprise.v1.CheckCustomersInMessageListResponse.InMessageListEntry
	21, // 2: moego.service.enterprise.v1.GetCustomersInMessageListResponse.bindings:type_name -> moego.service.enterprise.v1.GetCustomersInMessageListResponse.Binding
	27, // 3: moego.service.enterprise.v1.ExportNonStandardPetBreedRequest.pet_type:type_name -> moego.models.customer.v1.PetType
	22, // 4: moego.service.enterprise.v1.ImportPetCodeBindingResponse.skips:type_name -> moego.service.enterprise.v1.ImportPetCodeBindingResponse.Skip
	24, // 5: moego.service.enterprise.v1.CheckSpecialEvaluationRequest.pet_service_ids:type_name -> moego.service.enterprise.v1.CheckSpecialEvaluationRequest.PetServiceIdsEntry
	25, // 6: moego.service.enterprise.v1.CheckSpecialEvaluationResponse.results:type_name -> moego.service.enterprise.v1.CheckSpecialEvaluationResponse.Result
	28, // 7: moego.service.enterprise.v1.ListApplicableLineItemsResponse.order_line_items:type_name -> moego.models.order.v1.OrderLineItemModel
	29, // 8: moego.service.enterprise.v1.ListApplicableLineItemsResponse.pets:type_name -> moego.models.business_customer.v1.BusinessCustomerPetInfoModel
	1,  // 9: moego.service.enterprise.v1.EnableAllCustomersMarketingEmailRequest.appt_reminders:type_name -> moego.service.enterprise.v1.EnableAllCustomersMarketingEmailRequest.ApptReminderType
	30, // 10: moego.service.enterprise.v1.GetCustomersInMessageListResponse.Binding.customer:type_name -> moego.models.business_customer.v1.BusinessCustomerModel
	31, // 11: moego.service.enterprise.v1.GetCustomersInMessageListResponse.Binding.last_appointment:type_name -> moego.models.appointment.v1.AppointmentModel
	23, // 12: moego.service.enterprise.v1.CheckSpecialEvaluationRequest.PetServiceIdsEntry.value:type_name -> moego.service.enterprise.v1.CheckSpecialEvaluationRequest.ServiceIDs
	0,  // 13: moego.service.enterprise.v1.CheckSpecialEvaluationResponse.Result.evaluation_requirement:type_name -> moego.service.enterprise.v1.CheckSpecialEvaluationResponse.EvaluationRequirement
	2,  // 14: moego.service.enterprise.v1.SpecificSupportService.GetCompanyMessageCycles:input_type -> moego.service.enterprise.v1.GetCompanyMessageCyclesRequest
	4,  // 15: moego.service.enterprise.v1.SpecificSupportService.CheckCustomersInMessageList:input_type -> moego.service.enterprise.v1.CheckCustomersInMessageListRequest
	6,  // 16: moego.service.enterprise.v1.SpecificSupportService.GetCustomersInMessageList:input_type -> moego.service.enterprise.v1.GetCustomersInMessageListRequest
	8,  // 17: moego.service.enterprise.v1.SpecificSupportService.ExportNonStandardPetBreed:input_type -> moego.service.enterprise.v1.ExportNonStandardPetBreedRequest
	10, // 18: moego.service.enterprise.v1.SpecificSupportService.ImportPetCodeBinding:input_type -> moego.service.enterprise.v1.ImportPetCodeBindingRequest
	12, // 19: moego.service.enterprise.v1.SpecificSupportService.CheckSpecialEvaluation:input_type -> moego.service.enterprise.v1.CheckSpecialEvaluationRequest
	14, // 20: moego.service.enterprise.v1.SpecificSupportService.ListApplicableLineItems:input_type -> moego.service.enterprise.v1.ListApplicableLineItemsRequest
	16, // 21: moego.service.enterprise.v1.SpecificSupportService.StartVetVerifyTaskJob:input_type -> moego.service.enterprise.v1.StartVetVerifyTaskJobRequest
	18, // 22: moego.service.enterprise.v1.SpecificSupportService.EnableAllCustomersMarketingEmail:input_type -> moego.service.enterprise.v1.EnableAllCustomersMarketingEmailRequest
	3,  // 23: moego.service.enterprise.v1.SpecificSupportService.GetCompanyMessageCycles:output_type -> moego.service.enterprise.v1.GetCompanyMessageCyclesResponse
	5,  // 24: moego.service.enterprise.v1.SpecificSupportService.CheckCustomersInMessageList:output_type -> moego.service.enterprise.v1.CheckCustomersInMessageListResponse
	7,  // 25: moego.service.enterprise.v1.SpecificSupportService.GetCustomersInMessageList:output_type -> moego.service.enterprise.v1.GetCustomersInMessageListResponse
	9,  // 26: moego.service.enterprise.v1.SpecificSupportService.ExportNonStandardPetBreed:output_type -> moego.service.enterprise.v1.ExportNonStandardPetBreedResponse
	11, // 27: moego.service.enterprise.v1.SpecificSupportService.ImportPetCodeBinding:output_type -> moego.service.enterprise.v1.ImportPetCodeBindingResponse
	13, // 28: moego.service.enterprise.v1.SpecificSupportService.CheckSpecialEvaluation:output_type -> moego.service.enterprise.v1.CheckSpecialEvaluationResponse
	15, // 29: moego.service.enterprise.v1.SpecificSupportService.ListApplicableLineItems:output_type -> moego.service.enterprise.v1.ListApplicableLineItemsResponse
	17, // 30: moego.service.enterprise.v1.SpecificSupportService.StartVetVerifyTaskJob:output_type -> moego.service.enterprise.v1.StartVetVerifyTaskJobResponse
	19, // 31: moego.service.enterprise.v1.SpecificSupportService.EnableAllCustomersMarketingEmail:output_type -> moego.service.enterprise.v1.EnableAllCustomersMarketingEmailResponse
	23, // [23:32] is the sub-list for method output_type
	14, // [14:23] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_moego_service_enterprise_v1_specific_support_service_proto_init() }
func file_moego_service_enterprise_v1_specific_support_service_proto_init() {
	if File_moego_service_enterprise_v1_specific_support_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyMessageCyclesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyMessageCyclesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCustomersInMessageListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCustomersInMessageListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomersInMessageListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomersInMessageListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportNonStandardPetBreedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportNonStandardPetBreedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportPetCodeBindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportPetCodeBindingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSpecialEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSpecialEvaluationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListApplicableLineItemsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListApplicableLineItemsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartVetVerifyTaskJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartVetVerifyTaskJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnableAllCustomersMarketingEmailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnableAllCustomersMarketingEmailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomersInMessageListResponse_Binding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportPetCodeBindingResponse_Skip); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSpecialEvaluationRequest_ServiceIDs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSpecialEvaluationResponse_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_enterprise_v1_specific_support_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_enterprise_v1_specific_support_service_proto_goTypes,
		DependencyIndexes: file_moego_service_enterprise_v1_specific_support_service_proto_depIdxs,
		EnumInfos:         file_moego_service_enterprise_v1_specific_support_service_proto_enumTypes,
		MessageInfos:      file_moego_service_enterprise_v1_specific_support_service_proto_msgTypes,
	}.Build()
	File_moego_service_enterprise_v1_specific_support_service_proto = out.File
	file_moego_service_enterprise_v1_specific_support_service_proto_rawDesc = nil
	file_moego_service_enterprise_v1_specific_support_service_proto_goTypes = nil
	file_moego_service_enterprise_v1_specific_support_service_proto_depIdxs = nil
}
