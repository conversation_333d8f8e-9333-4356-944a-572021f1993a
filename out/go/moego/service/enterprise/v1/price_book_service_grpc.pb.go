// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/enterprise/v1/price_book_service.proto

package enterprisesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PriceBookServiceClient is the client API for PriceBookService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PriceBookServiceClient interface {
	// list price books
	ListPriceBooks(ctx context.Context, in *ListPriceBooksRequest, opts ...grpc.CallOption) (*ListPriceBooksResponse, error)
	// create price book
	CreatePriceBook(ctx context.Context, in *CreatePriceBookRequest, opts ...grpc.CallOption) (*CreatePriceBookResponse, error)
	// update price book
	UpdatePriceBook(ctx context.Context, in *UpdatePriceBookRequest, opts ...grpc.CallOption) (*UpdatePriceBookResponse, error)
	// delete price book
	DeletePriceBook(ctx context.Context, in *DeletePriceBookRequest, opts ...grpc.CallOption) (*DeletePriceBookResponse, error)
	// duplicate price book
	DuplicatePriceBook(ctx context.Context, in *DuplicatePriceBookRequest, opts ...grpc.CallOption) (*DuplicatePriceBookResponse, error)
	// migrate price book
	MigratePriceBook(ctx context.Context, in *MigratePriceBookRequest, opts ...grpc.CallOption) (*MigratePriceBookResponse, error)
	// init price book
	InitPriceBook(ctx context.Context, in *InitPriceBookRequest, opts ...grpc.CallOption) (*InitPriceBookResponse, error)
	// create and update existed categories
	SaveServiceCategories(ctx context.Context, in *SaveServiceCategoriesRequest, opts ...grpc.CallOption) (*SaveServiceCategoriesResponse, error)
	// list service categories
	ListServiceCategories(ctx context.Context, in *ListServiceCategoriesRequest, opts ...grpc.CallOption) (*ListServiceCategoriesResponse, error)
	// list pet breeds
	ListPetBreeds(ctx context.Context, in *ListPetBreedsRequest, opts ...grpc.CallOption) (*ListPetBreedsResponse, error)
	// list pet types
	ListPetTypes(ctx context.Context, in *ListPetTypesRequest, opts ...grpc.CallOption) (*ListPetTypesResponse, error)
	// create a service
	CreateService(ctx context.Context, in *CreateServiceRequest, opts ...grpc.CallOption) (*CreateServiceResponse, error)
	// get a service
	GetService(ctx context.Context, in *GetServiceRequest, opts ...grpc.CallOption) (*GetServiceResponse, error)
	// list services
	ListServices(ctx context.Context, in *ListServicesRequest, opts ...grpc.CallOption) (*ListServicesResponse, error)
	// update a service
	UpdateService(ctx context.Context, in *UpdateServiceRequest, opts ...grpc.CallOption) (*UpdateServiceResponse, error)
	// delete a service
	DeleteService(ctx context.Context, in *DeleteServiceRequest, opts ...grpc.CallOption) (*DeleteServiceResponse, error)
	// sort services
	SortServices(ctx context.Context, in *SortServicesRequest, opts ...grpc.CallOption) (*SortServicesResponse, error)
	// list service change histories
	ListServiceChangeHistories(ctx context.Context, in *ListServiceChangeHistoriesRequest, opts ...grpc.CallOption) (*ListServiceChangeHistoriesResponse, error)
	// list service changes
	ListServiceChanges(ctx context.Context, in *ListServiceChangesRequest, opts ...grpc.CallOption) (*ListServiceChangesResponse, error)
	// push service changes
	PushServiceChanges(ctx context.Context, in *PushServiceChangesRequest, opts ...grpc.CallOption) (*PushServiceChangesResponse, error)
	// evaluation service
	// create evaluation
	CreateEvaluation(ctx context.Context, in *CreateEvaluationRequest, opts ...grpc.CallOption) (*CreateEvaluationResponse, error)
	// list evaluations
	ListEvaluations(ctx context.Context, in *ListEvaluationsRequest, opts ...grpc.CallOption) (*ListEvaluationsResponse, error)
	// update evaluation
	UpdateEvaluation(ctx context.Context, in *UpdateEvaluationRequest, opts ...grpc.CallOption) (*UpdateEvaluationResponse, error)
	// todo: delete evaluation
	// sort evaluations
	SortEvaluations(ctx context.Context, in *SortEvaluationsRequest, opts ...grpc.CallOption) (*SortEvaluationsResponse, error)
	// push evaluation changes
	PushEvaluationChanges(ctx context.Context, in *PushEvaluationChangesRequest, opts ...grpc.CallOption) (*PushEvaluationChangesResponse, error)
	// pricing rule
	// create pricing rule
	CreatePricingRule(ctx context.Context, in *CreatePricingRuleRequest, opts ...grpc.CallOption) (*CreatePricingRuleResponse, error)
	// list pricing rules
	ListPricingRules(ctx context.Context, in *ListPricingRulesRequest, opts ...grpc.CallOption) (*ListPricingRulesResponse, error)
	// update pricing rule
	UpdatePricingRule(ctx context.Context, in *UpdatePricingRuleRequest, opts ...grpc.CallOption) (*UpdatePricingRuleResponse, error)
	// todo: delete pricing rule
	// sort pricing rules
	SortPricingRules(ctx context.Context, in *SortPricingRulesRequest, opts ...grpc.CallOption) (*SortPricingRulesResponse, error)
	// push pricing rule changes
	PushPricingRuleChanges(ctx context.Context, in *PushPricingRuleChangesRequest, opts ...grpc.CallOption) (*PushPricingRuleChangesResponse, error)
	// service charge
	// create service charge
	CreateServiceCharge(ctx context.Context, in *CreateServiceChargeRequest, opts ...grpc.CallOption) (*CreateServiceChargeResponse, error)
	// list service charges
	ListServiceCharges(ctx context.Context, in *ListServiceChargesRequest, opts ...grpc.CallOption) (*ListServiceChargesResponse, error)
	// update service charge
	UpdateServiceCharge(ctx context.Context, in *UpdateServiceChargeRequest, opts ...grpc.CallOption) (*UpdateServiceChargeResponse, error)
	// todo: delete pricing rule
	// sort service charges
	SortServiceCharges(ctx context.Context, in *SortServiceChargesRequest, opts ...grpc.CallOption) (*SortServiceChargesResponse, error)
	// push service charge changes
	PushServiceChargeChanges(ctx context.Context, in *PushServiceChargeChangesRequest, opts ...grpc.CallOption) (*PushServiceChargeChangesResponse, error)
}

type priceBookServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPriceBookServiceClient(cc grpc.ClientConnInterface) PriceBookServiceClient {
	return &priceBookServiceClient{cc}
}

func (c *priceBookServiceClient) ListPriceBooks(ctx context.Context, in *ListPriceBooksRequest, opts ...grpc.CallOption) (*ListPriceBooksResponse, error) {
	out := new(ListPriceBooksResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/ListPriceBooks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) CreatePriceBook(ctx context.Context, in *CreatePriceBookRequest, opts ...grpc.CallOption) (*CreatePriceBookResponse, error) {
	out := new(CreatePriceBookResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/CreatePriceBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) UpdatePriceBook(ctx context.Context, in *UpdatePriceBookRequest, opts ...grpc.CallOption) (*UpdatePriceBookResponse, error) {
	out := new(UpdatePriceBookResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/UpdatePriceBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) DeletePriceBook(ctx context.Context, in *DeletePriceBookRequest, opts ...grpc.CallOption) (*DeletePriceBookResponse, error) {
	out := new(DeletePriceBookResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/DeletePriceBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) DuplicatePriceBook(ctx context.Context, in *DuplicatePriceBookRequest, opts ...grpc.CallOption) (*DuplicatePriceBookResponse, error) {
	out := new(DuplicatePriceBookResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/DuplicatePriceBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) MigratePriceBook(ctx context.Context, in *MigratePriceBookRequest, opts ...grpc.CallOption) (*MigratePriceBookResponse, error) {
	out := new(MigratePriceBookResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/MigratePriceBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) InitPriceBook(ctx context.Context, in *InitPriceBookRequest, opts ...grpc.CallOption) (*InitPriceBookResponse, error) {
	out := new(InitPriceBookResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/InitPriceBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) SaveServiceCategories(ctx context.Context, in *SaveServiceCategoriesRequest, opts ...grpc.CallOption) (*SaveServiceCategoriesResponse, error) {
	out := new(SaveServiceCategoriesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/SaveServiceCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListServiceCategories(ctx context.Context, in *ListServiceCategoriesRequest, opts ...grpc.CallOption) (*ListServiceCategoriesResponse, error) {
	out := new(ListServiceCategoriesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/ListServiceCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListPetBreeds(ctx context.Context, in *ListPetBreedsRequest, opts ...grpc.CallOption) (*ListPetBreedsResponse, error) {
	out := new(ListPetBreedsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/ListPetBreeds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListPetTypes(ctx context.Context, in *ListPetTypesRequest, opts ...grpc.CallOption) (*ListPetTypesResponse, error) {
	out := new(ListPetTypesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/ListPetTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) CreateService(ctx context.Context, in *CreateServiceRequest, opts ...grpc.CallOption) (*CreateServiceResponse, error) {
	out := new(CreateServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/CreateService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) GetService(ctx context.Context, in *GetServiceRequest, opts ...grpc.CallOption) (*GetServiceResponse, error) {
	out := new(GetServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/GetService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListServices(ctx context.Context, in *ListServicesRequest, opts ...grpc.CallOption) (*ListServicesResponse, error) {
	out := new(ListServicesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/ListServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) UpdateService(ctx context.Context, in *UpdateServiceRequest, opts ...grpc.CallOption) (*UpdateServiceResponse, error) {
	out := new(UpdateServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/UpdateService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) DeleteService(ctx context.Context, in *DeleteServiceRequest, opts ...grpc.CallOption) (*DeleteServiceResponse, error) {
	out := new(DeleteServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/DeleteService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) SortServices(ctx context.Context, in *SortServicesRequest, opts ...grpc.CallOption) (*SortServicesResponse, error) {
	out := new(SortServicesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/SortServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListServiceChangeHistories(ctx context.Context, in *ListServiceChangeHistoriesRequest, opts ...grpc.CallOption) (*ListServiceChangeHistoriesResponse, error) {
	out := new(ListServiceChangeHistoriesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/ListServiceChangeHistories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListServiceChanges(ctx context.Context, in *ListServiceChangesRequest, opts ...grpc.CallOption) (*ListServiceChangesResponse, error) {
	out := new(ListServiceChangesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/ListServiceChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) PushServiceChanges(ctx context.Context, in *PushServiceChangesRequest, opts ...grpc.CallOption) (*PushServiceChangesResponse, error) {
	out := new(PushServiceChangesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/PushServiceChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) CreateEvaluation(ctx context.Context, in *CreateEvaluationRequest, opts ...grpc.CallOption) (*CreateEvaluationResponse, error) {
	out := new(CreateEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/CreateEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListEvaluations(ctx context.Context, in *ListEvaluationsRequest, opts ...grpc.CallOption) (*ListEvaluationsResponse, error) {
	out := new(ListEvaluationsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/ListEvaluations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) UpdateEvaluation(ctx context.Context, in *UpdateEvaluationRequest, opts ...grpc.CallOption) (*UpdateEvaluationResponse, error) {
	out := new(UpdateEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/UpdateEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) SortEvaluations(ctx context.Context, in *SortEvaluationsRequest, opts ...grpc.CallOption) (*SortEvaluationsResponse, error) {
	out := new(SortEvaluationsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/SortEvaluations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) PushEvaluationChanges(ctx context.Context, in *PushEvaluationChangesRequest, opts ...grpc.CallOption) (*PushEvaluationChangesResponse, error) {
	out := new(PushEvaluationChangesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/PushEvaluationChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) CreatePricingRule(ctx context.Context, in *CreatePricingRuleRequest, opts ...grpc.CallOption) (*CreatePricingRuleResponse, error) {
	out := new(CreatePricingRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/CreatePricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListPricingRules(ctx context.Context, in *ListPricingRulesRequest, opts ...grpc.CallOption) (*ListPricingRulesResponse, error) {
	out := new(ListPricingRulesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/ListPricingRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) UpdatePricingRule(ctx context.Context, in *UpdatePricingRuleRequest, opts ...grpc.CallOption) (*UpdatePricingRuleResponse, error) {
	out := new(UpdatePricingRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/UpdatePricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) SortPricingRules(ctx context.Context, in *SortPricingRulesRequest, opts ...grpc.CallOption) (*SortPricingRulesResponse, error) {
	out := new(SortPricingRulesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/SortPricingRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) PushPricingRuleChanges(ctx context.Context, in *PushPricingRuleChangesRequest, opts ...grpc.CallOption) (*PushPricingRuleChangesResponse, error) {
	out := new(PushPricingRuleChangesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/PushPricingRuleChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) CreateServiceCharge(ctx context.Context, in *CreateServiceChargeRequest, opts ...grpc.CallOption) (*CreateServiceChargeResponse, error) {
	out := new(CreateServiceChargeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/CreateServiceCharge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListServiceCharges(ctx context.Context, in *ListServiceChargesRequest, opts ...grpc.CallOption) (*ListServiceChargesResponse, error) {
	out := new(ListServiceChargesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/ListServiceCharges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) UpdateServiceCharge(ctx context.Context, in *UpdateServiceChargeRequest, opts ...grpc.CallOption) (*UpdateServiceChargeResponse, error) {
	out := new(UpdateServiceChargeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/UpdateServiceCharge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) SortServiceCharges(ctx context.Context, in *SortServiceChargesRequest, opts ...grpc.CallOption) (*SortServiceChargesResponse, error) {
	out := new(SortServiceChargesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/SortServiceCharges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) PushServiceChargeChanges(ctx context.Context, in *PushServiceChargeChangesRequest, opts ...grpc.CallOption) (*PushServiceChargeChangesResponse, error) {
	out := new(PushServiceChargeChangesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.PriceBookService/PushServiceChargeChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PriceBookServiceServer is the server API for PriceBookService service.
// All implementations must embed UnimplementedPriceBookServiceServer
// for forward compatibility
type PriceBookServiceServer interface {
	// list price books
	ListPriceBooks(context.Context, *ListPriceBooksRequest) (*ListPriceBooksResponse, error)
	// create price book
	CreatePriceBook(context.Context, *CreatePriceBookRequest) (*CreatePriceBookResponse, error)
	// update price book
	UpdatePriceBook(context.Context, *UpdatePriceBookRequest) (*UpdatePriceBookResponse, error)
	// delete price book
	DeletePriceBook(context.Context, *DeletePriceBookRequest) (*DeletePriceBookResponse, error)
	// duplicate price book
	DuplicatePriceBook(context.Context, *DuplicatePriceBookRequest) (*DuplicatePriceBookResponse, error)
	// migrate price book
	MigratePriceBook(context.Context, *MigratePriceBookRequest) (*MigratePriceBookResponse, error)
	// init price book
	InitPriceBook(context.Context, *InitPriceBookRequest) (*InitPriceBookResponse, error)
	// create and update existed categories
	SaveServiceCategories(context.Context, *SaveServiceCategoriesRequest) (*SaveServiceCategoriesResponse, error)
	// list service categories
	ListServiceCategories(context.Context, *ListServiceCategoriesRequest) (*ListServiceCategoriesResponse, error)
	// list pet breeds
	ListPetBreeds(context.Context, *ListPetBreedsRequest) (*ListPetBreedsResponse, error)
	// list pet types
	ListPetTypes(context.Context, *ListPetTypesRequest) (*ListPetTypesResponse, error)
	// create a service
	CreateService(context.Context, *CreateServiceRequest) (*CreateServiceResponse, error)
	// get a service
	GetService(context.Context, *GetServiceRequest) (*GetServiceResponse, error)
	// list services
	ListServices(context.Context, *ListServicesRequest) (*ListServicesResponse, error)
	// update a service
	UpdateService(context.Context, *UpdateServiceRequest) (*UpdateServiceResponse, error)
	// delete a service
	DeleteService(context.Context, *DeleteServiceRequest) (*DeleteServiceResponse, error)
	// sort services
	SortServices(context.Context, *SortServicesRequest) (*SortServicesResponse, error)
	// list service change histories
	ListServiceChangeHistories(context.Context, *ListServiceChangeHistoriesRequest) (*ListServiceChangeHistoriesResponse, error)
	// list service changes
	ListServiceChanges(context.Context, *ListServiceChangesRequest) (*ListServiceChangesResponse, error)
	// push service changes
	PushServiceChanges(context.Context, *PushServiceChangesRequest) (*PushServiceChangesResponse, error)
	// evaluation service
	// create evaluation
	CreateEvaluation(context.Context, *CreateEvaluationRequest) (*CreateEvaluationResponse, error)
	// list evaluations
	ListEvaluations(context.Context, *ListEvaluationsRequest) (*ListEvaluationsResponse, error)
	// update evaluation
	UpdateEvaluation(context.Context, *UpdateEvaluationRequest) (*UpdateEvaluationResponse, error)
	// todo: delete evaluation
	// sort evaluations
	SortEvaluations(context.Context, *SortEvaluationsRequest) (*SortEvaluationsResponse, error)
	// push evaluation changes
	PushEvaluationChanges(context.Context, *PushEvaluationChangesRequest) (*PushEvaluationChangesResponse, error)
	// pricing rule
	// create pricing rule
	CreatePricingRule(context.Context, *CreatePricingRuleRequest) (*CreatePricingRuleResponse, error)
	// list pricing rules
	ListPricingRules(context.Context, *ListPricingRulesRequest) (*ListPricingRulesResponse, error)
	// update pricing rule
	UpdatePricingRule(context.Context, *UpdatePricingRuleRequest) (*UpdatePricingRuleResponse, error)
	// todo: delete pricing rule
	// sort pricing rules
	SortPricingRules(context.Context, *SortPricingRulesRequest) (*SortPricingRulesResponse, error)
	// push pricing rule changes
	PushPricingRuleChanges(context.Context, *PushPricingRuleChangesRequest) (*PushPricingRuleChangesResponse, error)
	// service charge
	// create service charge
	CreateServiceCharge(context.Context, *CreateServiceChargeRequest) (*CreateServiceChargeResponse, error)
	// list service charges
	ListServiceCharges(context.Context, *ListServiceChargesRequest) (*ListServiceChargesResponse, error)
	// update service charge
	UpdateServiceCharge(context.Context, *UpdateServiceChargeRequest) (*UpdateServiceChargeResponse, error)
	// todo: delete pricing rule
	// sort service charges
	SortServiceCharges(context.Context, *SortServiceChargesRequest) (*SortServiceChargesResponse, error)
	// push service charge changes
	PushServiceChargeChanges(context.Context, *PushServiceChargeChangesRequest) (*PushServiceChargeChangesResponse, error)
	mustEmbedUnimplementedPriceBookServiceServer()
}

// UnimplementedPriceBookServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPriceBookServiceServer struct {
}

func (UnimplementedPriceBookServiceServer) ListPriceBooks(context.Context, *ListPriceBooksRequest) (*ListPriceBooksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPriceBooks not implemented")
}
func (UnimplementedPriceBookServiceServer) CreatePriceBook(context.Context, *CreatePriceBookRequest) (*CreatePriceBookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePriceBook not implemented")
}
func (UnimplementedPriceBookServiceServer) UpdatePriceBook(context.Context, *UpdatePriceBookRequest) (*UpdatePriceBookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePriceBook not implemented")
}
func (UnimplementedPriceBookServiceServer) DeletePriceBook(context.Context, *DeletePriceBookRequest) (*DeletePriceBookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePriceBook not implemented")
}
func (UnimplementedPriceBookServiceServer) DuplicatePriceBook(context.Context, *DuplicatePriceBookRequest) (*DuplicatePriceBookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DuplicatePriceBook not implemented")
}
func (UnimplementedPriceBookServiceServer) MigratePriceBook(context.Context, *MigratePriceBookRequest) (*MigratePriceBookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MigratePriceBook not implemented")
}
func (UnimplementedPriceBookServiceServer) InitPriceBook(context.Context, *InitPriceBookRequest) (*InitPriceBookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitPriceBook not implemented")
}
func (UnimplementedPriceBookServiceServer) SaveServiceCategories(context.Context, *SaveServiceCategoriesRequest) (*SaveServiceCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveServiceCategories not implemented")
}
func (UnimplementedPriceBookServiceServer) ListServiceCategories(context.Context, *ListServiceCategoriesRequest) (*ListServiceCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceCategories not implemented")
}
func (UnimplementedPriceBookServiceServer) ListPetBreeds(context.Context, *ListPetBreedsRequest) (*ListPetBreedsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetBreeds not implemented")
}
func (UnimplementedPriceBookServiceServer) ListPetTypes(context.Context, *ListPetTypesRequest) (*ListPetTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetTypes not implemented")
}
func (UnimplementedPriceBookServiceServer) CreateService(context.Context, *CreateServiceRequest) (*CreateServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateService not implemented")
}
func (UnimplementedPriceBookServiceServer) GetService(context.Context, *GetServiceRequest) (*GetServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetService not implemented")
}
func (UnimplementedPriceBookServiceServer) ListServices(context.Context, *ListServicesRequest) (*ListServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServices not implemented")
}
func (UnimplementedPriceBookServiceServer) UpdateService(context.Context, *UpdateServiceRequest) (*UpdateServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateService not implemented")
}
func (UnimplementedPriceBookServiceServer) DeleteService(context.Context, *DeleteServiceRequest) (*DeleteServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteService not implemented")
}
func (UnimplementedPriceBookServiceServer) SortServices(context.Context, *SortServicesRequest) (*SortServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortServices not implemented")
}
func (UnimplementedPriceBookServiceServer) ListServiceChangeHistories(context.Context, *ListServiceChangeHistoriesRequest) (*ListServiceChangeHistoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceChangeHistories not implemented")
}
func (UnimplementedPriceBookServiceServer) ListServiceChanges(context.Context, *ListServiceChangesRequest) (*ListServiceChangesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceChanges not implemented")
}
func (UnimplementedPriceBookServiceServer) PushServiceChanges(context.Context, *PushServiceChangesRequest) (*PushServiceChangesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushServiceChanges not implemented")
}
func (UnimplementedPriceBookServiceServer) CreateEvaluation(context.Context, *CreateEvaluationRequest) (*CreateEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEvaluation not implemented")
}
func (UnimplementedPriceBookServiceServer) ListEvaluations(context.Context, *ListEvaluationsRequest) (*ListEvaluationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEvaluations not implemented")
}
func (UnimplementedPriceBookServiceServer) UpdateEvaluation(context.Context, *UpdateEvaluationRequest) (*UpdateEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEvaluation not implemented")
}
func (UnimplementedPriceBookServiceServer) SortEvaluations(context.Context, *SortEvaluationsRequest) (*SortEvaluationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortEvaluations not implemented")
}
func (UnimplementedPriceBookServiceServer) PushEvaluationChanges(context.Context, *PushEvaluationChangesRequest) (*PushEvaluationChangesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushEvaluationChanges not implemented")
}
func (UnimplementedPriceBookServiceServer) CreatePricingRule(context.Context, *CreatePricingRuleRequest) (*CreatePricingRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePricingRule not implemented")
}
func (UnimplementedPriceBookServiceServer) ListPricingRules(context.Context, *ListPricingRulesRequest) (*ListPricingRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPricingRules not implemented")
}
func (UnimplementedPriceBookServiceServer) UpdatePricingRule(context.Context, *UpdatePricingRuleRequest) (*UpdatePricingRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePricingRule not implemented")
}
func (UnimplementedPriceBookServiceServer) SortPricingRules(context.Context, *SortPricingRulesRequest) (*SortPricingRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPricingRules not implemented")
}
func (UnimplementedPriceBookServiceServer) PushPricingRuleChanges(context.Context, *PushPricingRuleChangesRequest) (*PushPricingRuleChangesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushPricingRuleChanges not implemented")
}
func (UnimplementedPriceBookServiceServer) CreateServiceCharge(context.Context, *CreateServiceChargeRequest) (*CreateServiceChargeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceCharge not implemented")
}
func (UnimplementedPriceBookServiceServer) ListServiceCharges(context.Context, *ListServiceChargesRequest) (*ListServiceChargesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceCharges not implemented")
}
func (UnimplementedPriceBookServiceServer) UpdateServiceCharge(context.Context, *UpdateServiceChargeRequest) (*UpdateServiceChargeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServiceCharge not implemented")
}
func (UnimplementedPriceBookServiceServer) SortServiceCharges(context.Context, *SortServiceChargesRequest) (*SortServiceChargesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortServiceCharges not implemented")
}
func (UnimplementedPriceBookServiceServer) PushServiceChargeChanges(context.Context, *PushServiceChargeChangesRequest) (*PushServiceChargeChangesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushServiceChargeChanges not implemented")
}
func (UnimplementedPriceBookServiceServer) mustEmbedUnimplementedPriceBookServiceServer() {}

// UnsafePriceBookServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PriceBookServiceServer will
// result in compilation errors.
type UnsafePriceBookServiceServer interface {
	mustEmbedUnimplementedPriceBookServiceServer()
}

func RegisterPriceBookServiceServer(s grpc.ServiceRegistrar, srv PriceBookServiceServer) {
	s.RegisterService(&PriceBookService_ServiceDesc, srv)
}

func _PriceBookService_ListPriceBooks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPriceBooksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListPriceBooks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/ListPriceBooks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListPriceBooks(ctx, req.(*ListPriceBooksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_CreatePriceBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePriceBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).CreatePriceBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/CreatePriceBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).CreatePriceBook(ctx, req.(*CreatePriceBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_UpdatePriceBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePriceBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).UpdatePriceBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/UpdatePriceBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).UpdatePriceBook(ctx, req.(*UpdatePriceBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_DeletePriceBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePriceBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).DeletePriceBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/DeletePriceBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).DeletePriceBook(ctx, req.(*DeletePriceBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_DuplicatePriceBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DuplicatePriceBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).DuplicatePriceBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/DuplicatePriceBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).DuplicatePriceBook(ctx, req.(*DuplicatePriceBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_MigratePriceBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MigratePriceBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).MigratePriceBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/MigratePriceBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).MigratePriceBook(ctx, req.(*MigratePriceBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_InitPriceBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitPriceBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).InitPriceBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/InitPriceBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).InitPriceBook(ctx, req.(*InitPriceBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_SaveServiceCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveServiceCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).SaveServiceCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/SaveServiceCategories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).SaveServiceCategories(ctx, req.(*SaveServiceCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListServiceCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListServiceCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/ListServiceCategories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListServiceCategories(ctx, req.(*ListServiceCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListPetBreeds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetBreedsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListPetBreeds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/ListPetBreeds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListPetBreeds(ctx, req.(*ListPetBreedsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListPetTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListPetTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/ListPetTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListPetTypes(ctx, req.(*ListPetTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_CreateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).CreateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/CreateService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).CreateService(ctx, req.(*CreateServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_GetService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).GetService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/GetService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).GetService(ctx, req.(*GetServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/ListServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListServices(ctx, req.(*ListServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_UpdateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).UpdateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/UpdateService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).UpdateService(ctx, req.(*UpdateServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_DeleteService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).DeleteService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/DeleteService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).DeleteService(ctx, req.(*DeleteServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_SortServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).SortServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/SortServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).SortServices(ctx, req.(*SortServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListServiceChangeHistories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceChangeHistoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListServiceChangeHistories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/ListServiceChangeHistories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListServiceChangeHistories(ctx, req.(*ListServiceChangeHistoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListServiceChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListServiceChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/ListServiceChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListServiceChanges(ctx, req.(*ListServiceChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_PushServiceChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushServiceChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).PushServiceChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/PushServiceChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).PushServiceChanges(ctx, req.(*PushServiceChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_CreateEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).CreateEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/CreateEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).CreateEvaluation(ctx, req.(*CreateEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListEvaluations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEvaluationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListEvaluations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/ListEvaluations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListEvaluations(ctx, req.(*ListEvaluationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_UpdateEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).UpdateEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/UpdateEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).UpdateEvaluation(ctx, req.(*UpdateEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_SortEvaluations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortEvaluationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).SortEvaluations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/SortEvaluations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).SortEvaluations(ctx, req.(*SortEvaluationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_PushEvaluationChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushEvaluationChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).PushEvaluationChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/PushEvaluationChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).PushEvaluationChanges(ctx, req.(*PushEvaluationChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_CreatePricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePricingRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).CreatePricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/CreatePricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).CreatePricingRule(ctx, req.(*CreatePricingRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListPricingRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPricingRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListPricingRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/ListPricingRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListPricingRules(ctx, req.(*ListPricingRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_UpdatePricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePricingRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).UpdatePricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/UpdatePricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).UpdatePricingRule(ctx, req.(*UpdatePricingRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_SortPricingRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPricingRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).SortPricingRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/SortPricingRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).SortPricingRules(ctx, req.(*SortPricingRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_PushPricingRuleChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPricingRuleChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).PushPricingRuleChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/PushPricingRuleChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).PushPricingRuleChanges(ctx, req.(*PushPricingRuleChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_CreateServiceCharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceChargeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).CreateServiceCharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/CreateServiceCharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).CreateServiceCharge(ctx, req.(*CreateServiceChargeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListServiceCharges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceChargesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListServiceCharges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/ListServiceCharges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListServiceCharges(ctx, req.(*ListServiceChargesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_UpdateServiceCharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceChargeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).UpdateServiceCharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/UpdateServiceCharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).UpdateServiceCharge(ctx, req.(*UpdateServiceChargeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_SortServiceCharges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortServiceChargesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).SortServiceCharges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/SortServiceCharges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).SortServiceCharges(ctx, req.(*SortServiceChargesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_PushServiceChargeChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushServiceChargeChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).PushServiceChargeChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.PriceBookService/PushServiceChargeChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).PushServiceChargeChanges(ctx, req.(*PushServiceChargeChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PriceBookService_ServiceDesc is the grpc.ServiceDesc for PriceBookService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PriceBookService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.enterprise.v1.PriceBookService",
	HandlerType: (*PriceBookServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPriceBooks",
			Handler:    _PriceBookService_ListPriceBooks_Handler,
		},
		{
			MethodName: "CreatePriceBook",
			Handler:    _PriceBookService_CreatePriceBook_Handler,
		},
		{
			MethodName: "UpdatePriceBook",
			Handler:    _PriceBookService_UpdatePriceBook_Handler,
		},
		{
			MethodName: "DeletePriceBook",
			Handler:    _PriceBookService_DeletePriceBook_Handler,
		},
		{
			MethodName: "DuplicatePriceBook",
			Handler:    _PriceBookService_DuplicatePriceBook_Handler,
		},
		{
			MethodName: "MigratePriceBook",
			Handler:    _PriceBookService_MigratePriceBook_Handler,
		},
		{
			MethodName: "InitPriceBook",
			Handler:    _PriceBookService_InitPriceBook_Handler,
		},
		{
			MethodName: "SaveServiceCategories",
			Handler:    _PriceBookService_SaveServiceCategories_Handler,
		},
		{
			MethodName: "ListServiceCategories",
			Handler:    _PriceBookService_ListServiceCategories_Handler,
		},
		{
			MethodName: "ListPetBreeds",
			Handler:    _PriceBookService_ListPetBreeds_Handler,
		},
		{
			MethodName: "ListPetTypes",
			Handler:    _PriceBookService_ListPetTypes_Handler,
		},
		{
			MethodName: "CreateService",
			Handler:    _PriceBookService_CreateService_Handler,
		},
		{
			MethodName: "GetService",
			Handler:    _PriceBookService_GetService_Handler,
		},
		{
			MethodName: "ListServices",
			Handler:    _PriceBookService_ListServices_Handler,
		},
		{
			MethodName: "UpdateService",
			Handler:    _PriceBookService_UpdateService_Handler,
		},
		{
			MethodName: "DeleteService",
			Handler:    _PriceBookService_DeleteService_Handler,
		},
		{
			MethodName: "SortServices",
			Handler:    _PriceBookService_SortServices_Handler,
		},
		{
			MethodName: "ListServiceChangeHistories",
			Handler:    _PriceBookService_ListServiceChangeHistories_Handler,
		},
		{
			MethodName: "ListServiceChanges",
			Handler:    _PriceBookService_ListServiceChanges_Handler,
		},
		{
			MethodName: "PushServiceChanges",
			Handler:    _PriceBookService_PushServiceChanges_Handler,
		},
		{
			MethodName: "CreateEvaluation",
			Handler:    _PriceBookService_CreateEvaluation_Handler,
		},
		{
			MethodName: "ListEvaluations",
			Handler:    _PriceBookService_ListEvaluations_Handler,
		},
		{
			MethodName: "UpdateEvaluation",
			Handler:    _PriceBookService_UpdateEvaluation_Handler,
		},
		{
			MethodName: "SortEvaluations",
			Handler:    _PriceBookService_SortEvaluations_Handler,
		},
		{
			MethodName: "PushEvaluationChanges",
			Handler:    _PriceBookService_PushEvaluationChanges_Handler,
		},
		{
			MethodName: "CreatePricingRule",
			Handler:    _PriceBookService_CreatePricingRule_Handler,
		},
		{
			MethodName: "ListPricingRules",
			Handler:    _PriceBookService_ListPricingRules_Handler,
		},
		{
			MethodName: "UpdatePricingRule",
			Handler:    _PriceBookService_UpdatePricingRule_Handler,
		},
		{
			MethodName: "SortPricingRules",
			Handler:    _PriceBookService_SortPricingRules_Handler,
		},
		{
			MethodName: "PushPricingRuleChanges",
			Handler:    _PriceBookService_PushPricingRuleChanges_Handler,
		},
		{
			MethodName: "CreateServiceCharge",
			Handler:    _PriceBookService_CreateServiceCharge_Handler,
		},
		{
			MethodName: "ListServiceCharges",
			Handler:    _PriceBookService_ListServiceCharges_Handler,
		},
		{
			MethodName: "UpdateServiceCharge",
			Handler:    _PriceBookService_UpdateServiceCharge_Handler,
		},
		{
			MethodName: "SortServiceCharges",
			Handler:    _PriceBookService_SortServiceCharges_Handler,
		},
		{
			MethodName: "PushServiceChargeChanges",
			Handler:    _PriceBookService_PushServiceChargeChanges_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/enterprise/v1/price_book_service.proto",
}
