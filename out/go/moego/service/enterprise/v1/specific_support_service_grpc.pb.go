// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/enterprise/v1/specific_support_service.proto

package enterprisesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SpecificSupportServiceClient is the client API for SpecificSupportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SpecificSupportServiceClient interface {
	// Get company message cycles
	GetCompanyMessageCycles(ctx context.Context, in *GetCompanyMessageCyclesRequest, opts ...grpc.CallOption) (*GetCompanyMessageCyclesResponse, error)
	// Check customers in message list
	CheckCustomersInMessageList(ctx context.Context, in *CheckCustomersInMessageListRequest, opts ...grpc.CallOption) (*CheckCustomersInMessageListResponse, error)
	// Get customers in message list
	GetCustomersInMessageList(ctx context.Context, in *GetCustomersInMessageListRequest, opts ...grpc.CallOption) (*GetCustomersInMessageListResponse, error)
	// Export non standard pet breed
	ExportNonStandardPetBreed(ctx context.Context, in *ExportNonStandardPetBreedRequest, opts ...grpc.CallOption) (*ExportNonStandardPetBreedResponse, error)
	// Import pet code binding
	ImportPetCodeBinding(ctx context.Context, in *ImportPetCodeBindingRequest, opts ...grpc.CallOption) (*ImportPetCodeBindingResponse, error)
	// Check special evaluation
	CheckSpecialEvaluation(ctx context.Context, in *CheckSpecialEvaluationRequest, opts ...grpc.CallOption) (*CheckSpecialEvaluationResponse, error)
	// list applicable line items
	ListApplicableLineItems(ctx context.Context, in *ListApplicableLineItemsRequest, opts ...grpc.CallOption) (*ListApplicableLineItemsResponse, error)
	// start vet verify task job
	// collect customers who have appointments in the next 30 days.
	StartVetVerifyTaskJob(ctx context.Context, in *StartVetVerifyTaskJobRequest, opts ...grpc.CallOption) (*StartVetVerifyTaskJobResponse, error)
	// enable all customers marketing email
	EnableAllCustomersMarketingEmail(ctx context.Context, in *EnableAllCustomersMarketingEmailRequest, opts ...grpc.CallOption) (*EnableAllCustomersMarketingEmailResponse, error)
}

type specificSupportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSpecificSupportServiceClient(cc grpc.ClientConnInterface) SpecificSupportServiceClient {
	return &specificSupportServiceClient{cc}
}

func (c *specificSupportServiceClient) GetCompanyMessageCycles(ctx context.Context, in *GetCompanyMessageCyclesRequest, opts ...grpc.CallOption) (*GetCompanyMessageCyclesResponse, error) {
	out := new(GetCompanyMessageCyclesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SpecificSupportService/GetCompanyMessageCycles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *specificSupportServiceClient) CheckCustomersInMessageList(ctx context.Context, in *CheckCustomersInMessageListRequest, opts ...grpc.CallOption) (*CheckCustomersInMessageListResponse, error) {
	out := new(CheckCustomersInMessageListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SpecificSupportService/CheckCustomersInMessageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *specificSupportServiceClient) GetCustomersInMessageList(ctx context.Context, in *GetCustomersInMessageListRequest, opts ...grpc.CallOption) (*GetCustomersInMessageListResponse, error) {
	out := new(GetCustomersInMessageListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SpecificSupportService/GetCustomersInMessageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *specificSupportServiceClient) ExportNonStandardPetBreed(ctx context.Context, in *ExportNonStandardPetBreedRequest, opts ...grpc.CallOption) (*ExportNonStandardPetBreedResponse, error) {
	out := new(ExportNonStandardPetBreedResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SpecificSupportService/ExportNonStandardPetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *specificSupportServiceClient) ImportPetCodeBinding(ctx context.Context, in *ImportPetCodeBindingRequest, opts ...grpc.CallOption) (*ImportPetCodeBindingResponse, error) {
	out := new(ImportPetCodeBindingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SpecificSupportService/ImportPetCodeBinding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *specificSupportServiceClient) CheckSpecialEvaluation(ctx context.Context, in *CheckSpecialEvaluationRequest, opts ...grpc.CallOption) (*CheckSpecialEvaluationResponse, error) {
	out := new(CheckSpecialEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SpecificSupportService/CheckSpecialEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *specificSupportServiceClient) ListApplicableLineItems(ctx context.Context, in *ListApplicableLineItemsRequest, opts ...grpc.CallOption) (*ListApplicableLineItemsResponse, error) {
	out := new(ListApplicableLineItemsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SpecificSupportService/ListApplicableLineItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *specificSupportServiceClient) StartVetVerifyTaskJob(ctx context.Context, in *StartVetVerifyTaskJobRequest, opts ...grpc.CallOption) (*StartVetVerifyTaskJobResponse, error) {
	out := new(StartVetVerifyTaskJobResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SpecificSupportService/StartVetVerifyTaskJob", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *specificSupportServiceClient) EnableAllCustomersMarketingEmail(ctx context.Context, in *EnableAllCustomersMarketingEmailRequest, opts ...grpc.CallOption) (*EnableAllCustomersMarketingEmailResponse, error) {
	out := new(EnableAllCustomersMarketingEmailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SpecificSupportService/EnableAllCustomersMarketingEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SpecificSupportServiceServer is the server API for SpecificSupportService service.
// All implementations must embed UnimplementedSpecificSupportServiceServer
// for forward compatibility
type SpecificSupportServiceServer interface {
	// Get company message cycles
	GetCompanyMessageCycles(context.Context, *GetCompanyMessageCyclesRequest) (*GetCompanyMessageCyclesResponse, error)
	// Check customers in message list
	CheckCustomersInMessageList(context.Context, *CheckCustomersInMessageListRequest) (*CheckCustomersInMessageListResponse, error)
	// Get customers in message list
	GetCustomersInMessageList(context.Context, *GetCustomersInMessageListRequest) (*GetCustomersInMessageListResponse, error)
	// Export non standard pet breed
	ExportNonStandardPetBreed(context.Context, *ExportNonStandardPetBreedRequest) (*ExportNonStandardPetBreedResponse, error)
	// Import pet code binding
	ImportPetCodeBinding(context.Context, *ImportPetCodeBindingRequest) (*ImportPetCodeBindingResponse, error)
	// Check special evaluation
	CheckSpecialEvaluation(context.Context, *CheckSpecialEvaluationRequest) (*CheckSpecialEvaluationResponse, error)
	// list applicable line items
	ListApplicableLineItems(context.Context, *ListApplicableLineItemsRequest) (*ListApplicableLineItemsResponse, error)
	// start vet verify task job
	// collect customers who have appointments in the next 30 days.
	StartVetVerifyTaskJob(context.Context, *StartVetVerifyTaskJobRequest) (*StartVetVerifyTaskJobResponse, error)
	// enable all customers marketing email
	EnableAllCustomersMarketingEmail(context.Context, *EnableAllCustomersMarketingEmailRequest) (*EnableAllCustomersMarketingEmailResponse, error)
	mustEmbedUnimplementedSpecificSupportServiceServer()
}

// UnimplementedSpecificSupportServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSpecificSupportServiceServer struct {
}

func (UnimplementedSpecificSupportServiceServer) GetCompanyMessageCycles(context.Context, *GetCompanyMessageCyclesRequest) (*GetCompanyMessageCyclesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyMessageCycles not implemented")
}
func (UnimplementedSpecificSupportServiceServer) CheckCustomersInMessageList(context.Context, *CheckCustomersInMessageListRequest) (*CheckCustomersInMessageListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckCustomersInMessageList not implemented")
}
func (UnimplementedSpecificSupportServiceServer) GetCustomersInMessageList(context.Context, *GetCustomersInMessageListRequest) (*GetCustomersInMessageListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomersInMessageList not implemented")
}
func (UnimplementedSpecificSupportServiceServer) ExportNonStandardPetBreed(context.Context, *ExportNonStandardPetBreedRequest) (*ExportNonStandardPetBreedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportNonStandardPetBreed not implemented")
}
func (UnimplementedSpecificSupportServiceServer) ImportPetCodeBinding(context.Context, *ImportPetCodeBindingRequest) (*ImportPetCodeBindingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportPetCodeBinding not implemented")
}
func (UnimplementedSpecificSupportServiceServer) CheckSpecialEvaluation(context.Context, *CheckSpecialEvaluationRequest) (*CheckSpecialEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSpecialEvaluation not implemented")
}
func (UnimplementedSpecificSupportServiceServer) ListApplicableLineItems(context.Context, *ListApplicableLineItemsRequest) (*ListApplicableLineItemsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListApplicableLineItems not implemented")
}
func (UnimplementedSpecificSupportServiceServer) StartVetVerifyTaskJob(context.Context, *StartVetVerifyTaskJobRequest) (*StartVetVerifyTaskJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartVetVerifyTaskJob not implemented")
}
func (UnimplementedSpecificSupportServiceServer) EnableAllCustomersMarketingEmail(context.Context, *EnableAllCustomersMarketingEmailRequest) (*EnableAllCustomersMarketingEmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnableAllCustomersMarketingEmail not implemented")
}
func (UnimplementedSpecificSupportServiceServer) mustEmbedUnimplementedSpecificSupportServiceServer() {
}

// UnsafeSpecificSupportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SpecificSupportServiceServer will
// result in compilation errors.
type UnsafeSpecificSupportServiceServer interface {
	mustEmbedUnimplementedSpecificSupportServiceServer()
}

func RegisterSpecificSupportServiceServer(s grpc.ServiceRegistrar, srv SpecificSupportServiceServer) {
	s.RegisterService(&SpecificSupportService_ServiceDesc, srv)
}

func _SpecificSupportService_GetCompanyMessageCycles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyMessageCyclesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecificSupportServiceServer).GetCompanyMessageCycles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SpecificSupportService/GetCompanyMessageCycles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecificSupportServiceServer).GetCompanyMessageCycles(ctx, req.(*GetCompanyMessageCyclesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpecificSupportService_CheckCustomersInMessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCustomersInMessageListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecificSupportServiceServer).CheckCustomersInMessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SpecificSupportService/CheckCustomersInMessageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecificSupportServiceServer).CheckCustomersInMessageList(ctx, req.(*CheckCustomersInMessageListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpecificSupportService_GetCustomersInMessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomersInMessageListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecificSupportServiceServer).GetCustomersInMessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SpecificSupportService/GetCustomersInMessageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecificSupportServiceServer).GetCustomersInMessageList(ctx, req.(*GetCustomersInMessageListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpecificSupportService_ExportNonStandardPetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportNonStandardPetBreedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecificSupportServiceServer).ExportNonStandardPetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SpecificSupportService/ExportNonStandardPetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecificSupportServiceServer).ExportNonStandardPetBreed(ctx, req.(*ExportNonStandardPetBreedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpecificSupportService_ImportPetCodeBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportPetCodeBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecificSupportServiceServer).ImportPetCodeBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SpecificSupportService/ImportPetCodeBinding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecificSupportServiceServer).ImportPetCodeBinding(ctx, req.(*ImportPetCodeBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpecificSupportService_CheckSpecialEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSpecialEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecificSupportServiceServer).CheckSpecialEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SpecificSupportService/CheckSpecialEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecificSupportServiceServer).CheckSpecialEvaluation(ctx, req.(*CheckSpecialEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpecificSupportService_ListApplicableLineItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListApplicableLineItemsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecificSupportServiceServer).ListApplicableLineItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SpecificSupportService/ListApplicableLineItems",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecificSupportServiceServer).ListApplicableLineItems(ctx, req.(*ListApplicableLineItemsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpecificSupportService_StartVetVerifyTaskJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartVetVerifyTaskJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecificSupportServiceServer).StartVetVerifyTaskJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SpecificSupportService/StartVetVerifyTaskJob",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecificSupportServiceServer).StartVetVerifyTaskJob(ctx, req.(*StartVetVerifyTaskJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpecificSupportService_EnableAllCustomersMarketingEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableAllCustomersMarketingEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecificSupportServiceServer).EnableAllCustomersMarketingEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SpecificSupportService/EnableAllCustomersMarketingEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecificSupportServiceServer).EnableAllCustomersMarketingEmail(ctx, req.(*EnableAllCustomersMarketingEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SpecificSupportService_ServiceDesc is the grpc.ServiceDesc for SpecificSupportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SpecificSupportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.enterprise.v1.SpecificSupportService",
	HandlerType: (*SpecificSupportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCompanyMessageCycles",
			Handler:    _SpecificSupportService_GetCompanyMessageCycles_Handler,
		},
		{
			MethodName: "CheckCustomersInMessageList",
			Handler:    _SpecificSupportService_CheckCustomersInMessageList_Handler,
		},
		{
			MethodName: "GetCustomersInMessageList",
			Handler:    _SpecificSupportService_GetCustomersInMessageList_Handler,
		},
		{
			MethodName: "ExportNonStandardPetBreed",
			Handler:    _SpecificSupportService_ExportNonStandardPetBreed_Handler,
		},
		{
			MethodName: "ImportPetCodeBinding",
			Handler:    _SpecificSupportService_ImportPetCodeBinding_Handler,
		},
		{
			MethodName: "CheckSpecialEvaluation",
			Handler:    _SpecificSupportService_CheckSpecialEvaluation_Handler,
		},
		{
			MethodName: "ListApplicableLineItems",
			Handler:    _SpecificSupportService_ListApplicableLineItems_Handler,
		},
		{
			MethodName: "StartVetVerifyTaskJob",
			Handler:    _SpecificSupportService_StartVetVerifyTaskJob_Handler,
		},
		{
			MethodName: "EnableAllCustomersMarketingEmail",
			Handler:    _SpecificSupportService_EnableAllCustomersMarketingEmail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/enterprise/v1/specific_support_service.proto",
}
