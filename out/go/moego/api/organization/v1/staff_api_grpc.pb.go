// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/organization/v1/staff_api.proto

package organizationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// StaffServiceClient is the client API for StaffService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StaffServiceClient interface {
	// create a new staff
	CreateStaff(ctx context.Context, in *CreateStaffParams, opts ...grpc.CallOption) (*CreateStaffResult, error)
	// get staff detail
	GetStaffFullDetail(ctx context.Context, in *GetStaffFullDetailParams, opts ...grpc.CallOption) (*GetStaffFullDetailResult, error)
	// update staff
	UpdateStaff(ctx context.Context, in *UpdateStaffParams, opts ...grpc.CallOption) (*UpdateStaffResult, error)
	// delete staff
	DeleteStaff(ctx context.Context, in *DeleteStaffParams, opts ...grpc.CallOption) (*DeleteStaffResult, error)
	// query staff list by pagination
	QueryStaffListByPagination(ctx context.Context, in *QueryStaffListByPaginationParams, opts ...grpc.CallOption) (*QueryStaffListByPaginationResult, error)
	// get all working location staffs
	GetAllWorkingLocationStaffs(ctx context.Context, in *GetAllWorkingLocationStaffsParams, opts ...grpc.CallOption) (*GetAllWorkingLocationStaffsResult, error)
	// get staffs by working locations
	GetStaffsByWorkingLocationIds(ctx context.Context, in *GetStaffsByWorkingLocationIdsParams, opts ...grpc.CallOption) (*GetStaffsByWorkingLocationIdsResult, error)
	// get clock in out staffs of current staff
	GetClockInOutStaffs(ctx context.Context, in *GetClockInOutStaffsParams, opts ...grpc.CallOption) (*GetClockInOutStaffsResult, error)
	// get enterprise staffs by working locations
	GetEnterpriseStaffsByWorkingLocationIds(ctx context.Context, in *GetEnterpriseStaffsByWorkingLocationIdsParams, opts ...grpc.CallOption) (*GetEnterpriseStaffsByWorkingLocationIdsResult, error)
	// get staff login time
	GetStaffLoginTime(ctx context.Context, in *GetStaffLoginTimeParams, opts ...grpc.CallOption) (*GetStaffLoginTimeResult, error)
	// update staff login time
	UpdateStaffLoginTime(ctx context.Context, in *UpdateStaffLoginTimeParams, opts ...grpc.CallOption) (*UpdateStaffLoginTimeResult, error)
	// get recommended staff login time
	GetRecommendedStaffLoginTime(ctx context.Context, in *GetRecommendedStaffLoginTimeParams, opts ...grpc.CallOption) (*GetRecommendedStaffLoginTimeResult, error)
	// List staff group by role
	ListStaffGroupByRole(ctx context.Context, in *ListStaffGroupByRoleParams, opts ...grpc.CallOption) (*ListStaffGroupByRoleResult, error)
	// get business级别的 staff slot availability type 返回by time or by slot
	GetBusinessStaffAvailabilityType(ctx context.Context, in *GetBusinessStaffAvailabilityTypeParams, opts ...grpc.CallOption) (*GetBusinessStaffAvailabilityTypeResult, error)
	// update business级别的 staff slot availability type
	UpdateBusinessStaffAvailabilityType(ctx context.Context, in *UpdateBusinessStaffAvailabilityTypeParams, opts ...grpc.CallOption) (*UpdateBusinessStaffAvailabilityTypeResult, error)
	// get staff slot availability
	GetStaffAvailability(ctx context.Context, in *GetStaffAvailabilityParams, opts ...grpc.CallOption) (*GetStaffAvailabilityResult, error)
	// update staff slot availability
	UpdateStaffAvailability(ctx context.Context, in *UpdateStaffAvailabilityParams, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResult, error)
	// update staff override config
	UpdateStaffAvailabilityOverride(ctx context.Context, in *UpdateStaffAvailabilityOverrideParams, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResult, error)
	// get staff override config
	GetStaffAvailabilityOverride(ctx context.Context, in *GetStaffAvailabilityOverrideParams, opts ...grpc.CallOption) (*GetStaffAvailabilityOverrideResult, error)
	// delete staff slot availability override
	DeleteStaffAvailabilityOverride(ctx context.Context, in *DeleteStaffAvailabilityOverrideParams, opts ...grpc.CallOption) (*DeleteStaffAvailabilityOverrideResult, error)
	// get staff calender view
	GetStaffCalenderView(ctx context.Context, in *GetStaffCalenderViewParams, opts ...grpc.CallOption) (*GetStaffCalenderViewResult, error)
	// init staff availability
	InitStaffAvailability(ctx context.Context, in *InitStaffAvailabilityParams, opts ...grpc.CallOption) (*InitStaffAvailabilityResult, error)
	// list slot free services
	ListSlotFreeServices(ctx context.Context, in *ListSlotFreeServicesParams, opts ...grpc.CallOption) (*ListSlotFreeServicesResult, error)
	// update slot free services
	UpdateSlotFreeServices(ctx context.Context, in *UpdateSlotFreeServicesParams, opts ...grpc.CallOption) (*UpdateSlotFreeServicesResult, error)
}

type staffServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStaffServiceClient(cc grpc.ClientConnInterface) StaffServiceClient {
	return &staffServiceClient{cc}
}

func (c *staffServiceClient) CreateStaff(ctx context.Context, in *CreateStaffParams, opts ...grpc.CallOption) (*CreateStaffResult, error) {
	out := new(CreateStaffResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/CreateStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffFullDetail(ctx context.Context, in *GetStaffFullDetailParams, opts ...grpc.CallOption) (*GetStaffFullDetailResult, error) {
	out := new(GetStaffFullDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetStaffFullDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaff(ctx context.Context, in *UpdateStaffParams, opts ...grpc.CallOption) (*UpdateStaffResult, error) {
	out := new(UpdateStaffResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/UpdateStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) DeleteStaff(ctx context.Context, in *DeleteStaffParams, opts ...grpc.CallOption) (*DeleteStaffResult, error) {
	out := new(DeleteStaffResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/DeleteStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) QueryStaffListByPagination(ctx context.Context, in *QueryStaffListByPaginationParams, opts ...grpc.CallOption) (*QueryStaffListByPaginationResult, error) {
	out := new(QueryStaffListByPaginationResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/QueryStaffListByPagination", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetAllWorkingLocationStaffs(ctx context.Context, in *GetAllWorkingLocationStaffsParams, opts ...grpc.CallOption) (*GetAllWorkingLocationStaffsResult, error) {
	out := new(GetAllWorkingLocationStaffsResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetAllWorkingLocationStaffs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffsByWorkingLocationIds(ctx context.Context, in *GetStaffsByWorkingLocationIdsParams, opts ...grpc.CallOption) (*GetStaffsByWorkingLocationIdsResult, error) {
	out := new(GetStaffsByWorkingLocationIdsResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetStaffsByWorkingLocationIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetClockInOutStaffs(ctx context.Context, in *GetClockInOutStaffsParams, opts ...grpc.CallOption) (*GetClockInOutStaffsResult, error) {
	out := new(GetClockInOutStaffsResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetClockInOutStaffs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetEnterpriseStaffsByWorkingLocationIds(ctx context.Context, in *GetEnterpriseStaffsByWorkingLocationIdsParams, opts ...grpc.CallOption) (*GetEnterpriseStaffsByWorkingLocationIdsResult, error) {
	out := new(GetEnterpriseStaffsByWorkingLocationIdsResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetEnterpriseStaffsByWorkingLocationIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffLoginTime(ctx context.Context, in *GetStaffLoginTimeParams, opts ...grpc.CallOption) (*GetStaffLoginTimeResult, error) {
	out := new(GetStaffLoginTimeResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetStaffLoginTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaffLoginTime(ctx context.Context, in *UpdateStaffLoginTimeParams, opts ...grpc.CallOption) (*UpdateStaffLoginTimeResult, error) {
	out := new(UpdateStaffLoginTimeResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/UpdateStaffLoginTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetRecommendedStaffLoginTime(ctx context.Context, in *GetRecommendedStaffLoginTimeParams, opts ...grpc.CallOption) (*GetRecommendedStaffLoginTimeResult, error) {
	out := new(GetRecommendedStaffLoginTimeResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetRecommendedStaffLoginTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) ListStaffGroupByRole(ctx context.Context, in *ListStaffGroupByRoleParams, opts ...grpc.CallOption) (*ListStaffGroupByRoleResult, error) {
	out := new(ListStaffGroupByRoleResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/ListStaffGroupByRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetBusinessStaffAvailabilityType(ctx context.Context, in *GetBusinessStaffAvailabilityTypeParams, opts ...grpc.CallOption) (*GetBusinessStaffAvailabilityTypeResult, error) {
	out := new(GetBusinessStaffAvailabilityTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetBusinessStaffAvailabilityType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateBusinessStaffAvailabilityType(ctx context.Context, in *UpdateBusinessStaffAvailabilityTypeParams, opts ...grpc.CallOption) (*UpdateBusinessStaffAvailabilityTypeResult, error) {
	out := new(UpdateBusinessStaffAvailabilityTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/UpdateBusinessStaffAvailabilityType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffAvailability(ctx context.Context, in *GetStaffAvailabilityParams, opts ...grpc.CallOption) (*GetStaffAvailabilityResult, error) {
	out := new(GetStaffAvailabilityResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetStaffAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaffAvailability(ctx context.Context, in *UpdateStaffAvailabilityParams, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResult, error) {
	out := new(UpdateStaffAvailabilityResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/UpdateStaffAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateStaffAvailabilityOverride(ctx context.Context, in *UpdateStaffAvailabilityOverrideParams, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResult, error) {
	out := new(UpdateStaffAvailabilityResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/UpdateStaffAvailabilityOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffAvailabilityOverride(ctx context.Context, in *GetStaffAvailabilityOverrideParams, opts ...grpc.CallOption) (*GetStaffAvailabilityOverrideResult, error) {
	out := new(GetStaffAvailabilityOverrideResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetStaffAvailabilityOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) DeleteStaffAvailabilityOverride(ctx context.Context, in *DeleteStaffAvailabilityOverrideParams, opts ...grpc.CallOption) (*DeleteStaffAvailabilityOverrideResult, error) {
	out := new(DeleteStaffAvailabilityOverrideResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/DeleteStaffAvailabilityOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) GetStaffCalenderView(ctx context.Context, in *GetStaffCalenderViewParams, opts ...grpc.CallOption) (*GetStaffCalenderViewResult, error) {
	out := new(GetStaffCalenderViewResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/GetStaffCalenderView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) InitStaffAvailability(ctx context.Context, in *InitStaffAvailabilityParams, opts ...grpc.CallOption) (*InitStaffAvailabilityResult, error) {
	out := new(InitStaffAvailabilityResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/InitStaffAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) ListSlotFreeServices(ctx context.Context, in *ListSlotFreeServicesParams, opts ...grpc.CallOption) (*ListSlotFreeServicesResult, error) {
	out := new(ListSlotFreeServicesResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/ListSlotFreeServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffServiceClient) UpdateSlotFreeServices(ctx context.Context, in *UpdateSlotFreeServicesParams, opts ...grpc.CallOption) (*UpdateSlotFreeServicesResult, error) {
	out := new(UpdateSlotFreeServicesResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffService/UpdateSlotFreeServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StaffServiceServer is the server API for StaffService service.
// All implementations must embed UnimplementedStaffServiceServer
// for forward compatibility
type StaffServiceServer interface {
	// create a new staff
	CreateStaff(context.Context, *CreateStaffParams) (*CreateStaffResult, error)
	// get staff detail
	GetStaffFullDetail(context.Context, *GetStaffFullDetailParams) (*GetStaffFullDetailResult, error)
	// update staff
	UpdateStaff(context.Context, *UpdateStaffParams) (*UpdateStaffResult, error)
	// delete staff
	DeleteStaff(context.Context, *DeleteStaffParams) (*DeleteStaffResult, error)
	// query staff list by pagination
	QueryStaffListByPagination(context.Context, *QueryStaffListByPaginationParams) (*QueryStaffListByPaginationResult, error)
	// get all working location staffs
	GetAllWorkingLocationStaffs(context.Context, *GetAllWorkingLocationStaffsParams) (*GetAllWorkingLocationStaffsResult, error)
	// get staffs by working locations
	GetStaffsByWorkingLocationIds(context.Context, *GetStaffsByWorkingLocationIdsParams) (*GetStaffsByWorkingLocationIdsResult, error)
	// get clock in out staffs of current staff
	GetClockInOutStaffs(context.Context, *GetClockInOutStaffsParams) (*GetClockInOutStaffsResult, error)
	// get enterprise staffs by working locations
	GetEnterpriseStaffsByWorkingLocationIds(context.Context, *GetEnterpriseStaffsByWorkingLocationIdsParams) (*GetEnterpriseStaffsByWorkingLocationIdsResult, error)
	// get staff login time
	GetStaffLoginTime(context.Context, *GetStaffLoginTimeParams) (*GetStaffLoginTimeResult, error)
	// update staff login time
	UpdateStaffLoginTime(context.Context, *UpdateStaffLoginTimeParams) (*UpdateStaffLoginTimeResult, error)
	// get recommended staff login time
	GetRecommendedStaffLoginTime(context.Context, *GetRecommendedStaffLoginTimeParams) (*GetRecommendedStaffLoginTimeResult, error)
	// List staff group by role
	ListStaffGroupByRole(context.Context, *ListStaffGroupByRoleParams) (*ListStaffGroupByRoleResult, error)
	// get business级别的 staff slot availability type 返回by time or by slot
	GetBusinessStaffAvailabilityType(context.Context, *GetBusinessStaffAvailabilityTypeParams) (*GetBusinessStaffAvailabilityTypeResult, error)
	// update business级别的 staff slot availability type
	UpdateBusinessStaffAvailabilityType(context.Context, *UpdateBusinessStaffAvailabilityTypeParams) (*UpdateBusinessStaffAvailabilityTypeResult, error)
	// get staff slot availability
	GetStaffAvailability(context.Context, *GetStaffAvailabilityParams) (*GetStaffAvailabilityResult, error)
	// update staff slot availability
	UpdateStaffAvailability(context.Context, *UpdateStaffAvailabilityParams) (*UpdateStaffAvailabilityResult, error)
	// update staff override config
	UpdateStaffAvailabilityOverride(context.Context, *UpdateStaffAvailabilityOverrideParams) (*UpdateStaffAvailabilityResult, error)
	// get staff override config
	GetStaffAvailabilityOverride(context.Context, *GetStaffAvailabilityOverrideParams) (*GetStaffAvailabilityOverrideResult, error)
	// delete staff slot availability override
	DeleteStaffAvailabilityOverride(context.Context, *DeleteStaffAvailabilityOverrideParams) (*DeleteStaffAvailabilityOverrideResult, error)
	// get staff calender view
	GetStaffCalenderView(context.Context, *GetStaffCalenderViewParams) (*GetStaffCalenderViewResult, error)
	// init staff availability
	InitStaffAvailability(context.Context, *InitStaffAvailabilityParams) (*InitStaffAvailabilityResult, error)
	// list slot free services
	ListSlotFreeServices(context.Context, *ListSlotFreeServicesParams) (*ListSlotFreeServicesResult, error)
	// update slot free services
	UpdateSlotFreeServices(context.Context, *UpdateSlotFreeServicesParams) (*UpdateSlotFreeServicesResult, error)
	mustEmbedUnimplementedStaffServiceServer()
}

// UnimplementedStaffServiceServer must be embedded to have forward compatible implementations.
type UnimplementedStaffServiceServer struct {
}

func (UnimplementedStaffServiceServer) CreateStaff(context.Context, *CreateStaffParams) (*CreateStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStaff not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffFullDetail(context.Context, *GetStaffFullDetailParams) (*GetStaffFullDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffFullDetail not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaff(context.Context, *UpdateStaffParams) (*UpdateStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaff not implemented")
}
func (UnimplementedStaffServiceServer) DeleteStaff(context.Context, *DeleteStaffParams) (*DeleteStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStaff not implemented")
}
func (UnimplementedStaffServiceServer) QueryStaffListByPagination(context.Context, *QueryStaffListByPaginationParams) (*QueryStaffListByPaginationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStaffListByPagination not implemented")
}
func (UnimplementedStaffServiceServer) GetAllWorkingLocationStaffs(context.Context, *GetAllWorkingLocationStaffsParams) (*GetAllWorkingLocationStaffsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllWorkingLocationStaffs not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffsByWorkingLocationIds(context.Context, *GetStaffsByWorkingLocationIdsParams) (*GetStaffsByWorkingLocationIdsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffsByWorkingLocationIds not implemented")
}
func (UnimplementedStaffServiceServer) GetClockInOutStaffs(context.Context, *GetClockInOutStaffsParams) (*GetClockInOutStaffsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClockInOutStaffs not implemented")
}
func (UnimplementedStaffServiceServer) GetEnterpriseStaffsByWorkingLocationIds(context.Context, *GetEnterpriseStaffsByWorkingLocationIdsParams) (*GetEnterpriseStaffsByWorkingLocationIdsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterpriseStaffsByWorkingLocationIds not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffLoginTime(context.Context, *GetStaffLoginTimeParams) (*GetStaffLoginTimeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffLoginTime not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaffLoginTime(context.Context, *UpdateStaffLoginTimeParams) (*UpdateStaffLoginTimeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffLoginTime not implemented")
}
func (UnimplementedStaffServiceServer) GetRecommendedStaffLoginTime(context.Context, *GetRecommendedStaffLoginTimeParams) (*GetRecommendedStaffLoginTimeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommendedStaffLoginTime not implemented")
}
func (UnimplementedStaffServiceServer) ListStaffGroupByRole(context.Context, *ListStaffGroupByRoleParams) (*ListStaffGroupByRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStaffGroupByRole not implemented")
}
func (UnimplementedStaffServiceServer) GetBusinessStaffAvailabilityType(context.Context, *GetBusinessStaffAvailabilityTypeParams) (*GetBusinessStaffAvailabilityTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinessStaffAvailabilityType not implemented")
}
func (UnimplementedStaffServiceServer) UpdateBusinessStaffAvailabilityType(context.Context, *UpdateBusinessStaffAvailabilityTypeParams) (*UpdateBusinessStaffAvailabilityTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBusinessStaffAvailabilityType not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffAvailability(context.Context, *GetStaffAvailabilityParams) (*GetStaffAvailabilityResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffAvailability not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaffAvailability(context.Context, *UpdateStaffAvailabilityParams) (*UpdateStaffAvailabilityResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffAvailability not implemented")
}
func (UnimplementedStaffServiceServer) UpdateStaffAvailabilityOverride(context.Context, *UpdateStaffAvailabilityOverrideParams) (*UpdateStaffAvailabilityResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffAvailabilityOverride not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffAvailabilityOverride(context.Context, *GetStaffAvailabilityOverrideParams) (*GetStaffAvailabilityOverrideResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffAvailabilityOverride not implemented")
}
func (UnimplementedStaffServiceServer) DeleteStaffAvailabilityOverride(context.Context, *DeleteStaffAvailabilityOverrideParams) (*DeleteStaffAvailabilityOverrideResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStaffAvailabilityOverride not implemented")
}
func (UnimplementedStaffServiceServer) GetStaffCalenderView(context.Context, *GetStaffCalenderViewParams) (*GetStaffCalenderViewResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffCalenderView not implemented")
}
func (UnimplementedStaffServiceServer) InitStaffAvailability(context.Context, *InitStaffAvailabilityParams) (*InitStaffAvailabilityResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitStaffAvailability not implemented")
}
func (UnimplementedStaffServiceServer) ListSlotFreeServices(context.Context, *ListSlotFreeServicesParams) (*ListSlotFreeServicesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSlotFreeServices not implemented")
}
func (UnimplementedStaffServiceServer) UpdateSlotFreeServices(context.Context, *UpdateSlotFreeServicesParams) (*UpdateSlotFreeServicesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSlotFreeServices not implemented")
}
func (UnimplementedStaffServiceServer) mustEmbedUnimplementedStaffServiceServer() {}

// UnsafeStaffServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StaffServiceServer will
// result in compilation errors.
type UnsafeStaffServiceServer interface {
	mustEmbedUnimplementedStaffServiceServer()
}

func RegisterStaffServiceServer(s grpc.ServiceRegistrar, srv StaffServiceServer) {
	s.RegisterService(&StaffService_ServiceDesc, srv)
}

func _StaffService_CreateStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).CreateStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/CreateStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).CreateStaff(ctx, req.(*CreateStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffFullDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffFullDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffFullDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetStaffFullDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffFullDetail(ctx, req.(*GetStaffFullDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/UpdateStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaff(ctx, req.(*UpdateStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_DeleteStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).DeleteStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/DeleteStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).DeleteStaff(ctx, req.(*DeleteStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_QueryStaffListByPagination_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStaffListByPaginationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).QueryStaffListByPagination(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/QueryStaffListByPagination",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).QueryStaffListByPagination(ctx, req.(*QueryStaffListByPaginationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetAllWorkingLocationStaffs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllWorkingLocationStaffsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetAllWorkingLocationStaffs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetAllWorkingLocationStaffs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetAllWorkingLocationStaffs(ctx, req.(*GetAllWorkingLocationStaffsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffsByWorkingLocationIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffsByWorkingLocationIdsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffsByWorkingLocationIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetStaffsByWorkingLocationIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffsByWorkingLocationIds(ctx, req.(*GetStaffsByWorkingLocationIdsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetClockInOutStaffs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClockInOutStaffsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetClockInOutStaffs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetClockInOutStaffs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetClockInOutStaffs(ctx, req.(*GetClockInOutStaffsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetEnterpriseStaffsByWorkingLocationIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseStaffsByWorkingLocationIdsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetEnterpriseStaffsByWorkingLocationIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetEnterpriseStaffsByWorkingLocationIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetEnterpriseStaffsByWorkingLocationIds(ctx, req.(*GetEnterpriseStaffsByWorkingLocationIdsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffLoginTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffLoginTimeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffLoginTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetStaffLoginTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffLoginTime(ctx, req.(*GetStaffLoginTimeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaffLoginTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffLoginTimeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaffLoginTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/UpdateStaffLoginTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaffLoginTime(ctx, req.(*UpdateStaffLoginTimeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetRecommendedStaffLoginTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendedStaffLoginTimeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetRecommendedStaffLoginTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetRecommendedStaffLoginTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetRecommendedStaffLoginTime(ctx, req.(*GetRecommendedStaffLoginTimeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_ListStaffGroupByRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStaffGroupByRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).ListStaffGroupByRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/ListStaffGroupByRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).ListStaffGroupByRole(ctx, req.(*ListStaffGroupByRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetBusinessStaffAvailabilityType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessStaffAvailabilityTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetBusinessStaffAvailabilityType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetBusinessStaffAvailabilityType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetBusinessStaffAvailabilityType(ctx, req.(*GetBusinessStaffAvailabilityTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateBusinessStaffAvailabilityType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBusinessStaffAvailabilityTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateBusinessStaffAvailabilityType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/UpdateBusinessStaffAvailabilityType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateBusinessStaffAvailabilityType(ctx, req.(*UpdateBusinessStaffAvailabilityTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffAvailabilityParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetStaffAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffAvailability(ctx, req.(*GetStaffAvailabilityParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaffAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffAvailabilityParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaffAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/UpdateStaffAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaffAvailability(ctx, req.(*UpdateStaffAvailabilityParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateStaffAvailabilityOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffAvailabilityOverrideParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateStaffAvailabilityOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/UpdateStaffAvailabilityOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateStaffAvailabilityOverride(ctx, req.(*UpdateStaffAvailabilityOverrideParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffAvailabilityOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffAvailabilityOverrideParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffAvailabilityOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetStaffAvailabilityOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffAvailabilityOverride(ctx, req.(*GetStaffAvailabilityOverrideParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_DeleteStaffAvailabilityOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStaffAvailabilityOverrideParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).DeleteStaffAvailabilityOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/DeleteStaffAvailabilityOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).DeleteStaffAvailabilityOverride(ctx, req.(*DeleteStaffAvailabilityOverrideParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_GetStaffCalenderView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffCalenderViewParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).GetStaffCalenderView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/GetStaffCalenderView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).GetStaffCalenderView(ctx, req.(*GetStaffCalenderViewParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_InitStaffAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitStaffAvailabilityParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).InitStaffAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/InitStaffAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).InitStaffAvailability(ctx, req.(*InitStaffAvailabilityParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_ListSlotFreeServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSlotFreeServicesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).ListSlotFreeServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/ListSlotFreeServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).ListSlotFreeServices(ctx, req.(*ListSlotFreeServicesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffService_UpdateSlotFreeServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSlotFreeServicesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServiceServer).UpdateSlotFreeServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffService/UpdateSlotFreeServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServiceServer).UpdateSlotFreeServices(ctx, req.(*UpdateSlotFreeServicesParams))
	}
	return interceptor(ctx, in, info, handler)
}

// StaffService_ServiceDesc is the grpc.ServiceDesc for StaffService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StaffService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.organization.v1.StaffService",
	HandlerType: (*StaffServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateStaff",
			Handler:    _StaffService_CreateStaff_Handler,
		},
		{
			MethodName: "GetStaffFullDetail",
			Handler:    _StaffService_GetStaffFullDetail_Handler,
		},
		{
			MethodName: "UpdateStaff",
			Handler:    _StaffService_UpdateStaff_Handler,
		},
		{
			MethodName: "DeleteStaff",
			Handler:    _StaffService_DeleteStaff_Handler,
		},
		{
			MethodName: "QueryStaffListByPagination",
			Handler:    _StaffService_QueryStaffListByPagination_Handler,
		},
		{
			MethodName: "GetAllWorkingLocationStaffs",
			Handler:    _StaffService_GetAllWorkingLocationStaffs_Handler,
		},
		{
			MethodName: "GetStaffsByWorkingLocationIds",
			Handler:    _StaffService_GetStaffsByWorkingLocationIds_Handler,
		},
		{
			MethodName: "GetClockInOutStaffs",
			Handler:    _StaffService_GetClockInOutStaffs_Handler,
		},
		{
			MethodName: "GetEnterpriseStaffsByWorkingLocationIds",
			Handler:    _StaffService_GetEnterpriseStaffsByWorkingLocationIds_Handler,
		},
		{
			MethodName: "GetStaffLoginTime",
			Handler:    _StaffService_GetStaffLoginTime_Handler,
		},
		{
			MethodName: "UpdateStaffLoginTime",
			Handler:    _StaffService_UpdateStaffLoginTime_Handler,
		},
		{
			MethodName: "GetRecommendedStaffLoginTime",
			Handler:    _StaffService_GetRecommendedStaffLoginTime_Handler,
		},
		{
			MethodName: "ListStaffGroupByRole",
			Handler:    _StaffService_ListStaffGroupByRole_Handler,
		},
		{
			MethodName: "GetBusinessStaffAvailabilityType",
			Handler:    _StaffService_GetBusinessStaffAvailabilityType_Handler,
		},
		{
			MethodName: "UpdateBusinessStaffAvailabilityType",
			Handler:    _StaffService_UpdateBusinessStaffAvailabilityType_Handler,
		},
		{
			MethodName: "GetStaffAvailability",
			Handler:    _StaffService_GetStaffAvailability_Handler,
		},
		{
			MethodName: "UpdateStaffAvailability",
			Handler:    _StaffService_UpdateStaffAvailability_Handler,
		},
		{
			MethodName: "UpdateStaffAvailabilityOverride",
			Handler:    _StaffService_UpdateStaffAvailabilityOverride_Handler,
		},
		{
			MethodName: "GetStaffAvailabilityOverride",
			Handler:    _StaffService_GetStaffAvailabilityOverride_Handler,
		},
		{
			MethodName: "DeleteStaffAvailabilityOverride",
			Handler:    _StaffService_DeleteStaffAvailabilityOverride_Handler,
		},
		{
			MethodName: "GetStaffCalenderView",
			Handler:    _StaffService_GetStaffCalenderView_Handler,
		},
		{
			MethodName: "InitStaffAvailability",
			Handler:    _StaffService_InitStaffAvailability_Handler,
		},
		{
			MethodName: "ListSlotFreeServices",
			Handler:    _StaffService_ListSlotFreeServices_Handler,
		},
		{
			MethodName: "UpdateSlotFreeServices",
			Handler:    _StaffService_UpdateSlotFreeServices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/organization/v1/staff_api.proto",
}
