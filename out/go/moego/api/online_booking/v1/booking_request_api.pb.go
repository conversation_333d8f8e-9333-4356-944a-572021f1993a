// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/online_booking/v1/booking_request_api.proto

package onlinebookingapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	v14 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v15 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v16 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Get booking request list request
type GetBookingRequestListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// Keyword
	Keyword *string `protobuf:"bytes,3,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// order by (support multi fields), optional
	OrderBys []*v2.OrderBy `protobuf:"bytes,4,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// Service item type
	//
	//	repeated models.offering.v1.ServiceItemType service_item_types = 5 [(validate.rules).repeated = {
	//	  items: {
	//	    enum: {
	//	      defined_only: true,
	//	      not_in: [0]
	//	    }
	//	  }
	//	}];
	//
	// Service type include, bitmap value
	ServiceTypeIncludes []int32 `protobuf:"varint,6,rep,packed,name=service_type_includes,json=serviceTypeIncludes,proto3" json:"service_type_includes,omitempty"`
}

func (x *GetBookingRequestListRequest) Reset() {
	*x = GetBookingRequestListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingRequestListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingRequestListRequest) ProtoMessage() {}

func (x *GetBookingRequestListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingRequestListRequest.ProtoReflect.Descriptor instead.
func (*GetBookingRequestListRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetBookingRequestListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetBookingRequestListRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetBookingRequestListRequest) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *GetBookingRequestListRequest) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *GetBookingRequestListRequest) GetServiceTypeIncludes() []int32 {
	if x != nil {
		return x.ServiceTypeIncludes
	}
	return nil
}

// customer composite
type CustomerDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first name
	FirstName string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,5,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// email
	Email string `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,7,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// client color
	ClientColor string `protobuf:"bytes,8,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	// referral source id
	ReferralSourceId int32 `protobuf:"varint,9,opt,name=referral_source_id,json=referralSourceId,proto3" json:"referral_source_id,omitempty"`
	// preferred groomer id
	PreferredGroomerId int32 `protobuf:"varint,10,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3" json:"preferred_groomer_id,omitempty"`
	// preferred frequency type
	PreferredFrequencyType int32 `protobuf:"varint,11,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3" json:"preferred_frequency_type,omitempty"`
	// frequency day
	PreferredFrequencyDay int32 `protobuf:"varint,12,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3" json:"preferred_frequency_day,omitempty"`
	// preferred day
	PreferredDay []int32 `protobuf:"varint,13,rep,packed,name=preferred_day,json=preferredDay,proto3" json:"preferred_day,omitempty"`
	// preferred time
	PreferredTime []int32 `protobuf:"varint,14,rep,packed,name=preferred_time,json=preferredTime,proto3" json:"preferred_time,omitempty"`
	// is new customer
	IsNewCustomer bool `protobuf:"varint,15,opt,name=is_new_customer,json=isNewCustomer,proto3" json:"is_new_customer,omitempty"`
	// primary address
	PrimaryAddress *AddressDetail `protobuf:"bytes,16,opt,name=primary_address,json=primaryAddress,proto3,oneof" json:"primary_address,omitempty"`
	// new address
	NewAddress []*AddressDetail `protobuf:"bytes,17,rep,name=new_address,json=newAddress,proto3" json:"new_address,omitempty"`
	// question answers
	QuestionAnswers []*QuestionAnswerDetail `protobuf:"bytes,18,rep,name=question_answers,json=questionAnswers,proto3" json:"question_answers,omitempty"`
	// last alert note
	LastAlertNote *string `protobuf:"bytes,19,opt,name=last_alert_note,json=lastAlertNote,proto3,oneof" json:"last_alert_note,omitempty"`
	// out of service area
	OutOfServiceArea bool `protobuf:"varint,20,opt,name=out_of_service_area,json=outOfServiceArea,proto3" json:"out_of_service_area,omitempty"`
	// has Pet Parent App account
	HasPetParentAppAccount bool `protobuf:"varint,21,opt,name=has_pet_parent_app_account,json=hasPetParentAppAccount,proto3" json:"has_pet_parent_app_account,omitempty"`
	// emergency contact
	EmergencyContact *CustomerDetail_EmergencyContact `protobuf:"bytes,22,opt,name=emergency_contact,json=emergencyContact,proto3" json:"emergency_contact,omitempty"`
	// 复用这个结构了，偷懒，嘻嘻
	PickupContact *CustomerDetail_EmergencyContact `protobuf:"bytes,23,opt,name=pickup_contact,json=pickupContact,proto3" json:"pickup_contact,omitempty"`
	// customer birthday
	Birthday *date.Date `protobuf:"bytes,24,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
}

func (x *CustomerDetail) Reset() {
	*x = CustomerDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDetail) ProtoMessage() {}

func (x *CustomerDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDetail.ProtoReflect.Descriptor instead.
func (*CustomerDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerDetail) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CustomerDetail) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CustomerDetail) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CustomerDetail) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CustomerDetail) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CustomerDetail) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CustomerDetail) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *CustomerDetail) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

func (x *CustomerDetail) GetReferralSourceId() int32 {
	if x != nil {
		return x.ReferralSourceId
	}
	return 0
}

func (x *CustomerDetail) GetPreferredGroomerId() int32 {
	if x != nil {
		return x.PreferredGroomerId
	}
	return 0
}

func (x *CustomerDetail) GetPreferredFrequencyType() int32 {
	if x != nil {
		return x.PreferredFrequencyType
	}
	return 0
}

func (x *CustomerDetail) GetPreferredFrequencyDay() int32 {
	if x != nil {
		return x.PreferredFrequencyDay
	}
	return 0
}

func (x *CustomerDetail) GetPreferredDay() []int32 {
	if x != nil {
		return x.PreferredDay
	}
	return nil
}

func (x *CustomerDetail) GetPreferredTime() []int32 {
	if x != nil {
		return x.PreferredTime
	}
	return nil
}

func (x *CustomerDetail) GetIsNewCustomer() bool {
	if x != nil {
		return x.IsNewCustomer
	}
	return false
}

func (x *CustomerDetail) GetPrimaryAddress() *AddressDetail {
	if x != nil {
		return x.PrimaryAddress
	}
	return nil
}

func (x *CustomerDetail) GetNewAddress() []*AddressDetail {
	if x != nil {
		return x.NewAddress
	}
	return nil
}

func (x *CustomerDetail) GetQuestionAnswers() []*QuestionAnswerDetail {
	if x != nil {
		return x.QuestionAnswers
	}
	return nil
}

func (x *CustomerDetail) GetLastAlertNote() string {
	if x != nil && x.LastAlertNote != nil {
		return *x.LastAlertNote
	}
	return ""
}

func (x *CustomerDetail) GetOutOfServiceArea() bool {
	if x != nil {
		return x.OutOfServiceArea
	}
	return false
}

func (x *CustomerDetail) GetHasPetParentAppAccount() bool {
	if x != nil {
		return x.HasPetParentAppAccount
	}
	return false
}

func (x *CustomerDetail) GetEmergencyContact() *CustomerDetail_EmergencyContact {
	if x != nil {
		return x.EmergencyContact
	}
	return nil
}

func (x *CustomerDetail) GetPickupContact() *CustomerDetail_EmergencyContact {
	if x != nil {
		return x.PickupContact
	}
	return nil
}

func (x *CustomerDetail) GetBirthday() *date.Date {
	if x != nil {
		return x.Birthday
	}
	return nil
}

// Get booking request list response
type GetBookingRequestListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The booking request item
	BookingRequestItems []*GetBookingRequestItem `protobuf:"bytes,1,rep,name=booking_request_items,json=bookingRequestItems,proto3" json:"booking_request_items,omitempty"`
	// Pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetBookingRequestListResponse) Reset() {
	*x = GetBookingRequestListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingRequestListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingRequestListResponse) ProtoMessage() {}

func (x *GetBookingRequestListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingRequestListResponse.ProtoReflect.Descriptor instead.
func (*GetBookingRequestListResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetBookingRequestListResponse) GetBookingRequestItems() []*GetBookingRequestItem {
	if x != nil {
		return x.BookingRequestItems
	}
	return nil
}

func (x *GetBookingRequestListResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// Get booking request request
type GetBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetBookingRequestRequest) Reset() {
	*x = GetBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingRequestRequest) ProtoMessage() {}

func (x *GetBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*GetBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetBookingRequestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Get booking request item
type GetBookingRequestItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request
	BookingRequest *BookingRequestDetail `protobuf:"bytes,1,opt,name=booking_request,json=bookingRequest,proto3" json:"booking_request,omitempty"`
	// service
	ServiceDetails []*ServiceDetail `protobuf:"bytes,2,rep,name=service_details,json=serviceDetails,proto3" json:"service_details,omitempty"`
	// customer
	CustomerDetail *CustomerDetail `protobuf:"bytes,3,opt,name=customer_detail,json=customerDetail,proto3" json:"customer_detail,omitempty"`
	// pay
	Pay *PayBookingRequestView `protobuf:"bytes,4,opt,name=pay,proto3" json:"pay,omitempty"`
	// profile has request update
	HasRequestUpdate bool `protobuf:"varint,5,opt,name=has_request_update,json=hasRequestUpdate,proto3" json:"has_request_update,omitempty"`
	// auto assign
	// deprecated by Freeman since 2025/3/4, use incomplete_details instead
	//
	// Deprecated: Do not use.
	AssignRequire *AssignRequire `protobuf:"bytes,6,opt,name=assign_require,json=assignRequire,proto3" json:"assign_require,omitempty"`
	// active memberships for the customer
	MembershipSubscriptions *v1.MembershipSubscriptionListModel `protobuf:"bytes,7,opt,name=membership_subscriptions,json=membershipSubscriptions,proto3" json:"membership_subscriptions,omitempty"`
	// customer package info
	CustomerPackages []*v11.CustomerPackageView `protobuf:"bytes,8,rep,name=customer_packages,json=customerPackages,proto3" json:"customer_packages,omitempty"`
	// 还需要前端填充数据的 details，包括了：
	// - grooming/boarding/daycare/evaluation service details
	// - grooming/boarding/daycare addon details
	IncompleteDetails *IncompleteDetails `protobuf:"bytes,9,opt,name=incomplete_details,json=incompleteDetails,proto3" json:"incomplete_details,omitempty"`
	// related membership, only contains source membership now
	RelatedMemberships []*v1.MembershipModelPublicView `protobuf:"bytes,10,rep,name=related_memberships,json=relatedMemberships,proto3" json:"related_memberships,omitempty"`
}

func (x *GetBookingRequestItem) Reset() {
	*x = GetBookingRequestItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingRequestItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingRequestItem) ProtoMessage() {}

func (x *GetBookingRequestItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingRequestItem.ProtoReflect.Descriptor instead.
func (*GetBookingRequestItem) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetBookingRequestItem) GetBookingRequest() *BookingRequestDetail {
	if x != nil {
		return x.BookingRequest
	}
	return nil
}

func (x *GetBookingRequestItem) GetServiceDetails() []*ServiceDetail {
	if x != nil {
		return x.ServiceDetails
	}
	return nil
}

func (x *GetBookingRequestItem) GetCustomerDetail() *CustomerDetail {
	if x != nil {
		return x.CustomerDetail
	}
	return nil
}

func (x *GetBookingRequestItem) GetPay() *PayBookingRequestView {
	if x != nil {
		return x.Pay
	}
	return nil
}

func (x *GetBookingRequestItem) GetHasRequestUpdate() bool {
	if x != nil {
		return x.HasRequestUpdate
	}
	return false
}

// Deprecated: Do not use.
func (x *GetBookingRequestItem) GetAssignRequire() *AssignRequire {
	if x != nil {
		return x.AssignRequire
	}
	return nil
}

func (x *GetBookingRequestItem) GetMembershipSubscriptions() *v1.MembershipSubscriptionListModel {
	if x != nil {
		return x.MembershipSubscriptions
	}
	return nil
}

func (x *GetBookingRequestItem) GetCustomerPackages() []*v11.CustomerPackageView {
	if x != nil {
		return x.CustomerPackages
	}
	return nil
}

func (x *GetBookingRequestItem) GetIncompleteDetails() *IncompleteDetails {
	if x != nil {
		return x.IncompleteDetails
	}
	return nil
}

func (x *GetBookingRequestItem) GetRelatedMemberships() []*v1.MembershipModelPublicView {
	if x != nil {
		return x.RelatedMemberships
	}
	return nil
}

// Service detail
type ServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail
	PetDetail *PetDetail `protobuf:"bytes,1,opt,name=pet_detail,json=petDetail,proto3" json:"pet_detail,omitempty"`
	// the services
	Services []*ServiceDetail_Service `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *ServiceDetail) Reset() {
	*x = ServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDetail) ProtoMessage() {}

func (x *ServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDetail.ProtoReflect.Descriptor instead.
func (*ServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{5}
}

func (x *ServiceDetail) GetPetDetail() *PetDetail {
	if x != nil {
		return x.PetDetail
	}
	return nil
}

func (x *ServiceDetail) GetServices() []*ServiceDetail_Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// The booking request detail
type BookingRequestDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment id, generated after the booking request is scheduled
	AppointmentId int64 `protobuf:"varint,4,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// start date, format: yyyy-mm-dd
	StartDate string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// start time, the minutes from 00:00
	StartTime int32 `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end date, format: yyyy-mm-dd
	EndDate string `protobuf:"bytes,7,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// end time, the minutes from 00:00
	EndTime int32 `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// status, 0: pending, 1: confirmed, 2: canceled
	Status v12.BookingRequestStatus `protobuf:"varint,9,opt,name=status,proto3,enum=moego.models.online_booking.v1.BookingRequestStatus" json:"status,omitempty"`
	// is prepaid
	IsPrepaid bool `protobuf:"varint,10,opt,name=is_prepaid,json=isPrepaid,proto3" json:"is_prepaid,omitempty"`
	// additional note
	AdditionalNote string `protobuf:"bytes,11,opt,name=additional_note,json=additionalNote,proto3" json:"additional_note,omitempty"`
	// source platform
	SourcePlatform v12.BookingRequestSourcePlatform `protobuf:"varint,12,opt,name=source_platform,json=sourcePlatform,proto3,enum=moego.models.online_booking.v1.BookingRequestSourcePlatform" json:"source_platform,omitempty"`
	// service item type include, bitmap value
	ServiceTypeInclude int32 `protobuf:"varint,13,opt,name=service_type_include,json=serviceTypeInclude,proto3" json:"service_type_include,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// no start time
	NoStartTime bool `protobuf:"varint,21,opt,name=no_start_time,json=noStartTime,proto3" json:"no_start_time,omitempty"`
	// service type list
	ServiceItemTypes []v13.ServiceItemType `protobuf:"varint,22,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// staff name
	StaffName *string `protobuf:"bytes,23,opt,name=staff_name,json=staffName,proto3,oneof" json:"staff_name,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,24,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// specific dates only for daycare appointment
	SpecificDates []string `protobuf:"bytes,25,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// source
	// Note: if source is MEMBERSHIP, frontend will display as "Branded app"
	Source v12.BookingRequestModel_Source `protobuf:"varint,26,opt,name=source,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_Source" json:"source,omitempty"`
	// source id
	SourceId int64 `protobuf:"varint,27,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
}

func (x *BookingRequestDetail) Reset() {
	*x = BookingRequestDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingRequestDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingRequestDetail) ProtoMessage() {}

func (x *BookingRequestDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingRequestDetail.ProtoReflect.Descriptor instead.
func (*BookingRequestDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{6}
}

func (x *BookingRequestDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BookingRequestDetail) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BookingRequestDetail) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *BookingRequestDetail) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *BookingRequestDetail) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *BookingRequestDetail) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *BookingRequestDetail) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *BookingRequestDetail) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *BookingRequestDetail) GetStatus() v12.BookingRequestStatus {
	if x != nil {
		return x.Status
	}
	return v12.BookingRequestStatus(0)
}

func (x *BookingRequestDetail) GetIsPrepaid() bool {
	if x != nil {
		return x.IsPrepaid
	}
	return false
}

func (x *BookingRequestDetail) GetAdditionalNote() string {
	if x != nil {
		return x.AdditionalNote
	}
	return ""
}

func (x *BookingRequestDetail) GetSourcePlatform() v12.BookingRequestSourcePlatform {
	if x != nil {
		return x.SourcePlatform
	}
	return v12.BookingRequestSourcePlatform(0)
}

func (x *BookingRequestDetail) GetServiceTypeInclude() int32 {
	if x != nil {
		return x.ServiceTypeInclude
	}
	return 0
}

func (x *BookingRequestDetail) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BookingRequestDetail) GetNoStartTime() bool {
	if x != nil {
		return x.NoStartTime
	}
	return false
}

func (x *BookingRequestDetail) GetServiceItemTypes() []v13.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *BookingRequestDetail) GetStaffName() string {
	if x != nil && x.StaffName != nil {
		return *x.StaffName
	}
	return ""
}

func (x *BookingRequestDetail) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *BookingRequestDetail) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *BookingRequestDetail) GetSource() v12.BookingRequestModel_Source {
	if x != nil {
		return x.Source
	}
	return v12.BookingRequestModel_Source(0)
}

func (x *BookingRequestDetail) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

// Get booking request response
type GetBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request
	BookingRequest *BookingRequestDetail `protobuf:"bytes,1,opt,name=booking_request,json=bookingRequest,proto3" json:"booking_request,omitempty"`
	// service
	ServiceDetails []*ServiceDetail `protobuf:"bytes,2,rep,name=service_details,json=serviceDetails,proto3" json:"service_details,omitempty"`
	// customer
	CustomerDetail *CustomerDetail `protobuf:"bytes,3,opt,name=customer_detail,json=customerDetail,proto3" json:"customer_detail,omitempty"`
	// pay
	Pay *PayBookingRequestView `protobuf:"bytes,4,opt,name=pay,proto3" json:"pay,omitempty"`
	// profile has request update
	HasRequestUpdate bool `protobuf:"varint,5,opt,name=has_request_update,json=hasRequestUpdate,proto3" json:"has_request_update,omitempty"`
	// address
	Address *AddressDetail `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	// assign require
	// deprecated by Freeman since 2025/3/4, use incomplete_details instead
	//
	// Deprecated: Do not use.
	AssignRequire *AssignRequire `protobuf:"bytes,7,opt,name=assign_require,json=assignRequire,proto3" json:"assign_require,omitempty"`
	// order
	Order *OrderBookingRequestView `protobuf:"bytes,8,opt,name=order,proto3" json:"order,omitempty"`
	// 还需要前端填充数据的 details，包括了：
	// - grooming/boarding/daycare/evaluation service details
	// - grooming/boarding/daycare addon details
	IncompleteDetails *IncompleteDetails `protobuf:"bytes,9,opt,name=incomplete_details,json=incompleteDetails,proto3" json:"incomplete_details,omitempty"`
}

func (x *GetBookingRequestResponse) Reset() {
	*x = GetBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingRequestResponse) ProtoMessage() {}

func (x *GetBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*GetBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetBookingRequestResponse) GetBookingRequest() *BookingRequestDetail {
	if x != nil {
		return x.BookingRequest
	}
	return nil
}

func (x *GetBookingRequestResponse) GetServiceDetails() []*ServiceDetail {
	if x != nil {
		return x.ServiceDetails
	}
	return nil
}

func (x *GetBookingRequestResponse) GetCustomerDetail() *CustomerDetail {
	if x != nil {
		return x.CustomerDetail
	}
	return nil
}

func (x *GetBookingRequestResponse) GetPay() *PayBookingRequestView {
	if x != nil {
		return x.Pay
	}
	return nil
}

func (x *GetBookingRequestResponse) GetHasRequestUpdate() bool {
	if x != nil {
		return x.HasRequestUpdate
	}
	return false
}

func (x *GetBookingRequestResponse) GetAddress() *AddressDetail {
	if x != nil {
		return x.Address
	}
	return nil
}

// Deprecated: Do not use.
func (x *GetBookingRequestResponse) GetAssignRequire() *AssignRequire {
	if x != nil {
		return x.AssignRequire
	}
	return nil
}

func (x *GetBookingRequestResponse) GetOrder() *OrderBookingRequestView {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *GetBookingRequestResponse) GetIncompleteDetails() *IncompleteDetails {
	if x != nil {
		return x.IncompleteDetails
	}
	return nil
}

// The grooming service
type GroomingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the grooming service detail
	Service *GroomingServiceDetail `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// the addons
	Addons []*GroomingAddOnDetail `protobuf:"bytes,2,rep,name=addons,proto3" json:"addons,omitempty"`
	// auto assign
	AutoAssign *GroomingAutoAssignDetail `protobuf:"bytes,6,opt,name=auto_assign,json=autoAssign,proto3,oneof" json:"auto_assign,omitempty"`
}

func (x *GroomingService) Reset() {
	*x = GroomingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingService) ProtoMessage() {}

func (x *GroomingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingService.ProtoReflect.Descriptor instead.
func (*GroomingService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{8}
}

func (x *GroomingService) GetService() *GroomingServiceDetail {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *GroomingService) GetAddons() []*GroomingAddOnDetail {
	if x != nil {
		return x.Addons
	}
	return nil
}

func (x *GroomingService) GetAutoAssign() *GroomingAutoAssignDetail {
	if x != nil {
		return x.AutoAssign
	}
	return nil
}

// The grooming service or add-on detail
type GroomingServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of booking request
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The id of pet, associated with the current service
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of staff, associated with the current service
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The id of current service
	ServiceId int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// The time of current service, unit minute
	ServiceTime int32 `protobuf:"varint,6,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// The price of current service
	ServicePrice float64 `protobuf:"fixed64,7,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// The start date of the service, yyyy-MM-dd
	StartDate string `protobuf:"bytes,10,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// The start time of the service, unit minute, 540 means 09:00
	StartTime int32 `protobuf:"varint,11,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The end date of the service, yyyy-MM-dd
	EndDate string `protobuf:"bytes,12,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// The end time of the service, unit minute, 540 means 09:00
	EndTime int32 `protobuf:"varint,13,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,16,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// staff name
	StaffName string `protobuf:"bytes,17,opt,name=staff_name,json=staffName,proto3" json:"staff_name,omitempty"`
	// price override type
	PriceOverrideType v13.ServiceOverrideType `protobuf:"varint,18,opt,name=price_override_type,json=priceOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType" json:"price_override_type,omitempty"`
	// duration override type
	DurationOverrideType v13.ServiceOverrideType `protobuf:"varint,19,opt,name=duration_override_type,json=durationOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType" json:"duration_override_type,omitempty"`
	// date type
	DateType v14.PetDetailDateType `protobuf:"varint,20,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType" json:"date_type,omitempty"`
}

func (x *GroomingServiceDetail) Reset() {
	*x = GroomingServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingServiceDetail) ProtoMessage() {}

func (x *GroomingServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingServiceDetail.ProtoReflect.Descriptor instead.
func (*GroomingServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{9}
}

func (x *GroomingServiceDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroomingServiceDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *GroomingServiceDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GroomingServiceDetail) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GroomingServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *GroomingServiceDetail) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *GroomingServiceDetail) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *GroomingServiceDetail) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GroomingServiceDetail) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GroomingServiceDetail) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *GroomingServiceDetail) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GroomingServiceDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *GroomingServiceDetail) GetStaffName() string {
	if x != nil {
		return x.StaffName
	}
	return ""
}

func (x *GroomingServiceDetail) GetPriceOverrideType() v13.ServiceOverrideType {
	if x != nil {
		return x.PriceOverrideType
	}
	return v13.ServiceOverrideType(0)
}

func (x *GroomingServiceDetail) GetDurationOverrideType() v13.ServiceOverrideType {
	if x != nil {
		return x.DurationOverrideType
	}
	return v13.ServiceOverrideType(0)
}

func (x *GroomingServiceDetail) GetDateType() v14.PetDetailDateType {
	if x != nil {
		return x.DateType
	}
	return v14.PetDetailDateType(0)
}

// The grooming add-on detail
type GroomingAddOnDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of booking request
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The id of service detail
	ServiceDetailId int64 `protobuf:"varint,3,opt,name=service_detail_id,json=serviceDetailId,proto3" json:"service_detail_id,omitempty"`
	// The id of pet, associated with the current service
	PetId int64 `protobuf:"varint,4,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of staff, associated with the current service
	StaffId int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The id of add-on service, aka. grooming service id
	AddOnId int64 `protobuf:"varint,6,opt,name=add_on_id,json=addOnId,proto3" json:"add_on_id,omitempty"`
	// The time of current service, unit minute
	ServiceTime int32 `protobuf:"varint,7,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// The price of current service
	ServicePrice float64 `protobuf:"fixed64,8,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// The start date of the service, yyyy-MM-dd
	StartDate string `protobuf:"bytes,9,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// The start time of the service, unit minute, 540 means 09:00
	StartTime int32 `protobuf:"varint,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The end date of the service, yyyy-MM-dd
	EndDate string `protobuf:"bytes,11,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// The end time of the service, unit minute, 540 means 09:00
	EndTime int32 `protobuf:"varint,12,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,16,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// staff name
	StaffName string `protobuf:"bytes,17,opt,name=staff_name,json=staffName,proto3" json:"staff_name,omitempty"`
}

func (x *GroomingAddOnDetail) Reset() {
	*x = GroomingAddOnDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingAddOnDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingAddOnDetail) ProtoMessage() {}

func (x *GroomingAddOnDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingAddOnDetail.ProtoReflect.Descriptor instead.
func (*GroomingAddOnDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{10}
}

func (x *GroomingAddOnDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroomingAddOnDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *GroomingAddOnDetail) GetServiceDetailId() int64 {
	if x != nil {
		return x.ServiceDetailId
	}
	return 0
}

func (x *GroomingAddOnDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GroomingAddOnDetail) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GroomingAddOnDetail) GetAddOnId() int64 {
	if x != nil {
		return x.AddOnId
	}
	return 0
}

func (x *GroomingAddOnDetail) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *GroomingAddOnDetail) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *GroomingAddOnDetail) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GroomingAddOnDetail) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GroomingAddOnDetail) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *GroomingAddOnDetail) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GroomingAddOnDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *GroomingAddOnDetail) GetStaffName() string {
	if x != nil {
		return x.StaffName
	}
	return ""
}

// The boarding service
type BoardingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the boarding service detail
	Service *BoardingServiceDetail `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// the addons
	Addons []*BoardingAddOnDetail `protobuf:"bytes,2,rep,name=addons,proto3" json:"addons,omitempty"`
	// the feeding. Deprecated, use feedings instead
	//
	// Deprecated: Do not use.
	Feeding *FeedingDetail `protobuf:"bytes,3,opt,name=feeding,proto3" json:"feeding,omitempty"`
	// the medication. Deprecated, use medications instead
	//
	// Deprecated: Do not use.
	Medication *MedicationDetail `protobuf:"bytes,4,opt,name=medication,proto3" json:"medication,omitempty"`
	// auto assign
	//
	// Deprecated: Do not use.
	AutoAssign *BoardingAutoAssignDetail `protobuf:"bytes,6,opt,name=auto_assign,json=autoAssign,proto3,oneof" json:"auto_assign,omitempty"`
	// the feedings
	Feedings []*FeedingDetail `protobuf:"bytes,7,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// the medications
	Medications []*MedicationDetail `protobuf:"bytes,8,rep,name=medications,proto3" json:"medications,omitempty"`
}

func (x *BoardingService) Reset() {
	*x = BoardingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingService) ProtoMessage() {}

func (x *BoardingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingService.ProtoReflect.Descriptor instead.
func (*BoardingService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{11}
}

func (x *BoardingService) GetService() *BoardingServiceDetail {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *BoardingService) GetAddons() []*BoardingAddOnDetail {
	if x != nil {
		return x.Addons
	}
	return nil
}

// Deprecated: Do not use.
func (x *BoardingService) GetFeeding() *FeedingDetail {
	if x != nil {
		return x.Feeding
	}
	return nil
}

// Deprecated: Do not use.
func (x *BoardingService) GetMedication() *MedicationDetail {
	if x != nil {
		return x.Medication
	}
	return nil
}

// Deprecated: Do not use.
func (x *BoardingService) GetAutoAssign() *BoardingAutoAssignDetail {
	if x != nil {
		return x.AutoAssign
	}
	return nil
}

func (x *BoardingService) GetFeedings() []*FeedingDetail {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *BoardingService) GetMedications() []*MedicationDetail {
	if x != nil {
		return x.Medications
	}
	return nil
}

// The boarding service detail
type BoardingServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of booking request
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The id of pet, associated with the current service
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of lodging, associated with the current service
	//
	// Deprecated: Do not use.
	LodgingId int64 `protobuf:"varint,4,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// The id of current service
	ServiceId int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// specific dates
	SpecificDates []string `protobuf:"bytes,6,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// The price of current service
	ServicePrice float64 `protobuf:"fixed64,7,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// taxId
	TaxId int64 `protobuf:"varint,8,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// The pet arrival date of the service, yyyy-MM-dd
	StartDate string `protobuf:"bytes,9,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// The pet arrival time of the service, unit minute, 540 means 09:00
	StartTime int32 `protobuf:"varint,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The pet pickup date of the service, yyyy-MM-dd
	EndDate string `protobuf:"bytes,11,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// The pet pickup time of the service, unit minute, 540 means 09:00
	EndTime int32 `protobuf:"varint,12,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,16,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// lodging unit name
	//
	// Deprecated: Do not use.
	LodgingUnitName string `protobuf:"bytes,17,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
	// lodging type name
	//
	// Deprecated: Do not use.
	LodgingTypeName string `protobuf:"bytes,18,opt,name=lodging_type_name,json=lodgingTypeName,proto3" json:"lodging_type_name,omitempty"`
	// price unit
	PriceUnit v13.ServicePriceUnit `protobuf:"varint,19,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"price_unit,omitempty"`
}

func (x *BoardingServiceDetail) Reset() {
	*x = BoardingServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingServiceDetail) ProtoMessage() {}

func (x *BoardingServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingServiceDetail.ProtoReflect.Descriptor instead.
func (*BoardingServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{12}
}

func (x *BoardingServiceDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BoardingServiceDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *BoardingServiceDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

// Deprecated: Do not use.
func (x *BoardingServiceDetail) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *BoardingServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *BoardingServiceDetail) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *BoardingServiceDetail) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *BoardingServiceDetail) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *BoardingServiceDetail) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *BoardingServiceDetail) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *BoardingServiceDetail) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *BoardingServiceDetail) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *BoardingServiceDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

// Deprecated: Do not use.
func (x *BoardingServiceDetail) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

// Deprecated: Do not use.
func (x *BoardingServiceDetail) GetLodgingTypeName() string {
	if x != nil {
		return x.LodgingTypeName
	}
	return ""
}

func (x *BoardingServiceDetail) GetPriceUnit() v13.ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return v13.ServicePriceUnit(0)
}

// The boarding add-on detail
type BoardingAddOnDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of booking request
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The id of boarding service detail, associated with the current add-on
	ServiceDetailId int64 `protobuf:"varint,3,opt,name=service_detail_id,json=serviceDetailId,proto3" json:"service_detail_id,omitempty"`
	// The id of pet, associated with the current add-on
	PetId int64 `protobuf:"varint,4,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of current add-on service
	AddOnId int64 `protobuf:"varint,5,opt,name=add_on_id,json=addOnId,proto3" json:"add_on_id,omitempty"`
	// The specific dates of the add-on service
	SpecificDates []string `protobuf:"bytes,6,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// whether the add-on service is everyday, not include checkout day
	// deprecated. use date_type instead
	//
	// Deprecated: Do not use.
	IsEveryday bool `protobuf:"varint,7,opt,name=is_everyday,json=isEveryday,proto3" json:"is_everyday,omitempty"`
	// The price of current add-on service
	ServicePrice float64 `protobuf:"fixed64,8,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// taxId
	TaxId int64 `protobuf:"varint,9,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,10,opt,name=duration,proto3" json:"duration,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,16,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// quantity per day
	QuantityPerDay int32 `protobuf:"varint,17,opt,name=quantity_per_day,json=quantityPerDay,proto3" json:"quantity_per_day,omitempty"`
	// require dedicated staff
	RequireDedicatedStaff bool `protobuf:"varint,18,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// date type
	DateType *v14.PetDetailDateType `protobuf:"varint,13,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType,oneof" json:"date_type,omitempty"`
	// start date
	// Use start_date when date_type is PET_DETAIL_DATE_DATE_POINT
	StartDate *string `protobuf:"bytes,14,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
}

func (x *BoardingAddOnDetail) Reset() {
	*x = BoardingAddOnDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingAddOnDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingAddOnDetail) ProtoMessage() {}

func (x *BoardingAddOnDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingAddOnDetail.ProtoReflect.Descriptor instead.
func (*BoardingAddOnDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{13}
}

func (x *BoardingAddOnDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BoardingAddOnDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *BoardingAddOnDetail) GetServiceDetailId() int64 {
	if x != nil {
		return x.ServiceDetailId
	}
	return 0
}

func (x *BoardingAddOnDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *BoardingAddOnDetail) GetAddOnId() int64 {
	if x != nil {
		return x.AddOnId
	}
	return 0
}

func (x *BoardingAddOnDetail) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

// Deprecated: Do not use.
func (x *BoardingAddOnDetail) GetIsEveryday() bool {
	if x != nil {
		return x.IsEveryday
	}
	return false
}

func (x *BoardingAddOnDetail) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *BoardingAddOnDetail) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *BoardingAddOnDetail) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *BoardingAddOnDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *BoardingAddOnDetail) GetQuantityPerDay() int32 {
	if x != nil {
		return x.QuantityPerDay
	}
	return 0
}

func (x *BoardingAddOnDetail) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *BoardingAddOnDetail) GetDateType() v14.PetDetailDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return v14.PetDetailDateType(0)
}

func (x *BoardingAddOnDetail) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

// Stores information about feedings.
type FeedingDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The primary key identifier for each feeding.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The booking request identifier.
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The service detail identifier.
	ServiceDetailId int64 `protobuf:"varint,3,opt,name=service_detail_id,json=serviceDetailId,proto3" json:"service_detail_id,omitempty"`
	// service detail type, 1: boarding, 2: daycare
	//
	//	ServiceDetailType service_detail_type = 4;
	//
	// Feeding time.
	Time []*v12.FeedingModel_FeedingSchedule `protobuf:"bytes,5,rep,name=time,proto3" json:"time,omitempty"`
	// Feeding amount, must be greater than 0.
	//
	// Deprecated: Do not use.
	Amount float64 `protobuf:"fixed64,6,opt,name=amount,proto3" json:"amount,omitempty"`
	// Feeding unit.
	Unit string `protobuf:"bytes,7,opt,name=unit,proto3" json:"unit,omitempty"`
	// Food type.
	FoodType []string `protobuf:"bytes,8,rep,name=food_type,json=foodType,proto3" json:"food_type,omitempty"`
	// Food source.
	FoodSource string `protobuf:"bytes,9,opt,name=food_source,json=foodSource,proto3" json:"food_source,omitempty"`
	// Feeding instructions.
	Instruction string `protobuf:"bytes,10,opt,name=instruction,proto3" json:"instruction,omitempty"`
	// Feeding note
	Note string `protobuf:"bytes,11,opt,name=note,proto3" json:"note,omitempty"`
	// Feeding amount, such as 1.2, 1/2, 1 etc.
	AmountStr *string `protobuf:"bytes,12,opt,name=amount_str,json=amountStr,proto3,oneof" json:"amount_str,omitempty"`
}

func (x *FeedingDetail) Reset() {
	*x = FeedingDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedingDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedingDetail) ProtoMessage() {}

func (x *FeedingDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedingDetail.ProtoReflect.Descriptor instead.
func (*FeedingDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{14}
}

func (x *FeedingDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FeedingDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *FeedingDetail) GetServiceDetailId() int64 {
	if x != nil {
		return x.ServiceDetailId
	}
	return 0
}

func (x *FeedingDetail) GetTime() []*v12.FeedingModel_FeedingSchedule {
	if x != nil {
		return x.Time
	}
	return nil
}

// Deprecated: Do not use.
func (x *FeedingDetail) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *FeedingDetail) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *FeedingDetail) GetFoodType() []string {
	if x != nil {
		return x.FoodType
	}
	return nil
}

func (x *FeedingDetail) GetFoodSource() string {
	if x != nil {
		return x.FoodSource
	}
	return ""
}

func (x *FeedingDetail) GetInstruction() string {
	if x != nil {
		return x.Instruction
	}
	return ""
}

func (x *FeedingDetail) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *FeedingDetail) GetAmountStr() string {
	if x != nil && x.AmountStr != nil {
		return *x.AmountStr
	}
	return ""
}

// Stores information about medication events.
type MedicationDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The primary key identifier for each medication event.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The booking request identifier.
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The service detail identifier.
	ServiceDetailId int64 `protobuf:"varint,3,opt,name=service_detail_id,json=serviceDetailId,proto3" json:"service_detail_id,omitempty"`
	// service detail type, 1: boarding, 2: daycare
	//
	//	ServiceDetailType service_detail_type = 4;
	//
	// Medication time.
	Time []*v12.MedicationModel_MedicationSchedule `protobuf:"bytes,5,rep,name=time,proto3" json:"time,omitempty"`
	// Medication amount, must be greater than 0.
	//
	// Deprecated: Do not use.
	Amount float64 `protobuf:"fixed64,6,opt,name=amount,proto3" json:"amount,omitempty"`
	// Medication unit.
	Unit string `protobuf:"bytes,7,opt,name=unit,proto3" json:"unit,omitempty"`
	// Medication name.
	MedicationName string `protobuf:"bytes,8,opt,name=medication_name,json=medicationName,proto3" json:"medication_name,omitempty"`
	// Additional notes about the medication.
	Notes string `protobuf:"bytes,9,opt,name=notes,proto3" json:"notes,omitempty"`
	// Medication amount, such as 1.2, 1/2, 1 etc.
	AmountStr *string `protobuf:"bytes,12,opt,name=amount_str,json=amountStr,proto3,oneof" json:"amount_str,omitempty"`
	// Medication select date
	SelectedDate *v14.AppointmentPetMedicationScheduleDef_SelectedDateDef `protobuf:"bytes,13,opt,name=selected_date,json=selectedDate,proto3,oneof" json:"selected_date,omitempty"`
}

func (x *MedicationDetail) Reset() {
	*x = MedicationDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MedicationDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MedicationDetail) ProtoMessage() {}

func (x *MedicationDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MedicationDetail.ProtoReflect.Descriptor instead.
func (*MedicationDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{15}
}

func (x *MedicationDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MedicationDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *MedicationDetail) GetServiceDetailId() int64 {
	if x != nil {
		return x.ServiceDetailId
	}
	return 0
}

func (x *MedicationDetail) GetTime() []*v12.MedicationModel_MedicationSchedule {
	if x != nil {
		return x.Time
	}
	return nil
}

// Deprecated: Do not use.
func (x *MedicationDetail) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *MedicationDetail) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *MedicationDetail) GetMedicationName() string {
	if x != nil {
		return x.MedicationName
	}
	return ""
}

func (x *MedicationDetail) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *MedicationDetail) GetAmountStr() string {
	if x != nil && x.AmountStr != nil {
		return *x.AmountStr
	}
	return ""
}

func (x *MedicationDetail) GetSelectedDate() *v14.AppointmentPetMedicationScheduleDef_SelectedDateDef {
	if x != nil {
		return x.SelectedDate
	}
	return nil
}

// The daycare service
type DaycareService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the daycare service detail
	Service *DaycareServiceDetail `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// the addons
	Addons []*DaycareAddOnDetail `protobuf:"bytes,2,rep,name=addons,proto3" json:"addons,omitempty"`
	// the feeding. Deprecated, use feedings instead
	//
	// Deprecated: Do not use.
	Feeding *FeedingDetail `protobuf:"bytes,3,opt,name=feeding,proto3" json:"feeding,omitempty"`
	// the medication. Deprecated, use medications instead
	//
	// Deprecated: Do not use.
	Medication *MedicationDetail `protobuf:"bytes,4,opt,name=medication,proto3" json:"medication,omitempty"`
	// the feedings
	Feedings []*FeedingDetail `protobuf:"bytes,5,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// the medications
	Medications []*MedicationDetail `protobuf:"bytes,6,rep,name=medications,proto3" json:"medications,omitempty"`
}

func (x *DaycareService) Reset() {
	*x = DaycareService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareService) ProtoMessage() {}

func (x *DaycareService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareService.ProtoReflect.Descriptor instead.
func (*DaycareService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{16}
}

func (x *DaycareService) GetService() *DaycareServiceDetail {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *DaycareService) GetAddons() []*DaycareAddOnDetail {
	if x != nil {
		return x.Addons
	}
	return nil
}

// Deprecated: Do not use.
func (x *DaycareService) GetFeeding() *FeedingDetail {
	if x != nil {
		return x.Feeding
	}
	return nil
}

// Deprecated: Do not use.
func (x *DaycareService) GetMedication() *MedicationDetail {
	if x != nil {
		return x.Medication
	}
	return nil
}

func (x *DaycareService) GetFeedings() []*FeedingDetail {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *DaycareService) GetMedications() []*MedicationDetail {
	if x != nil {
		return x.Medications
	}
	return nil
}

// The daycare service detail
type DaycareServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of booking request
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The id of pet, associated with the current service
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of current service
	ServiceId int64 `protobuf:"varint,4,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// The specific dates of the daycare service
	SpecificDates []string `protobuf:"bytes,5,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// The price of current service
	ServicePrice float64 `protobuf:"fixed64,6,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// taxId
	TaxId int64 `protobuf:"varint,7,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// The max duration of the daycare service, unit minute
	MaxDuration int32 `protobuf:"varint,8,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// The pet arrival date of the service, yyyy-MM-dd
	StartDate string `protobuf:"bytes,9,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// The pet arrival time of the service, unit minute, 540 means 09:00
	StartTime int32 `protobuf:"varint,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The pet latest pickup date of the service, yyyy-MM-dd
	EndDate string `protobuf:"bytes,11,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// The pet latest pickup time of the service, unit minute, 540 means 09:00
	EndTime int32 `protobuf:"varint,12,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,16,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
}

func (x *DaycareServiceDetail) Reset() {
	*x = DaycareServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareServiceDetail) ProtoMessage() {}

func (x *DaycareServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareServiceDetail.ProtoReflect.Descriptor instead.
func (*DaycareServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{17}
}

func (x *DaycareServiceDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DaycareServiceDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *DaycareServiceDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *DaycareServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *DaycareServiceDetail) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *DaycareServiceDetail) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *DaycareServiceDetail) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *DaycareServiceDetail) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

func (x *DaycareServiceDetail) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *DaycareServiceDetail) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *DaycareServiceDetail) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *DaycareServiceDetail) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *DaycareServiceDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

// The daycare add-on detail
type DaycareAddOnDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of booking request
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The id of daycare service detail, associated with the current add-on
	ServiceDetailId int64 `protobuf:"varint,3,opt,name=service_detail_id,json=serviceDetailId,proto3" json:"service_detail_id,omitempty"`
	// The id of pet, associated with the current add-on
	PetId int64 `protobuf:"varint,4,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of current add-on service
	AddOnId int64 `protobuf:"varint,5,opt,name=add_on_id,json=addOnId,proto3" json:"add_on_id,omitempty"`
	// The specific dates of add-on service
	SpecificDates []string `protobuf:"bytes,6,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// The flag to indicate if the add-on service is everyday
	IsEveryday bool `protobuf:"varint,7,opt,name=is_everyday,json=isEveryday,proto3" json:"is_everyday,omitempty"`
	// The price of current add-on service
	ServicePrice float64 `protobuf:"fixed64,8,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// The id of tax, associated with the current add-on
	TaxId int64 `protobuf:"varint,9,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// The duration of current add-on service
	Duration int32 `protobuf:"varint,10,opt,name=duration,proto3" json:"duration,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,16,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// quantity per day
	QuantityPerDay int32 `protobuf:"varint,17,opt,name=quantity_per_day,json=quantityPerDay,proto3" json:"quantity_per_day,omitempty"`
	// require dedicated staff
	RequireDedicatedStaff bool `protobuf:"varint,18,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
}

func (x *DaycareAddOnDetail) Reset() {
	*x = DaycareAddOnDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareAddOnDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareAddOnDetail) ProtoMessage() {}

func (x *DaycareAddOnDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareAddOnDetail.ProtoReflect.Descriptor instead.
func (*DaycareAddOnDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{18}
}

func (x *DaycareAddOnDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DaycareAddOnDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *DaycareAddOnDetail) GetServiceDetailId() int64 {
	if x != nil {
		return x.ServiceDetailId
	}
	return 0
}

func (x *DaycareAddOnDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *DaycareAddOnDetail) GetAddOnId() int64 {
	if x != nil {
		return x.AddOnId
	}
	return 0
}

func (x *DaycareAddOnDetail) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *DaycareAddOnDetail) GetIsEveryday() bool {
	if x != nil {
		return x.IsEveryday
	}
	return false
}

func (x *DaycareAddOnDetail) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *DaycareAddOnDetail) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *DaycareAddOnDetail) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *DaycareAddOnDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *DaycareAddOnDetail) GetQuantityPerDay() int32 {
	if x != nil {
		return x.QuantityPerDay
	}
	return 0
}

func (x *DaycareAddOnDetail) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

// The evaluation service
type EvaluationService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation service detail
	Service *EvaluationTestDetail `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *EvaluationService) Reset() {
	*x = EvaluationService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationService) ProtoMessage() {}

func (x *EvaluationService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationService.ProtoReflect.Descriptor instead.
func (*EvaluationService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{19}
}

func (x *EvaluationService) GetService() *EvaluationTestDetail {
	if x != nil {
		return x.Service
	}
	return nil
}

// The evaluation test detail
type EvaluationTestDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of booking request
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The id of pet, associated with the current evaluation test
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of current evaluation test
	EvaluationId int64 `protobuf:"varint,4,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
	// The price of current evaluation test
	ServicePrice float64 `protobuf:"fixed64,5,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// The duration of current evaluation test, unit minute
	Duration int32 `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
	// The start date of the evaluation test, yyyy-MM-dd
	StartDate string `protobuf:"bytes,7,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// The start time of the evaluation test, unit minute, 540 means 09:00
	StartTime int32 `protobuf:"varint,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The end date of the evaluation test, yyyy-MM-dd
	EndDate string `protobuf:"bytes,9,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// The end time of the evaluation test, unit minute, 540 means 09:00
	EndTime int32 `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// service name
	// deprecated by Freeman since 2025/5/16, 避免和 service 名字混淆，use evaluation_name instead
	//
	// Deprecated: Do not use.
	ServiceName string `protobuf:"bytes,16,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// evaluation name
	EvaluationName string `protobuf:"bytes,17,opt,name=evaluation_name,json=evaluationName,proto3" json:"evaluation_name,omitempty"`
	// service id, evaluation 绑定的 service id
	// 0 表示没有绑定 service
	ServiceId int64 `protobuf:"varint,18,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
}

func (x *EvaluationTestDetail) Reset() {
	*x = EvaluationTestDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationTestDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationTestDetail) ProtoMessage() {}

func (x *EvaluationTestDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationTestDetail.ProtoReflect.Descriptor instead.
func (*EvaluationTestDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{20}
}

func (x *EvaluationTestDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EvaluationTestDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *EvaluationTestDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *EvaluationTestDetail) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

func (x *EvaluationTestDetail) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *EvaluationTestDetail) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *EvaluationTestDetail) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *EvaluationTestDetail) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *EvaluationTestDetail) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *EvaluationTestDetail) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

// Deprecated: Do not use.
func (x *EvaluationTestDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *EvaluationTestDetail) GetEvaluationName() string {
	if x != nil {
		return x.EvaluationName
	}
	return ""
}

func (x *EvaluationTestDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// The dog walking service
type DogWalkingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the dog walking service detail
	Service *DogWalkingServiceDetail `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *DogWalkingService) Reset() {
	*x = DogWalkingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DogWalkingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DogWalkingService) ProtoMessage() {}

func (x *DogWalkingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DogWalkingService.ProtoReflect.Descriptor instead.
func (*DogWalkingService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{21}
}

func (x *DogWalkingService) GetService() *DogWalkingServiceDetail {
	if x != nil {
		return x.Service
	}
	return nil
}

// The dog walking service detail
type DogWalkingServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of booking request
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The id of pet, associated with the current service
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of staff, associated with the current service
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The id of current service
	ServiceId int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// The time of current service, unit minute
	ServiceTime int32 `protobuf:"varint,6,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// The price of current service
	ServicePrice float64 `protobuf:"fixed64,7,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// The start date of the service, yyyy-MM-dd
	StartDate string `protobuf:"bytes,10,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// The start time of the service, unit minute, 540 means 09:00
	StartTime int32 `protobuf:"varint,11,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The end date of the service, yyyy-MM-dd
	EndDate string `protobuf:"bytes,12,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// The end time of the service, unit minute, 540 means 09:00
	EndTime int32 `protobuf:"varint,13,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,16,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// staff name
	StaffName string `protobuf:"bytes,17,opt,name=staff_name,json=staffName,proto3" json:"staff_name,omitempty"`
	// price override type
	PriceOverrideType v13.ServiceOverrideType `protobuf:"varint,18,opt,name=price_override_type,json=priceOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType" json:"price_override_type,omitempty"`
	// duration override type
	DurationOverrideType v13.ServiceOverrideType `protobuf:"varint,19,opt,name=duration_override_type,json=durationOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType" json:"duration_override_type,omitempty"`
}

func (x *DogWalkingServiceDetail) Reset() {
	*x = DogWalkingServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DogWalkingServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DogWalkingServiceDetail) ProtoMessage() {}

func (x *DogWalkingServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DogWalkingServiceDetail.ProtoReflect.Descriptor instead.
func (*DogWalkingServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{22}
}

func (x *DogWalkingServiceDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *DogWalkingServiceDetail) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *DogWalkingServiceDetail) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *DogWalkingServiceDetail) GetStaffName() string {
	if x != nil {
		return x.StaffName
	}
	return ""
}

func (x *DogWalkingServiceDetail) GetPriceOverrideType() v13.ServiceOverrideType {
	if x != nil {
		return x.PriceOverrideType
	}
	return v13.ServiceOverrideType(0)
}

func (x *DogWalkingServiceDetail) GetDurationOverrideType() v13.ServiceOverrideType {
	if x != nil {
		return x.DurationOverrideType
	}
	return v13.ServiceOverrideType(0)
}

// The group class service
type GroupClassService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The group class service detail
	Service *GroupClassServiceDetail `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *GroupClassService) Reset() {
	*x = GroupClassService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassService) ProtoMessage() {}

func (x *GroupClassService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassService.ProtoReflect.Descriptor instead.
func (*GroupClassService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{23}
}

func (x *GroupClassService) GetService() *GroupClassServiceDetail {
	if x != nil {
		return x.Service
	}
	return nil
}

// The group class service detail
type GroupClassServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of booking request
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The id of pet, associated with the current service
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of group class instance,
	ClassInstanceId int64 `protobuf:"varint,4,opt,name=class_instance_id,json=classInstanceId,proto3" json:"class_instance_id,omitempty"`
	// The id of staff, associated with the current service
	StaffId int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The id of current service
	ServiceId int64 `protobuf:"varint,6,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// The time of current service, unit minute
	SpecificDates []string `protobuf:"bytes,7,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// The start time of the service, unit minute, 540 means 09:00
	StartTime int32 `protobuf:"varint,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The end time of the service, unit minute, 540 means 09:00
	EndTime int32 `protobuf:"varint,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// Duration of each session in minutes
	DurationPerSession int32 `protobuf:"varint,10,opt,name=duration_per_session,json=durationPerSession,proto3" json:"duration_per_session,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,11,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// staff name
	StaffName string `protobuf:"bytes,12,opt,name=staff_name,json=staffName,proto3" json:"staff_name,omitempty"`
	// instance name
	InstanceName string `protobuf:"bytes,13,opt,name=instance_name,json=instanceName,proto3" json:"instance_name,omitempty"`
	// Number of sessions
	NumSessions int32 `protobuf:"varint,14,opt,name=num_sessions,json=numSessions,proto3" json:"num_sessions,omitempty"`
	// Group class instance occurrence
	Occurrence *v13.GroupClassInstance_Occurrence `protobuf:"bytes,15,opt,name=occurrence,proto3" json:"occurrence,omitempty"`
}

func (x *GroupClassServiceDetail) Reset() {
	*x = GroupClassServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassServiceDetail) ProtoMessage() {}

func (x *GroupClassServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassServiceDetail.ProtoReflect.Descriptor instead.
func (*GroupClassServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{24}
}

func (x *GroupClassServiceDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroupClassServiceDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *GroupClassServiceDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GroupClassServiceDetail) GetClassInstanceId() int64 {
	if x != nil {
		return x.ClassInstanceId
	}
	return 0
}

func (x *GroupClassServiceDetail) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GroupClassServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *GroupClassServiceDetail) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *GroupClassServiceDetail) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GroupClassServiceDetail) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GroupClassServiceDetail) GetDurationPerSession() int32 {
	if x != nil {
		return x.DurationPerSession
	}
	return 0
}

func (x *GroupClassServiceDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *GroupClassServiceDetail) GetStaffName() string {
	if x != nil {
		return x.StaffName
	}
	return ""
}

func (x *GroupClassServiceDetail) GetInstanceName() string {
	if x != nil {
		return x.InstanceName
	}
	return ""
}

func (x *GroupClassServiceDetail) GetNumSessions() int32 {
	if x != nil {
		return x.NumSessions
	}
	return 0
}

func (x *GroupClassServiceDetail) GetOccurrence() *v13.GroupClassInstance_Occurrence {
	if x != nil {
		return x.Occurrence
	}
	return nil
}

// pet detail
type PetDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,4,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,5,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// pet type id
	PetType v15.PetType `protobuf:"varint,6,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// breed
	Breed string `protobuf:"bytes,7,opt,name=breed,proto3" json:"breed,omitempty"`
	// breed mixed
	BreedMixed bool `protobuf:"varint,10,opt,name=breed_mixed,json=breedMixed,proto3" json:"breed_mixed,omitempty"`
	// gender
	Gender v15.PetGender `protobuf:"varint,11,opt,name=gender,proto3,enum=moego.models.customer.v1.PetGender" json:"gender,omitempty"`
	// weight
	Weight string `protobuf:"bytes,8,opt,name=weight,proto3" json:"weight,omitempty"`
	// coat type
	CoatType string `protobuf:"bytes,9,opt,name=coat_type,json=coatType,proto3" json:"coat_type,omitempty"`
	// fixed
	Fixed string `protobuf:"bytes,12,opt,name=fixed,proto3" json:"fixed,omitempty"`
	// behavior
	Behavior string `protobuf:"bytes,13,opt,name=behavior,proto3" json:"behavior,omitempty"`
	// birthday, may not exist
	Birthday *date.Date `protobuf:"bytes,15,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// passed away
	PassedAway bool `protobuf:"varint,16,opt,name=passed_away,json=passedAway,proto3" json:"passed_away,omitempty"`
	// deleted
	Deleted bool `protobuf:"varint,17,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// expiry notification
	ExpiryNotification bool `protobuf:"varint,20,opt,name=expiry_notification,json=expiryNotification,proto3" json:"expiry_notification,omitempty"`
	// vet name
	VetName string `protobuf:"bytes,30,opt,name=vet_name,json=vetName,proto3" json:"vet_name,omitempty"`
	// vet phone number
	VetPhoneNumber string `protobuf:"bytes,31,opt,name=vet_phone_number,json=vetPhoneNumber,proto3" json:"vet_phone_number,omitempty"`
	// vet address
	VetAddress string `protobuf:"bytes,32,opt,name=vet_address,json=vetAddress,proto3" json:"vet_address,omitempty"`
	// emergency contact name
	EmergencyContactName string `protobuf:"bytes,33,opt,name=emergency_contact_name,json=emergencyContactName,proto3" json:"emergency_contact_name,omitempty"`
	// emergency contact phone number
	EmergencyContactPhoneNumber string `protobuf:"bytes,34,opt,name=emergency_contact_phone_number,json=emergencyContactPhoneNumber,proto3" json:"emergency_contact_phone_number,omitempty"`
	// health issues
	HealthIssues string `protobuf:"bytes,35,opt,name=health_issues,json=healthIssues,proto3" json:"health_issues,omitempty"`
	// evaluation status
	EvaluationStatus v15.EvaluationStatus `protobuf:"varint,36,opt,name=evaluation_status,json=evaluationStatus,proto3,enum=moego.models.customer.v1.EvaluationStatus" json:"evaluation_status,omitempty"`
	// pet codes
	PetCodes []*PetCodeComposite `protobuf:"bytes,37,rep,name=pet_codes,json=petCodes,proto3" json:"pet_codes,omitempty"`
	// question answers
	QuestionAnswers []*QuestionAnswerDetail `protobuf:"bytes,38,rep,name=question_answers,json=questionAnswers,proto3" json:"question_answers,omitempty"`
	// pet vaccines
	PetVaccines []*PetVaccineComposite `protobuf:"bytes,39,rep,name=pet_vaccines,json=petVaccines,proto3" json:"pet_vaccines,omitempty"`
}

func (x *PetDetail) Reset() {
	*x = PetDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDetail) ProtoMessage() {}

func (x *PetDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDetail.ProtoReflect.Descriptor instead.
func (*PetDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{25}
}

func (x *PetDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetDetail) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *PetDetail) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *PetDetail) GetPetType() v15.PetType {
	if x != nil {
		return x.PetType
	}
	return v15.PetType(0)
}

func (x *PetDetail) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *PetDetail) GetBreedMixed() bool {
	if x != nil {
		return x.BreedMixed
	}
	return false
}

func (x *PetDetail) GetGender() v15.PetGender {
	if x != nil {
		return x.Gender
	}
	return v15.PetGender(0)
}

func (x *PetDetail) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

func (x *PetDetail) GetCoatType() string {
	if x != nil {
		return x.CoatType
	}
	return ""
}

func (x *PetDetail) GetFixed() string {
	if x != nil {
		return x.Fixed
	}
	return ""
}

func (x *PetDetail) GetBehavior() string {
	if x != nil {
		return x.Behavior
	}
	return ""
}

func (x *PetDetail) GetBirthday() *date.Date {
	if x != nil {
		return x.Birthday
	}
	return nil
}

func (x *PetDetail) GetPassedAway() bool {
	if x != nil {
		return x.PassedAway
	}
	return false
}

func (x *PetDetail) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *PetDetail) GetExpiryNotification() bool {
	if x != nil {
		return x.ExpiryNotification
	}
	return false
}

func (x *PetDetail) GetVetName() string {
	if x != nil {
		return x.VetName
	}
	return ""
}

func (x *PetDetail) GetVetPhoneNumber() string {
	if x != nil {
		return x.VetPhoneNumber
	}
	return ""
}

func (x *PetDetail) GetVetAddress() string {
	if x != nil {
		return x.VetAddress
	}
	return ""
}

func (x *PetDetail) GetEmergencyContactName() string {
	if x != nil {
		return x.EmergencyContactName
	}
	return ""
}

func (x *PetDetail) GetEmergencyContactPhoneNumber() string {
	if x != nil {
		return x.EmergencyContactPhoneNumber
	}
	return ""
}

func (x *PetDetail) GetHealthIssues() string {
	if x != nil {
		return x.HealthIssues
	}
	return ""
}

func (x *PetDetail) GetEvaluationStatus() v15.EvaluationStatus {
	if x != nil {
		return x.EvaluationStatus
	}
	return v15.EvaluationStatus(0)
}

func (x *PetDetail) GetPetCodes() []*PetCodeComposite {
	if x != nil {
		return x.PetCodes
	}
	return nil
}

func (x *PetDetail) GetQuestionAnswers() []*QuestionAnswerDetail {
	if x != nil {
		return x.QuestionAnswers
	}
	return nil
}

func (x *PetDetail) GetPetVaccines() []*PetVaccineComposite {
	if x != nil {
		return x.PetVaccines
	}
	return nil
}

// pet vaccine
type PetVaccineComposite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine record id
	VaccineBindingId int64 `protobuf:"varint,1,opt,name=vaccine_binding_id,json=vaccineBindingId,proto3" json:"vaccine_binding_id,omitempty"`
	// vaccine id
	VaccineId int64 `protobuf:"varint,3,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// expiration date, may not exist
	ExpirationDate *date.Date `protobuf:"bytes,4,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// vaccine document urls
	DocumentUrls []string `protobuf:"bytes,5,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
	// vaccine name
	VaccineName string `protobuf:"bytes,6,opt,name=vaccine_name,json=vaccineName,proto3" json:"vaccine_name,omitempty"`
}

func (x *PetVaccineComposite) Reset() {
	*x = PetVaccineComposite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetVaccineComposite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetVaccineComposite) ProtoMessage() {}

func (x *PetVaccineComposite) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetVaccineComposite.ProtoReflect.Descriptor instead.
func (*PetVaccineComposite) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{26}
}

func (x *PetVaccineComposite) GetVaccineBindingId() int64 {
	if x != nil {
		return x.VaccineBindingId
	}
	return 0
}

func (x *PetVaccineComposite) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *PetVaccineComposite) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

func (x *PetVaccineComposite) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

func (x *PetVaccineComposite) GetVaccineName() string {
	if x != nil {
		return x.VaccineName
	}
	return ""
}

// pet code model
type PetCodeComposite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet code abbreviation
	Abbreviation string `protobuf:"bytes,2,opt,name=abbreviation,proto3" json:"abbreviation,omitempty"`
	// pet code description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// pet code color
	Color string `protobuf:"bytes,5,opt,name=color,proto3" json:"color,omitempty"`
	// pet code sort. The larger the sort number, the higher the priority.
	Sort int32 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	// if the pet code is deleted
	Deleted bool `protobuf:"varint,7,opt,name=deleted,proto3" json:"deleted,omitempty"`
}

func (x *PetCodeComposite) Reset() {
	*x = PetCodeComposite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetCodeComposite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCodeComposite) ProtoMessage() {}

func (x *PetCodeComposite) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCodeComposite.ProtoReflect.Descriptor instead.
func (*PetCodeComposite) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{27}
}

func (x *PetCodeComposite) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetCodeComposite) GetAbbreviation() string {
	if x != nil {
		return x.Abbreviation
	}
	return ""
}

func (x *PetCodeComposite) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PetCodeComposite) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *PetCodeComposite) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *PetCodeComposite) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

// The Pay for Booking Request
type PayBookingRequestView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// paid amount
	PaidAmount *float64 `protobuf:"fixed64,1,opt,name=paid_amount,json=paidAmount,proto3,oneof" json:"paid_amount,omitempty"`
	// pre pay amount
	PrePayAmount *float64 `protobuf:"fixed64,2,opt,name=pre_pay_amount,json=prePayAmount,proto3,oneof" json:"pre_pay_amount,omitempty"`
	// refund amount
	RefundAmount *float64 `protobuf:"fixed64,3,opt,name=refund_amount,json=refundAmount,proto3,oneof" json:"refund_amount,omitempty"`
	// pre pay status
	PrePayStatus *int32 `protobuf:"varint,4,opt,name=pre_pay_status,json=prePayStatus,proto3,oneof" json:"pre_pay_status,omitempty"`
	// pre pay rate
	PrePayRate *float64 `protobuf:"fixed64,5,opt,name=pre_pay_rate,json=prePayRate,proto3,oneof" json:"pre_pay_rate,omitempty"`
	// pre auth enable
	PreAuthEnable *bool `protobuf:"varint,6,opt,name=pre_auth_enable,json=preAuthEnable,proto3,oneof" json:"pre_auth_enable,omitempty"`
}

func (x *PayBookingRequestView) Reset() {
	*x = PayBookingRequestView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayBookingRequestView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayBookingRequestView) ProtoMessage() {}

func (x *PayBookingRequestView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayBookingRequestView.ProtoReflect.Descriptor instead.
func (*PayBookingRequestView) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{28}
}

func (x *PayBookingRequestView) GetPaidAmount() float64 {
	if x != nil && x.PaidAmount != nil {
		return *x.PaidAmount
	}
	return 0
}

func (x *PayBookingRequestView) GetPrePayAmount() float64 {
	if x != nil && x.PrePayAmount != nil {
		return *x.PrePayAmount
	}
	return 0
}

func (x *PayBookingRequestView) GetRefundAmount() float64 {
	if x != nil && x.RefundAmount != nil {
		return *x.RefundAmount
	}
	return 0
}

func (x *PayBookingRequestView) GetPrePayStatus() int32 {
	if x != nil && x.PrePayStatus != nil {
		return *x.PrePayStatus
	}
	return 0
}

func (x *PayBookingRequestView) GetPrePayRate() float64 {
	if x != nil && x.PrePayRate != nil {
		return *x.PrePayRate
	}
	return 0
}

func (x *PayBookingRequestView) GetPreAuthEnable() bool {
	if x != nil && x.PreAuthEnable != nil {
		return *x.PreAuthEnable
	}
	return false
}

// Address detail
type AddressDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address id
	AddressId int32 `protobuf:"varint,1,opt,name=address_id,json=addressId,proto3" json:"address_id,omitempty"`
	// address 1
	Address1 string `protobuf:"bytes,2,opt,name=address1,proto3" json:"address1,omitempty"`
	// address 2
	Address2 string `protobuf:"bytes,3,opt,name=address2,proto3" json:"address2,omitempty"`
	// city
	City string `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	// state
	State string `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	// zip code
	Zipcode string `protobuf:"bytes,6,opt,name=zipcode,proto3" json:"zipcode,omitempty"`
	// country
	Country string `protobuf:"bytes,7,opt,name=country,proto3" json:"country,omitempty"`
	// latitude
	Lat string `protobuf:"bytes,8,opt,name=lat,proto3" json:"lat,omitempty"`
	// longitude
	Lng string `protobuf:"bytes,9,opt,name=lng,proto3" json:"lng,omitempty"`
	// boolean
	IsPrimary bool `protobuf:"varint,10,opt,name=is_primary,json=isPrimary,proto3" json:"is_primary,omitempty"`
	// is profile request address
	IsProfileRequestAddress *bool `protobuf:"varint,11,opt,name=is_profile_request_address,json=isProfileRequestAddress,proto3,oneof" json:"is_profile_request_address,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,12,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *AddressDetail) Reset() {
	*x = AddressDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressDetail) ProtoMessage() {}

func (x *AddressDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressDetail.ProtoReflect.Descriptor instead.
func (*AddressDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{29}
}

func (x *AddressDetail) GetAddressId() int32 {
	if x != nil {
		return x.AddressId
	}
	return 0
}

func (x *AddressDetail) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *AddressDetail) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *AddressDetail) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *AddressDetail) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *AddressDetail) GetZipcode() string {
	if x != nil {
		return x.Zipcode
	}
	return ""
}

func (x *AddressDetail) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *AddressDetail) GetLat() string {
	if x != nil {
		return x.Lat
	}
	return ""
}

func (x *AddressDetail) GetLng() string {
	if x != nil {
		return x.Lng
	}
	return ""
}

func (x *AddressDetail) GetIsPrimary() bool {
	if x != nil {
		return x.IsPrimary
	}
	return false
}

func (x *AddressDetail) GetIsProfileRequestAddress() bool {
	if x != nil && x.IsProfileRequestAddress != nil {
		return *x.IsProfileRequestAddress
	}
	return false
}

func (x *AddressDetail) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// question answer detail
type QuestionAnswerDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// question key
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// question
	Question string `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	// answer
	Answer string `protobuf:"bytes,3,opt,name=answer,proto3" json:"answer,omitempty"`
}

func (x *QuestionAnswerDetail) Reset() {
	*x = QuestionAnswerDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionAnswerDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionAnswerDetail) ProtoMessage() {}

func (x *QuestionAnswerDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionAnswerDetail.ProtoReflect.Descriptor instead.
func (*QuestionAnswerDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{30}
}

func (x *QuestionAnswerDetail) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *QuestionAnswerDetail) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *QuestionAnswerDetail) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

// grooming auto assign detail
type GroomingAutoAssignDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment id
	AppointmentId int32 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// staff id
	StaffId int32 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// appointment time
	AppointmentTime int32 `protobuf:"varint,4,opt,name=appointment_time,json=appointmentTime,proto3" json:"appointment_time,omitempty"`
}

func (x *GroomingAutoAssignDetail) Reset() {
	*x = GroomingAutoAssignDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingAutoAssignDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingAutoAssignDetail) ProtoMessage() {}

func (x *GroomingAutoAssignDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingAutoAssignDetail.ProtoReflect.Descriptor instead.
func (*GroomingAutoAssignDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{31}
}

func (x *GroomingAutoAssignDetail) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroomingAutoAssignDetail) GetAppointmentId() int32 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GroomingAutoAssignDetail) GetStaffId() int32 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GroomingAutoAssignDetail) GetAppointmentTime() int32 {
	if x != nil {
		return x.AppointmentTime
	}
	return 0
}

// boarding auto assign detail
type BoardingAutoAssignDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of booking request
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// boardingServiceDetailId
	BoardingServiceDetailId int64 `protobuf:"varint,3,opt,name=boarding_service_detail_id,json=boardingServiceDetailId,proto3" json:"boarding_service_detail_id,omitempty"`
	// The id of lodging
	LodgingId int64 `protobuf:"varint,4,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
}

func (x *BoardingAutoAssignDetail) Reset() {
	*x = BoardingAutoAssignDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingAutoAssignDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingAutoAssignDetail) ProtoMessage() {}

func (x *BoardingAutoAssignDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingAutoAssignDetail.ProtoReflect.Descriptor instead.
func (*BoardingAutoAssignDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{32}
}

func (x *BoardingAutoAssignDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BoardingAutoAssignDetail) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *BoardingAutoAssignDetail) GetBoardingServiceDetailId() int64 {
	if x != nil {
		return x.BoardingServiceDetailId
	}
	return 0
}

func (x *BoardingAutoAssignDetail) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

// assign require
type AssignRequire struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// room
	//
	// Deprecated: Do not use.
	RoomAssignRequires []*RoomAssignRequire `protobuf:"bytes,6,rep,name=room_assign_requires,json=roomAssignRequires,proto3" json:"room_assign_requires,omitempty"`
	// staff
	StaffAssignRequire []*StaffAssignRequire `protobuf:"bytes,7,rep,name=staff_assign_require,json=staffAssignRequire,proto3" json:"staff_assign_require,omitempty"`
}

func (x *AssignRequire) Reset() {
	*x = AssignRequire{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssignRequire) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignRequire) ProtoMessage() {}

func (x *AssignRequire) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignRequire.ProtoReflect.Descriptor instead.
func (*AssignRequire) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{33}
}

// Deprecated: Do not use.
func (x *AssignRequire) GetRoomAssignRequires() []*RoomAssignRequire {
	if x != nil {
		return x.RoomAssignRequires
	}
	return nil
}

func (x *AssignRequire) GetStaffAssignRequire() []*StaffAssignRequire {
	if x != nil {
		return x.StaffAssignRequire
	}
	return nil
}

// assign require
type IncompleteDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 还需要分配 staff/time 的 grooming services
	GroomingServices []*IncompleteDetails_GroomingService `protobuf:"bytes,1,rep,name=grooming_services,json=groomingServices,proto3" json:"grooming_services,omitempty"`
	// 还需要分配 loading/evaluation 的 boarding services
	BoardingServices []*IncompleteDetails_BoardingService `protobuf:"bytes,2,rep,name=boarding_services,json=boardingServices,proto3" json:"boarding_services,omitempty"`
	// 还需要分配 evaluation 的 daycare services
	DaycareServices []*IncompleteDetails_DaycareService `protobuf:"bytes,3,rep,name=daycare_services,json=daycareServices,proto3" json:"daycare_services,omitempty"`
	// 还需要分配 staff 的 evaluation services
	EvaluationServices []*IncompleteDetails_EvaluationService `protobuf:"bytes,4,rep,name=evaluation_services,json=evaluationServices,proto3" json:"evaluation_services,omitempty"`
	// 还需要分配 staff/time 的 grooming addons
	GroomingAddons []*IncompleteDetails_GroomingAddon `protobuf:"bytes,5,rep,name=grooming_addons,json=groomingAddons,proto3" json:"grooming_addons,omitempty"`
	// 还需要分配 staff/time 的 boarding addons
	BoardingAddons []*IncompleteDetails_BoardingAddon `protobuf:"bytes,6,rep,name=boarding_addons,json=boardingAddons,proto3" json:"boarding_addons,omitempty"`
	// 还需要分配 staff/time 的 daycare addons
	DaycareAddons []*IncompleteDetails_DaycareAddon `protobuf:"bytes,7,rep,name=daycare_addons,json=daycareAddons,proto3" json:"daycare_addons,omitempty"`
}

func (x *IncompleteDetails) Reset() {
	*x = IncompleteDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncompleteDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncompleteDetails) ProtoMessage() {}

func (x *IncompleteDetails) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncompleteDetails.ProtoReflect.Descriptor instead.
func (*IncompleteDetails) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{34}
}

func (x *IncompleteDetails) GetGroomingServices() []*IncompleteDetails_GroomingService {
	if x != nil {
		return x.GroomingServices
	}
	return nil
}

func (x *IncompleteDetails) GetBoardingServices() []*IncompleteDetails_BoardingService {
	if x != nil {
		return x.BoardingServices
	}
	return nil
}

func (x *IncompleteDetails) GetDaycareServices() []*IncompleteDetails_DaycareService {
	if x != nil {
		return x.DaycareServices
	}
	return nil
}

func (x *IncompleteDetails) GetEvaluationServices() []*IncompleteDetails_EvaluationService {
	if x != nil {
		return x.EvaluationServices
	}
	return nil
}

func (x *IncompleteDetails) GetGroomingAddons() []*IncompleteDetails_GroomingAddon {
	if x != nil {
		return x.GroomingAddons
	}
	return nil
}

func (x *IncompleteDetails) GetBoardingAddons() []*IncompleteDetails_BoardingAddon {
	if x != nil {
		return x.BoardingAddons
	}
	return nil
}

func (x *IncompleteDetails) GetDaycareAddons() []*IncompleteDetails_DaycareAddon {
	if x != nil {
		return x.DaycareAddons
	}
	return nil
}

// pet detail
//
// Deprecated: Do not use.
type RoomAssignRequire struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// start date
	StartDate string `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *RoomAssignRequire) Reset() {
	*x = RoomAssignRequire{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomAssignRequire) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomAssignRequire) ProtoMessage() {}

func (x *RoomAssignRequire) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomAssignRequire.ProtoReflect.Descriptor instead.
func (*RoomAssignRequire) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{35}
}

func (x *RoomAssignRequire) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *RoomAssignRequire) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *RoomAssignRequire) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *RoomAssignRequire) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *RoomAssignRequire) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

// pet detail
type StaffAssignRequire struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,7,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// specific dates, indicates that staff needs to be specified for multiple days
	SpecificDates []string `protobuf:"bytes,8,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
}

func (x *StaffAssignRequire) Reset() {
	*x = StaffAssignRequire{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffAssignRequire) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffAssignRequire) ProtoMessage() {}

func (x *StaffAssignRequire) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffAssignRequire.ProtoReflect.Descriptor instead.
func (*StaffAssignRequire) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{36}
}

func (x *StaffAssignRequire) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *StaffAssignRequire) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *StaffAssignRequire) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *StaffAssignRequire) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *StaffAssignRequire) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *StaffAssignRequire) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

// order booking request view
type OrderBookingRequestView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount code name
	DiscountCodeName string `protobuf:"bytes,1,opt,name=discount_code_name,json=discountCodeName,proto3" json:"discount_code_name,omitempty"`
}

func (x *OrderBookingRequestView) Reset() {
	*x = OrderBookingRequestView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderBookingRequestView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderBookingRequestView) ProtoMessage() {}

func (x *OrderBookingRequestView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderBookingRequestView.ProtoReflect.Descriptor instead.
func (*OrderBookingRequestView) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{37}
}

func (x *OrderBookingRequestView) GetDiscountCodeName() string {
	if x != nil {
		return x.DiscountCodeName
	}
	return ""
}

// Accept booking request request
type AcceptBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// assign lodging unit id
	PetToLodgings []*PetToLodging `protobuf:"bytes,2,rep,name=pet_to_lodgings,json=petToLodgings,proto3" json:"pet_to_lodgings,omitempty"`
	// assign staff
	PetToStaffs []*PetToStaff `protobuf:"bytes,3,rep,name=pet_to_staffs,json=petToStaffs,proto3" json:"pet_to_staffs,omitempty"`
	// assign service
	PetToServices []*PetToService `protobuf:"bytes,4,rep,name=pet_to_services,json=petToServices,proto3" json:"pet_to_services,omitempty"`
	// assign evaluation staff
	EvaluationPetToStaffs []*PetToStaff `protobuf:"bytes,5,rep,name=evaluation_pet_to_staffs,json=evaluationPetToStaffs,proto3" json:"evaluation_pet_to_staffs,omitempty"`
}

func (x *AcceptBookingRequestRequest) Reset() {
	*x = AcceptBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestRequest) ProtoMessage() {}

func (x *AcceptBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{38}
}

func (x *AcceptBookingRequestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestRequest) GetPetToLodgings() []*PetToLodging {
	if x != nil {
		return x.PetToLodgings
	}
	return nil
}

func (x *AcceptBookingRequestRequest) GetPetToStaffs() []*PetToStaff {
	if x != nil {
		return x.PetToStaffs
	}
	return nil
}

func (x *AcceptBookingRequestRequest) GetPetToServices() []*PetToService {
	if x != nil {
		return x.PetToServices
	}
	return nil
}

func (x *AcceptBookingRequestRequest) GetEvaluationPetToStaffs() []*PetToStaff {
	if x != nil {
		return x.EvaluationPetToStaffs
	}
	return nil
}

// Pet to lodging
type PetToLodging struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// lodging unit id
	LodgingUnitId int64 `protobuf:"varint,2,opt,name=lodging_unit_id,json=lodgingUnitId,proto3" json:"lodging_unit_id,omitempty"`
}

func (x *PetToLodging) Reset() {
	*x = PetToLodging{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetToLodging) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetToLodging) ProtoMessage() {}

func (x *PetToLodging) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetToLodging.ProtoReflect.Descriptor instead.
func (*PetToLodging) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{39}
}

func (x *PetToLodging) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetToLodging) GetLodgingUnitId() int64 {
	if x != nil {
		return x.LodgingUnitId
	}
	return 0
}

// Pet to staff
type PetToStaff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// start time
	StartTime int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
}

func (x *PetToStaff) Reset() {
	*x = PetToStaff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetToStaff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetToStaff) ProtoMessage() {}

func (x *PetToStaff) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetToStaff.ProtoReflect.Descriptor instead.
func (*PetToStaff) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{40}
}

func (x *PetToStaff) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetToStaff) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetToStaff) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *PetToStaff) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

// Pet to service
type PetToService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// old evaluation service id
	FromEvaluationServiceId *int64 `protobuf:"varint,2,opt,name=from_evaluation_service_id,json=fromEvaluationServiceId,proto3,oneof" json:"from_evaluation_service_id,omitempty"`
	// new evaluation service id
	ToEvaluationServiceId *int64 `protobuf:"varint,3,opt,name=to_evaluation_service_id,json=toEvaluationServiceId,proto3,oneof" json:"to_evaluation_service_id,omitempty"`
}

func (x *PetToService) Reset() {
	*x = PetToService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetToService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetToService) ProtoMessage() {}

func (x *PetToService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetToService.ProtoReflect.Descriptor instead.
func (*PetToService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{41}
}

func (x *PetToService) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetToService) GetFromEvaluationServiceId() int64 {
	if x != nil && x.FromEvaluationServiceId != nil {
		return *x.FromEvaluationServiceId
	}
	return 0
}

func (x *PetToService) GetToEvaluationServiceId() int64 {
	if x != nil && x.ToEvaluationServiceId != nil {
		return *x.ToEvaluationServiceId
	}
	return 0
}

// Auto assign request
type AutoAssignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	BookingRequestId int64 `protobuf:"varint,1,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
}

func (x *AutoAssignRequest) Reset() {
	*x = AutoAssignRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignRequest) ProtoMessage() {}

func (x *AutoAssignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignRequest.ProtoReflect.Descriptor instead.
func (*AutoAssignRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{42}
}

func (x *AutoAssignRequest) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

// Auto assign response
type AutoAssignResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding assign info require
	BoardingAssignRequires []*AutoAssignResponse_AssignRequire `protobuf:"bytes,1,rep,name=boarding_assign_requires,json=boardingAssignRequires,proto3" json:"boarding_assign_requires,omitempty"`
	// assign result. pet to lodging unit id
	PetToLodgings []*PetToLodging `protobuf:"bytes,2,rep,name=pet_to_lodgings,json=petToLodgings,proto3" json:"pet_to_lodgings,omitempty"`
	// evaluation assign info require
	EvaluationAssignRequires []*AutoAssignResponse_AssignRequire `protobuf:"bytes,3,rep,name=evaluation_assign_requires,json=evaluationAssignRequires,proto3" json:"evaluation_assign_requires,omitempty"`
	// assign result. pet to staff
	EvaluationPetToStaffs []*PetToStaff `protobuf:"bytes,4,rep,name=evaluation_pet_to_staffs,json=evaluationPetToStaffs,proto3" json:"evaluation_pet_to_staffs,omitempty"`
	// lodging details for auto assign
	Lodgings []*AutoAssignResponse_LodgingDetail `protobuf:"bytes,11,rep,name=lodgings,proto3" json:"lodgings,omitempty"`
	// staff details for auto assign
	Staffs []*AutoAssignResponse_StaffDetail `protobuf:"bytes,12,rep,name=staffs,proto3" json:"staffs,omitempty"`
}

func (x *AutoAssignResponse) Reset() {
	*x = AutoAssignResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignResponse) ProtoMessage() {}

func (x *AutoAssignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignResponse.ProtoReflect.Descriptor instead.
func (*AutoAssignResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{43}
}

func (x *AutoAssignResponse) GetBoardingAssignRequires() []*AutoAssignResponse_AssignRequire {
	if x != nil {
		return x.BoardingAssignRequires
	}
	return nil
}

func (x *AutoAssignResponse) GetPetToLodgings() []*PetToLodging {
	if x != nil {
		return x.PetToLodgings
	}
	return nil
}

func (x *AutoAssignResponse) GetEvaluationAssignRequires() []*AutoAssignResponse_AssignRequire {
	if x != nil {
		return x.EvaluationAssignRequires
	}
	return nil
}

func (x *AutoAssignResponse) GetEvaluationPetToStaffs() []*PetToStaff {
	if x != nil {
		return x.EvaluationPetToStaffs
	}
	return nil
}

func (x *AutoAssignResponse) GetLodgings() []*AutoAssignResponse_LodgingDetail {
	if x != nil {
		return x.Lodgings
	}
	return nil
}

func (x *AutoAssignResponse) GetStaffs() []*AutoAssignResponse_StaffDetail {
	if x != nil {
		return x.Staffs
	}
	return nil
}

// Accept booking request response
type AcceptBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// need to send notification appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *AcceptBookingRequestResponse) Reset() {
	*x = AcceptBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestResponse) ProtoMessage() {}

func (x *AcceptBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{44}
}

func (x *AcceptBookingRequestResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *AcceptBookingRequestResponse) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// Decline booking request request
type DeclineBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeclineBookingRequestRequest) Reset() {
	*x = DeclineBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeclineBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclineBookingRequestRequest) ProtoMessage() {}

func (x *DeclineBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclineBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*DeclineBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{45}
}

func (x *DeclineBookingRequestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Decline booking request response
type DeclineBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// need to send notification appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *DeclineBookingRequestResponse) Reset() {
	*x = DeclineBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeclineBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclineBookingRequestResponse) ProtoMessage() {}

func (x *DeclineBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclineBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*DeclineBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{46}
}

func (x *DeclineBookingRequestResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *DeclineBookingRequestResponse) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// AcceptBookingRequestV2 request
type AcceptBookingRequestV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// grooming services
	GroomingServices []*v16.AcceptBookingRequestV2Request_GroomingService `protobuf:"bytes,2,rep,name=grooming_services,json=groomingServices,proto3" json:"grooming_services,omitempty"`
	// boarding services
	BoardingServices []*v16.AcceptBookingRequestV2Request_BoardingService `protobuf:"bytes,3,rep,name=boarding_services,json=boardingServices,proto3" json:"boarding_services,omitempty"`
	// daycare services
	DaycareServices []*v16.AcceptBookingRequestV2Request_DaycareService `protobuf:"bytes,4,rep,name=daycare_services,json=daycareServices,proto3" json:"daycare_services,omitempty"`
	// evaluation services
	EvaluationServices []*v16.AcceptBookingRequestV2Request_EvaluationService `protobuf:"bytes,5,rep,name=evaluation_services,json=evaluationServices,proto3" json:"evaluation_services,omitempty"`
	// grooming addons
	GroomingAddons []*v16.AcceptBookingRequestV2Request_GroomingAddon `protobuf:"bytes,15,rep,name=grooming_addons,json=groomingAddons,proto3" json:"grooming_addons,omitempty"`
	// boarding addons
	BoardingAddons []*v16.AcceptBookingRequestV2Request_BoardingAddon `protobuf:"bytes,16,rep,name=boarding_addons,json=boardingAddons,proto3" json:"boarding_addons,omitempty"`
	// daycare addons
	DaycareAddons []*v16.AcceptBookingRequestV2Request_DaycareAddon `protobuf:"bytes,17,rep,name=daycare_addons,json=daycareAddons,proto3" json:"daycare_addons,omitempty"`
	// Create evaluation requests
	CreateEvaluationRequests []*v16.AcceptBookingRequestV2Request_CreateEvaluationRequest `protobuf:"bytes,18,rep,name=create_evaluation_requests,json=createEvaluationRequests,proto3" json:"create_evaluation_requests,omitempty"`
}

func (x *AcceptBookingRequestV2Request) Reset() {
	*x = AcceptBookingRequestV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Request) ProtoMessage() {}

func (x *AcceptBookingRequestV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Request.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Request) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{47}
}

func (x *AcceptBookingRequestV2Request) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestV2Request) GetGroomingServices() []*v16.AcceptBookingRequestV2Request_GroomingService {
	if x != nil {
		return x.GroomingServices
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetBoardingServices() []*v16.AcceptBookingRequestV2Request_BoardingService {
	if x != nil {
		return x.BoardingServices
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetDaycareServices() []*v16.AcceptBookingRequestV2Request_DaycareService {
	if x != nil {
		return x.DaycareServices
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetEvaluationServices() []*v16.AcceptBookingRequestV2Request_EvaluationService {
	if x != nil {
		return x.EvaluationServices
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetGroomingAddons() []*v16.AcceptBookingRequestV2Request_GroomingAddon {
	if x != nil {
		return x.GroomingAddons
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetBoardingAddons() []*v16.AcceptBookingRequestV2Request_BoardingAddon {
	if x != nil {
		return x.BoardingAddons
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetDaycareAddons() []*v16.AcceptBookingRequestV2Request_DaycareAddon {
	if x != nil {
		return x.DaycareAddons
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetCreateEvaluationRequests() []*v16.AcceptBookingRequestV2Request_CreateEvaluationRequest {
	if x != nil {
		return x.CreateEvaluationRequests
	}
	return nil
}

// AcceptBookingRequestV2 response
type AcceptBookingRequestV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *AcceptBookingRequestV2Response) Reset() {
	*x = AcceptBookingRequestV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Response) ProtoMessage() {}

func (x *AcceptBookingRequestV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Response.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Response) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{48}
}

func (x *AcceptBookingRequestV2Response) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *AcceptBookingRequestV2Response) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// AutoAssignV2 request
type AutoAssignV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	BookingRequestId int64 `protobuf:"varint,1,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
}

func (x *AutoAssignV2Request) Reset() {
	*x = AutoAssignV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignV2Request) ProtoMessage() {}

func (x *AutoAssignV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignV2Request.ProtoReflect.Descriptor instead.
func (*AutoAssignV2Request) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{49}
}

func (x *AutoAssignV2Request) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

// AutoAssignV2 response
type AutoAssignV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding services
	BoardingServices []*v16.AutoAssignResponse_BoardingService `protobuf:"bytes,1,rep,name=boarding_services,json=boardingServices,proto3" json:"boarding_services,omitempty"`
	// evaluation services
	EvaluationServices []*v16.AutoAssignResponse_EvaluationService `protobuf:"bytes,2,rep,name=evaluation_services,json=evaluationServices,proto3" json:"evaluation_services,omitempty"`
}

func (x *AutoAssignV2Response) Reset() {
	*x = AutoAssignV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignV2Response) ProtoMessage() {}

func (x *AutoAssignV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignV2Response.ProtoReflect.Descriptor instead.
func (*AutoAssignV2Response) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{50}
}

func (x *AutoAssignV2Response) GetBoardingServices() []*v16.AutoAssignResponse_BoardingService {
	if x != nil {
		return x.BoardingServices
	}
	return nil
}

func (x *AutoAssignV2Response) GetEvaluationServices() []*v16.AutoAssignResponse_EvaluationService {
	if x != nil {
		return x.EvaluationServices
	}
	return nil
}

// MoveBookingRequestToWaitlist params
type MoveBookingRequestToWaitlistParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The booking request id
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
}

func (x *MoveBookingRequestToWaitlistParams) Reset() {
	*x = MoveBookingRequestToWaitlistParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoveBookingRequestToWaitlistParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveBookingRequestToWaitlistParams) ProtoMessage() {}

func (x *MoveBookingRequestToWaitlistParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveBookingRequestToWaitlistParams.ProtoReflect.Descriptor instead.
func (*MoveBookingRequestToWaitlistParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{51}
}

func (x *MoveBookingRequestToWaitlistParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *MoveBookingRequestToWaitlistParams) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

// MoveBookingRequestToWaitlist result
type MoveBookingRequestToWaitlistResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MoveBookingRequestToWaitlistResult) Reset() {
	*x = MoveBookingRequestToWaitlistResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoveBookingRequestToWaitlistResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveBookingRequestToWaitlistResult) ProtoMessage() {}

func (x *MoveBookingRequestToWaitlistResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveBookingRequestToWaitlistResult.ProtoReflect.Descriptor instead.
func (*MoveBookingRequestToWaitlistResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{52}
}

// CRM-3555 customer emergency contact
type CustomerDetail_EmergencyContact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first name
	FirstName string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *CustomerDetail_EmergencyContact) Reset() {
	*x = CustomerDetail_EmergencyContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDetail_EmergencyContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDetail_EmergencyContact) ProtoMessage() {}

func (x *CustomerDetail_EmergencyContact) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDetail_EmergencyContact.ProtoReflect.Descriptor instead.
func (*CustomerDetail_EmergencyContact) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CustomerDetail_EmergencyContact) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CustomerDetail_EmergencyContact) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CustomerDetail_EmergencyContact) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// The booking request service
type ServiceDetail_Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the service type
	//
	// Types that are assignable to Service:
	//
	//	*ServiceDetail_Service_Grooming
	//	*ServiceDetail_Service_Boarding
	//	*ServiceDetail_Service_Daycare
	//	*ServiceDetail_Service_Evaluation
	//	*ServiceDetail_Service_DogWalking
	//	*ServiceDetail_Service_GroupClass
	Service isServiceDetail_Service_Service `protobuf_oneof:"service"`
	// service item type, different from service type, it includes grooming, boarding, daycare or other services.
	ServiceItemType v13.ServiceItemType `protobuf:"varint,5,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *ServiceDetail_Service) Reset() {
	*x = ServiceDetail_Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceDetail_Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDetail_Service) ProtoMessage() {}

func (x *ServiceDetail_Service) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDetail_Service.ProtoReflect.Descriptor instead.
func (*ServiceDetail_Service) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{5, 0}
}

func (m *ServiceDetail_Service) GetService() isServiceDetail_Service_Service {
	if m != nil {
		return m.Service
	}
	return nil
}

func (x *ServiceDetail_Service) GetGrooming() *GroomingService {
	if x, ok := x.GetService().(*ServiceDetail_Service_Grooming); ok {
		return x.Grooming
	}
	return nil
}

func (x *ServiceDetail_Service) GetBoarding() *BoardingService {
	if x, ok := x.GetService().(*ServiceDetail_Service_Boarding); ok {
		return x.Boarding
	}
	return nil
}

func (x *ServiceDetail_Service) GetDaycare() *DaycareService {
	if x, ok := x.GetService().(*ServiceDetail_Service_Daycare); ok {
		return x.Daycare
	}
	return nil
}

func (x *ServiceDetail_Service) GetEvaluation() *EvaluationService {
	if x, ok := x.GetService().(*ServiceDetail_Service_Evaluation); ok {
		return x.Evaluation
	}
	return nil
}

func (x *ServiceDetail_Service) GetDogWalking() *DogWalkingService {
	if x, ok := x.GetService().(*ServiceDetail_Service_DogWalking); ok {
		return x.DogWalking
	}
	return nil
}

func (x *ServiceDetail_Service) GetGroupClass() *GroupClassService {
	if x, ok := x.GetService().(*ServiceDetail_Service_GroupClass); ok {
		return x.GroupClass
	}
	return nil
}

func (x *ServiceDetail_Service) GetServiceItemType() v13.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v13.ServiceItemType(0)
}

type isServiceDetail_Service_Service interface {
	isServiceDetail_Service_Service()
}

type ServiceDetail_Service_Grooming struct {
	// grooming service
	Grooming *GroomingService `protobuf:"bytes,1,opt,name=grooming,proto3,oneof"`
}

type ServiceDetail_Service_Boarding struct {
	// boarding service
	Boarding *BoardingService `protobuf:"bytes,2,opt,name=boarding,proto3,oneof"`
}

type ServiceDetail_Service_Daycare struct {
	// daycare service
	Daycare *DaycareService `protobuf:"bytes,3,opt,name=daycare,proto3,oneof"`
}

type ServiceDetail_Service_Evaluation struct {
	// evaluation service
	Evaluation *EvaluationService `protobuf:"bytes,4,opt,name=evaluation,proto3,oneof"`
}

type ServiceDetail_Service_DogWalking struct {
	// dog walking service
	DogWalking *DogWalkingService `protobuf:"bytes,6,opt,name=dog_walking,json=dogWalking,proto3,oneof"`
}

type ServiceDetail_Service_GroupClass struct {
	// group class service
	GroupClass *GroupClassService `protobuf:"bytes,7,opt,name=group_class,json=groupClass,proto3,oneof"`
}

func (*ServiceDetail_Service_Grooming) isServiceDetail_Service_Service() {}

func (*ServiceDetail_Service_Boarding) isServiceDetail_Service_Service() {}

func (*ServiceDetail_Service_Daycare) isServiceDetail_Service_Service() {}

func (*ServiceDetail_Service_Evaluation) isServiceDetail_Service_Service() {}

func (*ServiceDetail_Service_DogWalking) isServiceDetail_Service_Service() {}

func (*ServiceDetail_Service_GroupClass) isServiceDetail_Service_Service() {}

// Grooming service
type IncompleteDetails_GroomingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming service detail
	ServiceDetail *v12.GroomingServiceDetailModel `protobuf:"bytes,1,opt,name=service_detail,json=serviceDetail,proto3" json:"service_detail,omitempty"`
	// service info
	Service *v13.ServiceBriefView `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *IncompleteDetails_GroomingService) Reset() {
	*x = IncompleteDetails_GroomingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncompleteDetails_GroomingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncompleteDetails_GroomingService) ProtoMessage() {}

func (x *IncompleteDetails_GroomingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncompleteDetails_GroomingService.ProtoReflect.Descriptor instead.
func (*IncompleteDetails_GroomingService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{34, 0}
}

func (x *IncompleteDetails_GroomingService) GetServiceDetail() *v12.GroomingServiceDetailModel {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *IncompleteDetails_GroomingService) GetService() *v13.ServiceBriefView {
	if x != nil {
		return x.Service
	}
	return nil
}

// Boarding service
type IncompleteDetails_BoardingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding service detail
	ServiceDetail *v12.BoardingServiceDetailModel `protobuf:"bytes,1,opt,name=service_detail,json=serviceDetail,proto3" json:"service_detail,omitempty"`
	// service info
	Service *v13.ServiceBriefView `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	// pet 对应 boarding service 所缺失的 evaluation
	MissingEvaluation *v13.EvaluationBriefView `protobuf:"bytes,3,opt,name=missing_evaluation,json=missingEvaluation,proto3,oneof" json:"missing_evaluation,omitempty"`
}

func (x *IncompleteDetails_BoardingService) Reset() {
	*x = IncompleteDetails_BoardingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncompleteDetails_BoardingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncompleteDetails_BoardingService) ProtoMessage() {}

func (x *IncompleteDetails_BoardingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncompleteDetails_BoardingService.ProtoReflect.Descriptor instead.
func (*IncompleteDetails_BoardingService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{34, 1}
}

func (x *IncompleteDetails_BoardingService) GetServiceDetail() *v12.BoardingServiceDetailModel {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *IncompleteDetails_BoardingService) GetService() *v13.ServiceBriefView {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *IncompleteDetails_BoardingService) GetMissingEvaluation() *v13.EvaluationBriefView {
	if x != nil {
		return x.MissingEvaluation
	}
	return nil
}

// Daycare service
type IncompleteDetails_DaycareService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// daycare service detail
	ServiceDetail *v12.DaycareServiceDetailModel `protobuf:"bytes,1,opt,name=service_detail,json=serviceDetail,proto3" json:"service_detail,omitempty"`
	// service info
	Service *v13.ServiceBriefView `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	// pet 对应 daycare service 所缺失的 evaluation
	MissingEvaluation *v13.EvaluationBriefView `protobuf:"bytes,3,opt,name=missing_evaluation,json=missingEvaluation,proto3,oneof" json:"missing_evaluation,omitempty"`
}

func (x *IncompleteDetails_DaycareService) Reset() {
	*x = IncompleteDetails_DaycareService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncompleteDetails_DaycareService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncompleteDetails_DaycareService) ProtoMessage() {}

func (x *IncompleteDetails_DaycareService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncompleteDetails_DaycareService.ProtoReflect.Descriptor instead.
func (*IncompleteDetails_DaycareService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{34, 2}
}

func (x *IncompleteDetails_DaycareService) GetServiceDetail() *v12.DaycareServiceDetailModel {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *IncompleteDetails_DaycareService) GetService() *v13.ServiceBriefView {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *IncompleteDetails_DaycareService) GetMissingEvaluation() *v13.EvaluationBriefView {
	if x != nil {
		return x.MissingEvaluation
	}
	return nil
}

// Evaluation service
type IncompleteDetails_EvaluationService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation service detail
	ServiceDetail *v12.EvaluationTestDetailModel `protobuf:"bytes,1,opt,name=service_detail,json=serviceDetail,proto3" json:"service_detail,omitempty"`
	// evaluation info
	Evaluation *v13.EvaluationBriefView `protobuf:"bytes,2,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
}

func (x *IncompleteDetails_EvaluationService) Reset() {
	*x = IncompleteDetails_EvaluationService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncompleteDetails_EvaluationService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncompleteDetails_EvaluationService) ProtoMessage() {}

func (x *IncompleteDetails_EvaluationService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncompleteDetails_EvaluationService.ProtoReflect.Descriptor instead.
func (*IncompleteDetails_EvaluationService) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{34, 3}
}

func (x *IncompleteDetails_EvaluationService) GetServiceDetail() *v12.EvaluationTestDetailModel {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *IncompleteDetails_EvaluationService) GetEvaluation() *v13.EvaluationBriefView {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

// Grooming addon
type IncompleteDetails_GroomingAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming addon detail
	AddonDetail *v12.GroomingAddOnDetailModel `protobuf:"bytes,1,opt,name=addon_detail,json=addonDetail,proto3" json:"addon_detail,omitempty"`
	// service info
	Service *v13.ServiceBriefView `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *IncompleteDetails_GroomingAddon) Reset() {
	*x = IncompleteDetails_GroomingAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncompleteDetails_GroomingAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncompleteDetails_GroomingAddon) ProtoMessage() {}

func (x *IncompleteDetails_GroomingAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncompleteDetails_GroomingAddon.ProtoReflect.Descriptor instead.
func (*IncompleteDetails_GroomingAddon) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{34, 4}
}

func (x *IncompleteDetails_GroomingAddon) GetAddonDetail() *v12.GroomingAddOnDetailModel {
	if x != nil {
		return x.AddonDetail
	}
	return nil
}

func (x *IncompleteDetails_GroomingAddon) GetService() *v13.ServiceBriefView {
	if x != nil {
		return x.Service
	}
	return nil
}

// Boarding addon
type IncompleteDetails_BoardingAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding addon detail
	AddonDetail *v12.BoardingAddOnDetailModel `protobuf:"bytes,1,opt,name=addon_detail,json=addonDetail,proto3" json:"addon_detail,omitempty"`
	// service info
	Service *v13.ServiceBriefView `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *IncompleteDetails_BoardingAddon) Reset() {
	*x = IncompleteDetails_BoardingAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncompleteDetails_BoardingAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncompleteDetails_BoardingAddon) ProtoMessage() {}

func (x *IncompleteDetails_BoardingAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncompleteDetails_BoardingAddon.ProtoReflect.Descriptor instead.
func (*IncompleteDetails_BoardingAddon) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{34, 5}
}

func (x *IncompleteDetails_BoardingAddon) GetAddonDetail() *v12.BoardingAddOnDetailModel {
	if x != nil {
		return x.AddonDetail
	}
	return nil
}

func (x *IncompleteDetails_BoardingAddon) GetService() *v13.ServiceBriefView {
	if x != nil {
		return x.Service
	}
	return nil
}

// Daycare addon
type IncompleteDetails_DaycareAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// daycare addon detail
	AddonDetail *v12.DaycareAddOnDetailModel `protobuf:"bytes,1,opt,name=addon_detail,json=addonDetail,proto3" json:"addon_detail,omitempty"`
	// service info
	Service *v13.ServiceBriefView `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *IncompleteDetails_DaycareAddon) Reset() {
	*x = IncompleteDetails_DaycareAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncompleteDetails_DaycareAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncompleteDetails_DaycareAddon) ProtoMessage() {}

func (x *IncompleteDetails_DaycareAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncompleteDetails_DaycareAddon.ProtoReflect.Descriptor instead.
func (*IncompleteDetails_DaycareAddon) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{34, 6}
}

func (x *IncompleteDetails_DaycareAddon) GetAddonDetail() *v12.DaycareAddOnDetailModel {
	if x != nil {
		return x.AddonDetail
	}
	return nil
}

func (x *IncompleteDetails_DaycareAddon) GetService() *v13.ServiceBriefView {
	if x != nil {
		return x.Service
	}
	return nil
}

// lodging assign info require
type AutoAssignResponse_AssignRequire struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// start date
	StartDate *string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	EndDate *string `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
}

func (x *AutoAssignResponse_AssignRequire) Reset() {
	*x = AutoAssignResponse_AssignRequire{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignResponse_AssignRequire) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignResponse_AssignRequire) ProtoMessage() {}

func (x *AutoAssignResponse_AssignRequire) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignResponse_AssignRequire.ProtoReflect.Descriptor instead.
func (*AutoAssignResponse_AssignRequire) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{43, 0}
}

func (x *AutoAssignResponse_AssignRequire) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *AutoAssignResponse_AssignRequire) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *AutoAssignResponse_AssignRequire) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *AutoAssignResponse_AssignRequire) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

// lodging detail for auto assigned
type AutoAssignResponse_LodgingDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging id
	LodgingId int64 `protobuf:"varint,1,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// lodging unit name
	LodgingUnitName string `protobuf:"bytes,2,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
	// lodging type name
	LodgingTypeName string `protobuf:"bytes,3,opt,name=lodging_type_name,json=lodgingTypeName,proto3" json:"lodging_type_name,omitempty"`
}

func (x *AutoAssignResponse_LodgingDetail) Reset() {
	*x = AutoAssignResponse_LodgingDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignResponse_LodgingDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignResponse_LodgingDetail) ProtoMessage() {}

func (x *AutoAssignResponse_LodgingDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignResponse_LodgingDetail.ProtoReflect.Descriptor instead.
func (*AutoAssignResponse_LodgingDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{43, 1}
}

func (x *AutoAssignResponse_LodgingDetail) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *AutoAssignResponse_LodgingDetail) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

func (x *AutoAssignResponse_LodgingDetail) GetLodgingTypeName() string {
	if x != nil {
		return x.LodgingTypeName
	}
	return ""
}

// staff detail for auto assign
type AutoAssignResponse_StaffDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *AutoAssignResponse_StaffDetail) Reset() {
	*x = AutoAssignResponse_StaffDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignResponse_StaffDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignResponse_StaffDetail) ProtoMessage() {}

func (x *AutoAssignResponse_StaffDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignResponse_StaffDetail.ProtoReflect.Descriptor instead.
func (*AutoAssignResponse_StaffDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{43, 2}
}

func (x *AutoAssignResponse_StaffDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AutoAssignResponse_StaffDetail) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *AutoAssignResponse_StaffDetail) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

var File_moego_api_online_booking_v1_booking_request_api_proto protoreflect.FileDescriptor

var file_moego_api_online_booking_v1_booking_request_api_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4a,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64,
	0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbf, 0x02, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x07,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73,
	0x12, 0x3e, 0x0a, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x00, 0x18, 0x01, 0x52, 0x13, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x73,
	0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x9a, 0x0b, 0x0a,
	0x0e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x79, 0x12, 0x23, 0x0a, 0x0d,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x05, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x61,
	0x79, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x6e,
	0x65, 0x77, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x12, 0x58, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0b, 0x6e, 0x65,
	0x77, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0a, 0x6e, 0x65, 0x77,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x5c, 0x0a, 0x10, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x73, 0x12, 0x2b, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x6c,
	0x65, 0x72, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x2d, 0x0a, 0x13, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x6f, 0x75, 0x74, 0x4f, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x72, 0x65,
	0x61, 0x12, 0x3a, 0x0a, 0x1a, 0x68, 0x61, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x68, 0x61, 0x73, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x69, 0x0a,
	0x11, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x10, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x63, 0x0a, 0x0e, 0x70, 0x69, 0x63, 0x6b,
	0x75, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x45, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x0d,
	0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x32, 0x0a,
	0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x48, 0x02, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x88, 0x01,
	0x01, 0x1a, 0x71, 0x0a, 0x10, 0x45, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x22, 0xcb, 0x01, 0x0a, 0x1d, 0x47, 0x65,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x15, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x13,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x33, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x84, 0x07, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x5a, 0x0a, 0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x0e, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x53, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x54, 0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x0a,
	0x03, 0x70, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x03,
	0x70, 0x61, 0x79, 0x12, 0x2c, 0x0a, 0x12, 0x68, 0x61, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x68, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x55, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x61, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x12, 0x76, 0x0a, 0x18, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x17, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x5a, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x12, 0x5d, 0x0a, 0x12,
	0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x66, 0x0a, 0x13, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x12, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x73, 0x22, 0xed, 0x05, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x45, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x09, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4e, 0x0a, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0xc4, 0x04, 0x0a,
	0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x12, 0x4a, 0x0a, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x47, 0x0a, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00,
	0x52, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x12, 0x50, 0x0a, 0x0a, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52,
	0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x51, 0x0a, 0x0b, 0x64,
	0x6f, 0x67, 0x5f, 0x77, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x48, 0x00, 0x52, 0x0a, 0x64, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x51,
	0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x22, 0xe2, 0x07, 0x0a, 0x14, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72,
	0x65, 0x70, 0x61, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x50,
	0x72, 0x65, 0x70, 0x61, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x65, 0x12,
	0x65, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6e, 0x6f, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x16, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x52, 0x0a, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x22, 0xde, 0x05, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x0e, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x53, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x54, 0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x0a,
	0x03, 0x70, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x03,
	0x70, 0x61, 0x79, 0x12, 0x2c, 0x0a, 0x12, 0x68, 0x61, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x68, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x44, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x55, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x0d, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x12, 0x4a,
	0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x5d, 0x0a, 0x12, 0x69, 0x6e,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x96, 0x02, 0x0a, 0x0f, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4c, 0x0a,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x06, 0x61,
	0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x61,
	0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x5b, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x88,
	0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x22, 0xb5, 0x05, 0x0a, 0x15, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5d, 0x0a, 0x13, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x70, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x63, 0x0a, 0x16, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a,
	0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xcb, 0x03, 0x0a, 0x13, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x09, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x61, 0x64, 0x64, 0x4f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd0, 0x04, 0x0a, 0x0f, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4c, 0x0a, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x06, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x51,
	0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x5f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x74,
	0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x02, 0x18,
	0x01, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x46, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4f, 0x0a, 0x0b, 0x6d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0b,
	0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x22, 0xd3, 0x04, 0x0a, 0x15,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0a, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61,
	0x74, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e,
	0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69,
	0x74, 0x22, 0xee, 0x04, 0x0a, 0x13, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64,
	0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x09, 0x61, 0x64,
	0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x4f, 0x6e, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x23, 0x0a,
	0x0b, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x72, 0x79, 0x64, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x76, 0x65, 0x72, 0x79, 0x64,
	0x61, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a,
	0x10, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61,
	0x79, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12,
	0x50, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x48, 0x00, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x22, 0xa2, 0x03, 0x0a, 0x0d, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x50,
	0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6f, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x6f, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x74, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x22, 0x84, 0x04, 0x0a, 0x10, 0x4d, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e,
	0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x27,
	0x0a, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x22, 0x0a,
	0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x88, 0x01,
	0x01, 0x12, 0x7a, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x0c, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xdc,
	0x03, 0x0a, 0x0e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x4b, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x47,
	0x0a, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x66, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x51, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4f, 0x0a, 0x0b,
	0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa7, 0x03,
	0x0a, 0x14, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd6, 0x03, 0x0a, 0x12, 0x44, 0x61, 0x79, 0x63,
	0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x61, 0x64, 0x64, 0x4f, 0x6e, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x72, 0x79, 0x64, 0x61,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x76, 0x65, 0x72, 0x79,
	0x64, 0x61, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x10, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64,
	0x61, 0x79, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x22, 0x60, 0x0a, 0x11, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4b, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x22, 0xb4, 0x03, 0x0a, 0x14, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x63, 0x0a, 0x11, 0x44, 0x6f, 0x67,
	0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4e,
	0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f,
	0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0xea,
	0x04, 0x0a, 0x17, 0x44, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5d, 0x0a, 0x13, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x11, 0x70, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x63, 0x0a, 0x16, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x63, 0x0a, 0x11, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x4e, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x22, 0xca, 0x04, 0x0a, 0x17, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x57, 0x0a, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x4f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x22, 0xfb, 0x08,
	0x0a, 0x09, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70,
	0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x72, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x69, 0x78, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x62, 0x72, 0x65, 0x65, 0x64, 0x4d, 0x69, 0x78, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x47, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72,
	0x12, 0x32, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61,
	0x79, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x61,
	0x77, 0x61, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x65,
	0x64, 0x41, 0x77, 0x61, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12,
	0x2f, 0x0a, 0x13, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x19, 0x0a, 0x08, 0x76, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x76,
	0x65, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x65, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x65, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x1e,
	0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x22,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x73, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x49, 0x73, 0x73, 0x75, 0x65, 0x73, 0x12, 0x57, 0x0a, 0x11, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x24, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x4a, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x25, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x65, 0x52, 0x08, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x10, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x18,
	0x26, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x12, 0x53, 0x0a, 0x0c, 0x70, 0x65, 0x74,
	0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x27, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65,
	0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x65, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x22, 0xff, 0x01, 0x0a, 0x13,
	0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x10, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64,
	0x12, 0x3f, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0e,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72,
	0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xac, 0x01,
	0x0a, 0x10, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f,
	0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22, 0xfe, 0x02, 0x0a,
	0x15, 0x50, 0x61, 0x79, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x70,
	0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e,
	0x70, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x50, 0x61, 0x79, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x48, 0x02,
	0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x29, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x48, 0x03, 0x52, 0x0c, 0x70, 0x72, 0x65,
	0x50, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0c,
	0x70, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x04, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x50, 0x61, 0x79, 0x52, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x0d,
	0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x61,
	0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x72, 0x65,
	0x5f, 0x70, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x72,
	0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x89, 0x03,
	0x0a, 0x0d, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73,
	0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x40, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x17, 0x69,
	0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x69,
	0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x5c, 0x0a, 0x14, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x22, 0x97, 0x01, 0x0a, 0x18, 0x47, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0xb4, 0x01, 0x0a, 0x18, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x75,
	0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x1a,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x17, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x22, 0xd8, 0x01, 0x0a, 0x0d, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x12, 0x64, 0x0a, 0x14, 0x72, 0x6f,
	0x6f, 0x6d, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12, 0x72, 0x6f,
	0x6f, 0x6d, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73,
	0x12, 0x61, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x52,
	0x12, 0x73, 0x74, 0x61, 0x66, 0x66, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x22, 0x89, 0x12, 0x0a, 0x11, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6b, 0x0a, 0x11, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x10, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x6b, 0x0a, 0x11, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x10, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x12, 0x68, 0x0a, 0x10, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x44, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0f, 0x64, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x71, 0x0a,
	0x13, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x12, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x65, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64,
	0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x0e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x65, 0x0a, 0x0f, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x49,
	0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x0e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x62,
	0x0a, 0x0e, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64,
	0x64, 0x6f, 0x6e, 0x52, 0x0d, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x6f,
	0x6e, 0x73, 0x1a, 0xba, 0x01, 0x0a, 0x0f, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x61, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x0a, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a,
	0xb4, 0x02, 0x0a, 0x0f, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x61, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x61, 0x0a, 0x12,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72,
	0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x11, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42,
	0x15, 0x0a, 0x13, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xb2, 0x02, 0x0a, 0x0e, 0x44, 0x61, 0x79, 0x63, 0x61,
	0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x60, 0x0a, 0x0e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x0a, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x61, 0x0a, 0x12, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x11,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xc4, 0x01, 0x0a, 0x11,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x60, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x4d, 0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0xb2, 0x01, 0x0a, 0x0d, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41,
	0x64, 0x64, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x44, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0xb2, 0x01, 0x0a, 0x0d, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x0c, 0x61, 0x64, 0x64,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0xb0, 0x01, 0x0a,
	0x0c, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x12, 0x5a, 0x0a,
	0x0c, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x4f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x0a, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22,
	0xa2, 0x01, 0x0a, 0x11, 0x52, 0x6f, 0x6f, 0x6d, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x3a, 0x02, 0x18, 0x01, 0x22, 0xcb, 0x01, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x70,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74,
	0x65, 0x73, 0x22, 0x47, 0x0a, 0x17, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x2c, 0x0a,
	0x12, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x8b, 0x03, 0x0a, 0x1b,
	0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x51, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54,
	0x6f, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x54, 0x6f, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4b, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x74,
	0x6f, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x54, 0x6f, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x73, 0x12, 0x51, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54,
	0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x54, 0x6f, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x60, 0x0a, 0x18, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x52, 0x15, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x65,
	0x74, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0x5f, 0x0a, 0x0c, 0x50, 0x65, 0x74,
	0x54, 0x6f, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x22, 0xa3, 0x01, 0x0a, 0x0a, 0x50,
	0x65, 0x74, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05,
	0x10, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0xfc, 0x01, 0x0a, 0x0c, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x49, 0x0a, 0x1a, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x17, 0x66, 0x72, 0x6f, 0x6d, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x18,
	0x74, 0x6f, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x15, 0x74, 0x6f, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x74, 0x6f, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x22,
	0x4a, 0x0a, 0x11, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xfb, 0x07, 0x0a, 0x12,
	0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x77, 0x0a, 0x18, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x52, 0x16, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x12, 0x51, 0x0a, 0x0f, 0x70,
	0x65, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x52,
	0x0d, 0x70, 0x65, 0x74, 0x54, 0x6f, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x7b,
	0x0a, 0x1a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x52, 0x18, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x12, 0x60, 0x0a, 0x18, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54,
	0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x15, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x59, 0x0a,
	0x08, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75,
	0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x08,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x53, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x1a, 0xa5, 0x01,
	0x0a, 0x0d, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x1a, 0x86, 0x01, 0x0a, 0x0d, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x59,
	0x0a, 0x0b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5d, 0x0a, 0x1c, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x37, 0x0a, 0x1c, 0x44, 0x65, 0x63, 0x6c,
	0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x5e, 0x0a, 0x1d, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0xa9, 0x08, 0x0a, 0x1d, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x7b, 0x0a, 0x11,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x10, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x7b, 0x0a, 0x11, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x10, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x78, 0x0a, 0x10, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72,
	0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x0f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x81, 0x01, 0x0a, 0x13, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x12, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x12, 0x75, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x0e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x75, 0x0a, 0x0f, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x10,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x6f, 0x6e, 0x52, 0x0e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f,
	0x6e, 0x73, 0x12, 0x72, 0x0a, 0x0e, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61,
	0x72, 0x65, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x0d, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65,
	0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x94, 0x01, 0x0a, 0x1a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x18, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x22, 0x5f, 0x0a,
	0x1e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x4c,
	0x0a, 0x13, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x56, 0x32, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x80, 0x02, 0x0a,
	0x14, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x56, 0x32, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x11, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x10, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x76, 0x0a, 0x13, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x12, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22,
	0x85, 0x01, 0x0a, 0x22, 0x4d, 0x6f, 0x76, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x24, 0x0a, 0x22, 0x4d, 0x6f, 0x76, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x57,
	0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xf1, 0x08,
	0x0a, 0x15, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a,
	0x0a, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02,
	0x01, 0x12, 0x73, 0x0a, 0x0c, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x56,
	0x32, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x56, 0x32, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x14, 0x41, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x91, 0x01, 0x0a, 0x16, 0x41, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x32, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01,
	0x0a, 0x15, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa0,
	0x01, 0x0a, 0x1c, 0x4d, 0x6f, 0x76, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x12,
	0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f,
	0x76, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x54, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x6f, 0x76, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x54, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x8c, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_online_booking_v1_booking_request_api_proto_rawDescOnce sync.Once
	file_moego_api_online_booking_v1_booking_request_api_proto_rawDescData = file_moego_api_online_booking_v1_booking_request_api_proto_rawDesc
)

func file_moego_api_online_booking_v1_booking_request_api_proto_rawDescGZIP() []byte {
	file_moego_api_online_booking_v1_booking_request_api_proto_rawDescOnce.Do(func() {
		file_moego_api_online_booking_v1_booking_request_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_online_booking_v1_booking_request_api_proto_rawDescData)
	})
	return file_moego_api_online_booking_v1_booking_request_api_proto_rawDescData
}

var file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes = make([]protoimpl.MessageInfo, 65)
var file_moego_api_online_booking_v1_booking_request_api_proto_goTypes = []interface{}{
	(*GetBookingRequestListRequest)(nil),                              // 0: moego.api.online_booking.v1.GetBookingRequestListRequest
	(*CustomerDetail)(nil),                                            // 1: moego.api.online_booking.v1.CustomerDetail
	(*GetBookingRequestListResponse)(nil),                             // 2: moego.api.online_booking.v1.GetBookingRequestListResponse
	(*GetBookingRequestRequest)(nil),                                  // 3: moego.api.online_booking.v1.GetBookingRequestRequest
	(*GetBookingRequestItem)(nil),                                     // 4: moego.api.online_booking.v1.GetBookingRequestItem
	(*ServiceDetail)(nil),                                             // 5: moego.api.online_booking.v1.ServiceDetail
	(*BookingRequestDetail)(nil),                                      // 6: moego.api.online_booking.v1.BookingRequestDetail
	(*GetBookingRequestResponse)(nil),                                 // 7: moego.api.online_booking.v1.GetBookingRequestResponse
	(*GroomingService)(nil),                                           // 8: moego.api.online_booking.v1.GroomingService
	(*GroomingServiceDetail)(nil),                                     // 9: moego.api.online_booking.v1.GroomingServiceDetail
	(*GroomingAddOnDetail)(nil),                                       // 10: moego.api.online_booking.v1.GroomingAddOnDetail
	(*BoardingService)(nil),                                           // 11: moego.api.online_booking.v1.BoardingService
	(*BoardingServiceDetail)(nil),                                     // 12: moego.api.online_booking.v1.BoardingServiceDetail
	(*BoardingAddOnDetail)(nil),                                       // 13: moego.api.online_booking.v1.BoardingAddOnDetail
	(*FeedingDetail)(nil),                                             // 14: moego.api.online_booking.v1.FeedingDetail
	(*MedicationDetail)(nil),                                          // 15: moego.api.online_booking.v1.MedicationDetail
	(*DaycareService)(nil),                                            // 16: moego.api.online_booking.v1.DaycareService
	(*DaycareServiceDetail)(nil),                                      // 17: moego.api.online_booking.v1.DaycareServiceDetail
	(*DaycareAddOnDetail)(nil),                                        // 18: moego.api.online_booking.v1.DaycareAddOnDetail
	(*EvaluationService)(nil),                                         // 19: moego.api.online_booking.v1.EvaluationService
	(*EvaluationTestDetail)(nil),                                      // 20: moego.api.online_booking.v1.EvaluationTestDetail
	(*DogWalkingService)(nil),                                         // 21: moego.api.online_booking.v1.DogWalkingService
	(*DogWalkingServiceDetail)(nil),                                   // 22: moego.api.online_booking.v1.DogWalkingServiceDetail
	(*GroupClassService)(nil),                                         // 23: moego.api.online_booking.v1.GroupClassService
	(*GroupClassServiceDetail)(nil),                                   // 24: moego.api.online_booking.v1.GroupClassServiceDetail
	(*PetDetail)(nil),                                                 // 25: moego.api.online_booking.v1.PetDetail
	(*PetVaccineComposite)(nil),                                       // 26: moego.api.online_booking.v1.PetVaccineComposite
	(*PetCodeComposite)(nil),                                          // 27: moego.api.online_booking.v1.PetCodeComposite
	(*PayBookingRequestView)(nil),                                     // 28: moego.api.online_booking.v1.PayBookingRequestView
	(*AddressDetail)(nil),                                             // 29: moego.api.online_booking.v1.AddressDetail
	(*QuestionAnswerDetail)(nil),                                      // 30: moego.api.online_booking.v1.QuestionAnswerDetail
	(*GroomingAutoAssignDetail)(nil),                                  // 31: moego.api.online_booking.v1.GroomingAutoAssignDetail
	(*BoardingAutoAssignDetail)(nil),                                  // 32: moego.api.online_booking.v1.BoardingAutoAssignDetail
	(*AssignRequire)(nil),                                             // 33: moego.api.online_booking.v1.AssignRequire
	(*IncompleteDetails)(nil),                                         // 34: moego.api.online_booking.v1.IncompleteDetails
	(*RoomAssignRequire)(nil),                                         // 35: moego.api.online_booking.v1.RoomAssignRequire
	(*StaffAssignRequire)(nil),                                        // 36: moego.api.online_booking.v1.StaffAssignRequire
	(*OrderBookingRequestView)(nil),                                   // 37: moego.api.online_booking.v1.OrderBookingRequestView
	(*AcceptBookingRequestRequest)(nil),                               // 38: moego.api.online_booking.v1.AcceptBookingRequestRequest
	(*PetToLodging)(nil),                                              // 39: moego.api.online_booking.v1.PetToLodging
	(*PetToStaff)(nil),                                                // 40: moego.api.online_booking.v1.PetToStaff
	(*PetToService)(nil),                                              // 41: moego.api.online_booking.v1.PetToService
	(*AutoAssignRequest)(nil),                                         // 42: moego.api.online_booking.v1.AutoAssignRequest
	(*AutoAssignResponse)(nil),                                        // 43: moego.api.online_booking.v1.AutoAssignResponse
	(*AcceptBookingRequestResponse)(nil),                              // 44: moego.api.online_booking.v1.AcceptBookingRequestResponse
	(*DeclineBookingRequestRequest)(nil),                              // 45: moego.api.online_booking.v1.DeclineBookingRequestRequest
	(*DeclineBookingRequestResponse)(nil),                             // 46: moego.api.online_booking.v1.DeclineBookingRequestResponse
	(*AcceptBookingRequestV2Request)(nil),                             // 47: moego.api.online_booking.v1.AcceptBookingRequestV2Request
	(*AcceptBookingRequestV2Response)(nil),                            // 48: moego.api.online_booking.v1.AcceptBookingRequestV2Response
	(*AutoAssignV2Request)(nil),                                       // 49: moego.api.online_booking.v1.AutoAssignV2Request
	(*AutoAssignV2Response)(nil),                                      // 50: moego.api.online_booking.v1.AutoAssignV2Response
	(*MoveBookingRequestToWaitlistParams)(nil),                        // 51: moego.api.online_booking.v1.MoveBookingRequestToWaitlistParams
	(*MoveBookingRequestToWaitlistResult)(nil),                        // 52: moego.api.online_booking.v1.MoveBookingRequestToWaitlistResult
	(*CustomerDetail_EmergencyContact)(nil),                           // 53: moego.api.online_booking.v1.CustomerDetail.EmergencyContact
	(*ServiceDetail_Service)(nil),                                     // 54: moego.api.online_booking.v1.ServiceDetail.Service
	(*IncompleteDetails_GroomingService)(nil),                         // 55: moego.api.online_booking.v1.IncompleteDetails.GroomingService
	(*IncompleteDetails_BoardingService)(nil),                         // 56: moego.api.online_booking.v1.IncompleteDetails.BoardingService
	(*IncompleteDetails_DaycareService)(nil),                          // 57: moego.api.online_booking.v1.IncompleteDetails.DaycareService
	(*IncompleteDetails_EvaluationService)(nil),                       // 58: moego.api.online_booking.v1.IncompleteDetails.EvaluationService
	(*IncompleteDetails_GroomingAddon)(nil),                           // 59: moego.api.online_booking.v1.IncompleteDetails.GroomingAddon
	(*IncompleteDetails_BoardingAddon)(nil),                           // 60: moego.api.online_booking.v1.IncompleteDetails.BoardingAddon
	(*IncompleteDetails_DaycareAddon)(nil),                            // 61: moego.api.online_booking.v1.IncompleteDetails.DaycareAddon
	(*AutoAssignResponse_AssignRequire)(nil),                          // 62: moego.api.online_booking.v1.AutoAssignResponse.AssignRequire
	(*AutoAssignResponse_LodgingDetail)(nil),                          // 63: moego.api.online_booking.v1.AutoAssignResponse.LodgingDetail
	(*AutoAssignResponse_StaffDetail)(nil),                            // 64: moego.api.online_booking.v1.AutoAssignResponse.StaffDetail
	(*v2.PaginationRequest)(nil),                                      // 65: moego.utils.v2.PaginationRequest
	(*v2.OrderBy)(nil),                                                // 66: moego.utils.v2.OrderBy
	(*date.Date)(nil),                                                 // 67: google.type.Date
	(*v2.PaginationResponse)(nil),                                     // 68: moego.utils.v2.PaginationResponse
	(*v1.MembershipSubscriptionListModel)(nil),                        // 69: moego.models.membership.v1.MembershipSubscriptionListModel
	(*v11.CustomerPackageView)(nil),                                   // 70: moego.api.appointment.v1.CustomerPackageView
	(*v1.MembershipModelPublicView)(nil),                              // 71: moego.models.membership.v1.MembershipModelPublicView
	(v12.BookingRequestStatus)(0),                                     // 72: moego.models.online_booking.v1.BookingRequestStatus
	(v12.BookingRequestSourcePlatform)(0),                             // 73: moego.models.online_booking.v1.BookingRequestSourcePlatform
	(*timestamppb.Timestamp)(nil),                                     // 74: google.protobuf.Timestamp
	(v13.ServiceItemType)(0),                                          // 75: moego.models.offering.v1.ServiceItemType
	(v12.BookingRequestModel_Source)(0),                               // 76: moego.models.online_booking.v1.BookingRequestModel.Source
	(v13.ServiceOverrideType)(0),                                      // 77: moego.models.offering.v1.ServiceOverrideType
	(v14.PetDetailDateType)(0),                                        // 78: moego.models.appointment.v1.PetDetailDateType
	(v13.ServicePriceUnit)(0),                                         // 79: moego.models.offering.v1.ServicePriceUnit
	(*v12.FeedingModel_FeedingSchedule)(nil),                          // 80: moego.models.online_booking.v1.FeedingModel.FeedingSchedule
	(*v12.MedicationModel_MedicationSchedule)(nil),                    // 81: moego.models.online_booking.v1.MedicationModel.MedicationSchedule
	(*v14.AppointmentPetMedicationScheduleDef_SelectedDateDef)(nil),   // 82: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
	(*v13.GroupClassInstance_Occurrence)(nil),                         // 83: moego.models.offering.v1.GroupClassInstance.Occurrence
	(v15.PetType)(0),                                                  // 84: moego.models.customer.v1.PetType
	(v15.PetGender)(0),                                                // 85: moego.models.customer.v1.PetGender
	(v15.EvaluationStatus)(0),                                         // 86: moego.models.customer.v1.EvaluationStatus
	(*v16.AcceptBookingRequestV2Request_GroomingService)(nil),         // 87: moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingService
	(*v16.AcceptBookingRequestV2Request_BoardingService)(nil),         // 88: moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingService
	(*v16.AcceptBookingRequestV2Request_DaycareService)(nil),          // 89: moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareService
	(*v16.AcceptBookingRequestV2Request_EvaluationService)(nil),       // 90: moego.service.online_booking.v1.AcceptBookingRequestV2Request.EvaluationService
	(*v16.AcceptBookingRequestV2Request_GroomingAddon)(nil),           // 91: moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingAddon
	(*v16.AcceptBookingRequestV2Request_BoardingAddon)(nil),           // 92: moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingAddon
	(*v16.AcceptBookingRequestV2Request_DaycareAddon)(nil),            // 93: moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareAddon
	(*v16.AcceptBookingRequestV2Request_CreateEvaluationRequest)(nil), // 94: moego.service.online_booking.v1.AcceptBookingRequestV2Request.CreateEvaluationRequest
	(*v16.AutoAssignResponse_BoardingService)(nil),                    // 95: moego.service.online_booking.v1.AutoAssignResponse.BoardingService
	(*v16.AutoAssignResponse_EvaluationService)(nil),                  // 96: moego.service.online_booking.v1.AutoAssignResponse.EvaluationService
	(*v12.GroomingServiceDetailModel)(nil),                            // 97: moego.models.online_booking.v1.GroomingServiceDetailModel
	(*v13.ServiceBriefView)(nil),                                      // 98: moego.models.offering.v1.ServiceBriefView
	(*v12.BoardingServiceDetailModel)(nil),                            // 99: moego.models.online_booking.v1.BoardingServiceDetailModel
	(*v13.EvaluationBriefView)(nil),                                   // 100: moego.models.offering.v1.EvaluationBriefView
	(*v12.DaycareServiceDetailModel)(nil),                             // 101: moego.models.online_booking.v1.DaycareServiceDetailModel
	(*v12.EvaluationTestDetailModel)(nil),                             // 102: moego.models.online_booking.v1.EvaluationTestDetailModel
	(*v12.GroomingAddOnDetailModel)(nil),                              // 103: moego.models.online_booking.v1.GroomingAddOnDetailModel
	(*v12.BoardingAddOnDetailModel)(nil),                              // 104: moego.models.online_booking.v1.BoardingAddOnDetailModel
	(*v12.DaycareAddOnDetailModel)(nil),                               // 105: moego.models.online_booking.v1.DaycareAddOnDetailModel
}
var file_moego_api_online_booking_v1_booking_request_api_proto_depIdxs = []int32{
	65,  // 0: moego.api.online_booking.v1.GetBookingRequestListRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	66,  // 1: moego.api.online_booking.v1.GetBookingRequestListRequest.order_bys:type_name -> moego.utils.v2.OrderBy
	29,  // 2: moego.api.online_booking.v1.CustomerDetail.primary_address:type_name -> moego.api.online_booking.v1.AddressDetail
	29,  // 3: moego.api.online_booking.v1.CustomerDetail.new_address:type_name -> moego.api.online_booking.v1.AddressDetail
	30,  // 4: moego.api.online_booking.v1.CustomerDetail.question_answers:type_name -> moego.api.online_booking.v1.QuestionAnswerDetail
	53,  // 5: moego.api.online_booking.v1.CustomerDetail.emergency_contact:type_name -> moego.api.online_booking.v1.CustomerDetail.EmergencyContact
	53,  // 6: moego.api.online_booking.v1.CustomerDetail.pickup_contact:type_name -> moego.api.online_booking.v1.CustomerDetail.EmergencyContact
	67,  // 7: moego.api.online_booking.v1.CustomerDetail.birthday:type_name -> google.type.Date
	4,   // 8: moego.api.online_booking.v1.GetBookingRequestListResponse.booking_request_items:type_name -> moego.api.online_booking.v1.GetBookingRequestItem
	68,  // 9: moego.api.online_booking.v1.GetBookingRequestListResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	6,   // 10: moego.api.online_booking.v1.GetBookingRequestItem.booking_request:type_name -> moego.api.online_booking.v1.BookingRequestDetail
	5,   // 11: moego.api.online_booking.v1.GetBookingRequestItem.service_details:type_name -> moego.api.online_booking.v1.ServiceDetail
	1,   // 12: moego.api.online_booking.v1.GetBookingRequestItem.customer_detail:type_name -> moego.api.online_booking.v1.CustomerDetail
	28,  // 13: moego.api.online_booking.v1.GetBookingRequestItem.pay:type_name -> moego.api.online_booking.v1.PayBookingRequestView
	33,  // 14: moego.api.online_booking.v1.GetBookingRequestItem.assign_require:type_name -> moego.api.online_booking.v1.AssignRequire
	69,  // 15: moego.api.online_booking.v1.GetBookingRequestItem.membership_subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionListModel
	70,  // 16: moego.api.online_booking.v1.GetBookingRequestItem.customer_packages:type_name -> moego.api.appointment.v1.CustomerPackageView
	34,  // 17: moego.api.online_booking.v1.GetBookingRequestItem.incomplete_details:type_name -> moego.api.online_booking.v1.IncompleteDetails
	71,  // 18: moego.api.online_booking.v1.GetBookingRequestItem.related_memberships:type_name -> moego.models.membership.v1.MembershipModelPublicView
	25,  // 19: moego.api.online_booking.v1.ServiceDetail.pet_detail:type_name -> moego.api.online_booking.v1.PetDetail
	54,  // 20: moego.api.online_booking.v1.ServiceDetail.services:type_name -> moego.api.online_booking.v1.ServiceDetail.Service
	72,  // 21: moego.api.online_booking.v1.BookingRequestDetail.status:type_name -> moego.models.online_booking.v1.BookingRequestStatus
	73,  // 22: moego.api.online_booking.v1.BookingRequestDetail.source_platform:type_name -> moego.models.online_booking.v1.BookingRequestSourcePlatform
	74,  // 23: moego.api.online_booking.v1.BookingRequestDetail.created_at:type_name -> google.protobuf.Timestamp
	75,  // 24: moego.api.online_booking.v1.BookingRequestDetail.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	76,  // 25: moego.api.online_booking.v1.BookingRequestDetail.source:type_name -> moego.models.online_booking.v1.BookingRequestModel.Source
	6,   // 26: moego.api.online_booking.v1.GetBookingRequestResponse.booking_request:type_name -> moego.api.online_booking.v1.BookingRequestDetail
	5,   // 27: moego.api.online_booking.v1.GetBookingRequestResponse.service_details:type_name -> moego.api.online_booking.v1.ServiceDetail
	1,   // 28: moego.api.online_booking.v1.GetBookingRequestResponse.customer_detail:type_name -> moego.api.online_booking.v1.CustomerDetail
	28,  // 29: moego.api.online_booking.v1.GetBookingRequestResponse.pay:type_name -> moego.api.online_booking.v1.PayBookingRequestView
	29,  // 30: moego.api.online_booking.v1.GetBookingRequestResponse.address:type_name -> moego.api.online_booking.v1.AddressDetail
	33,  // 31: moego.api.online_booking.v1.GetBookingRequestResponse.assign_require:type_name -> moego.api.online_booking.v1.AssignRequire
	37,  // 32: moego.api.online_booking.v1.GetBookingRequestResponse.order:type_name -> moego.api.online_booking.v1.OrderBookingRequestView
	34,  // 33: moego.api.online_booking.v1.GetBookingRequestResponse.incomplete_details:type_name -> moego.api.online_booking.v1.IncompleteDetails
	9,   // 34: moego.api.online_booking.v1.GroomingService.service:type_name -> moego.api.online_booking.v1.GroomingServiceDetail
	10,  // 35: moego.api.online_booking.v1.GroomingService.addons:type_name -> moego.api.online_booking.v1.GroomingAddOnDetail
	31,  // 36: moego.api.online_booking.v1.GroomingService.auto_assign:type_name -> moego.api.online_booking.v1.GroomingAutoAssignDetail
	77,  // 37: moego.api.online_booking.v1.GroomingServiceDetail.price_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	77,  // 38: moego.api.online_booking.v1.GroomingServiceDetail.duration_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	78,  // 39: moego.api.online_booking.v1.GroomingServiceDetail.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	12,  // 40: moego.api.online_booking.v1.BoardingService.service:type_name -> moego.api.online_booking.v1.BoardingServiceDetail
	13,  // 41: moego.api.online_booking.v1.BoardingService.addons:type_name -> moego.api.online_booking.v1.BoardingAddOnDetail
	14,  // 42: moego.api.online_booking.v1.BoardingService.feeding:type_name -> moego.api.online_booking.v1.FeedingDetail
	15,  // 43: moego.api.online_booking.v1.BoardingService.medication:type_name -> moego.api.online_booking.v1.MedicationDetail
	32,  // 44: moego.api.online_booking.v1.BoardingService.auto_assign:type_name -> moego.api.online_booking.v1.BoardingAutoAssignDetail
	14,  // 45: moego.api.online_booking.v1.BoardingService.feedings:type_name -> moego.api.online_booking.v1.FeedingDetail
	15,  // 46: moego.api.online_booking.v1.BoardingService.medications:type_name -> moego.api.online_booking.v1.MedicationDetail
	79,  // 47: moego.api.online_booking.v1.BoardingServiceDetail.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	78,  // 48: moego.api.online_booking.v1.BoardingAddOnDetail.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	80,  // 49: moego.api.online_booking.v1.FeedingDetail.time:type_name -> moego.models.online_booking.v1.FeedingModel.FeedingSchedule
	81,  // 50: moego.api.online_booking.v1.MedicationDetail.time:type_name -> moego.models.online_booking.v1.MedicationModel.MedicationSchedule
	82,  // 51: moego.api.online_booking.v1.MedicationDetail.selected_date:type_name -> moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
	17,  // 52: moego.api.online_booking.v1.DaycareService.service:type_name -> moego.api.online_booking.v1.DaycareServiceDetail
	18,  // 53: moego.api.online_booking.v1.DaycareService.addons:type_name -> moego.api.online_booking.v1.DaycareAddOnDetail
	14,  // 54: moego.api.online_booking.v1.DaycareService.feeding:type_name -> moego.api.online_booking.v1.FeedingDetail
	15,  // 55: moego.api.online_booking.v1.DaycareService.medication:type_name -> moego.api.online_booking.v1.MedicationDetail
	14,  // 56: moego.api.online_booking.v1.DaycareService.feedings:type_name -> moego.api.online_booking.v1.FeedingDetail
	15,  // 57: moego.api.online_booking.v1.DaycareService.medications:type_name -> moego.api.online_booking.v1.MedicationDetail
	20,  // 58: moego.api.online_booking.v1.EvaluationService.service:type_name -> moego.api.online_booking.v1.EvaluationTestDetail
	22,  // 59: moego.api.online_booking.v1.DogWalkingService.service:type_name -> moego.api.online_booking.v1.DogWalkingServiceDetail
	77,  // 60: moego.api.online_booking.v1.DogWalkingServiceDetail.price_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	77,  // 61: moego.api.online_booking.v1.DogWalkingServiceDetail.duration_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	24,  // 62: moego.api.online_booking.v1.GroupClassService.service:type_name -> moego.api.online_booking.v1.GroupClassServiceDetail
	83,  // 63: moego.api.online_booking.v1.GroupClassServiceDetail.occurrence:type_name -> moego.models.offering.v1.GroupClassInstance.Occurrence
	84,  // 64: moego.api.online_booking.v1.PetDetail.pet_type:type_name -> moego.models.customer.v1.PetType
	85,  // 65: moego.api.online_booking.v1.PetDetail.gender:type_name -> moego.models.customer.v1.PetGender
	67,  // 66: moego.api.online_booking.v1.PetDetail.birthday:type_name -> google.type.Date
	86,  // 67: moego.api.online_booking.v1.PetDetail.evaluation_status:type_name -> moego.models.customer.v1.EvaluationStatus
	27,  // 68: moego.api.online_booking.v1.PetDetail.pet_codes:type_name -> moego.api.online_booking.v1.PetCodeComposite
	30,  // 69: moego.api.online_booking.v1.PetDetail.question_answers:type_name -> moego.api.online_booking.v1.QuestionAnswerDetail
	26,  // 70: moego.api.online_booking.v1.PetDetail.pet_vaccines:type_name -> moego.api.online_booking.v1.PetVaccineComposite
	67,  // 71: moego.api.online_booking.v1.PetVaccineComposite.expiration_date:type_name -> google.type.Date
	35,  // 72: moego.api.online_booking.v1.AssignRequire.room_assign_requires:type_name -> moego.api.online_booking.v1.RoomAssignRequire
	36,  // 73: moego.api.online_booking.v1.AssignRequire.staff_assign_require:type_name -> moego.api.online_booking.v1.StaffAssignRequire
	55,  // 74: moego.api.online_booking.v1.IncompleteDetails.grooming_services:type_name -> moego.api.online_booking.v1.IncompleteDetails.GroomingService
	56,  // 75: moego.api.online_booking.v1.IncompleteDetails.boarding_services:type_name -> moego.api.online_booking.v1.IncompleteDetails.BoardingService
	57,  // 76: moego.api.online_booking.v1.IncompleteDetails.daycare_services:type_name -> moego.api.online_booking.v1.IncompleteDetails.DaycareService
	58,  // 77: moego.api.online_booking.v1.IncompleteDetails.evaluation_services:type_name -> moego.api.online_booking.v1.IncompleteDetails.EvaluationService
	59,  // 78: moego.api.online_booking.v1.IncompleteDetails.grooming_addons:type_name -> moego.api.online_booking.v1.IncompleteDetails.GroomingAddon
	60,  // 79: moego.api.online_booking.v1.IncompleteDetails.boarding_addons:type_name -> moego.api.online_booking.v1.IncompleteDetails.BoardingAddon
	61,  // 80: moego.api.online_booking.v1.IncompleteDetails.daycare_addons:type_name -> moego.api.online_booking.v1.IncompleteDetails.DaycareAddon
	39,  // 81: moego.api.online_booking.v1.AcceptBookingRequestRequest.pet_to_lodgings:type_name -> moego.api.online_booking.v1.PetToLodging
	40,  // 82: moego.api.online_booking.v1.AcceptBookingRequestRequest.pet_to_staffs:type_name -> moego.api.online_booking.v1.PetToStaff
	41,  // 83: moego.api.online_booking.v1.AcceptBookingRequestRequest.pet_to_services:type_name -> moego.api.online_booking.v1.PetToService
	40,  // 84: moego.api.online_booking.v1.AcceptBookingRequestRequest.evaluation_pet_to_staffs:type_name -> moego.api.online_booking.v1.PetToStaff
	62,  // 85: moego.api.online_booking.v1.AutoAssignResponse.boarding_assign_requires:type_name -> moego.api.online_booking.v1.AutoAssignResponse.AssignRequire
	39,  // 86: moego.api.online_booking.v1.AutoAssignResponse.pet_to_lodgings:type_name -> moego.api.online_booking.v1.PetToLodging
	62,  // 87: moego.api.online_booking.v1.AutoAssignResponse.evaluation_assign_requires:type_name -> moego.api.online_booking.v1.AutoAssignResponse.AssignRequire
	40,  // 88: moego.api.online_booking.v1.AutoAssignResponse.evaluation_pet_to_staffs:type_name -> moego.api.online_booking.v1.PetToStaff
	63,  // 89: moego.api.online_booking.v1.AutoAssignResponse.lodgings:type_name -> moego.api.online_booking.v1.AutoAssignResponse.LodgingDetail
	64,  // 90: moego.api.online_booking.v1.AutoAssignResponse.staffs:type_name -> moego.api.online_booking.v1.AutoAssignResponse.StaffDetail
	87,  // 91: moego.api.online_booking.v1.AcceptBookingRequestV2Request.grooming_services:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingService
	88,  // 92: moego.api.online_booking.v1.AcceptBookingRequestV2Request.boarding_services:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingService
	89,  // 93: moego.api.online_booking.v1.AcceptBookingRequestV2Request.daycare_services:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareService
	90,  // 94: moego.api.online_booking.v1.AcceptBookingRequestV2Request.evaluation_services:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.EvaluationService
	91,  // 95: moego.api.online_booking.v1.AcceptBookingRequestV2Request.grooming_addons:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingAddon
	92,  // 96: moego.api.online_booking.v1.AcceptBookingRequestV2Request.boarding_addons:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingAddon
	93,  // 97: moego.api.online_booking.v1.AcceptBookingRequestV2Request.daycare_addons:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareAddon
	94,  // 98: moego.api.online_booking.v1.AcceptBookingRequestV2Request.create_evaluation_requests:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.CreateEvaluationRequest
	95,  // 99: moego.api.online_booking.v1.AutoAssignV2Response.boarding_services:type_name -> moego.service.online_booking.v1.AutoAssignResponse.BoardingService
	96,  // 100: moego.api.online_booking.v1.AutoAssignV2Response.evaluation_services:type_name -> moego.service.online_booking.v1.AutoAssignResponse.EvaluationService
	8,   // 101: moego.api.online_booking.v1.ServiceDetail.Service.grooming:type_name -> moego.api.online_booking.v1.GroomingService
	11,  // 102: moego.api.online_booking.v1.ServiceDetail.Service.boarding:type_name -> moego.api.online_booking.v1.BoardingService
	16,  // 103: moego.api.online_booking.v1.ServiceDetail.Service.daycare:type_name -> moego.api.online_booking.v1.DaycareService
	19,  // 104: moego.api.online_booking.v1.ServiceDetail.Service.evaluation:type_name -> moego.api.online_booking.v1.EvaluationService
	21,  // 105: moego.api.online_booking.v1.ServiceDetail.Service.dog_walking:type_name -> moego.api.online_booking.v1.DogWalkingService
	23,  // 106: moego.api.online_booking.v1.ServiceDetail.Service.group_class:type_name -> moego.api.online_booking.v1.GroupClassService
	75,  // 107: moego.api.online_booking.v1.ServiceDetail.Service.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	97,  // 108: moego.api.online_booking.v1.IncompleteDetails.GroomingService.service_detail:type_name -> moego.models.online_booking.v1.GroomingServiceDetailModel
	98,  // 109: moego.api.online_booking.v1.IncompleteDetails.GroomingService.service:type_name -> moego.models.offering.v1.ServiceBriefView
	99,  // 110: moego.api.online_booking.v1.IncompleteDetails.BoardingService.service_detail:type_name -> moego.models.online_booking.v1.BoardingServiceDetailModel
	98,  // 111: moego.api.online_booking.v1.IncompleteDetails.BoardingService.service:type_name -> moego.models.offering.v1.ServiceBriefView
	100, // 112: moego.api.online_booking.v1.IncompleteDetails.BoardingService.missing_evaluation:type_name -> moego.models.offering.v1.EvaluationBriefView
	101, // 113: moego.api.online_booking.v1.IncompleteDetails.DaycareService.service_detail:type_name -> moego.models.online_booking.v1.DaycareServiceDetailModel
	98,  // 114: moego.api.online_booking.v1.IncompleteDetails.DaycareService.service:type_name -> moego.models.offering.v1.ServiceBriefView
	100, // 115: moego.api.online_booking.v1.IncompleteDetails.DaycareService.missing_evaluation:type_name -> moego.models.offering.v1.EvaluationBriefView
	102, // 116: moego.api.online_booking.v1.IncompleteDetails.EvaluationService.service_detail:type_name -> moego.models.online_booking.v1.EvaluationTestDetailModel
	100, // 117: moego.api.online_booking.v1.IncompleteDetails.EvaluationService.evaluation:type_name -> moego.models.offering.v1.EvaluationBriefView
	103, // 118: moego.api.online_booking.v1.IncompleteDetails.GroomingAddon.addon_detail:type_name -> moego.models.online_booking.v1.GroomingAddOnDetailModel
	98,  // 119: moego.api.online_booking.v1.IncompleteDetails.GroomingAddon.service:type_name -> moego.models.offering.v1.ServiceBriefView
	104, // 120: moego.api.online_booking.v1.IncompleteDetails.BoardingAddon.addon_detail:type_name -> moego.models.online_booking.v1.BoardingAddOnDetailModel
	98,  // 121: moego.api.online_booking.v1.IncompleteDetails.BoardingAddon.service:type_name -> moego.models.offering.v1.ServiceBriefView
	105, // 122: moego.api.online_booking.v1.IncompleteDetails.DaycareAddon.addon_detail:type_name -> moego.models.online_booking.v1.DaycareAddOnDetailModel
	98,  // 123: moego.api.online_booking.v1.IncompleteDetails.DaycareAddon.service:type_name -> moego.models.offering.v1.ServiceBriefView
	0,   // 124: moego.api.online_booking.v1.BookingRequestService.GetBookingRequestList:input_type -> moego.api.online_booking.v1.GetBookingRequestListRequest
	3,   // 125: moego.api.online_booking.v1.BookingRequestService.GetBookingRequest:input_type -> moego.api.online_booking.v1.GetBookingRequestRequest
	42,  // 126: moego.api.online_booking.v1.BookingRequestService.AutoAssign:input_type -> moego.api.online_booking.v1.AutoAssignRequest
	49,  // 127: moego.api.online_booking.v1.BookingRequestService.AutoAssignV2:input_type -> moego.api.online_booking.v1.AutoAssignV2Request
	38,  // 128: moego.api.online_booking.v1.BookingRequestService.AcceptBookingRequest:input_type -> moego.api.online_booking.v1.AcceptBookingRequestRequest
	47,  // 129: moego.api.online_booking.v1.BookingRequestService.AcceptBookingRequestV2:input_type -> moego.api.online_booking.v1.AcceptBookingRequestV2Request
	45,  // 130: moego.api.online_booking.v1.BookingRequestService.DeclineBookingRequest:input_type -> moego.api.online_booking.v1.DeclineBookingRequestRequest
	51,  // 131: moego.api.online_booking.v1.BookingRequestService.MoveBookingRequestToWaitlist:input_type -> moego.api.online_booking.v1.MoveBookingRequestToWaitlistParams
	2,   // 132: moego.api.online_booking.v1.BookingRequestService.GetBookingRequestList:output_type -> moego.api.online_booking.v1.GetBookingRequestListResponse
	7,   // 133: moego.api.online_booking.v1.BookingRequestService.GetBookingRequest:output_type -> moego.api.online_booking.v1.GetBookingRequestResponse
	43,  // 134: moego.api.online_booking.v1.BookingRequestService.AutoAssign:output_type -> moego.api.online_booking.v1.AutoAssignResponse
	50,  // 135: moego.api.online_booking.v1.BookingRequestService.AutoAssignV2:output_type -> moego.api.online_booking.v1.AutoAssignV2Response
	44,  // 136: moego.api.online_booking.v1.BookingRequestService.AcceptBookingRequest:output_type -> moego.api.online_booking.v1.AcceptBookingRequestResponse
	48,  // 137: moego.api.online_booking.v1.BookingRequestService.AcceptBookingRequestV2:output_type -> moego.api.online_booking.v1.AcceptBookingRequestV2Response
	46,  // 138: moego.api.online_booking.v1.BookingRequestService.DeclineBookingRequest:output_type -> moego.api.online_booking.v1.DeclineBookingRequestResponse
	52,  // 139: moego.api.online_booking.v1.BookingRequestService.MoveBookingRequestToWaitlist:output_type -> moego.api.online_booking.v1.MoveBookingRequestToWaitlistResult
	132, // [132:140] is the sub-list for method output_type
	124, // [124:132] is the sub-list for method input_type
	124, // [124:124] is the sub-list for extension type_name
	124, // [124:124] is the sub-list for extension extendee
	0,   // [0:124] is the sub-list for field type_name
}

func init() { file_moego_api_online_booking_v1_booking_request_api_proto_init() }
func file_moego_api_online_booking_v1_booking_request_api_proto_init() {
	if File_moego_api_online_booking_v1_booking_request_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingRequestListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingRequestListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingRequestItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingRequestDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingAddOnDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingAddOnDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedingDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MedicationDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareAddOnDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationTestDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DogWalkingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DogWalkingServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetVaccineComposite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetCodeComposite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayBookingRequestView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionAnswerDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingAutoAssignDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingAutoAssignDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssignRequire); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncompleteDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomAssignRequire); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffAssignRequire); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderBookingRequestView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetToLodging); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetToStaff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetToService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeclineBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeclineBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoveBookingRequestToWaitlistParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoveBookingRequestToWaitlistResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDetail_EmergencyContact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceDetail_Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncompleteDetails_GroomingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncompleteDetails_BoardingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncompleteDetails_DaycareService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncompleteDetails_EvaluationService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncompleteDetails_GroomingAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncompleteDetails_BoardingAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncompleteDetails_DaycareAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignResponse_AssignRequire); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignResponse_LodgingDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignResponse_StaffDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[25].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[26].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[28].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[29].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[41].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[54].OneofWrappers = []interface{}{
		(*ServiceDetail_Service_Grooming)(nil),
		(*ServiceDetail_Service_Boarding)(nil),
		(*ServiceDetail_Service_Daycare)(nil),
		(*ServiceDetail_Service_Evaluation)(nil),
		(*ServiceDetail_Service_DogWalking)(nil),
		(*ServiceDetail_Service_GroupClass)(nil),
	}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[56].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[57].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes[62].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_online_booking_v1_booking_request_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   65,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_online_booking_v1_booking_request_api_proto_goTypes,
		DependencyIndexes: file_moego_api_online_booking_v1_booking_request_api_proto_depIdxs,
		MessageInfos:      file_moego_api_online_booking_v1_booking_request_api_proto_msgTypes,
	}.Build()
	File_moego_api_online_booking_v1_booking_request_api_proto = out.File
	file_moego_api_online_booking_v1_booking_request_api_proto_rawDesc = nil
	file_moego_api_online_booking_v1_booking_request_api_proto_goTypes = nil
	file_moego_api_online_booking_v1_booking_request_api_proto_depIdxs = nil
}
