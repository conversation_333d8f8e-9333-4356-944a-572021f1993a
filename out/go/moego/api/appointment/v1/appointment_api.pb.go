// @since 2024-01-15 15:02:36
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/appointment_api.proto

package appointmentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v2"
	v16 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v15 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	v14 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create a appointment params
type CreateAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Appointment params
	Appointment *v1.AppointmentCreateDef `protobuf:"bytes,2,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// Selected pet and services
	PetDetails []*v1.PetDetailDef `protobuf:"bytes,3,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// Pre-auth
	// Simply request a card on file, and we'll pre-authorize the ticket amount 24 hours before the appointment.
	// Final payment will be automatically charged at the end of the service day.
	PreAuth *v11.PreAuthEnableDef `protobuf:"bytes,4,opt,name=pre_auth,json=preAuth,proto3,oneof" json:"pre_auth,omitempty"`
	// Appointment notes, contains ticket comments and alert notes
	// Ticket comments: For this appointment. Private to business only
	// Alert notes: It will be shown as a red exclamation mark on the appointment card. Private for business.
	Notes []*v1.AppointmentNoteCreateDef `protobuf:"bytes,5,rep,name=notes,proto3" json:"notes,omitempty"`
	// Pet belongings
	PetBelongings []*CreateAppointmentParams_PetBelonging `protobuf:"bytes,6,rep,name=pet_belongings,json=petBelongings,proto3" json:"pet_belongings,omitempty"`
	// created by, if not set, use current staff id, used for data migration from other systems
	CreatedBy *int64 `protobuf:"varint,7,opt,name=created_by,json=createdBy,proto3,oneof" json:"created_by,omitempty"`
	// created at, if not set, use current time, used for data migration from other systems
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3,oneof" json:"created_at,omitempty"`
}

func (x *CreateAppointmentParams) Reset() {
	*x = CreateAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppointmentParams) ProtoMessage() {}

func (x *CreateAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppointmentParams.ProtoReflect.Descriptor instead.
func (*CreateAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateAppointmentParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateAppointmentParams) GetAppointment() *v1.AppointmentCreateDef {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *CreateAppointmentParams) GetPetDetails() []*v1.PetDetailDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CreateAppointmentParams) GetPreAuth() *v11.PreAuthEnableDef {
	if x != nil {
		return x.PreAuth
	}
	return nil
}

func (x *CreateAppointmentParams) GetNotes() []*v1.AppointmentNoteCreateDef {
	if x != nil {
		return x.Notes
	}
	return nil
}

func (x *CreateAppointmentParams) GetPetBelongings() []*CreateAppointmentParams_PetBelonging {
	if x != nil {
		return x.PetBelongings
	}
	return nil
}

func (x *CreateAppointmentParams) GetCreatedBy() int64 {
	if x != nil && x.CreatedBy != nil {
		return *x.CreatedBy
	}
	return 0
}

func (x *CreateAppointmentParams) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// Create a appointment result
type CreateAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *CreateAppointmentResult) Reset() {
	*x = CreateAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppointmentResult) ProtoMessage() {}

func (x *CreateAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppointmentResult.ProtoReflect.Descriptor instead.
func (*CreateAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAppointmentResult) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// Update a appointment params, contains appointment date and pet details
type UpdateAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// update params
	Appointment *v1.AppointmentUpdateDef `protobuf:"bytes,2,opt,name=appointment,proto3" json:"appointment,omitempty"`
}

func (x *UpdateAppointmentParams) Reset() {
	*x = UpdateAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentParams) ProtoMessage() {}

func (x *UpdateAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentParams.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateAppointmentParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpdateAppointmentParams) GetAppointment() *v1.AppointmentUpdateDef {
	if x != nil {
		return x.Appointment
	}
	return nil
}

// Update a appointment result
type UpdateAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateAppointmentResult) Reset() {
	*x = UpdateAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentResult) ProtoMessage() {}

func (x *UpdateAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentResult.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{3}
}

// Get appointment params
type GetAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetAppointmentParams) Reset() {
	*x = GetAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentParams) ProtoMessage() {}

func (x *GetAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentParams.ProtoReflect.Descriptor instead.
func (*GetAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetAppointmentParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// Get appointment result
type GetAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment detail
	Appointment *v1.AppointmentCalendarView `protobuf:"bytes,1,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// pet and services
	ServiceDetail []*ServiceDetail `protobuf:"bytes,2,rep,name=service_detail,json=serviceDetail,proto3" json:"service_detail,omitempty"`
	// notes
	Notes []*v1.AppointmentNoteModel `protobuf:"bytes,3,rep,name=notes,proto3" json:"notes,omitempty"`
	// customer
	Customer *CustomerComposite `protobuf:"bytes,4,opt,name=customer,proto3" json:"customer,omitempty"`
	// invoice, Please use orders instead.
	//
	// Deprecated: Do not use.
	Invoice *v12.InvoiceCalendarView `protobuf:"bytes,5,opt,name=invoice,proto3" json:"invoice,omitempty"`
	// no show invoice, Please use orders instead.
	//
	// Deprecated: Do not use.
	NoShowInvoice *v12.NoShowInvoiceCalendarView `protobuf:"bytes,6,opt,name=no_show_invoice,json=noShowInvoice,proto3" json:"no_show_invoice,omitempty"`
	// pre pay
	PrePay *v11.PrePayCalendarView `protobuf:"bytes,7,opt,name=pre_pay,json=prePay,proto3" json:"pre_pay,omitempty"`
	// pre auth
	PreAuth *v11.PreAuthCalendarView `protobuf:"bytes,8,opt,name=pre_auth,json=preAuth,proto3" json:"pre_auth,omitempty"`
	// auto assign
	AutoAssign *v1.AutoAssignCalendarView `protobuf:"bytes,9,opt,name=auto_assign,json=autoAssign,proto3" json:"auto_assign,omitempty"`
	// wait list
	WaitList *v1.WaitListCalendarView `protobuf:"bytes,10,opt,name=wait_list,json=waitList,proto3" json:"wait_list,omitempty"`
	// staff
	Staffs []*v13.StaffModel `protobuf:"bytes,11,rep,name=staffs,proto3" json:"staffs,omitempty"`
	// service type list
	ServiceItemTypes []v14.ServiceItemType `protobuf:"varint,12,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// memberships
	MembershipSubscriptions *v15.MembershipSubscriptionListModel `protobuf:"bytes,13,opt,name=membership_subscriptions,json=membershipSubscriptions,proto3" json:"membership_subscriptions,omitempty"`
	// pricing rule apply log list, deprecated, use pricing_rule_apply_logs_v2 instead
	//
	// Deprecated: Do not use.
	PricingRuleApplyLogs []*v1.PricingRuleApplyLogDrawerView `protobuf:"bytes,14,rep,name=pricing_rule_apply_logs,json=pricingRuleApplyLogs,proto3" json:"pricing_rule_apply_logs,omitempty"`
	// pricing rule apply log list
	PricingRuleApplyLogsV2 []*v2.PricingRuleApplyLogDrawerView `protobuf:"bytes,15,rep,name=pricing_rule_apply_logs_v2,json=pricingRuleApplyLogsV2,proto3" json:"pricing_rule_apply_logs_v2,omitempty"`
	// Order list, might be empty if the appointment not charged yet.
	Orders []*v12.OrderModelAppointmentView `protobuf:"bytes,16,rep,name=orders,proto3" json:"orders,omitempty"`
	// Payment summary
	PaymentSummary *PaymentSummary `protobuf:"bytes,17,opt,name=payment_summary,json=paymentSummary,proto3" json:"payment_summary,omitempty"`
}

func (x *GetAppointmentResult) Reset() {
	*x = GetAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentResult) ProtoMessage() {}

func (x *GetAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentResult.ProtoReflect.Descriptor instead.
func (*GetAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetAppointmentResult) GetAppointment() *v1.AppointmentCalendarView {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *GetAppointmentResult) GetServiceDetail() []*ServiceDetail {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *GetAppointmentResult) GetNotes() []*v1.AppointmentNoteModel {
	if x != nil {
		return x.Notes
	}
	return nil
}

func (x *GetAppointmentResult) GetCustomer() *CustomerComposite {
	if x != nil {
		return x.Customer
	}
	return nil
}

// Deprecated: Do not use.
func (x *GetAppointmentResult) GetInvoice() *v12.InvoiceCalendarView {
	if x != nil {
		return x.Invoice
	}
	return nil
}

// Deprecated: Do not use.
func (x *GetAppointmentResult) GetNoShowInvoice() *v12.NoShowInvoiceCalendarView {
	if x != nil {
		return x.NoShowInvoice
	}
	return nil
}

func (x *GetAppointmentResult) GetPrePay() *v11.PrePayCalendarView {
	if x != nil {
		return x.PrePay
	}
	return nil
}

func (x *GetAppointmentResult) GetPreAuth() *v11.PreAuthCalendarView {
	if x != nil {
		return x.PreAuth
	}
	return nil
}

func (x *GetAppointmentResult) GetAutoAssign() *v1.AutoAssignCalendarView {
	if x != nil {
		return x.AutoAssign
	}
	return nil
}

func (x *GetAppointmentResult) GetWaitList() *v1.WaitListCalendarView {
	if x != nil {
		return x.WaitList
	}
	return nil
}

func (x *GetAppointmentResult) GetStaffs() []*v13.StaffModel {
	if x != nil {
		return x.Staffs
	}
	return nil
}

func (x *GetAppointmentResult) GetServiceItemTypes() []v14.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *GetAppointmentResult) GetMembershipSubscriptions() *v15.MembershipSubscriptionListModel {
	if x != nil {
		return x.MembershipSubscriptions
	}
	return nil
}

// Deprecated: Do not use.
func (x *GetAppointmentResult) GetPricingRuleApplyLogs() []*v1.PricingRuleApplyLogDrawerView {
	if x != nil {
		return x.PricingRuleApplyLogs
	}
	return nil
}

func (x *GetAppointmentResult) GetPricingRuleApplyLogsV2() []*v2.PricingRuleApplyLogDrawerView {
	if x != nil {
		return x.PricingRuleApplyLogsV2
	}
	return nil
}

func (x *GetAppointmentResult) GetOrders() []*v12.OrderModelAppointmentView {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *GetAppointmentResult) GetPaymentSummary() *PaymentSummary {
	if x != nil {
		return x.PaymentSummary
	}
	return nil
}

// Payment summary
type PaymentSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Service subtotal = sum(service total_price) + sum(service_charge price)
	SubtotalAmount *money.Money `protobuf:"bytes,1,opt,name=subtotal_amount,json=subtotalAmount,proto3" json:"subtotal_amount,omitempty"`
	// Payment collected = sum(order paid_amount)
	CollectedAmount *money.Money `protobuf:"bytes,2,opt,name=collected_amount,json=collectedAmount,proto3" json:"collected_amount,omitempty"`
	// deposit amount
	DepositAmount *money.Money `protobuf:"bytes,3,opt,name=deposit_amount,json=depositAmount,proto3" json:"deposit_amount,omitempty"`
	// Collected deposit amount = sum(deposit paid_amount)
	CollectedDepositAmount *money.Money `protobuf:"bytes,4,opt,name=collected_deposit_amount,json=collectedDepositAmount,proto3" json:"collected_deposit_amount,omitempty"`
	// Use store credit
	UseStoreCredit bool `protobuf:"varint,5,opt,name=use_store_credit,json=useStoreCredit,proto3" json:"use_store_credit,omitempty"`
	// Collected tips amount = sum(order tips_amount)
	CollectedTipsAmount *money.Money `protobuf:"bytes,6,opt,name=collected_tips_amount,json=collectedTipsAmount,proto3" json:"collected_tips_amount,omitempty"`
}

func (x *PaymentSummary) Reset() {
	*x = PaymentSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentSummary) ProtoMessage() {}

func (x *PaymentSummary) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentSummary.ProtoReflect.Descriptor instead.
func (*PaymentSummary) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{6}
}

func (x *PaymentSummary) GetSubtotalAmount() *money.Money {
	if x != nil {
		return x.SubtotalAmount
	}
	return nil
}

func (x *PaymentSummary) GetCollectedAmount() *money.Money {
	if x != nil {
		return x.CollectedAmount
	}
	return nil
}

func (x *PaymentSummary) GetDepositAmount() *money.Money {
	if x != nil {
		return x.DepositAmount
	}
	return nil
}

func (x *PaymentSummary) GetCollectedDepositAmount() *money.Money {
	if x != nil {
		return x.CollectedDepositAmount
	}
	return nil
}

func (x *PaymentSummary) GetUseStoreCredit() bool {
	if x != nil {
		return x.UseStoreCredit
	}
	return false
}

func (x *PaymentSummary) GetCollectedTipsAmount() *money.Money {
	if x != nil {
		return x.CollectedTipsAmount
	}
	return nil
}

// pet detail
type ServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet
	Pet *v16.BusinessCustomerPetModel `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// services
	Services []*ServiceComposite `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
	// add-ons
	AddOns []*AddOnComposite `protobuf:"bytes,3,rep,name=add_ons,json=addOns,proto3" json:"add_ons,omitempty"`
	// pet code
	PetCodes []*v16.BusinessPetCodeModel `protobuf:"bytes,4,rep,name=pet_codes,json=petCodes,proto3" json:"pet_codes,omitempty"`
	// vaccine
	Vaccines []*PetVaccineComposite `protobuf:"bytes,5,rep,name=vaccines,proto3" json:"vaccines,omitempty"`
	// evaluation
	Evaluations []*EvaluationServiceComposite `protobuf:"bytes,6,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
	// pet evaluation
	PetEvaluations []*v16.PetEvaluationModel `protobuf:"bytes,7,rep,name=pet_evaluations,json=petEvaluations,proto3" json:"pet_evaluations,omitempty"`
}

func (x *ServiceDetail) Reset() {
	*x = ServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDetail) ProtoMessage() {}

func (x *ServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDetail.ProtoReflect.Descriptor instead.
func (*ServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{7}
}

func (x *ServiceDetail) GetPet() *v16.BusinessCustomerPetModel {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *ServiceDetail) GetServices() []*ServiceComposite {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ServiceDetail) GetAddOns() []*AddOnComposite {
	if x != nil {
		return x.AddOns
	}
	return nil
}

func (x *ServiceDetail) GetPetCodes() []*v16.BusinessPetCodeModel {
	if x != nil {
		return x.PetCodes
	}
	return nil
}

func (x *ServiceDetail) GetVaccines() []*PetVaccineComposite {
	if x != nil {
		return x.Vaccines
	}
	return nil
}

func (x *ServiceDetail) GetEvaluations() []*EvaluationServiceComposite {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

func (x *ServiceDetail) GetPetEvaluations() []*v16.PetEvaluationModel {
	if x != nil {
		return x.PetEvaluations
	}
	return nil
}

// pet detail service composite
type ServiceComposite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail
	ServiceDetail *ServiceDetailComposite `protobuf:"bytes,1,opt,name=service_detail,json=serviceDetail,proto3" json:"service_detail,omitempty"`
	// operation
	Operations []*v1.ServiceOperationModel `protobuf:"bytes,2,rep,name=operations,proto3" json:"operations,omitempty"`
	// split lodgings
	SplitLodgings []*v1.BoardingSplitLodgingDetailDef `protobuf:"bytes,3,rep,name=split_lodgings,json=splitLodgings,proto3" json:"split_lodgings,omitempty"`
	// is slot free service
	IsSlotFreeService bool `protobuf:"varint,4,opt,name=is_slot_free_service,json=isSlotFreeService,proto3" json:"is_slot_free_service,omitempty"`
}

func (x *ServiceComposite) Reset() {
	*x = ServiceComposite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceComposite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceComposite) ProtoMessage() {}

func (x *ServiceComposite) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceComposite.ProtoReflect.Descriptor instead.
func (*ServiceComposite) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{8}
}

func (x *ServiceComposite) GetServiceDetail() *ServiceDetailComposite {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *ServiceComposite) GetOperations() []*v1.ServiceOperationModel {
	if x != nil {
		return x.Operations
	}
	return nil
}

func (x *ServiceComposite) GetSplitLodgings() []*v1.BoardingSplitLodgingDetailDef {
	if x != nil {
		return x.SplitLodgings
	}
	return nil
}

func (x *ServiceComposite) GetIsSlotFreeService() bool {
	if x != nil {
		return x.IsSlotFreeService
	}
	return false
}

// pet detail add on composite
type AddOnComposite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail
	ServiceDetail *ServiceDetailComposite `protobuf:"bytes,1,opt,name=service_detail,json=serviceDetail,proto3" json:"service_detail,omitempty"`
	// operation
	Operations []*v1.ServiceOperationModel `protobuf:"bytes,2,rep,name=operations,proto3" json:"operations,omitempty"`
}

func (x *AddOnComposite) Reset() {
	*x = AddOnComposite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddOnComposite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOnComposite) ProtoMessage() {}

func (x *AddOnComposite) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOnComposite.ProtoReflect.Descriptor instead.
func (*AddOnComposite) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{9}
}

func (x *AddOnComposite) GetServiceDetail() *ServiceDetailComposite {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *AddOnComposite) GetOperations() []*v1.ServiceOperationModel {
	if x != nil {
		return x.Operations
	}
	return nil
}

// service detail composite
type ServiceDetailComposite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service type
	ServiceType v14.ServiceType `protobuf:"varint,6,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// service time, in minutes
	ServiceTime int32 `protobuf:"varint,7,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// service price
	ServicePrice float64 `protobuf:"fixed64,8,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// start time, in minutes
	StartTime int32 `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time, in minutes
	EndTime int32 `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// scope type price
	ScopeTypePrice v14.ServiceScopeType `protobuf:"varint,13,opt,name=scope_type_price,json=scopeTypePrice,proto3,enum=moego.models.offering.v1.ServiceScopeType" json:"scope_type_price,omitempty"`
	// scope type time
	ScopeTypeTime v14.ServiceScopeType `protobuf:"varint,14,opt,name=scope_type_time,json=scopeTypeTime,proto3,enum=moego.models.offering.v1.ServiceScopeType" json:"scope_type_time,omitempty"`
	// package service id
	PackageServiceId int64 `protobuf:"varint,16,opt,name=package_service_id,json=packageServiceId,proto3" json:"package_service_id,omitempty"`
	// enable operation
	EnableOperation bool `protobuf:"varint,21,opt,name=enable_operation,json=enableOperation,proto3" json:"enable_operation,omitempty"`
	// work mode, 0-parallel, 1-sequence
	WorkMode v1.WorkMode `protobuf:"varint,22,opt,name=work_mode,json=workMode,proto3,enum=moego.models.appointment.v1.WorkMode" json:"work_mode,omitempty"`
	// service color code
	ServiceColorCode string `protobuf:"bytes,23,opt,name=service_color_code,json=serviceColorCode,proto3" json:"service_color_code,omitempty"`
	// service start date, in yyyy-MM-dd format, for boarding or daycare service
	StartDate string `protobuf:"bytes,24,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// service end date, in yyyy-MM-dd format, for boarding or daycare service
	EndDate string `protobuf:"bytes,25,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// service item type, different from service type, it includes grooming, boarding, daycare or other services.
	ServiceItemType v14.ServiceItemType `protobuf:"varint,26,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// lodging id, only for boarding service item type
	LodgingId int64 `protobuf:"varint,27,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// price unit, 1 - per session, 2 - per night, 3 - per hour, 4 - per day
	PriceUnit int32 `protobuf:"varint,28,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit,omitempty"`
	// specific dates, yyyy-MM-dd
	SpecificDates []string `protobuf:"bytes,29,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// add-on associated service id
	AssociatedServiceId int64 `protobuf:"varint,30,opt,name=associated_service_id,json=associatedServiceId,proto3" json:"associated_service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,31,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// lodging unit name
	LodgingUnitName string `protobuf:"bytes,32,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
	// lodging type name
	LodgingTypeName string `protobuf:"bytes,33,opt,name=lodging_type_name,json=lodgingTypeName,proto3" json:"lodging_type_name,omitempty"`
	// staff name
	StaffName string `protobuf:"bytes,34,opt,name=staff_name,json=staffName,proto3" json:"staff_name,omitempty"`
	// service delete
	IsDeleted bool `protobuf:"varint,35,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// service inactive
	Inactive bool `protobuf:"varint,36,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// max duration (only for daycare service)
	MaxDuration int32 `protobuf:"varint,37,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// pet detail date type
	DateType *v1.PetDetailDateType `protobuf:"varint,38,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType,oneof" json:"date_type,omitempty"`
	// price override type
	PriceOverrideType *v14.ServiceOverrideType `protobuf:"varint,39,opt,name=price_override_type,json=priceOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType,oneof" json:"price_override_type,omitempty"`
	// duration override type
	DurationOverrideType *v14.ServiceOverrideType `protobuf:"varint,40,opt,name=duration_override_type,json=durationOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType,oneof" json:"duration_override_type,omitempty"`
	// quantity per day
	QuantityPerDay int32 `protobuf:"varint,41,opt,name=quantity_per_day,json=quantityPerDay,proto3" json:"quantity_per_day,omitempty"`
	// order line item id
	OrderLineItemId int64 `protobuf:"varint,42,opt,name=order_line_item_id,json=orderLineItemId,proto3" json:"order_line_item_id,omitempty"`
	// require dedicated staff
	RequireDedicatedStaff bool `protobuf:"varint,43,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
}

func (x *ServiceDetailComposite) Reset() {
	*x = ServiceDetailComposite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceDetailComposite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDetailComposite) ProtoMessage() {}

func (x *ServiceDetailComposite) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDetailComposite.ProtoReflect.Descriptor instead.
func (*ServiceDetailComposite) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{10}
}

func (x *ServiceDetailComposite) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceDetailComposite) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ServiceDetailComposite) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ServiceDetailComposite) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *ServiceDetailComposite) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceDetailComposite) GetServiceType() v14.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v14.ServiceType(0)
}

func (x *ServiceDetailComposite) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *ServiceDetailComposite) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *ServiceDetailComposite) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ServiceDetailComposite) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ServiceDetailComposite) GetScopeTypePrice() v14.ServiceScopeType {
	if x != nil {
		return x.ScopeTypePrice
	}
	return v14.ServiceScopeType(0)
}

func (x *ServiceDetailComposite) GetScopeTypeTime() v14.ServiceScopeType {
	if x != nil {
		return x.ScopeTypeTime
	}
	return v14.ServiceScopeType(0)
}

func (x *ServiceDetailComposite) GetPackageServiceId() int64 {
	if x != nil {
		return x.PackageServiceId
	}
	return 0
}

func (x *ServiceDetailComposite) GetEnableOperation() bool {
	if x != nil {
		return x.EnableOperation
	}
	return false
}

func (x *ServiceDetailComposite) GetWorkMode() v1.WorkMode {
	if x != nil {
		return x.WorkMode
	}
	return v1.WorkMode(0)
}

func (x *ServiceDetailComposite) GetServiceColorCode() string {
	if x != nil {
		return x.ServiceColorCode
	}
	return ""
}

func (x *ServiceDetailComposite) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *ServiceDetailComposite) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *ServiceDetailComposite) GetServiceItemType() v14.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v14.ServiceItemType(0)
}

func (x *ServiceDetailComposite) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *ServiceDetailComposite) GetPriceUnit() int32 {
	if x != nil {
		return x.PriceUnit
	}
	return 0
}

func (x *ServiceDetailComposite) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *ServiceDetailComposite) GetAssociatedServiceId() int64 {
	if x != nil {
		return x.AssociatedServiceId
	}
	return 0
}

func (x *ServiceDetailComposite) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceDetailComposite) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

func (x *ServiceDetailComposite) GetLodgingTypeName() string {
	if x != nil {
		return x.LodgingTypeName
	}
	return ""
}

func (x *ServiceDetailComposite) GetStaffName() string {
	if x != nil {
		return x.StaffName
	}
	return ""
}

func (x *ServiceDetailComposite) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *ServiceDetailComposite) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *ServiceDetailComposite) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

func (x *ServiceDetailComposite) GetDateType() v1.PetDetailDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return v1.PetDetailDateType(0)
}

func (x *ServiceDetailComposite) GetPriceOverrideType() v14.ServiceOverrideType {
	if x != nil && x.PriceOverrideType != nil {
		return *x.PriceOverrideType
	}
	return v14.ServiceOverrideType(0)
}

func (x *ServiceDetailComposite) GetDurationOverrideType() v14.ServiceOverrideType {
	if x != nil && x.DurationOverrideType != nil {
		return *x.DurationOverrideType
	}
	return v14.ServiceOverrideType(0)
}

func (x *ServiceDetailComposite) GetQuantityPerDay() int32 {
	if x != nil {
		return x.QuantityPerDay
	}
	return 0
}

func (x *ServiceDetailComposite) GetOrderLineItemId() int64 {
	if x != nil {
		return x.OrderLineItemId
	}
	return 0
}

func (x *ServiceDetailComposite) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

// customer composite
type CustomerComposite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer
	CustomerProfile *v16.BusinessCustomerModel `protobuf:"bytes,1,opt,name=customer_profile,json=customerProfile,proto3" json:"customer_profile,omitempty"`
	// customer tag
	CustomerTags []*v16.BusinessCustomerTagModel `protobuf:"bytes,2,rep,name=customer_tags,json=customerTags,proto3" json:"customer_tags,omitempty"`
	// customer address
	CustomerAddress *v16.BusinessCustomerAddressModel `protobuf:"bytes,3,opt,name=customer_address,json=customerAddress,proto3" json:"customer_address,omitempty"`
	// is new customer
	IsNewCustomer bool `protobuf:"varint,4,opt,name=is_new_customer,json=isNewCustomer,proto3" json:"is_new_customer,omitempty"`
	// required sign
	RequiredSign bool `protobuf:"varint,5,opt,name=required_sign,json=requiredSign,proto3" json:"required_sign,omitempty"`
	// review booster sent
	ReviewBoosterSent bool `protobuf:"varint,6,opt,name=review_booster_sent,json=reviewBoosterSent,proto3" json:"review_booster_sent,omitempty"`
	// last alert note
	LastAlertNote *v1.AppointmentNoteModel `protobuf:"bytes,7,opt,name=last_alert_note,json=lastAlertNote,proto3,oneof" json:"last_alert_note,omitempty"`
	// customer package info
	CustomerPackages []*CustomerPackageView `protobuf:"bytes,8,rep,name=customer_packages,json=customerPackages,proto3" json:"customer_packages,omitempty"`
}

func (x *CustomerComposite) Reset() {
	*x = CustomerComposite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerComposite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerComposite) ProtoMessage() {}

func (x *CustomerComposite) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerComposite.ProtoReflect.Descriptor instead.
func (*CustomerComposite) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{11}
}

func (x *CustomerComposite) GetCustomerProfile() *v16.BusinessCustomerModel {
	if x != nil {
		return x.CustomerProfile
	}
	return nil
}

func (x *CustomerComposite) GetCustomerTags() []*v16.BusinessCustomerTagModel {
	if x != nil {
		return x.CustomerTags
	}
	return nil
}

func (x *CustomerComposite) GetCustomerAddress() *v16.BusinessCustomerAddressModel {
	if x != nil {
		return x.CustomerAddress
	}
	return nil
}

func (x *CustomerComposite) GetIsNewCustomer() bool {
	if x != nil {
		return x.IsNewCustomer
	}
	return false
}

func (x *CustomerComposite) GetRequiredSign() bool {
	if x != nil {
		return x.RequiredSign
	}
	return false
}

func (x *CustomerComposite) GetReviewBoosterSent() bool {
	if x != nil {
		return x.ReviewBoosterSent
	}
	return false
}

func (x *CustomerComposite) GetLastAlertNote() *v1.AppointmentNoteModel {
	if x != nil {
		return x.LastAlertNote
	}
	return nil
}

func (x *CustomerComposite) GetCustomerPackages() []*CustomerPackageView {
	if x != nil {
		return x.CustomerPackages
	}
	return nil
}

// pet vaccine
type PetVaccineComposite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine record id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// vaccine id
	VaccineId int64 `protobuf:"varint,3,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// expiration date, may not exist
	ExpirationDate *date.Date `protobuf:"bytes,4,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// vaccine document urls
	DocumentUrls []string `protobuf:"bytes,5,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
	// vaccine name
	VaccineName string `protobuf:"bytes,6,opt,name=vaccine_name,json=vaccineName,proto3" json:"vaccine_name,omitempty"`
}

func (x *PetVaccineComposite) Reset() {
	*x = PetVaccineComposite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetVaccineComposite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetVaccineComposite) ProtoMessage() {}

func (x *PetVaccineComposite) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetVaccineComposite.ProtoReflect.Descriptor instead.
func (*PetVaccineComposite) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{12}
}

func (x *PetVaccineComposite) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetVaccineComposite) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *PetVaccineComposite) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

func (x *PetVaccineComposite) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

func (x *PetVaccineComposite) GetVaccineName() string {
	if x != nil {
		return x.VaccineName
	}
	return ""
}

// pet evaluation
type EvaluationServiceComposite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet evaluation detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,4,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service time, in minutes
	ServiceTime int32 `protobuf:"varint,5,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// service price
	ServicePrice float64 `protobuf:"fixed64,6,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// start time, in minutes
	StartTime int32 `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time, in minutes
	EndTime int32 `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// service start date, in yyyy-MM-dd format
	StartDate string `protobuf:"bytes,9,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// service end date, in yyyy-MM-dd format
	EndDate string `protobuf:"bytes,10,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// service item type, always be evaluation services.
	ServiceItemType v14.ServiceItemType `protobuf:"varint,11,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,12,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,13,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,14,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// lodging id
	LodgingId *int64 `protobuf:"varint,15,opt,name=lodging_id,json=lodgingId,proto3,oneof" json:"lodging_id,omitempty"`
	// lodging unit name
	LodgingUnitName *string `protobuf:"bytes,16,opt,name=lodging_unit_name,json=lodgingUnitName,proto3,oneof" json:"lodging_unit_name,omitempty"`
	// lodging type name
	LodgingTypeName *string `protobuf:"bytes,17,opt,name=lodging_type_name,json=lodgingTypeName,proto3,oneof" json:"lodging_type_name,omitempty"`
	// staff name
	StaffName *string `protobuf:"bytes,34,opt,name=staff_name,json=staffName,proto3,oneof" json:"staff_name,omitempty"`
	// order line item id
	OrderLineItemId int64 `protobuf:"varint,42,opt,name=order_line_item_id,json=orderLineItemId,proto3" json:"order_line_item_id,omitempty"`
}

func (x *EvaluationServiceComposite) Reset() {
	*x = EvaluationServiceComposite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationServiceComposite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationServiceComposite) ProtoMessage() {}

func (x *EvaluationServiceComposite) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationServiceComposite.ProtoReflect.Descriptor instead.
func (*EvaluationServiceComposite) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{13}
}

func (x *EvaluationServiceComposite) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EvaluationServiceComposite) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *EvaluationServiceComposite) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *EvaluationServiceComposite) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *EvaluationServiceComposite) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *EvaluationServiceComposite) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *EvaluationServiceComposite) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *EvaluationServiceComposite) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *EvaluationServiceComposite) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *EvaluationServiceComposite) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *EvaluationServiceComposite) GetServiceItemType() v14.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v14.ServiceItemType(0)
}

func (x *EvaluationServiceComposite) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *EvaluationServiceComposite) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *EvaluationServiceComposite) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *EvaluationServiceComposite) GetLodgingId() int64 {
	if x != nil && x.LodgingId != nil {
		return *x.LodgingId
	}
	return 0
}

func (x *EvaluationServiceComposite) GetLodgingUnitName() string {
	if x != nil && x.LodgingUnitName != nil {
		return *x.LodgingUnitName
	}
	return ""
}

func (x *EvaluationServiceComposite) GetLodgingTypeName() string {
	if x != nil && x.LodgingTypeName != nil {
		return *x.LodgingTypeName
	}
	return ""
}

func (x *EvaluationServiceComposite) GetStaffName() string {
	if x != nil && x.StaffName != nil {
		return *x.StaffName
	}
	return ""
}

func (x *EvaluationServiceComposite) GetOrderLineItemId() int64 {
	if x != nil {
		return x.OrderLineItemId
	}
	return 0
}

// get customer last appointment params
type GetCustomerLastAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// selected business id
	SelectedBusinessId *int64 `protobuf:"varint,2,opt,name=selected_business_id,json=selectedBusinessId,proto3,oneof" json:"selected_business_id,omitempty"`
}

func (x *GetCustomerLastAppointmentParams) Reset() {
	*x = GetCustomerLastAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerLastAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerLastAppointmentParams) ProtoMessage() {}

func (x *GetCustomerLastAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerLastAppointmentParams.ProtoReflect.Descriptor instead.
func (*GetCustomerLastAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetCustomerLastAppointmentParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *GetCustomerLastAppointmentParams) GetSelectedBusinessId() int64 {
	if x != nil && x.SelectedBusinessId != nil {
		return *x.SelectedBusinessId
	}
	return 0
}

// get customer last finished appointment result
type GetCustomerLastAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// has last appointment
	HasLastAppointment bool `protobuf:"varint,1,opt,name=has_last_appointment,json=hasLastAppointment,proto3" json:"has_last_appointment,omitempty"`
	// customer
	Customer *CustomerComposite `protobuf:"bytes,2,opt,name=customer,proto3" json:"customer,omitempty"`
	// Appointment detail
	Appointment *v1.AppointmentCalendarView `protobuf:"bytes,3,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// pet and services
	ServiceDetail []*ServiceDetail `protobuf:"bytes,4,rep,name=service_detail,json=serviceDetail,proto3" json:"service_detail,omitempty"`
	// notes
	Notes []*v1.AppointmentNoteModel `protobuf:"bytes,5,rep,name=notes,proto3" json:"notes,omitempty"`
	// staff
	Staffs []*v13.StaffModel `protobuf:"bytes,6,rep,name=staffs,proto3" json:"staffs,omitempty"`
	// service type list
	ServiceItemTypes []v14.ServiceItemType `protobuf:"varint,7,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// pricing rule apply log list, deprecated, use pricing_rule_apply_logs_v2 instead
	//
	// Deprecated: Do not use.
	PricingRuleApplyLogs []*v1.PricingRuleApplyLogDrawerView `protobuf:"bytes,8,rep,name=pricing_rule_apply_logs,json=pricingRuleApplyLogs,proto3" json:"pricing_rule_apply_logs,omitempty"`
	// pricing rule apply log list
	PricingRuleApplyLogsV2 []*v2.PricingRuleApplyLogDrawerView `protobuf:"bytes,15,rep,name=pricing_rule_apply_logs_v2,json=pricingRuleApplyLogsV2,proto3" json:"pricing_rule_apply_logs_v2,omitempty"`
}

func (x *GetCustomerLastAppointmentResult) Reset() {
	*x = GetCustomerLastAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerLastAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerLastAppointmentResult) ProtoMessage() {}

func (x *GetCustomerLastAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerLastAppointmentResult.ProtoReflect.Descriptor instead.
func (*GetCustomerLastAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{15}
}

func (x *GetCustomerLastAppointmentResult) GetHasLastAppointment() bool {
	if x != nil {
		return x.HasLastAppointment
	}
	return false
}

func (x *GetCustomerLastAppointmentResult) GetCustomer() *CustomerComposite {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *GetCustomerLastAppointmentResult) GetAppointment() *v1.AppointmentCalendarView {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *GetCustomerLastAppointmentResult) GetServiceDetail() []*ServiceDetail {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *GetCustomerLastAppointmentResult) GetNotes() []*v1.AppointmentNoteModel {
	if x != nil {
		return x.Notes
	}
	return nil
}

func (x *GetCustomerLastAppointmentResult) GetStaffs() []*v13.StaffModel {
	if x != nil {
		return x.Staffs
	}
	return nil
}

func (x *GetCustomerLastAppointmentResult) GetServiceItemTypes() []v14.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// Deprecated: Do not use.
func (x *GetCustomerLastAppointmentResult) GetPricingRuleApplyLogs() []*v1.PricingRuleApplyLogDrawerView {
	if x != nil {
		return x.PricingRuleApplyLogs
	}
	return nil
}

func (x *GetCustomerLastAppointmentResult) GetPricingRuleApplyLogsV2() []*v2.PricingRuleApplyLogDrawerView {
	if x != nil {
		return x.PricingRuleApplyLogsV2
	}
	return nil
}

// calculate appointment invoice params
type CalculateAppointmentInvoiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected pet and services
	PetDetails []*v1.PetDetailDef `protobuf:"bytes,3,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
}

func (x *CalculateAppointmentInvoiceParams) Reset() {
	*x = CalculateAppointmentInvoiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateAppointmentInvoiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateAppointmentInvoiceParams) ProtoMessage() {}

func (x *CalculateAppointmentInvoiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateAppointmentInvoiceParams.ProtoReflect.Descriptor instead.
func (*CalculateAppointmentInvoiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{16}
}

func (x *CalculateAppointmentInvoiceParams) GetPetDetails() []*v1.PetDetailDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

// calculate appointment invoice result
type CalculateAppointmentInvoiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order detail
	Order *v12.OrderDef `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *CalculateAppointmentInvoiceResult) Reset() {
	*x = CalculateAppointmentInvoiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateAppointmentInvoiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateAppointmentInvoiceResult) ProtoMessage() {}

func (x *CalculateAppointmentInvoiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateAppointmentInvoiceResult.ProtoReflect.Descriptor instead.
func (*CalculateAppointmentInvoiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{17}
}

func (x *CalculateAppointmentInvoiceResult) GetOrder() *v12.OrderDef {
	if x != nil {
		return x.Order
	}
	return nil
}

// get in progress evaluation appointment params
type GetInProgressEvaluationAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// customer id
	CustomerId *int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
}

func (x *GetInProgressEvaluationAppointmentParams) Reset() {
	*x = GetInProgressEvaluationAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInProgressEvaluationAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInProgressEvaluationAppointmentParams) ProtoMessage() {}

func (x *GetInProgressEvaluationAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInProgressEvaluationAppointmentParams.ProtoReflect.Descriptor instead.
func (*GetInProgressEvaluationAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{18}
}

func (x *GetInProgressEvaluationAppointmentParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetInProgressEvaluationAppointmentParams) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *GetInProgressEvaluationAppointmentParams) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

// get in progress evaluation appointment result
type GetInProgressEvaluationAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId *int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
}

func (x *GetInProgressEvaluationAppointmentResult) Reset() {
	*x = GetInProgressEvaluationAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInProgressEvaluationAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInProgressEvaluationAppointmentResult) ProtoMessage() {}

func (x *GetInProgressEvaluationAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInProgressEvaluationAppointmentResult.ProtoReflect.Descriptor instead.
func (*GetInProgressEvaluationAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{19}
}

func (x *GetInProgressEvaluationAppointmentResult) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

// Get service summary params
type GetServiceSummaryParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// start date, in yyyy-MM-dd format
	StartDate string `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date, in yyyy-MM-dd format
	EndDate string `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *GetServiceSummaryParams) Reset() {
	*x = GetServiceSummaryParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceSummaryParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceSummaryParams) ProtoMessage() {}

func (x *GetServiceSummaryParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceSummaryParams.ProtoReflect.Descriptor instead.
func (*GetServiceSummaryParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{20}
}

func (x *GetServiceSummaryParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetServiceSummaryParams) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GetServiceSummaryParams) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

// Get service summary result
type GetServiceSummaryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Count by appointment
	CountByAppointment *GetServiceSummaryResult_CountedByAppointment `protobuf:"bytes,1,opt,name=count_by_appointment,json=countByAppointment,proto3" json:"count_by_appointment,omitempty"`
	// Count by pet
	CountByPet *GetServiceSummaryResult_CountedByPet `protobuf:"bytes,2,opt,name=count_by_pet,json=countByPet,proto3" json:"count_by_pet,omitempty"`
}

func (x *GetServiceSummaryResult) Reset() {
	*x = GetServiceSummaryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceSummaryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceSummaryResult) ProtoMessage() {}

func (x *GetServiceSummaryResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceSummaryResult.ProtoReflect.Descriptor instead.
func (*GetServiceSummaryResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{21}
}

func (x *GetServiceSummaryResult) GetCountByAppointment() *GetServiceSummaryResult_CountedByAppointment {
	if x != nil {
		return x.CountByAppointment
	}
	return nil
}

func (x *GetServiceSummaryResult) GetCountByPet() *GetServiceSummaryResult_CountedByPet {
	if x != nil {
		return x.CountByPet
	}
	return nil
}

// get day summary params
type GetDaySummaryParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// need query date, in yyyy-MM-dd format
	Date string `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *GetDaySummaryParams) Reset() {
	*x = GetDaySummaryParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDaySummaryParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDaySummaryParams) ProtoMessage() {}

func (x *GetDaySummaryParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDaySummaryParams.ProtoReflect.Descriptor instead.
func (*GetDaySummaryParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{22}
}

func (x *GetDaySummaryParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetDaySummaryParams) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

// get day summary result
type GetDaySummaryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// need query date, in yyyy-MM-dd format
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"` // 日期
	// appointment_day_summary
	AppointmentDaySummary *AppointmentDaySummary `protobuf:"bytes,2,opt,name=appointment_day_summary,json=appointmentDaySummary,proto3" json:"appointment_day_summary,omitempty"`
	// pet_day_summary
	PetDaySummary *PetDaySummary `protobuf:"bytes,3,opt,name=pet_day_summary,json=petDaySummary,proto3" json:"pet_day_summary,omitempty"`
}

func (x *GetDaySummaryResult) Reset() {
	*x = GetDaySummaryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDaySummaryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDaySummaryResult) ProtoMessage() {}

func (x *GetDaySummaryResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDaySummaryResult.ProtoReflect.Descriptor instead.
func (*GetDaySummaryResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{23}
}

func (x *GetDaySummaryResult) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *GetDaySummaryResult) GetAppointmentDaySummary() *AppointmentDaySummary {
	if x != nil {
		return x.AppointmentDaySummary
	}
	return nil
}

func (x *GetDaySummaryResult) GetPetDaySummary() *PetDaySummary {
	if x != nil {
		return x.PetDaySummary
	}
	return nil
}

// appointment day summary
type AppointmentDaySummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// daily status
	DailyStatus *DailyStatus `protobuf:"bytes,1,opt,name=daily_status,json=dailyStatus,proto3" json:"daily_status,omitempty"`
	// care_list
	CareList []*CareCategory `protobuf:"bytes,2,rep,name=care_list,json=careList,proto3" json:"care_list,omitempty"`
}

func (x *AppointmentDaySummary) Reset() {
	*x = AppointmentDaySummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentDaySummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentDaySummary) ProtoMessage() {}

func (x *AppointmentDaySummary) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentDaySummary.ProtoReflect.Descriptor instead.
func (*AppointmentDaySummary) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{24}
}

func (x *AppointmentDaySummary) GetDailyStatus() *DailyStatus {
	if x != nil {
		return x.DailyStatus
	}
	return nil
}

func (x *AppointmentDaySummary) GetCareList() []*CareCategory {
	if x != nil {
		return x.CareList
	}
	return nil
}

// pet day summary
type PetDaySummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// daily_status
	DailyStatus *DailyStatus `protobuf:"bytes,1,opt,name=daily_status,json=dailyStatus,proto3" json:"daily_status,omitempty"`
	// care_list
	CareList []*CareCategory `protobuf:"bytes,2,rep,name=care_list,json=careList,proto3" json:"care_list,omitempty"`
}

func (x *PetDaySummary) Reset() {
	*x = PetDaySummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetDaySummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDaySummary) ProtoMessage() {}

func (x *PetDaySummary) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDaySummary.ProtoReflect.Descriptor instead.
func (*PetDaySummary) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{25}
}

func (x *PetDaySummary) GetDailyStatus() *DailyStatus {
	if x != nil {
		return x.DailyStatus
	}
	return nil
}

func (x *PetDaySummary) GetCareList() []*CareCategory {
	if x != nil {
		return x.CareList
	}
	return nil
}

// daily status
type DailyStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total in count
	In uint32 `protobuf:"varint,1,opt,name=in,proto3" json:"in,omitempty"`
	// total out count
	Out uint32 `protobuf:"varint,2,opt,name=out,proto3" json:"out,omitempty"`
	// total overnight count
	Overnight uint32 `protobuf:"varint,3,opt,name=overnight,proto3" json:"overnight,omitempty"`
	// total count
	Total uint32 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *DailyStatus) Reset() {
	*x = DailyStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyStatus) ProtoMessage() {}

func (x *DailyStatus) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyStatus.ProtoReflect.Descriptor instead.
func (*DailyStatus) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{26}
}

func (x *DailyStatus) GetIn() uint32 {
	if x != nil {
		return x.In
	}
	return 0
}

func (x *DailyStatus) GetOut() uint32 {
	if x != nil {
		return x.Out
	}
	return 0
}

func (x *DailyStatus) GetOvernight() uint32 {
	if x != nil {
		return x.Overnight
	}
	return 0
}

func (x *DailyStatus) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// care category
type CareCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// category type: grooming, boarding
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// total
	Total uint32 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	// service_list. eg: grooming: full grooming、bath.
	ServiceList []*ServiceItem `protobuf:"bytes,3,rep,name=service_list,json=serviceList,proto3" json:"service_list,omitempty"`
}

func (x *CareCategory) Reset() {
	*x = CareCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CareCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CareCategory) ProtoMessage() {}

func (x *CareCategory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CareCategory.ProtoReflect.Descriptor instead.
func (*CareCategory) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{27}
}

func (x *CareCategory) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CareCategory) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *CareCategory) GetServiceList() []*ServiceItem {
	if x != nil {
		return x.ServiceList
	}
	return nil
}

// service item
type ServiceItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// total
	Total uint32 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ServiceItem) Reset() {
	*x = ServiceItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceItem) ProtoMessage() {}

func (x *ServiceItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceItem.ProtoReflect.Descriptor instead.
func (*ServiceItem) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{28}
}

func (x *ServiceItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceItem) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// get appointment lodging info params
type GetAppointmentLodgingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetAppointmentLodgingParams) Reset() {
	*x = GetAppointmentLodgingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentLodgingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentLodgingParams) ProtoMessage() {}

func (x *GetAppointmentLodgingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentLodgingParams.ProtoReflect.Descriptor instead.
func (*GetAppointmentLodgingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{29}
}

func (x *GetAppointmentLodgingParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get appointment lodging info result
type GetAppointmentLodgingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet lodging view list
	PetLodgingViews []*GetAppointmentLodgingResult_PetLodgingView `protobuf:"bytes,1,rep,name=pet_lodging_views,json=petLodgingViews,proto3" json:"pet_lodging_views,omitempty"`
}

func (x *GetAppointmentLodgingResult) Reset() {
	*x = GetAppointmentLodgingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentLodgingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentLodgingResult) ProtoMessage() {}

func (x *GetAppointmentLodgingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentLodgingResult.ProtoReflect.Descriptor instead.
func (*GetAppointmentLodgingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{30}
}

func (x *GetAppointmentLodgingResult) GetPetLodgingViews() []*GetAppointmentLodgingResult_PetLodgingView {
	if x != nil {
		return x.PetLodgingViews
	}
	return nil
}

// Batch quick check in for appointment params
type BatchQuickCheckInParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Selected service
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Selected multi pet ids
	PetIds []int64 `protobuf:"varint,3,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// Selected date
	Date *date.Date `protobuf:"bytes,4,opt,name=date,proto3" json:"date,omitempty"`
	// source
	Source v1.AppointmentSource `protobuf:"varint,5,opt,name=source,proto3,enum=moego.models.appointment.v1.AppointmentSource" json:"source,omitempty"`
}

func (x *BatchQuickCheckInParams) Reset() {
	*x = BatchQuickCheckInParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchQuickCheckInParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQuickCheckInParams) ProtoMessage() {}

func (x *BatchQuickCheckInParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQuickCheckInParams.ProtoReflect.Descriptor instead.
func (*BatchQuickCheckInParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{31}
}

func (x *BatchQuickCheckInParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchQuickCheckInParams) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *BatchQuickCheckInParams) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *BatchQuickCheckInParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *BatchQuickCheckInParams) GetSource() v1.AppointmentSource {
	if x != nil {
		return x.Source
	}
	return v1.AppointmentSource(0)
}

// Quick check in for appointment result
type BatchQuickCheckInResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// created appointment id
	CreatedAppointmentIds []int64 `protobuf:"varint,1,rep,packed,name=created_appointment_ids,json=createdAppointmentIds,proto3" json:"created_appointment_ids,omitempty"`
	// updated appointment id
	UpdatedAppointmentIds []int64 `protobuf:"varint,2,rep,packed,name=updated_appointment_ids,json=updatedAppointmentIds,proto3" json:"updated_appointment_ids,omitempty"`
}

func (x *BatchQuickCheckInResult) Reset() {
	*x = BatchQuickCheckInResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchQuickCheckInResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQuickCheckInResult) ProtoMessage() {}

func (x *BatchQuickCheckInResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQuickCheckInResult.ProtoReflect.Descriptor instead.
func (*BatchQuickCheckInResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{32}
}

func (x *BatchQuickCheckInResult) GetCreatedAppointmentIds() []int64 {
	if x != nil {
		return x.CreatedAppointmentIds
	}
	return nil
}

func (x *BatchQuickCheckInResult) GetUpdatedAppointmentIds() []int64 {
	if x != nil {
		return x.UpdatedAppointmentIds
	}
	return nil
}

// ListStaffAppointmentsParams
type ListStaffAppointmentsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// query period
	Period *interval.Interval `protobuf:"bytes,1,opt,name=period,proto3" json:"period,omitempty"`
	// business ids
	BusinessIds []uint64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// pagination
	Pagination *v21.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// staff id, override header staff id
	StaffId *uint64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// get staff appt by operation detail(multi-staff)
	IncludeServiceOperation *bool `protobuf:"varint,5,opt,name=include_service_operation,json=includeServiceOperation,proto3,oneof" json:"include_service_operation,omitempty"`
	// query appointment date
	AppointmentDate *date.Date `protobuf:"bytes,6,opt,name=appointment_date,json=appointmentDate,proto3,oneof" json:"appointment_date,omitempty"`
}

func (x *ListStaffAppointmentsParams) Reset() {
	*x = ListStaffAppointmentsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentsParams) ProtoMessage() {}

func (x *ListStaffAppointmentsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentsParams.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{33}
}

func (x *ListStaffAppointmentsParams) GetPeriod() *interval.Interval {
	if x != nil {
		return x.Period
	}
	return nil
}

func (x *ListStaffAppointmentsParams) GetBusinessIds() []uint64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListStaffAppointmentsParams) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListStaffAppointmentsParams) GetStaffId() uint64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *ListStaffAppointmentsParams) GetIncludeServiceOperation() bool {
	if x != nil && x.IncludeServiceOperation != nil {
		return *x.IncludeServiceOperation
	}
	return false
}

func (x *ListStaffAppointmentsParams) GetAppointmentDate() *date.Date {
	if x != nil {
		return x.AppointmentDate
	}
	return nil
}

// ListStaffAppointmentsResult
type ListStaffAppointmentsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment list
	Appointments []*ListStaffAppointmentsResult_AppointmentDetailView `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// pagination response
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListStaffAppointmentsResult) Reset() {
	*x = ListStaffAppointmentsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentsResult) ProtoMessage() {}

func (x *ListStaffAppointmentsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentsResult.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{34}
}

func (x *ListStaffAppointmentsResult) GetAppointments() []*ListStaffAppointmentsResult_AppointmentDetailView {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *ListStaffAppointmentsResult) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// Batch book again appointment by staff and date params
type BatchBookAgainAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Selected staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Selected date
	SourceDate *date.Date `protobuf:"bytes,3,opt,name=source_date,json=sourceDate,proto3" json:"source_date,omitempty"`
	// Target date
	TargetDate *date.Date `protobuf:"bytes,4,opt,name=target_date,json=targetDate,proto3" json:"target_date,omitempty"`
}

func (x *BatchBookAgainAppointmentParams) Reset() {
	*x = BatchBookAgainAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchBookAgainAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchBookAgainAppointmentParams) ProtoMessage() {}

func (x *BatchBookAgainAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchBookAgainAppointmentParams.ProtoReflect.Descriptor instead.
func (*BatchBookAgainAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{35}
}

func (x *BatchBookAgainAppointmentParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchBookAgainAppointmentParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *BatchBookAgainAppointmentParams) GetSourceDate() *date.Date {
	if x != nil {
		return x.SourceDate
	}
	return nil
}

func (x *BatchBookAgainAppointmentParams) GetTargetDate() *date.Date {
	if x != nil {
		return x.TargetDate
	}
	return nil
}

// Batch book again appointment by staff and date result
type BatchBookAgainAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// book again appointments detail
	Appointments []*v1.AppointmentModel `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *BatchBookAgainAppointmentResult) Reset() {
	*x = BatchBookAgainAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchBookAgainAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchBookAgainAppointmentResult) ProtoMessage() {}

func (x *BatchBookAgainAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchBookAgainAppointmentResult.ProtoReflect.Descriptor instead.
func (*BatchBookAgainAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{36}
}

func (x *BatchBookAgainAppointmentResult) GetAppointments() []*v1.AppointmentModel {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// Batch cancel appointment by staff and date params
type BatchCancelAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Selected staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Selected date
	Date *date.Date `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
	// cancel reason, optional
	CancelReason *string `protobuf:"bytes,4,opt,name=cancel_reason,json=cancelReason,proto3,oneof" json:"cancel_reason,omitempty"`
}

func (x *BatchCancelAppointmentParams) Reset() {
	*x = BatchCancelAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCancelAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCancelAppointmentParams) ProtoMessage() {}

func (x *BatchCancelAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCancelAppointmentParams.ProtoReflect.Descriptor instead.
func (*BatchCancelAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{37}
}

func (x *BatchCancelAppointmentParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchCancelAppointmentParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *BatchCancelAppointmentParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *BatchCancelAppointmentParams) GetCancelReason() string {
	if x != nil && x.CancelReason != nil {
		return *x.CancelReason
	}
	return ""
}

// Batch cancel appointment by staff and date result
type BatchCancelAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// canceled appointments detail
	Appointments []*v1.AppointmentModel `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *BatchCancelAppointmentResult) Reset() {
	*x = BatchCancelAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCancelAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCancelAppointmentResult) ProtoMessage() {}

func (x *BatchCancelAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCancelAppointmentResult.ProtoReflect.Descriptor instead.
func (*BatchCancelAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{38}
}

func (x *BatchCancelAppointmentResult) GetAppointments() []*v1.AppointmentModel {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// RescheduleBoardingAppointment params
type RescheduleBoardingAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// start date, optional
	StartDate *string `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date, optional
	EndDate *string `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
}

func (x *RescheduleBoardingAppointmentParams) Reset() {
	*x = RescheduleBoardingAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleBoardingAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleBoardingAppointmentParams) ProtoMessage() {}

func (x *RescheduleBoardingAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleBoardingAppointmentParams.ProtoReflect.Descriptor instead.
func (*RescheduleBoardingAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{39}
}

func (x *RescheduleBoardingAppointmentParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *RescheduleBoardingAppointmentParams) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *RescheduleBoardingAppointmentParams) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

// RescheduleBoardingAppointment result
type RescheduleBoardingAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RescheduleBoardingAppointmentResult) Reset() {
	*x = RescheduleBoardingAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleBoardingAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleBoardingAppointmentResult) ProtoMessage() {}

func (x *RescheduleBoardingAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleBoardingAppointmentResult.ProtoReflect.Descriptor instead.
func (*RescheduleBoardingAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{40}
}

// Pet belonging
type CreateAppointmentParams_PetBelonging struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// pet area
	Area *string `protobuf:"bytes,3,opt,name=area,proto3,oneof" json:"area,omitempty"`
	// pet photo url
	PhotoUrl *string `protobuf:"bytes,4,opt,name=photo_url,json=photoUrl,proto3,oneof" json:"photo_url,omitempty"`
}

func (x *CreateAppointmentParams_PetBelonging) Reset() {
	*x = CreateAppointmentParams_PetBelonging{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppointmentParams_PetBelonging) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppointmentParams_PetBelonging) ProtoMessage() {}

func (x *CreateAppointmentParams_PetBelonging) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppointmentParams_PetBelonging.ProtoReflect.Descriptor instead.
func (*CreateAppointmentParams_PetBelonging) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CreateAppointmentParams_PetBelonging) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *CreateAppointmentParams_PetBelonging) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateAppointmentParams_PetBelonging) GetArea() string {
	if x != nil && x.Area != nil {
		return *x.Area
	}
	return ""
}

func (x *CreateAppointmentParams_PetBelonging) GetPhotoUrl() string {
	if x != nil && x.PhotoUrl != nil {
		return *x.PhotoUrl
	}
	return ""
}

// Count by appointment
type GetServiceSummaryResult_CountedByAppointment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rows
	Rows []*GetServiceSummaryResult_ServiceSummaryRow `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (x *GetServiceSummaryResult_CountedByAppointment) Reset() {
	*x = GetServiceSummaryResult_CountedByAppointment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceSummaryResult_CountedByAppointment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceSummaryResult_CountedByAppointment) ProtoMessage() {}

func (x *GetServiceSummaryResult_CountedByAppointment) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceSummaryResult_CountedByAppointment.ProtoReflect.Descriptor instead.
func (*GetServiceSummaryResult_CountedByAppointment) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{21, 0}
}

func (x *GetServiceSummaryResult_CountedByAppointment) GetRows() []*GetServiceSummaryResult_ServiceSummaryRow {
	if x != nil {
		return x.Rows
	}
	return nil
}

// Count by pet
type GetServiceSummaryResult_CountedByPet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rows
	Rows []*GetServiceSummaryResult_ServiceSummaryRow `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (x *GetServiceSummaryResult_CountedByPet) Reset() {
	*x = GetServiceSummaryResult_CountedByPet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceSummaryResult_CountedByPet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceSummaryResult_CountedByPet) ProtoMessage() {}

func (x *GetServiceSummaryResult_CountedByPet) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceSummaryResult_CountedByPet.ProtoReflect.Descriptor instead.
func (*GetServiceSummaryResult_CountedByPet) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{21, 1}
}

func (x *GetServiceSummaryResult_CountedByPet) GetRows() []*GetServiceSummaryResult_ServiceSummaryRow {
	if x != nil {
		return x.Rows
	}
	return nil
}

// Row
type GetServiceSummaryResult_ServiceSummaryRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date, in yyyy-MM-dd format
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// total in count
	In int32 `protobuf:"varint,2,opt,name=in,proto3" json:"in,omitempty"`
	// total out count
	Out int32 `protobuf:"varint,3,opt,name=out,proto3" json:"out,omitempty"`
	// total overnight count
	Overnight int32 `protobuf:"varint,4,opt,name=overnight,proto3" json:"overnight,omitempty"`
	// total count
	Total int32 `protobuf:"varint,5,opt,name=total,proto3" json:"total,omitempty"`
	// details
	Children *GetServiceSummaryResult_ServiceSummaryChildren `protobuf:"bytes,6,opt,name=children,proto3" json:"children,omitempty"`
}

func (x *GetServiceSummaryResult_ServiceSummaryRow) Reset() {
	*x = GetServiceSummaryResult_ServiceSummaryRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceSummaryResult_ServiceSummaryRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceSummaryResult_ServiceSummaryRow) ProtoMessage() {}

func (x *GetServiceSummaryResult_ServiceSummaryRow) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceSummaryResult_ServiceSummaryRow.ProtoReflect.Descriptor instead.
func (*GetServiceSummaryResult_ServiceSummaryRow) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{21, 2}
}

func (x *GetServiceSummaryResult_ServiceSummaryRow) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *GetServiceSummaryResult_ServiceSummaryRow) GetIn() int32 {
	if x != nil {
		return x.In
	}
	return 0
}

func (x *GetServiceSummaryResult_ServiceSummaryRow) GetOut() int32 {
	if x != nil {
		return x.Out
	}
	return 0
}

func (x *GetServiceSummaryResult_ServiceSummaryRow) GetOvernight() int32 {
	if x != nil {
		return x.Overnight
	}
	return 0
}

func (x *GetServiceSummaryResult_ServiceSummaryRow) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetServiceSummaryResult_ServiceSummaryRow) GetChildren() *GetServiceSummaryResult_ServiceSummaryChildren {
	if x != nil {
		return x.Children
	}
	return nil
}

// Service summary children
type GetServiceSummaryResult_ServiceSummaryChildren struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming
	Grooming *GetServiceSummaryResult_ServiceRow `protobuf:"bytes,1,opt,name=grooming,proto3" json:"grooming,omitempty"`
	// boarding
	Boarding *GetServiceSummaryResult_ServiceRow `protobuf:"bytes,2,opt,name=boarding,proto3" json:"boarding,omitempty"`
	// daycare
	Daycare *GetServiceSummaryResult_ServiceRow `protobuf:"bytes,3,opt,name=daycare,proto3" json:"daycare,omitempty"`
	// evaluation
	Evaluation *GetServiceSummaryResult_ServiceRow `protobuf:"bytes,4,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
	// dog walking
	DogWalking *GetServiceSummaryResult_ServiceRow `protobuf:"bytes,5,opt,name=dog_walking,json=dogWalking,proto3" json:"dog_walking,omitempty"`
}

func (x *GetServiceSummaryResult_ServiceSummaryChildren) Reset() {
	*x = GetServiceSummaryResult_ServiceSummaryChildren{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceSummaryResult_ServiceSummaryChildren) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceSummaryResult_ServiceSummaryChildren) ProtoMessage() {}

func (x *GetServiceSummaryResult_ServiceSummaryChildren) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceSummaryResult_ServiceSummaryChildren.ProtoReflect.Descriptor instead.
func (*GetServiceSummaryResult_ServiceSummaryChildren) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{21, 3}
}

func (x *GetServiceSummaryResult_ServiceSummaryChildren) GetGrooming() *GetServiceSummaryResult_ServiceRow {
	if x != nil {
		return x.Grooming
	}
	return nil
}

func (x *GetServiceSummaryResult_ServiceSummaryChildren) GetBoarding() *GetServiceSummaryResult_ServiceRow {
	if x != nil {
		return x.Boarding
	}
	return nil
}

func (x *GetServiceSummaryResult_ServiceSummaryChildren) GetDaycare() *GetServiceSummaryResult_ServiceRow {
	if x != nil {
		return x.Daycare
	}
	return nil
}

func (x *GetServiceSummaryResult_ServiceSummaryChildren) GetEvaluation() *GetServiceSummaryResult_ServiceRow {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

func (x *GetServiceSummaryResult_ServiceSummaryChildren) GetDogWalking() *GetServiceSummaryResult_ServiceRow {
	if x != nil {
		return x.DogWalking
	}
	return nil
}

// Row detail
type GetServiceSummaryResult_ServiceRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// in count
	In int32 `protobuf:"varint,1,opt,name=in,proto3" json:"in,omitempty"`
	// out count
	Out int32 `protobuf:"varint,2,opt,name=out,proto3" json:"out,omitempty"`
	// overnight count
	Overnight int32 `protobuf:"varint,3,opt,name=overnight,proto3" json:"overnight,omitempty"`
	// total count
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *GetServiceSummaryResult_ServiceRow) Reset() {
	*x = GetServiceSummaryResult_ServiceRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceSummaryResult_ServiceRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceSummaryResult_ServiceRow) ProtoMessage() {}

func (x *GetServiceSummaryResult_ServiceRow) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceSummaryResult_ServiceRow.ProtoReflect.Descriptor instead.
func (*GetServiceSummaryResult_ServiceRow) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{21, 4}
}

func (x *GetServiceSummaryResult_ServiceRow) GetIn() int32 {
	if x != nil {
		return x.In
	}
	return 0
}

func (x *GetServiceSummaryResult_ServiceRow) GetOut() int32 {
	if x != nil {
		return x.Out
	}
	return 0
}

func (x *GetServiceSummaryResult_ServiceRow) GetOvernight() int32 {
	if x != nil {
		return x.Overnight
	}
	return 0
}

func (x *GetServiceSummaryResult_ServiceRow) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// pet lodging view
type GetAppointmentLodgingResult_PetLodgingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service lodging view list
	ServiceLodgings []*GetAppointmentLodgingResult_ServiceLodgingView `protobuf:"bytes,2,rep,name=service_lodgings,json=serviceLodgings,proto3" json:"service_lodgings,omitempty"`
	// evaluation lodging view list
	EvaluationLodgings []*GetAppointmentLodgingResult_EvaluationLodgingView `protobuf:"bytes,3,rep,name=evaluation_lodgings,json=evaluationLodgings,proto3" json:"evaluation_lodgings,omitempty"`
}

func (x *GetAppointmentLodgingResult_PetLodgingView) Reset() {
	*x = GetAppointmentLodgingResult_PetLodgingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentLodgingResult_PetLodgingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentLodgingResult_PetLodgingView) ProtoMessage() {}

func (x *GetAppointmentLodgingResult_PetLodgingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentLodgingResult_PetLodgingView.ProtoReflect.Descriptor instead.
func (*GetAppointmentLodgingResult_PetLodgingView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{30, 0}
}

func (x *GetAppointmentLodgingResult_PetLodgingView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GetAppointmentLodgingResult_PetLodgingView) GetServiceLodgings() []*GetAppointmentLodgingResult_ServiceLodgingView {
	if x != nil {
		return x.ServiceLodgings
	}
	return nil
}

func (x *GetAppointmentLodgingResult_PetLodgingView) GetEvaluationLodgings() []*GetAppointmentLodgingResult_EvaluationLodgingView {
	if x != nil {
		return x.EvaluationLodgings
	}
	return nil
}

// lodgings info for pet service detail
type GetAppointmentLodgingResult_ServiceLodgingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType v14.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// pet service detail id
	PetServiceDetailId int64 `protobuf:"varint,4,opt,name=pet_service_detail_id,json=petServiceDetailId,proto3" json:"pet_service_detail_id,omitempty"`
	// service start date, in yyyy-MM-dd format
	StartDate string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// service end date, in yyyy-MM-dd format
	EndDate string `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// specific dates, yyyy-MM-dd
	SpecificDates []string `protobuf:"bytes,7,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// lodging unit id
	LodgingUnitId int64 `protobuf:"varint,8,opt,name=lodging_unit_id,json=lodgingUnitId,proto3" json:"lodging_unit_id,omitempty"`
	// lodging unit name
	LodgingUnitName string `protobuf:"bytes,9,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) Reset() {
	*x = GetAppointmentLodgingResult_ServiceLodgingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentLodgingResult_ServiceLodgingView) ProtoMessage() {}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentLodgingResult_ServiceLodgingView.ProtoReflect.Descriptor instead.
func (*GetAppointmentLodgingResult_ServiceLodgingView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{30, 1}
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) GetServiceItemType() v14.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v14.ServiceItemType(0)
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) GetPetServiceDetailId() int64 {
	if x != nil {
		return x.PetServiceDetailId
	}
	return 0
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) GetLodgingUnitId() int64 {
	if x != nil {
		return x.LodgingUnitId
	}
	return 0
}

func (x *GetAppointmentLodgingResult_ServiceLodgingView) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

// lodgings info for pet evaluation detail
type GetAppointmentLodgingResult_EvaluationLodgingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType v14.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// evaluation service id
	EvaluationId int64 `protobuf:"varint,2,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
	// evaluation service name
	EvaluationName string `protobuf:"bytes,3,opt,name=evaluation_name,json=evaluationName,proto3" json:"evaluation_name,omitempty"`
	// pet evaluation detail id
	PetServiceEvaluationId int64 `protobuf:"varint,4,opt,name=pet_service_evaluation_id,json=petServiceEvaluationId,proto3" json:"pet_service_evaluation_id,omitempty"`
	// service start date, in yyyy-MM-dd format
	StartDate string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// service end date, in yyyy-MM-dd format
	EndDate string `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// lodging unit id
	LodgingUnitId int64 `protobuf:"varint,7,opt,name=lodging_unit_id,json=lodgingUnitId,proto3" json:"lodging_unit_id,omitempty"`
	// lodging unit name
	LodgingUnitName string `protobuf:"bytes,8,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) Reset() {
	*x = GetAppointmentLodgingResult_EvaluationLodgingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentLodgingResult_EvaluationLodgingView) ProtoMessage() {}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentLodgingResult_EvaluationLodgingView.ProtoReflect.Descriptor instead.
func (*GetAppointmentLodgingResult_EvaluationLodgingView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{30, 2}
}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) GetServiceItemType() v14.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v14.ServiceItemType(0)
}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) GetEvaluationName() string {
	if x != nil {
		return x.EvaluationName
	}
	return ""
}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) GetPetServiceEvaluationId() int64 {
	if x != nil {
		return x.PetServiceEvaluationId
	}
	return 0
}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) GetLodgingUnitId() int64 {
	if x != nil {
		return x.LodgingUnitId
	}
	return 0
}

func (x *GetAppointmentLodgingResult_EvaluationLodgingView) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

// Invoice detail view
type ListStaffAppointmentsResult_InvoiceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// net sale
	NetSale *money.Money `protobuf:"bytes,1,opt,name=net_sale,json=netSale,proto3" json:"net_sale,omitempty"`
	// tips
	Tips *money.Money `protobuf:"bytes,2,opt,name=tips,proto3" json:"tips,omitempty"`
}

func (x *ListStaffAppointmentsResult_InvoiceView) Reset() {
	*x = ListStaffAppointmentsResult_InvoiceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentsResult_InvoiceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentsResult_InvoiceView) ProtoMessage() {}

func (x *ListStaffAppointmentsResult_InvoiceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentsResult_InvoiceView.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentsResult_InvoiceView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{34, 0}
}

func (x *ListStaffAppointmentsResult_InvoiceView) GetNetSale() *money.Money {
	if x != nil {
		return x.NetSale
	}
	return nil
}

func (x *ListStaffAppointmentsResult_InvoiceView) GetTips() *money.Money {
	if x != nil {
		return x.Tips
	}
	return nil
}

// Customer detail view
type ListStaffAppointmentsResult_CustomerView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer profile
	CustomerProfile *v16.BusinessCustomerInfoModel `protobuf:"bytes,1,opt,name=customer_profile,json=customerProfile,proto3" json:"customer_profile,omitempty"`
	// is new customer
	IsNewCustomer bool `protobuf:"varint,2,opt,name=is_new_customer,json=isNewCustomer,proto3" json:"is_new_customer,omitempty"`
	// customer address
	CustomerAddress *v16.BusinessCustomerAddressModel `protobuf:"bytes,3,opt,name=customer_address,json=customerAddress,proto3,oneof" json:"customer_address,omitempty"`
}

func (x *ListStaffAppointmentsResult_CustomerView) Reset() {
	*x = ListStaffAppointmentsResult_CustomerView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentsResult_CustomerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentsResult_CustomerView) ProtoMessage() {}

func (x *ListStaffAppointmentsResult_CustomerView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentsResult_CustomerView.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentsResult_CustomerView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{34, 1}
}

func (x *ListStaffAppointmentsResult_CustomerView) GetCustomerProfile() *v16.BusinessCustomerInfoModel {
	if x != nil {
		return x.CustomerProfile
	}
	return nil
}

func (x *ListStaffAppointmentsResult_CustomerView) GetIsNewCustomer() bool {
	if x != nil {
		return x.IsNewCustomer
	}
	return false
}

func (x *ListStaffAppointmentsResult_CustomerView) GetCustomerAddress() *v16.BusinessCustomerAddressModel {
	if x != nil {
		return x.CustomerAddress
	}
	return nil
}

// Appointment detail view
type ListStaffAppointmentsResult_AppointmentDetailView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment
	Appointment *v1.AppointmentModel `protobuf:"bytes,1,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// service detail
	ServiceDetail []*ServiceDetail `protobuf:"bytes,2,rep,name=service_detail,json=serviceDetail,proto3" json:"service_detail,omitempty"`
	// invoice amount detail
	Invoice *ListStaffAppointmentsResult_InvoiceView `protobuf:"bytes,3,opt,name=invoice,proto3" json:"invoice,omitempty"`
	// customer detail
	Customer *ListStaffAppointmentsResult_CustomerView `protobuf:"bytes,4,opt,name=customer,proto3" json:"customer,omitempty"`
	// include service item types
	ServiceItemTypes []v14.ServiceItemType `protobuf:"varint,5,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
}

func (x *ListStaffAppointmentsResult_AppointmentDetailView) Reset() {
	*x = ListStaffAppointmentsResult_AppointmentDetailView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentsResult_AppointmentDetailView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentsResult_AppointmentDetailView) ProtoMessage() {}

func (x *ListStaffAppointmentsResult_AppointmentDetailView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_api_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentsResult_AppointmentDetailView.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentsResult_AppointmentDetailView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP(), []int{34, 2}
}

func (x *ListStaffAppointmentsResult_AppointmentDetailView) GetAppointment() *v1.AppointmentModel {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *ListStaffAppointmentsResult_AppointmentDetailView) GetServiceDetail() []*ServiceDetail {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *ListStaffAppointmentsResult_AppointmentDetailView) GetInvoice() *ListStaffAppointmentsResult_InvoiceView {
	if x != nil {
		return x.Invoice
	}
	return nil
}

func (x *ListStaffAppointmentsResult_AppointmentDetailView) GetCustomer() *ListStaffAppointmentsResult_CustomerView {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *ListStaffAppointmentsResult_AppointmentDetailView) GetServiceItemTypes() []v14.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

var File_moego_api_appointment_v1_appointment_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_appointment_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75,
	0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x48, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x61,
	0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x46, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x2f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x65, 0x5f, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xef,
	0x06, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x5d, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0x64, 0x22,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x53, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x08, 0x01, 0x48, 0x00, 0x52, 0x07, 0x70, 0x72, 0x65,
	0x41, 0x75, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x5e, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x6f, 0x74, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa,
	0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x6f, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x62,
	0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x67, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x42, 0x65,
	0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x02, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x1a, 0xb6, 0x01, 0x0a,
	0x0c, 0x50, 0x65, 0x74, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x0a,
	0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x08, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x04, 0x61, 0x72, 0x65, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x18, 0x64, 0x48, 0x00, 0x52, 0x04, 0x61, 0x72, 0x65, 0x61, 0x88, 0x01, 0x01, 0x12,
	0x2d, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x18, 0x80, 0x08, 0x88, 0x01, 0x01, 0x48,
	0x01, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x68, 0x6f, 0x74,
	0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x22, 0x40, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x5d,
	0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x19, 0x0a,
	0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x46, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0xcc, 0x0b, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x56, 0x0a, 0x0b, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x4e, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x47, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x08, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x5c, 0x0a,
	0x0f, 0x6e, 0x6f, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4e,
	0x6f, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x6e, 0x6f,
	0x53, 0x68, 0x6f, 0x77, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x07, 0x70,
	0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x50, 0x61, 0x79, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x70, 0x72, 0x65, 0x50, 0x61,
	0x79, 0x12, 0x47, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x65, 0x41, 0x75, 0x74, 0x68, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x07, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x12, 0x54, 0x0a, 0x0b, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75,
	0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x12, 0x4e, 0x0a, 0x09, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x77, 0x61, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x40, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x73, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x76, 0x0a, 0x18, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x17, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x75, 0x0a, 0x17, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x0e,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x76, 0x0a, 0x1a, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x5f, 0x6c, 0x6f, 0x67, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67,
	0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x16, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x73,
	0x56, 0x32, 0x12, 0x48, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x51, 0x0a, 0x0f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22,
	0x87, 0x03, 0x0a, 0x0e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x3b, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0e, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x3d, 0x0a, 0x10, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39,
	0x0a, 0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x18, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x16, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x73, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x12, 0x46, 0x0a, 0x15, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54,
	0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc2, 0x04, 0x0a, 0x0d, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4d, 0x0a, 0x03, 0x70,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x46, 0x0a, 0x08, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x12, 0x41, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x64, 0x64, 0x4f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x52, 0x06, 0x61,
	0x64, 0x64, 0x4f, 0x6e, 0x73, 0x12, 0x54, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x08, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x08, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x52, 0x08, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x65, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x5e,
	0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0e,
	0x70, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xd3,
	0x02, 0x0a, 0x10, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x65, 0x12, 0x57, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x52, 0x0d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x52, 0x0a, 0x0a,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x61, 0x0a, 0x0e, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x70, 0x6c, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x66,
	0x72, 0x65, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x69, 0x73, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x22, 0xbd, 0x01, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x12, 0x57, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x65, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x52, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0xf3, 0x0d, 0x0a, 0x16, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x54, 0x0a, 0x10, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x53, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a,
	0x12, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x32,
	0x0a, 0x15, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x61,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x23, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x25, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d,
	0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x09, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x62, 0x0a, 0x13,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x01, 0x52, 0x11, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x68, 0x0a, 0x16, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x02, 0x52, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x10, 0x71, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x29,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x65,
	0x72, 0x44, 0x61, 0x79, 0x12, 0x2b, 0x0a, 0x12, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x49,
	0x64, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x2b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42,
	0x19, 0x0a, 0x17, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x93, 0x05, 0x0a, 0x11, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65,
	0x12, 0x63, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x60, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x54, 0x61, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x73, 0x12, 0x6a, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73,
	0x4e, 0x65, 0x77, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x69, 0x67, 0x6e,
	0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74,
	0x65, 0x72, 0x5f, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x53, 0x65, 0x6e, 0x74,
	0x12, 0x5e, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x6e,
	0x6f, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x0d,
	0x6c, 0x61, 0x73, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x5a, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x42, 0x12, 0x0a, 0x10,
	0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65,
	0x22, 0xe1, 0x01, 0x0a, 0x13, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x22, 0xac, 0x06, 0x0a, 0x1a, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x55, 0x0a, 0x11,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x11, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55,
	0x6e, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x11, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x04, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x2b, 0x0a, 0x12, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x14,
	0x0a, 0x12, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0xa5, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x3e, 0x0a, 0x14, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x12, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x98, 0x06, 0x0a, 0x20,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x30, 0x0a, 0x14, 0x68, 0x61, 0x73, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12,
	0x68, 0x61, 0x73, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x47, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x65, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x56, 0x0a, 0x0b, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x47, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x57,
	0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x75, 0x0a, 0x17, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x6c, 0x6f,
	0x67, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72,
	0x56, 0x69, 0x65, 0x77, 0x42, 0x02, 0x18, 0x01, 0x52, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x76,
	0x0a, 0x1a, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x0f, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x4c, 0x6f, 0x67, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x16,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x4c, 0x6f, 0x67, 0x73, 0x56, 0x32, 0x22, 0x82, 0x01, 0x0a, 0x21, 0x43, 0x61, 0x6c, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5d, 0x0a, 0x0b,
	0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42,
	0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x5a, 0x0a, 0x21, 0x43,
	0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x35, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x66,
	0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xd8, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x01, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x05, 0x70,
	0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x22, 0x69, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2a,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x22, 0xb5, 0x01,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13,
	0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x35,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d,
	0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0xfd, 0x09, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x78, 0x0a, 0x14, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x42, 0x79, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x12, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x60, 0x0a, 0x0c, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x42, 0x79, 0x50, 0x65,
	0x74, 0x52, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x50, 0x65, 0x74, 0x1a, 0x6f, 0x0a,
	0x14, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x42, 0x79, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x57, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x6f, 0x77, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x1a, 0x67,
	0x0a, 0x0c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x42, 0x79, 0x50, 0x65, 0x74, 0x12, 0x57,
	0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x6f,
	0x77, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x1a, 0xe3, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x6f, 0x77, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69,
	0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x6f, 0x75, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x6e, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x6e, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x64, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64,
	0x72, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x68, 0x69, 0x6c, 0x64,
	0x72, 0x65, 0x6e, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x1a, 0xe1, 0x03,
	0x0a, 0x16, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x43, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x12, 0x58, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x12, 0x58, 0x0a, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x6f, 0x77, 0x52, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x56, 0x0a, 0x07,
	0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x07, 0x64, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x12, 0x5c, 0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x5d, 0x0a, 0x0b, 0x64, 0x6f, 0x67, 0x5f, 0x77, 0x61, 0x6c, 0x6b, 0x69, 0x6e,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x0a, 0x64, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e,
	0x67, 0x1a, 0x62, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x6f, 0x77, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6f, 0x75,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x6f, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x61, 0x79, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64,
	0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22, 0xe3, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x61,
	0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x67, 0x0a, 0x17, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x79, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x15, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x61, 0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x4f, 0x0a, 0x0f, 0x70,
	0x65, 0x74, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x44, 0x61, 0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x0d, 0x70,
	0x65, 0x74, 0x44, 0x61, 0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0xa6, 0x01, 0x0a,
	0x15, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x79, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x48, 0x0a, 0x0c, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0b, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x43, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x72, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x72,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x9e, 0x01, 0x0a, 0x0d, 0x50, 0x65, 0x74, 0x44, 0x61, 0x79,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x48, 0x0a, 0x0c, 0x64, 0x61, 0x69, 0x6c, 0x79,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x43, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x72, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61,
	0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x63, 0x0a, 0x0b, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x6f, 0x75, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x6e,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6f, 0x76, 0x65, 0x72,
	0x6e, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x82, 0x01, 0x0a, 0x0c,
	0x43, 0x61, 0x72, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x48, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x37, 0x0a, 0x0b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x36, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x22, 0xcc, 0x09, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x70, 0x0a, 0x11, 0x70, 0x65, 0x74, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x0f, 0x70, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x56, 0x69,
	0x65, 0x77, 0x73, 0x1a, 0x9a, 0x02, 0x0a, 0x0e, 0x50, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x73, 0x0a,
	0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x73, 0x12, 0x7c, 0x0a, 0x13, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x12, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73,
	0x1a, 0x95, 0x03, 0x0a, 0x12, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x31, 0x0a, 0x15, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x12, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44,
	0x61, 0x74, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x55, 0x6e, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x85, 0x03, 0x0a, 0x15, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x27,
	0x0a, 0x0f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x70, 0x65, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x16, 0x70, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e,
	0x69, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x9d, 0x02, 0x0a, 0x17, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2b,
	0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x12, 0xfa, 0x42, 0x0f, 0x92, 0x01, 0x0c, 0x08, 0x01, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2f, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x52, 0x0a, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0x89, 0x01, 0x0a, 0x17, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x36, 0x0a, 0x17,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x15, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x15, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xb9, 0x03, 0x0a,
	0x1b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x37, 0x0a, 0x06,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x70,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0e, 0xfa, 0x42, 0x0b,
	0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x32, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x19, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x17, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x48, 0x02, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xb1, 0x08, 0x0a, 0x1b, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6f, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x64, 0x0a,
	0x0b, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x2d, 0x0a, 0x08,
	0x6e, 0x65, 0x74, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x74,
	0x69, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x04, 0x74,
	0x69, 0x70, 0x73, 0x1a, 0xa5, 0x02, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x67, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x6f, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x48, 0x00, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x1a, 0xce, 0x03, 0x0a, 0x15,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x56, 0x69, 0x65, 0x77, 0x12, 0x4f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x5b, 0x0a, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x49,
	0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x69, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x12, 0x5e, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xd7, 0x01, 0x0a,
	0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x41, 0x67, 0x61, 0x69, 0x6e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x32,
	0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x32, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0x74, 0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42,
	0x6f, 0x6f, 0x6b, 0x41, 0x67, 0x61, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x0c, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xd9, 0x01, 0x0a,
	0x1c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x32, 0x0a, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0x80, 0x08, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x71, 0x0a, 0x1c, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xe9, 0x01, 0x0a, 0x23,
	0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11,
	0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32,
	0x7d, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x38, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5c, 0x64, 0x7b, 0x34,
	0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x48, 0x01, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x25, 0x0a, 0x23, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0x90,
	0x0f, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x79, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x79, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x94, 0x01,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c,
	0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x97, 0x01, 0x0a, 0x1b, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xac,
	0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x44, 0x61, 0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x79,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x79, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x79, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x79, 0x0a, 0x11, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x6e, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x91, 0x01, 0x0a, 0x19, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b,
	0x41, 0x67, 0x61, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x41, 0x67, 0x61, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b,
	0x41, 0x67, 0x61, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x9d, 0x01, 0x0a, 0x1d, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x84, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_appointment_v1_appointment_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_appointment_api_proto_rawDescData = file_moego_api_appointment_v1_appointment_api_proto_rawDesc
)

func file_moego_api_appointment_v1_appointment_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_appointment_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_appointment_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_appointment_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_appointment_api_proto_rawDescData
}

var file_moego_api_appointment_v1_appointment_api_proto_msgTypes = make([]protoimpl.MessageInfo, 53)
var file_moego_api_appointment_v1_appointment_api_proto_goTypes = []interface{}{
	(*CreateAppointmentParams)(nil),                           // 0: moego.api.appointment.v1.CreateAppointmentParams
	(*CreateAppointmentResult)(nil),                           // 1: moego.api.appointment.v1.CreateAppointmentResult
	(*UpdateAppointmentParams)(nil),                           // 2: moego.api.appointment.v1.UpdateAppointmentParams
	(*UpdateAppointmentResult)(nil),                           // 3: moego.api.appointment.v1.UpdateAppointmentResult
	(*GetAppointmentParams)(nil),                              // 4: moego.api.appointment.v1.GetAppointmentParams
	(*GetAppointmentResult)(nil),                              // 5: moego.api.appointment.v1.GetAppointmentResult
	(*PaymentSummary)(nil),                                    // 6: moego.api.appointment.v1.PaymentSummary
	(*ServiceDetail)(nil),                                     // 7: moego.api.appointment.v1.ServiceDetail
	(*ServiceComposite)(nil),                                  // 8: moego.api.appointment.v1.ServiceComposite
	(*AddOnComposite)(nil),                                    // 9: moego.api.appointment.v1.AddOnComposite
	(*ServiceDetailComposite)(nil),                            // 10: moego.api.appointment.v1.ServiceDetailComposite
	(*CustomerComposite)(nil),                                 // 11: moego.api.appointment.v1.CustomerComposite
	(*PetVaccineComposite)(nil),                               // 12: moego.api.appointment.v1.PetVaccineComposite
	(*EvaluationServiceComposite)(nil),                        // 13: moego.api.appointment.v1.EvaluationServiceComposite
	(*GetCustomerLastAppointmentParams)(nil),                  // 14: moego.api.appointment.v1.GetCustomerLastAppointmentParams
	(*GetCustomerLastAppointmentResult)(nil),                  // 15: moego.api.appointment.v1.GetCustomerLastAppointmentResult
	(*CalculateAppointmentInvoiceParams)(nil),                 // 16: moego.api.appointment.v1.CalculateAppointmentInvoiceParams
	(*CalculateAppointmentInvoiceResult)(nil),                 // 17: moego.api.appointment.v1.CalculateAppointmentInvoiceResult
	(*GetInProgressEvaluationAppointmentParams)(nil),          // 18: moego.api.appointment.v1.GetInProgressEvaluationAppointmentParams
	(*GetInProgressEvaluationAppointmentResult)(nil),          // 19: moego.api.appointment.v1.GetInProgressEvaluationAppointmentResult
	(*GetServiceSummaryParams)(nil),                           // 20: moego.api.appointment.v1.GetServiceSummaryParams
	(*GetServiceSummaryResult)(nil),                           // 21: moego.api.appointment.v1.GetServiceSummaryResult
	(*GetDaySummaryParams)(nil),                               // 22: moego.api.appointment.v1.GetDaySummaryParams
	(*GetDaySummaryResult)(nil),                               // 23: moego.api.appointment.v1.GetDaySummaryResult
	(*AppointmentDaySummary)(nil),                             // 24: moego.api.appointment.v1.AppointmentDaySummary
	(*PetDaySummary)(nil),                                     // 25: moego.api.appointment.v1.PetDaySummary
	(*DailyStatus)(nil),                                       // 26: moego.api.appointment.v1.DailyStatus
	(*CareCategory)(nil),                                      // 27: moego.api.appointment.v1.CareCategory
	(*ServiceItem)(nil),                                       // 28: moego.api.appointment.v1.ServiceItem
	(*GetAppointmentLodgingParams)(nil),                       // 29: moego.api.appointment.v1.GetAppointmentLodgingParams
	(*GetAppointmentLodgingResult)(nil),                       // 30: moego.api.appointment.v1.GetAppointmentLodgingResult
	(*BatchQuickCheckInParams)(nil),                           // 31: moego.api.appointment.v1.BatchQuickCheckInParams
	(*BatchQuickCheckInResult)(nil),                           // 32: moego.api.appointment.v1.BatchQuickCheckInResult
	(*ListStaffAppointmentsParams)(nil),                       // 33: moego.api.appointment.v1.ListStaffAppointmentsParams
	(*ListStaffAppointmentsResult)(nil),                       // 34: moego.api.appointment.v1.ListStaffAppointmentsResult
	(*BatchBookAgainAppointmentParams)(nil),                   // 35: moego.api.appointment.v1.BatchBookAgainAppointmentParams
	(*BatchBookAgainAppointmentResult)(nil),                   // 36: moego.api.appointment.v1.BatchBookAgainAppointmentResult
	(*BatchCancelAppointmentParams)(nil),                      // 37: moego.api.appointment.v1.BatchCancelAppointmentParams
	(*BatchCancelAppointmentResult)(nil),                      // 38: moego.api.appointment.v1.BatchCancelAppointmentResult
	(*RescheduleBoardingAppointmentParams)(nil),               // 39: moego.api.appointment.v1.RescheduleBoardingAppointmentParams
	(*RescheduleBoardingAppointmentResult)(nil),               // 40: moego.api.appointment.v1.RescheduleBoardingAppointmentResult
	(*CreateAppointmentParams_PetBelonging)(nil),              // 41: moego.api.appointment.v1.CreateAppointmentParams.PetBelonging
	(*GetServiceSummaryResult_CountedByAppointment)(nil),      // 42: moego.api.appointment.v1.GetServiceSummaryResult.CountedByAppointment
	(*GetServiceSummaryResult_CountedByPet)(nil),              // 43: moego.api.appointment.v1.GetServiceSummaryResult.CountedByPet
	(*GetServiceSummaryResult_ServiceSummaryRow)(nil),         // 44: moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryRow
	(*GetServiceSummaryResult_ServiceSummaryChildren)(nil),    // 45: moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryChildren
	(*GetServiceSummaryResult_ServiceRow)(nil),                // 46: moego.api.appointment.v1.GetServiceSummaryResult.ServiceRow
	(*GetAppointmentLodgingResult_PetLodgingView)(nil),        // 47: moego.api.appointment.v1.GetAppointmentLodgingResult.PetLodgingView
	(*GetAppointmentLodgingResult_ServiceLodgingView)(nil),    // 48: moego.api.appointment.v1.GetAppointmentLodgingResult.ServiceLodgingView
	(*GetAppointmentLodgingResult_EvaluationLodgingView)(nil), // 49: moego.api.appointment.v1.GetAppointmentLodgingResult.EvaluationLodgingView
	(*ListStaffAppointmentsResult_InvoiceView)(nil),           // 50: moego.api.appointment.v1.ListStaffAppointmentsResult.InvoiceView
	(*ListStaffAppointmentsResult_CustomerView)(nil),          // 51: moego.api.appointment.v1.ListStaffAppointmentsResult.CustomerView
	(*ListStaffAppointmentsResult_AppointmentDetailView)(nil), // 52: moego.api.appointment.v1.ListStaffAppointmentsResult.AppointmentDetailView
	(*v1.AppointmentCreateDef)(nil),                           // 53: moego.models.appointment.v1.AppointmentCreateDef
	(*v1.PetDetailDef)(nil),                                   // 54: moego.models.appointment.v1.PetDetailDef
	(*v11.PreAuthEnableDef)(nil),                              // 55: moego.models.payment.v1.PreAuthEnableDef
	(*v1.AppointmentNoteCreateDef)(nil),                       // 56: moego.models.appointment.v1.AppointmentNoteCreateDef
	(*timestamppb.Timestamp)(nil),                             // 57: google.protobuf.Timestamp
	(*v1.AppointmentUpdateDef)(nil),                           // 58: moego.models.appointment.v1.AppointmentUpdateDef
	(*v1.AppointmentCalendarView)(nil),                        // 59: moego.models.appointment.v1.AppointmentCalendarView
	(*v1.AppointmentNoteModel)(nil),                           // 60: moego.models.appointment.v1.AppointmentNoteModel
	(*v12.InvoiceCalendarView)(nil),                           // 61: moego.models.order.v1.InvoiceCalendarView
	(*v12.NoShowInvoiceCalendarView)(nil),                     // 62: moego.models.order.v1.NoShowInvoiceCalendarView
	(*v11.PrePayCalendarView)(nil),                            // 63: moego.models.payment.v1.PrePayCalendarView
	(*v11.PreAuthCalendarView)(nil),                           // 64: moego.models.payment.v1.PreAuthCalendarView
	(*v1.AutoAssignCalendarView)(nil),                         // 65: moego.models.appointment.v1.AutoAssignCalendarView
	(*v1.WaitListCalendarView)(nil),                           // 66: moego.models.appointment.v1.WaitListCalendarView
	(*v13.StaffModel)(nil),                                    // 67: moego.models.organization.v1.StaffModel
	(v14.ServiceItemType)(0),                                  // 68: moego.models.offering.v1.ServiceItemType
	(*v15.MembershipSubscriptionListModel)(nil),               // 69: moego.models.membership.v1.MembershipSubscriptionListModel
	(*v1.PricingRuleApplyLogDrawerView)(nil),                  // 70: moego.models.appointment.v1.PricingRuleApplyLogDrawerView
	(*v2.PricingRuleApplyLogDrawerView)(nil),                  // 71: moego.models.appointment.v2.PricingRuleApplyLogDrawerView
	(*v12.OrderModelAppointmentView)(nil),                     // 72: moego.models.order.v1.OrderModelAppointmentView
	(*money.Money)(nil),                                       // 73: google.type.Money
	(*v16.BusinessCustomerPetModel)(nil),                      // 74: moego.models.business_customer.v1.BusinessCustomerPetModel
	(*v16.BusinessPetCodeModel)(nil),                          // 75: moego.models.business_customer.v1.BusinessPetCodeModel
	(*v16.PetEvaluationModel)(nil),                            // 76: moego.models.business_customer.v1.PetEvaluationModel
	(*v1.ServiceOperationModel)(nil),                          // 77: moego.models.appointment.v1.ServiceOperationModel
	(*v1.BoardingSplitLodgingDetailDef)(nil),                  // 78: moego.models.appointment.v1.BoardingSplitLodgingDetailDef
	(v14.ServiceType)(0),                                      // 79: moego.models.offering.v1.ServiceType
	(v14.ServiceScopeType)(0),                                 // 80: moego.models.offering.v1.ServiceScopeType
	(v1.WorkMode)(0),                                          // 81: moego.models.appointment.v1.WorkMode
	(v1.PetDetailDateType)(0),                                 // 82: moego.models.appointment.v1.PetDetailDateType
	(v14.ServiceOverrideType)(0),                              // 83: moego.models.offering.v1.ServiceOverrideType
	(*v16.BusinessCustomerModel)(nil),                         // 84: moego.models.business_customer.v1.BusinessCustomerModel
	(*v16.BusinessCustomerTagModel)(nil),                      // 85: moego.models.business_customer.v1.BusinessCustomerTagModel
	(*v16.BusinessCustomerAddressModel)(nil),                  // 86: moego.models.business_customer.v1.BusinessCustomerAddressModel
	(*CustomerPackageView)(nil),                               // 87: moego.api.appointment.v1.CustomerPackageView
	(*date.Date)(nil),                                         // 88: google.type.Date
	(*v12.OrderDef)(nil),                                      // 89: moego.models.order.v1.OrderDef
	(v1.AppointmentSource)(0),                                 // 90: moego.models.appointment.v1.AppointmentSource
	(*interval.Interval)(nil),                                 // 91: google.type.Interval
	(*v21.PaginationRequest)(nil),                             // 92: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),                            // 93: moego.utils.v2.PaginationResponse
	(*v1.AppointmentModel)(nil),                               // 94: moego.models.appointment.v1.AppointmentModel
	(*v16.BusinessCustomerInfoModel)(nil),                     // 95: moego.models.business_customer.v1.BusinessCustomerInfoModel
}
var file_moego_api_appointment_v1_appointment_api_proto_depIdxs = []int32{
	53,  // 0: moego.api.appointment.v1.CreateAppointmentParams.appointment:type_name -> moego.models.appointment.v1.AppointmentCreateDef
	54,  // 1: moego.api.appointment.v1.CreateAppointmentParams.pet_details:type_name -> moego.models.appointment.v1.PetDetailDef
	55,  // 2: moego.api.appointment.v1.CreateAppointmentParams.pre_auth:type_name -> moego.models.payment.v1.PreAuthEnableDef
	56,  // 3: moego.api.appointment.v1.CreateAppointmentParams.notes:type_name -> moego.models.appointment.v1.AppointmentNoteCreateDef
	41,  // 4: moego.api.appointment.v1.CreateAppointmentParams.pet_belongings:type_name -> moego.api.appointment.v1.CreateAppointmentParams.PetBelonging
	57,  // 5: moego.api.appointment.v1.CreateAppointmentParams.created_at:type_name -> google.protobuf.Timestamp
	58,  // 6: moego.api.appointment.v1.UpdateAppointmentParams.appointment:type_name -> moego.models.appointment.v1.AppointmentUpdateDef
	59,  // 7: moego.api.appointment.v1.GetAppointmentResult.appointment:type_name -> moego.models.appointment.v1.AppointmentCalendarView
	7,   // 8: moego.api.appointment.v1.GetAppointmentResult.service_detail:type_name -> moego.api.appointment.v1.ServiceDetail
	60,  // 9: moego.api.appointment.v1.GetAppointmentResult.notes:type_name -> moego.models.appointment.v1.AppointmentNoteModel
	11,  // 10: moego.api.appointment.v1.GetAppointmentResult.customer:type_name -> moego.api.appointment.v1.CustomerComposite
	61,  // 11: moego.api.appointment.v1.GetAppointmentResult.invoice:type_name -> moego.models.order.v1.InvoiceCalendarView
	62,  // 12: moego.api.appointment.v1.GetAppointmentResult.no_show_invoice:type_name -> moego.models.order.v1.NoShowInvoiceCalendarView
	63,  // 13: moego.api.appointment.v1.GetAppointmentResult.pre_pay:type_name -> moego.models.payment.v1.PrePayCalendarView
	64,  // 14: moego.api.appointment.v1.GetAppointmentResult.pre_auth:type_name -> moego.models.payment.v1.PreAuthCalendarView
	65,  // 15: moego.api.appointment.v1.GetAppointmentResult.auto_assign:type_name -> moego.models.appointment.v1.AutoAssignCalendarView
	66,  // 16: moego.api.appointment.v1.GetAppointmentResult.wait_list:type_name -> moego.models.appointment.v1.WaitListCalendarView
	67,  // 17: moego.api.appointment.v1.GetAppointmentResult.staffs:type_name -> moego.models.organization.v1.StaffModel
	68,  // 18: moego.api.appointment.v1.GetAppointmentResult.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	69,  // 19: moego.api.appointment.v1.GetAppointmentResult.membership_subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionListModel
	70,  // 20: moego.api.appointment.v1.GetAppointmentResult.pricing_rule_apply_logs:type_name -> moego.models.appointment.v1.PricingRuleApplyLogDrawerView
	71,  // 21: moego.api.appointment.v1.GetAppointmentResult.pricing_rule_apply_logs_v2:type_name -> moego.models.appointment.v2.PricingRuleApplyLogDrawerView
	72,  // 22: moego.api.appointment.v1.GetAppointmentResult.orders:type_name -> moego.models.order.v1.OrderModelAppointmentView
	6,   // 23: moego.api.appointment.v1.GetAppointmentResult.payment_summary:type_name -> moego.api.appointment.v1.PaymentSummary
	73,  // 24: moego.api.appointment.v1.PaymentSummary.subtotal_amount:type_name -> google.type.Money
	73,  // 25: moego.api.appointment.v1.PaymentSummary.collected_amount:type_name -> google.type.Money
	73,  // 26: moego.api.appointment.v1.PaymentSummary.deposit_amount:type_name -> google.type.Money
	73,  // 27: moego.api.appointment.v1.PaymentSummary.collected_deposit_amount:type_name -> google.type.Money
	73,  // 28: moego.api.appointment.v1.PaymentSummary.collected_tips_amount:type_name -> google.type.Money
	74,  // 29: moego.api.appointment.v1.ServiceDetail.pet:type_name -> moego.models.business_customer.v1.BusinessCustomerPetModel
	8,   // 30: moego.api.appointment.v1.ServiceDetail.services:type_name -> moego.api.appointment.v1.ServiceComposite
	9,   // 31: moego.api.appointment.v1.ServiceDetail.add_ons:type_name -> moego.api.appointment.v1.AddOnComposite
	75,  // 32: moego.api.appointment.v1.ServiceDetail.pet_codes:type_name -> moego.models.business_customer.v1.BusinessPetCodeModel
	12,  // 33: moego.api.appointment.v1.ServiceDetail.vaccines:type_name -> moego.api.appointment.v1.PetVaccineComposite
	13,  // 34: moego.api.appointment.v1.ServiceDetail.evaluations:type_name -> moego.api.appointment.v1.EvaluationServiceComposite
	76,  // 35: moego.api.appointment.v1.ServiceDetail.pet_evaluations:type_name -> moego.models.business_customer.v1.PetEvaluationModel
	10,  // 36: moego.api.appointment.v1.ServiceComposite.service_detail:type_name -> moego.api.appointment.v1.ServiceDetailComposite
	77,  // 37: moego.api.appointment.v1.ServiceComposite.operations:type_name -> moego.models.appointment.v1.ServiceOperationModel
	78,  // 38: moego.api.appointment.v1.ServiceComposite.split_lodgings:type_name -> moego.models.appointment.v1.BoardingSplitLodgingDetailDef
	10,  // 39: moego.api.appointment.v1.AddOnComposite.service_detail:type_name -> moego.api.appointment.v1.ServiceDetailComposite
	77,  // 40: moego.api.appointment.v1.AddOnComposite.operations:type_name -> moego.models.appointment.v1.ServiceOperationModel
	79,  // 41: moego.api.appointment.v1.ServiceDetailComposite.service_type:type_name -> moego.models.offering.v1.ServiceType
	80,  // 42: moego.api.appointment.v1.ServiceDetailComposite.scope_type_price:type_name -> moego.models.offering.v1.ServiceScopeType
	80,  // 43: moego.api.appointment.v1.ServiceDetailComposite.scope_type_time:type_name -> moego.models.offering.v1.ServiceScopeType
	81,  // 44: moego.api.appointment.v1.ServiceDetailComposite.work_mode:type_name -> moego.models.appointment.v1.WorkMode
	68,  // 45: moego.api.appointment.v1.ServiceDetailComposite.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	82,  // 46: moego.api.appointment.v1.ServiceDetailComposite.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	83,  // 47: moego.api.appointment.v1.ServiceDetailComposite.price_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	83,  // 48: moego.api.appointment.v1.ServiceDetailComposite.duration_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	84,  // 49: moego.api.appointment.v1.CustomerComposite.customer_profile:type_name -> moego.models.business_customer.v1.BusinessCustomerModel
	85,  // 50: moego.api.appointment.v1.CustomerComposite.customer_tags:type_name -> moego.models.business_customer.v1.BusinessCustomerTagModel
	86,  // 51: moego.api.appointment.v1.CustomerComposite.customer_address:type_name -> moego.models.business_customer.v1.BusinessCustomerAddressModel
	60,  // 52: moego.api.appointment.v1.CustomerComposite.last_alert_note:type_name -> moego.models.appointment.v1.AppointmentNoteModel
	87,  // 53: moego.api.appointment.v1.CustomerComposite.customer_packages:type_name -> moego.api.appointment.v1.CustomerPackageView
	88,  // 54: moego.api.appointment.v1.PetVaccineComposite.expiration_date:type_name -> google.type.Date
	68,  // 55: moego.api.appointment.v1.EvaluationServiceComposite.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	11,  // 56: moego.api.appointment.v1.GetCustomerLastAppointmentResult.customer:type_name -> moego.api.appointment.v1.CustomerComposite
	59,  // 57: moego.api.appointment.v1.GetCustomerLastAppointmentResult.appointment:type_name -> moego.models.appointment.v1.AppointmentCalendarView
	7,   // 58: moego.api.appointment.v1.GetCustomerLastAppointmentResult.service_detail:type_name -> moego.api.appointment.v1.ServiceDetail
	60,  // 59: moego.api.appointment.v1.GetCustomerLastAppointmentResult.notes:type_name -> moego.models.appointment.v1.AppointmentNoteModel
	67,  // 60: moego.api.appointment.v1.GetCustomerLastAppointmentResult.staffs:type_name -> moego.models.organization.v1.StaffModel
	68,  // 61: moego.api.appointment.v1.GetCustomerLastAppointmentResult.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	70,  // 62: moego.api.appointment.v1.GetCustomerLastAppointmentResult.pricing_rule_apply_logs:type_name -> moego.models.appointment.v1.PricingRuleApplyLogDrawerView
	71,  // 63: moego.api.appointment.v1.GetCustomerLastAppointmentResult.pricing_rule_apply_logs_v2:type_name -> moego.models.appointment.v2.PricingRuleApplyLogDrawerView
	54,  // 64: moego.api.appointment.v1.CalculateAppointmentInvoiceParams.pet_details:type_name -> moego.models.appointment.v1.PetDetailDef
	89,  // 65: moego.api.appointment.v1.CalculateAppointmentInvoiceResult.order:type_name -> moego.models.order.v1.OrderDef
	42,  // 66: moego.api.appointment.v1.GetServiceSummaryResult.count_by_appointment:type_name -> moego.api.appointment.v1.GetServiceSummaryResult.CountedByAppointment
	43,  // 67: moego.api.appointment.v1.GetServiceSummaryResult.count_by_pet:type_name -> moego.api.appointment.v1.GetServiceSummaryResult.CountedByPet
	24,  // 68: moego.api.appointment.v1.GetDaySummaryResult.appointment_day_summary:type_name -> moego.api.appointment.v1.AppointmentDaySummary
	25,  // 69: moego.api.appointment.v1.GetDaySummaryResult.pet_day_summary:type_name -> moego.api.appointment.v1.PetDaySummary
	26,  // 70: moego.api.appointment.v1.AppointmentDaySummary.daily_status:type_name -> moego.api.appointment.v1.DailyStatus
	27,  // 71: moego.api.appointment.v1.AppointmentDaySummary.care_list:type_name -> moego.api.appointment.v1.CareCategory
	26,  // 72: moego.api.appointment.v1.PetDaySummary.daily_status:type_name -> moego.api.appointment.v1.DailyStatus
	27,  // 73: moego.api.appointment.v1.PetDaySummary.care_list:type_name -> moego.api.appointment.v1.CareCategory
	28,  // 74: moego.api.appointment.v1.CareCategory.service_list:type_name -> moego.api.appointment.v1.ServiceItem
	47,  // 75: moego.api.appointment.v1.GetAppointmentLodgingResult.pet_lodging_views:type_name -> moego.api.appointment.v1.GetAppointmentLodgingResult.PetLodgingView
	88,  // 76: moego.api.appointment.v1.BatchQuickCheckInParams.date:type_name -> google.type.Date
	90,  // 77: moego.api.appointment.v1.BatchQuickCheckInParams.source:type_name -> moego.models.appointment.v1.AppointmentSource
	91,  // 78: moego.api.appointment.v1.ListStaffAppointmentsParams.period:type_name -> google.type.Interval
	92,  // 79: moego.api.appointment.v1.ListStaffAppointmentsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	88,  // 80: moego.api.appointment.v1.ListStaffAppointmentsParams.appointment_date:type_name -> google.type.Date
	52,  // 81: moego.api.appointment.v1.ListStaffAppointmentsResult.appointments:type_name -> moego.api.appointment.v1.ListStaffAppointmentsResult.AppointmentDetailView
	93,  // 82: moego.api.appointment.v1.ListStaffAppointmentsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	88,  // 83: moego.api.appointment.v1.BatchBookAgainAppointmentParams.source_date:type_name -> google.type.Date
	88,  // 84: moego.api.appointment.v1.BatchBookAgainAppointmentParams.target_date:type_name -> google.type.Date
	94,  // 85: moego.api.appointment.v1.BatchBookAgainAppointmentResult.appointments:type_name -> moego.models.appointment.v1.AppointmentModel
	88,  // 86: moego.api.appointment.v1.BatchCancelAppointmentParams.date:type_name -> google.type.Date
	94,  // 87: moego.api.appointment.v1.BatchCancelAppointmentResult.appointments:type_name -> moego.models.appointment.v1.AppointmentModel
	44,  // 88: moego.api.appointment.v1.GetServiceSummaryResult.CountedByAppointment.rows:type_name -> moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryRow
	44,  // 89: moego.api.appointment.v1.GetServiceSummaryResult.CountedByPet.rows:type_name -> moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryRow
	45,  // 90: moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryRow.children:type_name -> moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryChildren
	46,  // 91: moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryChildren.grooming:type_name -> moego.api.appointment.v1.GetServiceSummaryResult.ServiceRow
	46,  // 92: moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryChildren.boarding:type_name -> moego.api.appointment.v1.GetServiceSummaryResult.ServiceRow
	46,  // 93: moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryChildren.daycare:type_name -> moego.api.appointment.v1.GetServiceSummaryResult.ServiceRow
	46,  // 94: moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryChildren.evaluation:type_name -> moego.api.appointment.v1.GetServiceSummaryResult.ServiceRow
	46,  // 95: moego.api.appointment.v1.GetServiceSummaryResult.ServiceSummaryChildren.dog_walking:type_name -> moego.api.appointment.v1.GetServiceSummaryResult.ServiceRow
	48,  // 96: moego.api.appointment.v1.GetAppointmentLodgingResult.PetLodgingView.service_lodgings:type_name -> moego.api.appointment.v1.GetAppointmentLodgingResult.ServiceLodgingView
	49,  // 97: moego.api.appointment.v1.GetAppointmentLodgingResult.PetLodgingView.evaluation_lodgings:type_name -> moego.api.appointment.v1.GetAppointmentLodgingResult.EvaluationLodgingView
	68,  // 98: moego.api.appointment.v1.GetAppointmentLodgingResult.ServiceLodgingView.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	68,  // 99: moego.api.appointment.v1.GetAppointmentLodgingResult.EvaluationLodgingView.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	73,  // 100: moego.api.appointment.v1.ListStaffAppointmentsResult.InvoiceView.net_sale:type_name -> google.type.Money
	73,  // 101: moego.api.appointment.v1.ListStaffAppointmentsResult.InvoiceView.tips:type_name -> google.type.Money
	95,  // 102: moego.api.appointment.v1.ListStaffAppointmentsResult.CustomerView.customer_profile:type_name -> moego.models.business_customer.v1.BusinessCustomerInfoModel
	86,  // 103: moego.api.appointment.v1.ListStaffAppointmentsResult.CustomerView.customer_address:type_name -> moego.models.business_customer.v1.BusinessCustomerAddressModel
	94,  // 104: moego.api.appointment.v1.ListStaffAppointmentsResult.AppointmentDetailView.appointment:type_name -> moego.models.appointment.v1.AppointmentModel
	7,   // 105: moego.api.appointment.v1.ListStaffAppointmentsResult.AppointmentDetailView.service_detail:type_name -> moego.api.appointment.v1.ServiceDetail
	50,  // 106: moego.api.appointment.v1.ListStaffAppointmentsResult.AppointmentDetailView.invoice:type_name -> moego.api.appointment.v1.ListStaffAppointmentsResult.InvoiceView
	51,  // 107: moego.api.appointment.v1.ListStaffAppointmentsResult.AppointmentDetailView.customer:type_name -> moego.api.appointment.v1.ListStaffAppointmentsResult.CustomerView
	68,  // 108: moego.api.appointment.v1.ListStaffAppointmentsResult.AppointmentDetailView.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	0,   // 109: moego.api.appointment.v1.AppointmentService.CreateAppointment:input_type -> moego.api.appointment.v1.CreateAppointmentParams
	2,   // 110: moego.api.appointment.v1.AppointmentService.UpdateAppointment:input_type -> moego.api.appointment.v1.UpdateAppointmentParams
	4,   // 111: moego.api.appointment.v1.AppointmentService.GetAppointment:input_type -> moego.api.appointment.v1.GetAppointmentParams
	14,  // 112: moego.api.appointment.v1.AppointmentService.GetCustomerLastAppointment:input_type -> moego.api.appointment.v1.GetCustomerLastAppointmentParams
	16,  // 113: moego.api.appointment.v1.AppointmentService.CalculateAppointmentInvoice:input_type -> moego.api.appointment.v1.CalculateAppointmentInvoiceParams
	18,  // 114: moego.api.appointment.v1.AppointmentService.GetInProgressEvaluationAppointment:input_type -> moego.api.appointment.v1.GetInProgressEvaluationAppointmentParams
	22,  // 115: moego.api.appointment.v1.AppointmentService.GetDaySummary:input_type -> moego.api.appointment.v1.GetDaySummaryParams
	20,  // 116: moego.api.appointment.v1.AppointmentService.GetServiceSummary:input_type -> moego.api.appointment.v1.GetServiceSummaryParams
	29,  // 117: moego.api.appointment.v1.AppointmentService.GetAppointmentLodging:input_type -> moego.api.appointment.v1.GetAppointmentLodgingParams
	31,  // 118: moego.api.appointment.v1.AppointmentService.BatchQuickCheckIn:input_type -> moego.api.appointment.v1.BatchQuickCheckInParams
	33,  // 119: moego.api.appointment.v1.AppointmentService.ListStaffAppointments:input_type -> moego.api.appointment.v1.ListStaffAppointmentsParams
	35,  // 120: moego.api.appointment.v1.AppointmentService.BatchBookAgainAppointment:input_type -> moego.api.appointment.v1.BatchBookAgainAppointmentParams
	37,  // 121: moego.api.appointment.v1.AppointmentService.BatchCancelAppointment:input_type -> moego.api.appointment.v1.BatchCancelAppointmentParams
	39,  // 122: moego.api.appointment.v1.AppointmentService.RescheduleBoardingAppointment:input_type -> moego.api.appointment.v1.RescheduleBoardingAppointmentParams
	1,   // 123: moego.api.appointment.v1.AppointmentService.CreateAppointment:output_type -> moego.api.appointment.v1.CreateAppointmentResult
	3,   // 124: moego.api.appointment.v1.AppointmentService.UpdateAppointment:output_type -> moego.api.appointment.v1.UpdateAppointmentResult
	5,   // 125: moego.api.appointment.v1.AppointmentService.GetAppointment:output_type -> moego.api.appointment.v1.GetAppointmentResult
	15,  // 126: moego.api.appointment.v1.AppointmentService.GetCustomerLastAppointment:output_type -> moego.api.appointment.v1.GetCustomerLastAppointmentResult
	17,  // 127: moego.api.appointment.v1.AppointmentService.CalculateAppointmentInvoice:output_type -> moego.api.appointment.v1.CalculateAppointmentInvoiceResult
	19,  // 128: moego.api.appointment.v1.AppointmentService.GetInProgressEvaluationAppointment:output_type -> moego.api.appointment.v1.GetInProgressEvaluationAppointmentResult
	23,  // 129: moego.api.appointment.v1.AppointmentService.GetDaySummary:output_type -> moego.api.appointment.v1.GetDaySummaryResult
	21,  // 130: moego.api.appointment.v1.AppointmentService.GetServiceSummary:output_type -> moego.api.appointment.v1.GetServiceSummaryResult
	30,  // 131: moego.api.appointment.v1.AppointmentService.GetAppointmentLodging:output_type -> moego.api.appointment.v1.GetAppointmentLodgingResult
	32,  // 132: moego.api.appointment.v1.AppointmentService.BatchQuickCheckIn:output_type -> moego.api.appointment.v1.BatchQuickCheckInResult
	34,  // 133: moego.api.appointment.v1.AppointmentService.ListStaffAppointments:output_type -> moego.api.appointment.v1.ListStaffAppointmentsResult
	36,  // 134: moego.api.appointment.v1.AppointmentService.BatchBookAgainAppointment:output_type -> moego.api.appointment.v1.BatchBookAgainAppointmentResult
	38,  // 135: moego.api.appointment.v1.AppointmentService.BatchCancelAppointment:output_type -> moego.api.appointment.v1.BatchCancelAppointmentResult
	40,  // 136: moego.api.appointment.v1.AppointmentService.RescheduleBoardingAppointment:output_type -> moego.api.appointment.v1.RescheduleBoardingAppointmentResult
	123, // [123:137] is the sub-list for method output_type
	109, // [109:123] is the sub-list for method input_type
	109, // [109:109] is the sub-list for extension type_name
	109, // [109:109] is the sub-list for extension extendee
	0,   // [0:109] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_appointment_api_proto_init() }
func file_moego_api_appointment_v1_appointment_api_proto_init() {
	if File_moego_api_appointment_v1_appointment_api_proto != nil {
		return
	}
	file_moego_api_appointment_v1_appointment_view_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceComposite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddOnComposite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceDetailComposite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerComposite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetVaccineComposite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationServiceComposite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerLastAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerLastAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateAppointmentInvoiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateAppointmentInvoiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInProgressEvaluationAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInProgressEvaluationAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceSummaryParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceSummaryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDaySummaryParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDaySummaryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentDaySummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetDaySummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CareCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentLodgingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentLodgingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchQuickCheckInParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchQuickCheckInResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchBookAgainAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchBookAgainAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCancelAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCancelAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleBoardingAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleBoardingAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppointmentParams_PetBelonging); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceSummaryResult_CountedByAppointment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceSummaryResult_CountedByPet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceSummaryResult_ServiceSummaryRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceSummaryResult_ServiceSummaryChildren); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceSummaryResult_ServiceRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentLodgingResult_PetLodgingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentLodgingResult_ServiceLodgingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentLodgingResult_EvaluationLodgingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentsResult_InvoiceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentsResult_CustomerView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_api_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentsResult_AppointmentDetailView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[19].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[33].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[37].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[39].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[41].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_api_proto_msgTypes[51].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_appointment_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   53,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_appointment_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_appointment_api_proto_depIdxs,
		MessageInfos:      file_moego_api_appointment_v1_appointment_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_appointment_api_proto = out.File
	file_moego_api_appointment_v1_appointment_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_appointment_api_proto_goTypes = nil
	file_moego_api_appointment_v1_appointment_api_proto_depIdxs = nil
}
