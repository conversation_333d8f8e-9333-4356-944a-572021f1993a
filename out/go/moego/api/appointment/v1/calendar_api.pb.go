// @since 2024-05-09 11:44:04
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/calendar_api.proto

package appointmentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v16 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v17 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v14 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v15 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	v18 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Not be resize reasons
type CalendarCardDraggableInfo_NotResizeReason int32

const (
	// unspecified
	CalendarCardDraggableInfo_NOT_RESIZE_REASON_UNSPECIFIED CalendarCardDraggableInfo_NotResizeReason = 0
	// Appointment finished
	CalendarCardDraggableInfo_APPOINTMENT_FINISHED CalendarCardDraggableInfo_NotResizeReason = 1
	// Include multiple services
	CalendarCardDraggableInfo_INCLUDE_MULTIPLE_SERVICES CalendarCardDraggableInfo_NotResizeReason = 2
	// Is a booking request card
	CalendarCardDraggableInfo_IS_BOOKING_REQUEST CalendarCardDraggableInfo_NotResizeReason = 3
)

// Enum value maps for CalendarCardDraggableInfo_NotResizeReason.
var (
	CalendarCardDraggableInfo_NotResizeReason_name = map[int32]string{
		0: "NOT_RESIZE_REASON_UNSPECIFIED",
		1: "APPOINTMENT_FINISHED",
		2: "INCLUDE_MULTIPLE_SERVICES",
		3: "IS_BOOKING_REQUEST",
	}
	CalendarCardDraggableInfo_NotResizeReason_value = map[string]int32{
		"NOT_RESIZE_REASON_UNSPECIFIED": 0,
		"APPOINTMENT_FINISHED":          1,
		"INCLUDE_MULTIPLE_SERVICES":     2,
		"IS_BOOKING_REQUEST":            3,
	}
)

func (x CalendarCardDraggableInfo_NotResizeReason) Enum() *CalendarCardDraggableInfo_NotResizeReason {
	p := new(CalendarCardDraggableInfo_NotResizeReason)
	*p = x
	return p
}

func (x CalendarCardDraggableInfo_NotResizeReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CalendarCardDraggableInfo_NotResizeReason) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_api_appointment_v1_calendar_api_proto_enumTypes[0].Descriptor()
}

func (CalendarCardDraggableInfo_NotResizeReason) Type() protoreflect.EnumType {
	return &file_moego_api_appointment_v1_calendar_api_proto_enumTypes[0]
}

func (x CalendarCardDraggableInfo_NotResizeReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CalendarCardDraggableInfo_NotResizeReason.Descriptor instead.
func (CalendarCardDraggableInfo_NotResizeReason) EnumDescriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{14, 0}
}

// list day cards params
type ListDayCardsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the start date
	StartDate *date.Date `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// the end date
	EndDate *date.Date `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// selected the business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// filter no start time
	FilterNoStartTime bool `protobuf:"varint,6,opt,name=filter_no_start_time,json=filterNoStartTime,proto3" json:"filter_no_start_time,omitempty"`
	// filter no staff
	FilterNoStaff bool `protobuf:"varint,7,opt,name=filter_no_staff,json=filterNoStaff,proto3" json:"filter_no_staff,omitempty"`
}

func (x *ListDayCardsParams) Reset() {
	*x = ListDayCardsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsParams) ProtoMessage() {}

func (x *ListDayCardsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsParams.ProtoReflect.Descriptor instead.
func (*ListDayCardsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListDayCardsParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListDayCardsParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListDayCardsParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListDayCardsParams) GetFilterNoStartTime() bool {
	if x != nil {
		return x.FilterNoStartTime
	}
	return false
}

func (x *ListDayCardsParams) GetFilterNoStaff() bool {
	if x != nil {
		return x.FilterNoStaff
	}
	return false
}

// list day cards result
type ListDayCardsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the list of calendar cards
	Cards []*ListDayCardsResult_CalendarCardCompositeView `protobuf:"bytes,1,rep,name=cards,proto3" json:"cards,omitempty"`
}

func (x *ListDayCardsResult) Reset() {
	*x = ListDayCardsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult) ProtoMessage() {}

func (x *ListDayCardsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListDayCardsResult) GetCards() []*ListDayCardsResult_CalendarCardCompositeView {
	if x != nil {
		return x.Cards
	}
	return nil
}

// list month cards params
type ListMonthCardsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the start date
	StartDate *date.Date `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// the end date
	EndDate *date.Date `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// selected the business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// is waiting list
	IsWaitingList bool `protobuf:"varint,5,opt,name=is_waiting_list,json=isWaitingList,proto3" json:"is_waiting_list,omitempty"`
	// filter no start time
	FilterNoStartTime bool `protobuf:"varint,6,opt,name=filter_no_start_time,json=filterNoStartTime,proto3" json:"filter_no_start_time,omitempty"`
	// filter no staff
	FilterNoStaff bool `protobuf:"varint,7,opt,name=filter_no_staff,json=filterNoStaff,proto3" json:"filter_no_staff,omitempty"`
	// predicate
	Predicate *v2.Predicate `protobuf:"bytes,8,opt,name=predicate,proto3,oneof" json:"predicate,omitempty"`
}

func (x *ListMonthCardsParams) Reset() {
	*x = ListMonthCardsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonthCardsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonthCardsParams) ProtoMessage() {}

func (x *ListMonthCardsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonthCardsParams.ProtoReflect.Descriptor instead.
func (*ListMonthCardsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{2}
}

func (x *ListMonthCardsParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListMonthCardsParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListMonthCardsParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListMonthCardsParams) GetIsWaitingList() bool {
	if x != nil {
		return x.IsWaitingList
	}
	return false
}

func (x *ListMonthCardsParams) GetFilterNoStartTime() bool {
	if x != nil {
		return x.FilterNoStartTime
	}
	return false
}

func (x *ListMonthCardsParams) GetFilterNoStaff() bool {
	if x != nil {
		return x.FilterNoStaff
	}
	return false
}

func (x *ListMonthCardsParams) GetPredicate() *v2.Predicate {
	if x != nil {
		return x.Predicate
	}
	return nil
}

// list month cards result
type ListMonthCardsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the list of month cards
	Cards []*ListMonthCardsResult_CalendarCardSimpleView `protobuf:"bytes,1,rep,name=cards,proto3" json:"cards,omitempty"`
}

func (x *ListMonthCardsResult) Reset() {
	*x = ListMonthCardsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonthCardsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonthCardsResult) ProtoMessage() {}

func (x *ListMonthCardsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonthCardsResult.ProtoReflect.Descriptor instead.
func (*ListMonthCardsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListMonthCardsResult) GetCards() []*ListMonthCardsResult_CalendarCardSimpleView {
	if x != nil {
		return x.Cards
	}
	return nil
}

// The params for preview calendar cards
type PreviewCalendarCardsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Selected customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Selected appointment id
	// Used to edit schedule
	AppointmentId *int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
	// Multi pets start at the same time, default to false
	AllPetsStartAtSameTime bool `protobuf:"varint,4,opt,name=all_pets_start_at_same_time,json=allPetsStartAtSameTime,proto3" json:"all_pets_start_at_same_time,omitempty"`
	// Appointment calendar schedule
	AppointmentSchedule *v1.AppointmentCalendarScheduleDef `protobuf:"bytes,5,opt,name=appointment_schedule,json=appointmentSchedule,proto3" json:"appointment_schedule,omitempty"`
	// Selected pet and services
	PetServices []*v1.PetServiceCalendarDef `protobuf:"bytes,6,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// Original pet id
	// Apply to existing appointment modify directly replace pet scenario
	OriginalPetId *int64 `protobuf:"varint,8,opt,name=original_pet_id,json=originalPetId,proto3,oneof" json:"original_pet_id,omitempty"`
}

func (x *PreviewCalendarCardsParams) Reset() {
	*x = PreviewCalendarCardsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewCalendarCardsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewCalendarCardsParams) ProtoMessage() {}

func (x *PreviewCalendarCardsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewCalendarCardsParams.ProtoReflect.Descriptor instead.
func (*PreviewCalendarCardsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{4}
}

func (x *PreviewCalendarCardsParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PreviewCalendarCardsParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *PreviewCalendarCardsParams) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

func (x *PreviewCalendarCardsParams) GetAllPetsStartAtSameTime() bool {
	if x != nil {
		return x.AllPetsStartAtSameTime
	}
	return false
}

func (x *PreviewCalendarCardsParams) GetAppointmentSchedule() *v1.AppointmentCalendarScheduleDef {
	if x != nil {
		return x.AppointmentSchedule
	}
	return nil
}

func (x *PreviewCalendarCardsParams) GetPetServices() []*v1.PetServiceCalendarDef {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *PreviewCalendarCardsParams) GetOriginalPetId() int64 {
	if x != nil && x.OriginalPetId != nil {
		return *x.OriginalPetId
	}
	return 0
}

// The result for preview calendar cards
type PreviewCalendarCardsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Selected customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Selected appointment id
	// Used to edit schedule
	AppointmentId *int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
	// Multi pets start at the same time, default to false
	AllPetsStartAtSameTime bool `protobuf:"varint,4,opt,name=all_pets_start_at_same_time,json=allPetsStartAtSameTime,proto3" json:"all_pets_start_at_same_time,omitempty"`
	// Appointment calendar schedule
	AppointmentSchedule *v1.AppointmentScheduleDef `protobuf:"bytes,5,opt,name=appointment_schedule,json=appointmentSchedule,proto3" json:"appointment_schedule,omitempty"`
	// Selected pet and service schedules
	PetServiceSchedules []*v1.PetServiceCalendarScheduleDef `protobuf:"bytes,6,rep,name=pet_service_schedules,json=petServiceSchedules,proto3" json:"pet_service_schedules,omitempty"`
	// The list of calendar cards
	// Used to preview the calendar cards
	Cards []*CalendarCardView `protobuf:"bytes,7,rep,name=cards,proto3" json:"cards,omitempty"`
}

func (x *PreviewCalendarCardsResult) Reset() {
	*x = PreviewCalendarCardsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewCalendarCardsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewCalendarCardsResult) ProtoMessage() {}

func (x *PreviewCalendarCardsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewCalendarCardsResult.ProtoReflect.Descriptor instead.
func (*PreviewCalendarCardsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{5}
}

func (x *PreviewCalendarCardsResult) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PreviewCalendarCardsResult) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *PreviewCalendarCardsResult) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

func (x *PreviewCalendarCardsResult) GetAllPetsStartAtSameTime() bool {
	if x != nil {
		return x.AllPetsStartAtSameTime
	}
	return false
}

func (x *PreviewCalendarCardsResult) GetAppointmentSchedule() *v1.AppointmentScheduleDef {
	if x != nil {
		return x.AppointmentSchedule
	}
	return nil
}

func (x *PreviewCalendarCardsResult) GetPetServiceSchedules() []*v1.PetServiceCalendarScheduleDef {
	if x != nil {
		return x.PetServiceSchedules
	}
	return nil
}

func (x *PreviewCalendarCardsResult) GetCards() []*CalendarCardView {
	if x != nil {
		return x.Cards
	}
	return nil
}

// Calendar card view
type CalendarCardView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// UUID, Back-end memory generation for front-end rendering components
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// Has related split cards
	// If true, a pop-up window is required to indicate whether all cards are affected.
	HasRelatedSplitCards bool `protobuf:"varint,2,opt,name=has_related_split_cards,json=hasRelatedSplitCards,proto3" json:"has_related_split_cards,omitempty"`
	// the card type
	CardType v1.CalendarCardType `protobuf:"varint,3,opt,name=card_type,json=cardType,proto3,enum=moego.models.appointment.v1.CalendarCardType" json:"card_type,omitempty"`
	// appointment status
	AppointmentStatus *v1.AppointmentStatus `protobuf:"varint,4,opt,name=appointment_status,json=appointmentStatus,proto3,enum=moego.models.appointment.v1.AppointmentStatus,oneof" json:"appointment_status,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,5,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// staff id, If there is no staff id, it means no staff assigned
	StaffId *int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// the start date
	Date string `protobuf:"bytes,7,opt,name=date,proto3" json:"date,omitempty"`
	// the end date
	EndDate string `protobuf:"bytes,8,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// the start time, in minutes
	StartTime int32 `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// the end time, in minutes
	EndTime int32 `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// appointment payment status
	PaymentStatus *v1.AppointmentPaymentStatus `protobuf:"varint,11,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.appointment.v1.AppointmentPaymentStatus,oneof" json:"payment_status,omitempty"`
	// pre-auth
	PreAuthInfo *v11.PreAuthCalendarView `protobuf:"bytes,12,opt,name=pre_auth_info,json=preAuthInfo,proto3,oneof" json:"pre_auth_info,omitempty"`
	// required sign agreement
	RequiredSign *bool `protobuf:"varint,13,opt,name=required_sign,json=requiredSign,proto3,oneof" json:"required_sign,omitempty"`
	// alert notes
	AlertNotes *string `protobuf:"bytes,14,opt,name=alert_notes,json=alertNotes,proto3,oneof" json:"alert_notes,omitempty"`
	// ticket comments
	TicketComments *string `protobuf:"bytes,15,opt,name=ticket_comments,json=ticketComments,proto3,oneof" json:"ticket_comments,omitempty"`
	// repeat id, used for repeat appointment
	RepeatId *int64 `protobuf:"varint,16,opt,name=repeat_id,json=repeatId,proto3,oneof" json:"repeat_id,omitempty"`
	// appointment color code
	AppointmentColor string `protobuf:"bytes,17,opt,name=appointment_color,json=appointmentColor,proto3" json:"appointment_color,omitempty"`
	// booking request id
	BookingRequestId *int64 `protobuf:"varint,18,opt,name=booking_request_id,json=bookingRequestId,proto3,oneof" json:"booking_request_id,omitempty"`
	// is auto accept, used for online booking auto accept
	IsAutoAccept *bool `protobuf:"varint,19,opt,name=is_auto_accept,json=isAutoAccept,proto3,oneof" json:"is_auto_accept,omitempty"`
	// auto assign, staff and start time for auto assign
	AutoAssign *v12.GroomingAutoAssignView `protobuf:"bytes,20,opt,name=auto_assign,json=autoAssign,proto3,oneof" json:"auto_assign,omitempty"`
	// customer info
	CustomerInfo *CalendarCardCustomerInfo `protobuf:"bytes,21,opt,name=customer_info,json=customerInfo,proto3,oneof" json:"customer_info,omitempty"`
	// pet info
	Pets []*CalendarCardPetInfo `protobuf:"bytes,22,rep,name=pets,proto3" json:"pets,omitempty"`
	// estimated total price
	EstimatedTotalPrice *float64 `protobuf:"fixed64,23,opt,name=estimated_total_price,json=estimatedTotalPrice,proto3,oneof" json:"estimated_total_price,omitempty"`
	// paid amount
	PaidAmount *float64 `protobuf:"fixed64,24,opt,name=paid_amount,json=paidAmount,proto3,oneof" json:"paid_amount,omitempty"`
	// prepaid amount
	PrePayInfo *v11.PrePayCalendarView `protobuf:"bytes,25,opt,name=pre_pay_info,json=prePayInfo,proto3,oneof" json:"pre_pay_info,omitempty"`
	// pet detail ids, used for reschedule card
	PetDetailIds []int64 `protobuf:"varint,27,rep,packed,name=pet_detail_ids,json=petDetailIds,proto3" json:"pet_detail_ids,omitempty"`
	// draggable info, used for reschedule card
	DraggableInfo *CalendarCardDraggableInfo `protobuf:"bytes,28,opt,name=draggable_info,json=draggableInfo,proto3" json:"draggable_info,omitempty"`
	// service item types
	ServiceItemTypes []v13.ServiceItemType `protobuf:"varint,29,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// memberships
	MembershipSubscriptions *v14.MembershipSubscriptionListModel `protobuf:"bytes,30,opt,name=membership_subscriptions,json=membershipSubscriptions,proto3,oneof" json:"membership_subscriptions,omitempty"`
	// invoice
	Invoice *v15.InvoiceCalendarView `protobuf:"bytes,31,opt,name=invoice,proto3" json:"invoice,omitempty"`
	// hit predicate
	HitPredicate bool `protobuf:"varint,32,opt,name=hit_predicate,json=hitPredicate,proto3" json:"hit_predicate,omitempty"`
}

func (x *CalendarCardView) Reset() {
	*x = CalendarCardView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarCardView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarCardView) ProtoMessage() {}

func (x *CalendarCardView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarCardView.ProtoReflect.Descriptor instead.
func (*CalendarCardView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{6}
}

func (x *CalendarCardView) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CalendarCardView) GetHasRelatedSplitCards() bool {
	if x != nil {
		return x.HasRelatedSplitCards
	}
	return false
}

func (x *CalendarCardView) GetCardType() v1.CalendarCardType {
	if x != nil {
		return x.CardType
	}
	return v1.CalendarCardType(0)
}

func (x *CalendarCardView) GetAppointmentStatus() v1.AppointmentStatus {
	if x != nil && x.AppointmentStatus != nil {
		return *x.AppointmentStatus
	}
	return v1.AppointmentStatus(0)
}

func (x *CalendarCardView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *CalendarCardView) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *CalendarCardView) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *CalendarCardView) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *CalendarCardView) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *CalendarCardView) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *CalendarCardView) GetPaymentStatus() v1.AppointmentPaymentStatus {
	if x != nil && x.PaymentStatus != nil {
		return *x.PaymentStatus
	}
	return v1.AppointmentPaymentStatus(0)
}

func (x *CalendarCardView) GetPreAuthInfo() *v11.PreAuthCalendarView {
	if x != nil {
		return x.PreAuthInfo
	}
	return nil
}

func (x *CalendarCardView) GetRequiredSign() bool {
	if x != nil && x.RequiredSign != nil {
		return *x.RequiredSign
	}
	return false
}

func (x *CalendarCardView) GetAlertNotes() string {
	if x != nil && x.AlertNotes != nil {
		return *x.AlertNotes
	}
	return ""
}

func (x *CalendarCardView) GetTicketComments() string {
	if x != nil && x.TicketComments != nil {
		return *x.TicketComments
	}
	return ""
}

func (x *CalendarCardView) GetRepeatId() int64 {
	if x != nil && x.RepeatId != nil {
		return *x.RepeatId
	}
	return 0
}

func (x *CalendarCardView) GetAppointmentColor() string {
	if x != nil {
		return x.AppointmentColor
	}
	return ""
}

func (x *CalendarCardView) GetBookingRequestId() int64 {
	if x != nil && x.BookingRequestId != nil {
		return *x.BookingRequestId
	}
	return 0
}

func (x *CalendarCardView) GetIsAutoAccept() bool {
	if x != nil && x.IsAutoAccept != nil {
		return *x.IsAutoAccept
	}
	return false
}

func (x *CalendarCardView) GetAutoAssign() *v12.GroomingAutoAssignView {
	if x != nil {
		return x.AutoAssign
	}
	return nil
}

func (x *CalendarCardView) GetCustomerInfo() *CalendarCardCustomerInfo {
	if x != nil {
		return x.CustomerInfo
	}
	return nil
}

func (x *CalendarCardView) GetPets() []*CalendarCardPetInfo {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *CalendarCardView) GetEstimatedTotalPrice() float64 {
	if x != nil && x.EstimatedTotalPrice != nil {
		return *x.EstimatedTotalPrice
	}
	return 0
}

func (x *CalendarCardView) GetPaidAmount() float64 {
	if x != nil && x.PaidAmount != nil {
		return *x.PaidAmount
	}
	return 0
}

func (x *CalendarCardView) GetPrePayInfo() *v11.PrePayCalendarView {
	if x != nil {
		return x.PrePayInfo
	}
	return nil
}

func (x *CalendarCardView) GetPetDetailIds() []int64 {
	if x != nil {
		return x.PetDetailIds
	}
	return nil
}

func (x *CalendarCardView) GetDraggableInfo() *CalendarCardDraggableInfo {
	if x != nil {
		return x.DraggableInfo
	}
	return nil
}

func (x *CalendarCardView) GetServiceItemTypes() []v13.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *CalendarCardView) GetMembershipSubscriptions() *v14.MembershipSubscriptionListModel {
	if x != nil {
		return x.MembershipSubscriptions
	}
	return nil
}

func (x *CalendarCardView) GetInvoice() *v15.InvoiceCalendarView {
	if x != nil {
		return x.Invoice
	}
	return nil
}

func (x *CalendarCardView) GetHitPredicate() bool {
	if x != nil {
		return x.HitPredicate
	}
	return false
}

// the customer info for calendar card view
type CalendarCardCustomerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// the customer last name
	LastName string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// the customer first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// the color code
	ClientColor string `protobuf:"bytes,4,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	// new flag
	IsNewClient bool `protobuf:"varint,5,opt,name=is_new_client,json=isNewClient,proto3" json:"is_new_client,omitempty"`
	// primary address
	PrimaryAddress *v16.BusinessCustomerAddressView `protobuf:"bytes,6,opt,name=primary_address,json=primaryAddress,proto3" json:"primary_address,omitempty"`
	// certain areas
	Areas []*CalendarCardCertainArea `protobuf:"bytes,7,rep,name=areas,proto3" json:"areas,omitempty"`
}

func (x *CalendarCardCustomerInfo) Reset() {
	*x = CalendarCardCustomerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarCardCustomerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarCardCustomerInfo) ProtoMessage() {}

func (x *CalendarCardCustomerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarCardCustomerInfo.ProtoReflect.Descriptor instead.
func (*CalendarCardCustomerInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{7}
}

func (x *CalendarCardCustomerInfo) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CalendarCardCustomerInfo) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CalendarCardCustomerInfo) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CalendarCardCustomerInfo) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

func (x *CalendarCardCustomerInfo) GetIsNewClient() bool {
	if x != nil {
		return x.IsNewClient
	}
	return false
}

func (x *CalendarCardCustomerInfo) GetPrimaryAddress() *v16.BusinessCustomerAddressView {
	if x != nil {
		return x.PrimaryAddress
	}
	return nil
}

func (x *CalendarCardCustomerInfo) GetAreas() []*CalendarCardCertainArea {
	if x != nil {
		return x.Areas
	}
	return nil
}

// the certain area for calendar card view
type CalendarCardCertainArea struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// area id
	AreaId int64 `protobuf:"varint,1,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	// area name
	AreaName string `protobuf:"bytes,2,opt,name=area_name,json=areaName,proto3" json:"area_name,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,3,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
}

func (x *CalendarCardCertainArea) Reset() {
	*x = CalendarCardCertainArea{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarCardCertainArea) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarCardCertainArea) ProtoMessage() {}

func (x *CalendarCardCertainArea) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarCardCertainArea.ProtoReflect.Descriptor instead.
func (*CalendarCardCertainArea) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{8}
}

func (x *CalendarCardCertainArea) GetAreaId() int64 {
	if x != nil {
		return x.AreaId
	}
	return 0
}

func (x *CalendarCardCertainArea) GetAreaName() string {
	if x != nil {
		return x.AreaName
	}
	return ""
}

func (x *CalendarCardCertainArea) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

// the pet info for calendar card view
type CalendarCardPetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// the pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// the pet breed name
	Breed string `protobuf:"bytes,3,opt,name=breed,proto3" json:"breed,omitempty"`
	// the pet code list
	PetCodes []*CalendarCardPetCodeInfo `protobuf:"bytes,4,rep,name=pet_codes,json=petCodes,proto3" json:"pet_codes,omitempty"`
	// the service info list
	Services []*CalendarCardServiceInfo `protobuf:"bytes,5,rep,name=services,proto3" json:"services,omitempty"`
	// the vaccine list
	Vaccines []*CalendarCardVaccineInfo `protobuf:"bytes,6,rep,name=vaccines,proto3" json:"vaccines,omitempty"`
	// the evaluation detail list
	Evaluations []*CalendarCardEvaluationInfo `protobuf:"bytes,7,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
	// pet type
	PetType v17.PetType `protobuf:"varint,8,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// enable vaccine expiry notification, ture is enable, false is disable
	EnableVaccineExpiryNotification bool `protobuf:"varint,9,opt,name=enable_vaccine_expiry_notification,json=enableVaccineExpiryNotification,proto3" json:"enable_vaccine_expiry_notification,omitempty"`
	// pet size id
	PetSizeId int64 `protobuf:"varint,10,opt,name=pet_size_id,json=petSizeId,proto3" json:"pet_size_id,omitempty"`
}

func (x *CalendarCardPetInfo) Reset() {
	*x = CalendarCardPetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarCardPetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarCardPetInfo) ProtoMessage() {}

func (x *CalendarCardPetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarCardPetInfo.ProtoReflect.Descriptor instead.
func (*CalendarCardPetInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{9}
}

func (x *CalendarCardPetInfo) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *CalendarCardPetInfo) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *CalendarCardPetInfo) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *CalendarCardPetInfo) GetPetCodes() []*CalendarCardPetCodeInfo {
	if x != nil {
		return x.PetCodes
	}
	return nil
}

func (x *CalendarCardPetInfo) GetServices() []*CalendarCardServiceInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *CalendarCardPetInfo) GetVaccines() []*CalendarCardVaccineInfo {
	if x != nil {
		return x.Vaccines
	}
	return nil
}

func (x *CalendarCardPetInfo) GetEvaluations() []*CalendarCardEvaluationInfo {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

func (x *CalendarCardPetInfo) GetPetType() v17.PetType {
	if x != nil {
		return x.PetType
	}
	return v17.PetType(0)
}

func (x *CalendarCardPetInfo) GetEnableVaccineExpiryNotification() bool {
	if x != nil {
		return x.EnableVaccineExpiryNotification
	}
	return false
}

func (x *CalendarCardPetInfo) GetPetSizeId() int64 {
	if x != nil {
		return x.PetSizeId
	}
	return 0
}

// the pet code info for calendar card view
type CalendarCardPetCodeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pet code id
	PetCodeId int32 `protobuf:"varint,1,opt,name=pet_code_id,json=petCodeId,proto3" json:"pet_code_id,omitempty"`
	// the pet code abbreviation
	Abbreviation string `protobuf:"bytes,2,opt,name=abbreviation,proto3" json:"abbreviation,omitempty"`
	// the pet code description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// the pet code color
	Color string `protobuf:"bytes,4,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *CalendarCardPetCodeInfo) Reset() {
	*x = CalendarCardPetCodeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarCardPetCodeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarCardPetCodeInfo) ProtoMessage() {}

func (x *CalendarCardPetCodeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarCardPetCodeInfo.ProtoReflect.Descriptor instead.
func (*CalendarCardPetCodeInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{10}
}

func (x *CalendarCardPetCodeInfo) GetPetCodeId() int32 {
	if x != nil {
		return x.PetCodeId
	}
	return 0
}

func (x *CalendarCardPetCodeInfo) GetAbbreviation() string {
	if x != nil {
		return x.Abbreviation
	}
	return ""
}

func (x *CalendarCardPetCodeInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CalendarCardPetCodeInfo) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// the evaluation service detail
type CalendarCardEvaluationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation detail id
	EvaluationDetailId int64 `protobuf:"varint,1,opt,name=evaluation_detail_id,json=evaluationDetailId,proto3" json:"evaluation_detail_id,omitempty"`
	// the service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// the service name
	ServiceName string `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// the service color code
	ColorCode string `protobuf:"bytes,4,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// the service time, in minutes
	ServiceTime int32 `protobuf:"varint,5,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// the service price
	ServicePrice float64 `protobuf:"fixed64,6,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
}

func (x *CalendarCardEvaluationInfo) Reset() {
	*x = CalendarCardEvaluationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarCardEvaluationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarCardEvaluationInfo) ProtoMessage() {}

func (x *CalendarCardEvaluationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarCardEvaluationInfo.ProtoReflect.Descriptor instead.
func (*CalendarCardEvaluationInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{11}
}

func (x *CalendarCardEvaluationInfo) GetEvaluationDetailId() int64 {
	if x != nil {
		return x.EvaluationDetailId
	}
	return 0
}

func (x *CalendarCardEvaluationInfo) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *CalendarCardEvaluationInfo) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *CalendarCardEvaluationInfo) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *CalendarCardEvaluationInfo) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *CalendarCardEvaluationInfo) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

// the service info for calendar card view
type CalendarCardServiceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pet detail id
	PetDetailId int64 `protobuf:"varint,1,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// the service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// the service name
	ServiceName string `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// the service color code
	ColorCode string `protobuf:"bytes,4,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// the service time, in minutes
	ServiceTime int32 `protobuf:"varint,5,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// the service price
	ServicePrice float64 `protobuf:"fixed64,6,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// The start time of the service, unit minute, 540 means 09:00
	StartTime *int32 `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// The end time of the service, unit minute, 540 means 09:00
	EndTime *int32 `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
}

func (x *CalendarCardServiceInfo) Reset() {
	*x = CalendarCardServiceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarCardServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarCardServiceInfo) ProtoMessage() {}

func (x *CalendarCardServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarCardServiceInfo.ProtoReflect.Descriptor instead.
func (*CalendarCardServiceInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{12}
}

func (x *CalendarCardServiceInfo) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *CalendarCardServiceInfo) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *CalendarCardServiceInfo) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *CalendarCardServiceInfo) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *CalendarCardServiceInfo) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *CalendarCardServiceInfo) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *CalendarCardServiceInfo) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *CalendarCardServiceInfo) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

// the pet vaccine for calendar card view
type CalendarCardVaccineInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the vaccine id
	VaccineId int64 `protobuf:"varint,1,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// the vaccine name
	VaccineName string `protobuf:"bytes,2,opt,name=vaccine_name,json=vaccineName,proto3" json:"vaccine_name,omitempty"`
	// the expiration date
	ExpirationDate string `protobuf:"bytes,3,opt,name=expiration_date,json=expirationDate,proto3" json:"expiration_date,omitempty"`
}

func (x *CalendarCardVaccineInfo) Reset() {
	*x = CalendarCardVaccineInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarCardVaccineInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarCardVaccineInfo) ProtoMessage() {}

func (x *CalendarCardVaccineInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarCardVaccineInfo.ProtoReflect.Descriptor instead.
func (*CalendarCardVaccineInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{13}
}

func (x *CalendarCardVaccineInfo) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *CalendarCardVaccineInfo) GetVaccineName() string {
	if x != nil {
		return x.VaccineName
	}
	return ""
}

func (x *CalendarCardVaccineInfo) GetExpirationDate() string {
	if x != nil {
		return x.ExpirationDate
	}
	return ""
}

// the draggable info for calendar card view
type CalendarCardDraggableInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// draggable flag
	Draggable bool `protobuf:"varint,1,opt,name=draggable,proto3" json:"draggable,omitempty"`
	// draggable earliest date
	EarliestDate *string `protobuf:"bytes,2,opt,name=earliest_date,json=earliestDate,proto3,oneof" json:"earliest_date,omitempty"`
	// draggable latest date
	LatestDate *string `protobuf:"bytes,3,opt,name=latest_date,json=latestDate,proto3,oneof" json:"latest_date,omitempty"`
	// unavailable draggable staff ids
	UnavailableStaffList []int64 `protobuf:"varint,4,rep,packed,name=unavailable_staff_list,json=unavailableStaffList,proto3" json:"unavailable_staff_list,omitempty"`
	// allowed to no assigned staff
	AllowedNoAssignedStaff bool `protobuf:"varint,5,opt,name=allowed_no_assigned_staff,json=allowedNoAssignedStaff,proto3" json:"allowed_no_assigned_staff,omitempty"`
	// resize flag, elongation or shortening
	Resize bool `protobuf:"varint,6,opt,name=resize,proto3" json:"resize,omitempty"`
	// Not be resize reason
	NotResizeReason CalendarCardDraggableInfo_NotResizeReason `protobuf:"varint,7,opt,name=not_resize_reason,json=notResizeReason,proto3,enum=moego.api.appointment.v1.CalendarCardDraggableInfo_NotResizeReason" json:"not_resize_reason,omitempty"`
}

func (x *CalendarCardDraggableInfo) Reset() {
	*x = CalendarCardDraggableInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarCardDraggableInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarCardDraggableInfo) ProtoMessage() {}

func (x *CalendarCardDraggableInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarCardDraggableInfo.ProtoReflect.Descriptor instead.
func (*CalendarCardDraggableInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{14}
}

func (x *CalendarCardDraggableInfo) GetDraggable() bool {
	if x != nil {
		return x.Draggable
	}
	return false
}

func (x *CalendarCardDraggableInfo) GetEarliestDate() string {
	if x != nil && x.EarliestDate != nil {
		return *x.EarliestDate
	}
	return ""
}

func (x *CalendarCardDraggableInfo) GetLatestDate() string {
	if x != nil && x.LatestDate != nil {
		return *x.LatestDate
	}
	return ""
}

func (x *CalendarCardDraggableInfo) GetUnavailableStaffList() []int64 {
	if x != nil {
		return x.UnavailableStaffList
	}
	return nil
}

func (x *CalendarCardDraggableInfo) GetAllowedNoAssignedStaff() bool {
	if x != nil {
		return x.AllowedNoAssignedStaff
	}
	return false
}

func (x *CalendarCardDraggableInfo) GetResize() bool {
	if x != nil {
		return x.Resize
	}
	return false
}

func (x *CalendarCardDraggableInfo) GetNotResizeReason() CalendarCardDraggableInfo_NotResizeReason {
	if x != nil {
		return x.NotResizeReason
	}
	return CalendarCardDraggableInfo_NOT_RESIZE_REASON_UNSPECIFIED
}

// The params of list day cards with mix type
type ListDayCardsWithMixTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the start date
	StartDate *date.Date `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// the end date
	EndDate *date.Date `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// selected the business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// filter no start time
	FilterNoStartTime bool `protobuf:"varint,6,opt,name=filter_no_start_time,json=filterNoStartTime,proto3" json:"filter_no_start_time,omitempty"`
	// filter no staff
	FilterNoStaff bool `protobuf:"varint,7,opt,name=filter_no_staff,json=filterNoStaff,proto3" json:"filter_no_staff,omitempty"`
	// predicate
	Predicate *v2.Predicate `protobuf:"bytes,8,opt,name=predicate,proto3,oneof" json:"predicate,omitempty"`
	// view type
	ViewType *v1.ViewType `protobuf:"varint,9,opt,name=view_type,json=viewType,proto3,enum=moego.models.appointment.v1.ViewType,oneof" json:"view_type,omitempty"`
}

func (x *ListDayCardsWithMixTypeParams) Reset() {
	*x = ListDayCardsWithMixTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsWithMixTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsWithMixTypeParams) ProtoMessage() {}

func (x *ListDayCardsWithMixTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsWithMixTypeParams.ProtoReflect.Descriptor instead.
func (*ListDayCardsWithMixTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{15}
}

func (x *ListDayCardsWithMixTypeParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListDayCardsWithMixTypeParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListDayCardsWithMixTypeParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListDayCardsWithMixTypeParams) GetFilterNoStartTime() bool {
	if x != nil {
		return x.FilterNoStartTime
	}
	return false
}

func (x *ListDayCardsWithMixTypeParams) GetFilterNoStaff() bool {
	if x != nil {
		return x.FilterNoStaff
	}
	return false
}

func (x *ListDayCardsWithMixTypeParams) GetPredicate() *v2.Predicate {
	if x != nil {
		return x.Predicate
	}
	return nil
}

func (x *ListDayCardsWithMixTypeParams) GetViewType() v1.ViewType {
	if x != nil && x.ViewType != nil {
		return *x.ViewType
	}
	return v1.ViewType(0)
}

// The result of list day cards with mix type
type ListDayCardsWithMixTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the list of calendar cards
	Cards []*CalendarCardView `protobuf:"bytes,1,rep,name=cards,proto3" json:"cards,omitempty"`
}

func (x *ListDayCardsWithMixTypeResult) Reset() {
	*x = ListDayCardsWithMixTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsWithMixTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsWithMixTypeResult) ProtoMessage() {}

func (x *ListDayCardsWithMixTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsWithMixTypeResult.ProtoReflect.Descriptor instead.
func (*ListDayCardsWithMixTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{16}
}

func (x *ListDayCardsWithMixTypeResult) GetCards() []*CalendarCardView {
	if x != nil {
		return x.Cards
	}
	return nil
}

// list day slot infos result
type ListDaySlotInfosParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the start date
	StartDate string `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// the end date
	EndDate string `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// selected the business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *ListDaySlotInfosParams) Reset() {
	*x = ListDaySlotInfosParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDaySlotInfosParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDaySlotInfosParams) ProtoMessage() {}

func (x *ListDaySlotInfosParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDaySlotInfosParams.ProtoReflect.Descriptor instead.
func (*ListDaySlotInfosParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{17}
}

func (x *ListDaySlotInfosParams) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *ListDaySlotInfosParams) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *ListDaySlotInfosParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// list day slot infos result
type ListDaySlotInfosResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the list of day slot infos
	SlotInfos []*ListDaySlotInfosResult_DaySlotInfo `protobuf:"bytes,1,rep,name=slot_infos,json=slotInfos,proto3" json:"slot_infos,omitempty"`
	// the list of setting day slot infos
	SettingSlotInfos []*ListDaySlotInfosResult_SettingDaySlotInfo `protobuf:"bytes,2,rep,name=setting_slot_infos,json=settingSlotInfos,proto3" json:"setting_slot_infos,omitempty"`
}

func (x *ListDaySlotInfosResult) Reset() {
	*x = ListDaySlotInfosResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDaySlotInfosResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDaySlotInfosResult) ProtoMessage() {}

func (x *ListDaySlotInfosResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDaySlotInfosResult.ProtoReflect.Descriptor instead.
func (*ListDaySlotInfosResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{18}
}

func (x *ListDaySlotInfosResult) GetSlotInfos() []*ListDaySlotInfosResult_DaySlotInfo {
	if x != nil {
		return x.SlotInfos
	}
	return nil
}

func (x *ListDaySlotInfosResult) GetSettingSlotInfos() []*ListDaySlotInfosResult_SettingDaySlotInfo {
	if x != nil {
		return x.SettingSlotInfos
	}
	return nil
}

// calendar card
type ListDayCardsResult_CalendarCardCompositeView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Globally unique ID, type + date + id, eg: APPOINTMENT_2024-05-09_5686153
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// The unique ID corresponding to each card type
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// the card type
	CardType v1.CalendarCardType `protobuf:"varint,3,opt,name=card_type,json=cardType,proto3,enum=moego.models.appointment.v1.CalendarCardType" json:"card_type,omitempty"`
	// appointment status
	AppointmentStatus *v1.AppointmentStatus `protobuf:"varint,4,opt,name=appointment_status,json=appointmentStatus,proto3,enum=moego.models.appointment.v1.AppointmentStatus,oneof" json:"appointment_status,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,5,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// staff id, If there is no staff id, it means no staff assigned
	StaffId *int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// the start date
	Date string `protobuf:"bytes,7,opt,name=date,proto3" json:"date,omitempty"`
	// the end date
	EndDate string `protobuf:"bytes,8,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// the start time, in minutes
	StartTime int64 `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// the end time, in minutes
	EndTime int64 `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// appointment payment status
	PaymentStatus *v1.AppointmentPaymentStatus `protobuf:"varint,11,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.appointment.v1.AppointmentPaymentStatus,oneof" json:"payment_status,omitempty"`
	// pre-auth
	PreAuthInfo *v11.PreAuthCalendarView `protobuf:"bytes,12,opt,name=pre_auth_info,json=preAuthInfo,proto3,oneof" json:"pre_auth_info,omitempty"`
	// required sign agreement
	RequiredSign *bool `protobuf:"varint,13,opt,name=required_sign,json=requiredSign,proto3,oneof" json:"required_sign,omitempty"`
	// alert notes
	AlertNotes *string `protobuf:"bytes,14,opt,name=alert_notes,json=alertNotes,proto3,oneof" json:"alert_notes,omitempty"`
	// ticket comments
	TicketComments *string `protobuf:"bytes,15,opt,name=ticket_comments,json=ticketComments,proto3,oneof" json:"ticket_comments,omitempty"`
	// repeat id, used for repeat appointment
	RepeatId *int64 `protobuf:"varint,16,opt,name=repeat_id,json=repeatId,proto3,oneof" json:"repeat_id,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,17,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// booking request id
	BookingRequestId *int64 `protobuf:"varint,18,opt,name=booking_request_id,json=bookingRequestId,proto3,oneof" json:"booking_request_id,omitempty"`
	// is auto accept, used for online booking auto accept
	IsAutoAccept *bool `protobuf:"varint,19,opt,name=is_auto_accept,json=isAutoAccept,proto3,oneof" json:"is_auto_accept,omitempty"`
	// auto assign, staff and start time for auto assign
	AutoAssign *v12.GroomingAutoAssignView `protobuf:"bytes,20,opt,name=auto_assign,json=autoAssign,proto3,oneof" json:"auto_assign,omitempty"`
	// client info
	ClientInfo *ListDayCardsResult_CalendarCardClientInfo `protobuf:"bytes,21,opt,name=client_info,json=clientInfo,proto3,oneof" json:"client_info,omitempty"`
	// pet info
	PetList []*ListDayCardsResult_CalendarCardPetInfo `protobuf:"bytes,22,rep,name=pet_list,json=petList,proto3" json:"pet_list,omitempty"`
	// estimated total price
	EstimatedTotalPrice *float64 `protobuf:"fixed64,23,opt,name=estimated_total_price,json=estimatedTotalPrice,proto3,oneof" json:"estimated_total_price,omitempty"`
	// paid amount
	PaidAmount *float64 `protobuf:"fixed64,24,opt,name=paid_amount,json=paidAmount,proto3,oneof" json:"paid_amount,omitempty"`
	// prepaid amount
	PrepaidAmount *float64 `protobuf:"fixed64,25,opt,name=prepaid_amount,json=prepaidAmount,proto3,oneof" json:"prepaid_amount,omitempty"`
	// prepay status, used for prepaid booking request
	PrepayStatus *v11.DepositStatus `protobuf:"varint,26,opt,name=prepay_status,json=prepayStatus,proto3,enum=moego.models.payment.v1.DepositStatus,oneof" json:"prepay_status,omitempty"`
	// pet detail ids, used for reschedule card
	PetDetailIds []int32 `protobuf:"varint,27,rep,packed,name=pet_detail_ids,json=petDetailIds,proto3" json:"pet_detail_ids,omitempty"`
	// draggable info, used for reschedule card
	DraggableInfo *ListDayCardsResult_CalendarCardDraggableInfo `protobuf:"bytes,28,opt,name=draggable_info,json=draggableInfo,proto3" json:"draggable_info,omitempty"`
	// service item types
	ServiceItemTypes []v13.ServiceItemType `protobuf:"varint,29,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// memberships
	MembershipSubscriptions *v14.MembershipSubscriptionListModel `protobuf:"bytes,30,opt,name=membership_subscriptions,json=membershipSubscriptions,proto3" json:"membership_subscriptions,omitempty"`
	// invoice. please use orders instead
	//
	// Deprecated: Do not use.
	Invoice *v15.InvoiceCalendarView `protobuf:"bytes,31,opt,name=invoice,proto3" json:"invoice,omitempty"`
	// orders
	Orders []*v15.OrderModelAppointmentView `protobuf:"bytes,32,rep,name=orders,proto3" json:"orders,omitempty"`
}

func (x *ListDayCardsResult_CalendarCardCompositeView) Reset() {
	*x = ListDayCardsResult_CalendarCardCompositeView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult_CalendarCardCompositeView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult_CalendarCardCompositeView) ProtoMessage() {}

func (x *ListDayCardsResult_CalendarCardCompositeView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult_CalendarCardCompositeView.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult_CalendarCardCompositeView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetCardType() v1.CalendarCardType {
	if x != nil {
		return x.CardType
	}
	return v1.CalendarCardType(0)
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetAppointmentStatus() v1.AppointmentStatus {
	if x != nil && x.AppointmentStatus != nil {
		return *x.AppointmentStatus
	}
	return v1.AppointmentStatus(0)
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetPaymentStatus() v1.AppointmentPaymentStatus {
	if x != nil && x.PaymentStatus != nil {
		return *x.PaymentStatus
	}
	return v1.AppointmentPaymentStatus(0)
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetPreAuthInfo() *v11.PreAuthCalendarView {
	if x != nil {
		return x.PreAuthInfo
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetRequiredSign() bool {
	if x != nil && x.RequiredSign != nil {
		return *x.RequiredSign
	}
	return false
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetAlertNotes() string {
	if x != nil && x.AlertNotes != nil {
		return *x.AlertNotes
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetTicketComments() string {
	if x != nil && x.TicketComments != nil {
		return *x.TicketComments
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetRepeatId() int64 {
	if x != nil && x.RepeatId != nil {
		return *x.RepeatId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetBookingRequestId() int64 {
	if x != nil && x.BookingRequestId != nil {
		return *x.BookingRequestId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetIsAutoAccept() bool {
	if x != nil && x.IsAutoAccept != nil {
		return *x.IsAutoAccept
	}
	return false
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetAutoAssign() *v12.GroomingAutoAssignView {
	if x != nil {
		return x.AutoAssign
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetClientInfo() *ListDayCardsResult_CalendarCardClientInfo {
	if x != nil {
		return x.ClientInfo
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetPetList() []*ListDayCardsResult_CalendarCardPetInfo {
	if x != nil {
		return x.PetList
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetEstimatedTotalPrice() float64 {
	if x != nil && x.EstimatedTotalPrice != nil {
		return *x.EstimatedTotalPrice
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetPaidAmount() float64 {
	if x != nil && x.PaidAmount != nil {
		return *x.PaidAmount
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetPrepaidAmount() float64 {
	if x != nil && x.PrepaidAmount != nil {
		return *x.PrepaidAmount
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetPrepayStatus() v11.DepositStatus {
	if x != nil && x.PrepayStatus != nil {
		return *x.PrepayStatus
	}
	return v11.DepositStatus(0)
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetPetDetailIds() []int32 {
	if x != nil {
		return x.PetDetailIds
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetDraggableInfo() *ListDayCardsResult_CalendarCardDraggableInfo {
	if x != nil {
		return x.DraggableInfo
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetServiceItemTypes() []v13.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetMembershipSubscriptions() *v14.MembershipSubscriptionListModel {
	if x != nil {
		return x.MembershipSubscriptions
	}
	return nil
}

// Deprecated: Do not use.
func (x *ListDayCardsResult_CalendarCardCompositeView) GetInvoice() *v15.InvoiceCalendarView {
	if x != nil {
		return x.Invoice
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardCompositeView) GetOrders() []*v15.OrderModelAppointmentView {
	if x != nil {
		return x.Orders
	}
	return nil
}

// the client info for calendar card view
type ListDayCardsResult_CalendarCardClientInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the customer id
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// the customer last name
	CustomerLastName string `protobuf:"bytes,2,opt,name=customer_last_name,json=customerLastName,proto3" json:"customer_last_name,omitempty"`
	// the customer first name
	CustomerFirstName string `protobuf:"bytes,3,opt,name=customer_first_name,json=customerFirstName,proto3" json:"customer_first_name,omitempty"`
	// the color code
	ClientColor string `protobuf:"bytes,4,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	// new flag
	IsNewClient bool `protobuf:"varint,5,opt,name=is_new_client,json=isNewClient,proto3" json:"is_new_client,omitempty"`
	// full address name
	FullAddress string `protobuf:"bytes,6,opt,name=full_address,json=fullAddress,proto3" json:"full_address,omitempty"`
	// certain areas
	Areas []*ListDayCardsResult_CalendarCardCertainArea `protobuf:"bytes,7,rep,name=areas,proto3" json:"areas,omitempty"`
	// address line1
	Address1 string `protobuf:"bytes,8,opt,name=address1,proto3" json:"address1,omitempty"`
	// address line2
	Address2 string `protobuf:"bytes,9,opt,name=address2,proto3" json:"address2,omitempty"`
	// country
	Country string `protobuf:"bytes,10,opt,name=country,proto3" json:"country,omitempty"`
	// address state
	State string `protobuf:"bytes,11,opt,name=state,proto3" json:"state,omitempty"`
	// address city
	City string `protobuf:"bytes,12,opt,name=city,proto3" json:"city,omitempty"`
	// address zipcode
	Zipcode string `protobuf:"bytes,13,opt,name=zipcode,proto3" json:"zipcode,omitempty"`
	// lat lng
	Coordinate *latlng.LatLng `protobuf:"bytes,14,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
}

func (x *ListDayCardsResult_CalendarCardClientInfo) Reset() {
	*x = ListDayCardsResult_CalendarCardClientInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult_CalendarCardClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult_CalendarCardClientInfo) ProtoMessage() {}

func (x *ListDayCardsResult_CalendarCardClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult_CalendarCardClientInfo.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult_CalendarCardClientInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1, 1}
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetCustomerLastName() string {
	if x != nil {
		return x.CustomerLastName
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetCustomerFirstName() string {
	if x != nil {
		return x.CustomerFirstName
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetIsNewClient() bool {
	if x != nil {
		return x.IsNewClient
	}
	return false
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetFullAddress() string {
	if x != nil {
		return x.FullAddress
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetAreas() []*ListDayCardsResult_CalendarCardCertainArea {
	if x != nil {
		return x.Areas
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetZipcode() string {
	if x != nil {
		return x.Zipcode
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardClientInfo) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

// the certain area for calendar card view
type ListDayCardsResult_CalendarCardCertainArea struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// area id
	AreaId int64 `protobuf:"varint,1,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	// area name
	AreaName string `protobuf:"bytes,2,opt,name=area_name,json=areaName,proto3" json:"area_name,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,3,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
}

func (x *ListDayCardsResult_CalendarCardCertainArea) Reset() {
	*x = ListDayCardsResult_CalendarCardCertainArea{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult_CalendarCardCertainArea) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult_CalendarCardCertainArea) ProtoMessage() {}

func (x *ListDayCardsResult_CalendarCardCertainArea) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult_CalendarCardCertainArea.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult_CalendarCardCertainArea) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1, 2}
}

func (x *ListDayCardsResult_CalendarCardCertainArea) GetAreaId() int64 {
	if x != nil {
		return x.AreaId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardCertainArea) GetAreaName() string {
	if x != nil {
		return x.AreaName
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardCertainArea) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

// the pet info for calendar card view
type ListDayCardsResult_CalendarCardPetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pet id
	PetId int32 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// the pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// the pet breed name
	PetBreedName string `protobuf:"bytes,3,opt,name=pet_breed_name,json=petBreedName,proto3" json:"pet_breed_name,omitempty"`
	// the pet code list
	PetCodeList []*ListDayCardsResult_CalendarCardPetCodeInfo `protobuf:"bytes,4,rep,name=pet_code_list,json=petCodeList,proto3" json:"pet_code_list,omitempty"`
	// the service info list
	ServiceList []*ListDayCardsResult_CalendarCardServiceInfo `protobuf:"bytes,5,rep,name=service_list,json=serviceList,proto3" json:"service_list,omitempty"`
	// the vaccine alert info list, deprecated
	//
	// Deprecated: Do not use.
	VaccineAlerts []*ListDayCardsResult_CalendarCardVaccineAlertInfo `protobuf:"bytes,6,rep,name=vaccine_alerts,json=vaccineAlerts,proto3" json:"vaccine_alerts,omitempty"`
	// the evaluation detail list
	Evaluations []*ListDayCardsResult_CalendarCardEvaluationInfo `protobuf:"bytes,7,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
	// pet type
	PetType v17.PetType `protobuf:"varint,8,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// enable vaccine expiry notification, ture is enable, false is disable
	EnableVaccineExpiryNotification bool `protobuf:"varint,9,opt,name=enable_vaccine_expiry_notification,json=enableVaccineExpiryNotification,proto3" json:"enable_vaccine_expiry_notification,omitempty"`
	// the vaccine list
	Vaccines []*ListDayCardsResult_CalendarCardVaccineInfo `protobuf:"bytes,10,rep,name=vaccines,proto3" json:"vaccines,omitempty"`
	// pet size id
	PetSizeId int64 `protobuf:"varint,11,opt,name=pet_size_id,json=petSizeId,proto3" json:"pet_size_id,omitempty"`
}

func (x *ListDayCardsResult_CalendarCardPetInfo) Reset() {
	*x = ListDayCardsResult_CalendarCardPetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult_CalendarCardPetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult_CalendarCardPetInfo) ProtoMessage() {}

func (x *ListDayCardsResult_CalendarCardPetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult_CalendarCardPetInfo.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult_CalendarCardPetInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1, 3}
}

func (x *ListDayCardsResult_CalendarCardPetInfo) GetPetId() int32 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardPetInfo) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardPetInfo) GetPetBreedName() string {
	if x != nil {
		return x.PetBreedName
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardPetInfo) GetPetCodeList() []*ListDayCardsResult_CalendarCardPetCodeInfo {
	if x != nil {
		return x.PetCodeList
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardPetInfo) GetServiceList() []*ListDayCardsResult_CalendarCardServiceInfo {
	if x != nil {
		return x.ServiceList
	}
	return nil
}

// Deprecated: Do not use.
func (x *ListDayCardsResult_CalendarCardPetInfo) GetVaccineAlerts() []*ListDayCardsResult_CalendarCardVaccineAlertInfo {
	if x != nil {
		return x.VaccineAlerts
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardPetInfo) GetEvaluations() []*ListDayCardsResult_CalendarCardEvaluationInfo {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardPetInfo) GetPetType() v17.PetType {
	if x != nil {
		return x.PetType
	}
	return v17.PetType(0)
}

func (x *ListDayCardsResult_CalendarCardPetInfo) GetEnableVaccineExpiryNotification() bool {
	if x != nil {
		return x.EnableVaccineExpiryNotification
	}
	return false
}

func (x *ListDayCardsResult_CalendarCardPetInfo) GetVaccines() []*ListDayCardsResult_CalendarCardVaccineInfo {
	if x != nil {
		return x.Vaccines
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardPetInfo) GetPetSizeId() int64 {
	if x != nil {
		return x.PetSizeId
	}
	return 0
}

// the pet code info for calendar card view
type ListDayCardsResult_CalendarCardPetCodeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pet id
	PetId int32 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// the pet code id
	PetCodeId int32 `protobuf:"varint,2,opt,name=pet_code_id,json=petCodeId,proto3" json:"pet_code_id,omitempty"`
	// the pet code number
	CodeNumber string `protobuf:"bytes,3,opt,name=code_number,json=codeNumber,proto3" json:"code_number,omitempty"`
	// the pet code description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// the pet code color
	Color string `protobuf:"bytes,5,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *ListDayCardsResult_CalendarCardPetCodeInfo) Reset() {
	*x = ListDayCardsResult_CalendarCardPetCodeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult_CalendarCardPetCodeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult_CalendarCardPetCodeInfo) ProtoMessage() {}

func (x *ListDayCardsResult_CalendarCardPetCodeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult_CalendarCardPetCodeInfo.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult_CalendarCardPetCodeInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1, 4}
}

func (x *ListDayCardsResult_CalendarCardPetCodeInfo) GetPetId() int32 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardPetCodeInfo) GetPetCodeId() int32 {
	if x != nil {
		return x.PetCodeId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardPetCodeInfo) GetCodeNumber() string {
	if x != nil {
		return x.CodeNumber
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardPetCodeInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardPetCodeInfo) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// the evaluation service detail
type ListDayCardsResult_CalendarCardEvaluationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation detail id
	EvaluationDetailId int64 `protobuf:"varint,1,opt,name=evaluation_detail_id,json=evaluationDetailId,proto3" json:"evaluation_detail_id,omitempty"`
	// the service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// the service name
	ServiceName string `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// the service color code
	ColorCode string `protobuf:"bytes,4,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// the service time, in minutes
	ServiceTime int32 `protobuf:"varint,5,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// the service price
	ServicePrice float64 `protobuf:"fixed64,6,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
}

func (x *ListDayCardsResult_CalendarCardEvaluationInfo) Reset() {
	*x = ListDayCardsResult_CalendarCardEvaluationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult_CalendarCardEvaluationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult_CalendarCardEvaluationInfo) ProtoMessage() {}

func (x *ListDayCardsResult_CalendarCardEvaluationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult_CalendarCardEvaluationInfo.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult_CalendarCardEvaluationInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1, 5}
}

func (x *ListDayCardsResult_CalendarCardEvaluationInfo) GetEvaluationDetailId() int64 {
	if x != nil {
		return x.EvaluationDetailId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardEvaluationInfo) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardEvaluationInfo) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardEvaluationInfo) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardEvaluationInfo) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardEvaluationInfo) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

// the service info for calendar card view
type ListDayCardsResult_CalendarCardServiceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pet detail id
	PetDetailId int32 `protobuf:"varint,1,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// the service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// the service name
	ServiceName string `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// the service color code
	ColorCode string `protobuf:"bytes,4,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// the service time, in minutes
	ServiceTime int32 `protobuf:"varint,5,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// the service price
	ServicePrice float64 `protobuf:"fixed64,6,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
}

func (x *ListDayCardsResult_CalendarCardServiceInfo) Reset() {
	*x = ListDayCardsResult_CalendarCardServiceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult_CalendarCardServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult_CalendarCardServiceInfo) ProtoMessage() {}

func (x *ListDayCardsResult_CalendarCardServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult_CalendarCardServiceInfo.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult_CalendarCardServiceInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1, 6}
}

func (x *ListDayCardsResult_CalendarCardServiceInfo) GetPetDetailId() int32 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardServiceInfo) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardServiceInfo) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardServiceInfo) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardServiceInfo) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardServiceInfo) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

// the pet vaccine alert for calendar card view
type ListDayCardsResult_CalendarCardVaccineAlertInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the vaccine id
	VaccineId int32 `protobuf:"varint,1,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// the vaccine name
	VaccineName string `protobuf:"bytes,2,opt,name=vaccine_name,json=vaccineName,proto3" json:"vaccine_name,omitempty"`
	// the expiration date
	ExpirationDate string `protobuf:"bytes,3,opt,name=expiration_date,json=expirationDate,proto3" json:"expiration_date,omitempty"`
}

func (x *ListDayCardsResult_CalendarCardVaccineAlertInfo) Reset() {
	*x = ListDayCardsResult_CalendarCardVaccineAlertInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult_CalendarCardVaccineAlertInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult_CalendarCardVaccineAlertInfo) ProtoMessage() {}

func (x *ListDayCardsResult_CalendarCardVaccineAlertInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult_CalendarCardVaccineAlertInfo.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult_CalendarCardVaccineAlertInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1, 7}
}

func (x *ListDayCardsResult_CalendarCardVaccineAlertInfo) GetVaccineId() int32 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardVaccineAlertInfo) GetVaccineName() string {
	if x != nil {
		return x.VaccineName
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardVaccineAlertInfo) GetExpirationDate() string {
	if x != nil {
		return x.ExpirationDate
	}
	return ""
}

// the pet vaccine info for calendar card view
type ListDayCardsResult_CalendarCardVaccineInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the vaccine id
	VaccineId int32 `protobuf:"varint,1,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// the vaccine name
	VaccineName string `protobuf:"bytes,2,opt,name=vaccine_name,json=vaccineName,proto3" json:"vaccine_name,omitempty"`
	// the expiration date
	ExpirationDate string `protobuf:"bytes,3,opt,name=expiration_date,json=expirationDate,proto3" json:"expiration_date,omitempty"`
}

func (x *ListDayCardsResult_CalendarCardVaccineInfo) Reset() {
	*x = ListDayCardsResult_CalendarCardVaccineInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult_CalendarCardVaccineInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult_CalendarCardVaccineInfo) ProtoMessage() {}

func (x *ListDayCardsResult_CalendarCardVaccineInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult_CalendarCardVaccineInfo.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult_CalendarCardVaccineInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1, 8}
}

func (x *ListDayCardsResult_CalendarCardVaccineInfo) GetVaccineId() int32 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *ListDayCardsResult_CalendarCardVaccineInfo) GetVaccineName() string {
	if x != nil {
		return x.VaccineName
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardVaccineInfo) GetExpirationDate() string {
	if x != nil {
		return x.ExpirationDate
	}
	return ""
}

// the draggable info for calendar card view
type ListDayCardsResult_CalendarCardDraggableInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// draggable flag
	Draggable bool `protobuf:"varint,1,opt,name=draggable,proto3" json:"draggable,omitempty"`
	// draggable earliest date
	EarliestDate *string `protobuf:"bytes,2,opt,name=earliest_date,json=earliestDate,proto3,oneof" json:"earliest_date,omitempty"`
	// draggable latest date
	LatestDate *string `protobuf:"bytes,3,opt,name=latest_date,json=latestDate,proto3,oneof" json:"latest_date,omitempty"`
	// unavailable draggable staff ids
	UnavailableStaffList []int64 `protobuf:"varint,4,rep,packed,name=unavailable_staff_list,json=unavailableStaffList,proto3" json:"unavailable_staff_list,omitempty"`
	// allowed to no assigned staff
	AllowedNoAssignedStaff bool `protobuf:"varint,5,opt,name=allowed_no_assigned_staff,json=allowedNoAssignedStaff,proto3" json:"allowed_no_assigned_staff,omitempty"`
}

func (x *ListDayCardsResult_CalendarCardDraggableInfo) Reset() {
	*x = ListDayCardsResult_CalendarCardDraggableInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDayCardsResult_CalendarCardDraggableInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDayCardsResult_CalendarCardDraggableInfo) ProtoMessage() {}

func (x *ListDayCardsResult_CalendarCardDraggableInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDayCardsResult_CalendarCardDraggableInfo.ProtoReflect.Descriptor instead.
func (*ListDayCardsResult_CalendarCardDraggableInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{1, 9}
}

func (x *ListDayCardsResult_CalendarCardDraggableInfo) GetDraggable() bool {
	if x != nil {
		return x.Draggable
	}
	return false
}

func (x *ListDayCardsResult_CalendarCardDraggableInfo) GetEarliestDate() string {
	if x != nil && x.EarliestDate != nil {
		return *x.EarliestDate
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardDraggableInfo) GetLatestDate() string {
	if x != nil && x.LatestDate != nil {
		return *x.LatestDate
	}
	return ""
}

func (x *ListDayCardsResult_CalendarCardDraggableInfo) GetUnavailableStaffList() []int64 {
	if x != nil {
		return x.UnavailableStaffList
	}
	return nil
}

func (x *ListDayCardsResult_CalendarCardDraggableInfo) GetAllowedNoAssignedStaff() bool {
	if x != nil {
		return x.AllowedNoAssignedStaff
	}
	return false
}

// calendar card simple view
type ListMonthCardsResult_CalendarCardSimpleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Globally unique ID, type + date + id, eg: APPOINTMENT_2024-05-09_5686153
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// The unique ID corresponding to each card type
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// the card type
	CardType v1.CalendarCardType `protobuf:"varint,3,opt,name=card_type,json=cardType,proto3,enum=moego.models.appointment.v1.CalendarCardType" json:"card_type,omitempty"`
	// appointment status
	AppointmentStatus *v1.AppointmentStatus `protobuf:"varint,4,opt,name=appointment_status,json=appointmentStatus,proto3,enum=moego.models.appointment.v1.AppointmentStatus,oneof" json:"appointment_status,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,5,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// the appointment start date
	AppointmentDate string `protobuf:"bytes,8,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// the start time, in minutes
	StartTime int64 `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// the end time, in minutes
	EndTime int64 `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// repeat id, used for repeat appointment
	RepeatId *int64 `protobuf:"varint,11,opt,name=repeat_id,json=repeatId,proto3,oneof" json:"repeat_id,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,12,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// is auto accept, used for online booking auto accept
	IsAutoAccept bool `protobuf:"varint,13,opt,name=is_auto_accept,json=isAutoAccept,proto3" json:"is_auto_accept,omitempty"`
	// auto assign, staff and start time for auto assign
	AutoAssign *v12.GroomingAutoAssignView `protobuf:"bytes,14,opt,name=auto_assign,json=autoAssign,proto3,oneof" json:"auto_assign,omitempty"`
	// pet detail ids
	PetDetailIds []int32 `protobuf:"varint,15,rep,packed,name=pet_detail_ids,json=petDetailIds,proto3" json:"pet_detail_ids,omitempty"`
	// pet detail id
	PetDetailId int32 `protobuf:"varint,16,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// the customer id
	CustomerId *int64 `protobuf:"varint,17,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// the customer last name
	CustomerLastName *string `protobuf:"bytes,18,opt,name=customer_last_name,json=customerLastName,proto3,oneof" json:"customer_last_name,omitempty"`
	// the customer first name
	CustomerFirstName *string `protobuf:"bytes,19,opt,name=customer_first_name,json=customerFirstName,proto3,oneof" json:"customer_first_name,omitempty"`
	// the color code
	CustomerColor *string `protobuf:"bytes,20,opt,name=customer_color,json=customerColor,proto3,oneof" json:"customer_color,omitempty"`
	// the service color code
	ServiceColorCode string `protobuf:"bytes,21,opt,name=service_color_code,json=serviceColorCode,proto3" json:"service_color_code,omitempty"`
	// no start time flag
	NoStartTime bool `protobuf:"varint,22,opt,name=no_start_time,json=noStartTime,proto3" json:"no_start_time,omitempty"`
	// is block time flag
	IsBlock bool `protobuf:"varint,23,opt,name=is_block,json=isBlock,proto3" json:"is_block,omitempty"`
	// the block time description
	Desc string `protobuf:"bytes,24,opt,name=desc,proto3" json:"desc,omitempty"`
	// service item types
	ServiceItemTypes []v13.ServiceItemType `protobuf:"varint,29,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) Reset() {
	*x = ListMonthCardsResult_CalendarCardSimpleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonthCardsResult_CalendarCardSimpleView) ProtoMessage() {}

func (x *ListMonthCardsResult_CalendarCardSimpleView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonthCardsResult_CalendarCardSimpleView.ProtoReflect.Descriptor instead.
func (*ListMonthCardsResult_CalendarCardSimpleView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetCardType() v1.CalendarCardType {
	if x != nil {
		return x.CardType
	}
	return v1.CalendarCardType(0)
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetAppointmentStatus() v1.AppointmentStatus {
	if x != nil && x.AppointmentStatus != nil {
		return *x.AppointmentStatus
	}
	return v1.AppointmentStatus(0)
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetRepeatId() int64 {
	if x != nil && x.RepeatId != nil {
		return *x.RepeatId
	}
	return 0
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetIsAutoAccept() bool {
	if x != nil {
		return x.IsAutoAccept
	}
	return false
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetAutoAssign() *v12.GroomingAutoAssignView {
	if x != nil {
		return x.AutoAssign
	}
	return nil
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetPetDetailIds() []int32 {
	if x != nil {
		return x.PetDetailIds
	}
	return nil
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetPetDetailId() int32 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetCustomerLastName() string {
	if x != nil && x.CustomerLastName != nil {
		return *x.CustomerLastName
	}
	return ""
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetCustomerFirstName() string {
	if x != nil && x.CustomerFirstName != nil {
		return *x.CustomerFirstName
	}
	return ""
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetCustomerColor() string {
	if x != nil && x.CustomerColor != nil {
		return *x.CustomerColor
	}
	return ""
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetServiceColorCode() string {
	if x != nil {
		return x.ServiceColorCode
	}
	return ""
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetNoStartTime() bool {
	if x != nil {
		return x.NoStartTime
	}
	return false
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetIsBlock() bool {
	if x != nil {
		return x.IsBlock
	}
	return false
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ListMonthCardsResult_CalendarCardSimpleView) GetServiceItemTypes() []v13.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// day slot info
type ListDaySlotInfosResult_DaySlotInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the start date
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// the staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// the start time, in minutes
	StartTime int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// the end time, in minutes
	EndTime int32 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// pet/family capacity
	PetCapacity int32 `protobuf:"varint,5,opt,name=pet_capacity,json=petCapacity,proto3" json:"pet_capacity,omitempty"`
	// used capacity
	UsedPetCapacity int32 `protobuf:"varint,6,opt,name=used_pet_capacity,json=usedPetCapacity,proto3" json:"used_pet_capacity,omitempty"`
	// used family capacity
	UsedFamilyCapacity int32 `protobuf:"varint,7,opt,name=used_family_capacity,json=usedFamilyCapacity,proto3" json:"used_family_capacity,omitempty"`
	// limitation groups
	LimitationGroups []*v18.LimitationGroup `protobuf:"bytes,8,rep,name=limitation_groups,json=limitationGroups,proto3" json:"limitation_groups,omitempty"`
	// include slot free services
	IncludeSlotFreeServices bool `protobuf:"varint,9,opt,name=include_slot_free_services,json=includeSlotFreeServices,proto3" json:"include_slot_free_services,omitempty"`
}

func (x *ListDaySlotInfosResult_DaySlotInfo) Reset() {
	*x = ListDaySlotInfosResult_DaySlotInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDaySlotInfosResult_DaySlotInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDaySlotInfosResult_DaySlotInfo) ProtoMessage() {}

func (x *ListDaySlotInfosResult_DaySlotInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDaySlotInfosResult_DaySlotInfo.ProtoReflect.Descriptor instead.
func (*ListDaySlotInfosResult_DaySlotInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{18, 0}
}

func (x *ListDaySlotInfosResult_DaySlotInfo) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *ListDaySlotInfosResult_DaySlotInfo) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *ListDaySlotInfosResult_DaySlotInfo) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListDaySlotInfosResult_DaySlotInfo) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListDaySlotInfosResult_DaySlotInfo) GetPetCapacity() int32 {
	if x != nil {
		return x.PetCapacity
	}
	return 0
}

func (x *ListDaySlotInfosResult_DaySlotInfo) GetUsedPetCapacity() int32 {
	if x != nil {
		return x.UsedPetCapacity
	}
	return 0
}

func (x *ListDaySlotInfosResult_DaySlotInfo) GetUsedFamilyCapacity() int32 {
	if x != nil {
		return x.UsedFamilyCapacity
	}
	return 0
}

func (x *ListDaySlotInfosResult_DaySlotInfo) GetLimitationGroups() []*v18.LimitationGroup {
	if x != nil {
		return x.LimitationGroups
	}
	return nil
}

func (x *ListDaySlotInfosResult_DaySlotInfo) GetIncludeSlotFreeServices() bool {
	if x != nil {
		return x.IncludeSlotFreeServices
	}
	return false
}

// setting day slot info
type ListDaySlotInfosResult_SettingDaySlotInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the start date
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// the staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// pet/family capacity
	PetCapacity int32 `protobuf:"varint,5,opt,name=pet_capacity,json=petCapacity,proto3" json:"pet_capacity,omitempty"`
	// used capacity
	UsedPetCapacity int32 `protobuf:"varint,6,opt,name=used_pet_capacity,json=usedPetCapacity,proto3" json:"used_pet_capacity,omitempty"`
	// appointment count
	AppointmentCount int32 `protobuf:"varint,7,opt,name=appointment_count,json=appointmentCount,proto3" json:"appointment_count,omitempty"`
	// limitation groups
	LimitationGroups []*v18.LimitationGroup `protobuf:"bytes,8,rep,name=limitation_groups,json=limitationGroups,proto3" json:"limitation_groups,omitempty"`
}

func (x *ListDaySlotInfosResult_SettingDaySlotInfo) Reset() {
	*x = ListDaySlotInfosResult_SettingDaySlotInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDaySlotInfosResult_SettingDaySlotInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDaySlotInfosResult_SettingDaySlotInfo) ProtoMessage() {}

func (x *ListDaySlotInfosResult_SettingDaySlotInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_calendar_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDaySlotInfosResult_SettingDaySlotInfo.ProtoReflect.Descriptor instead.
func (*ListDaySlotInfosResult_SettingDaySlotInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP(), []int{18, 1}
}

func (x *ListDaySlotInfosResult_SettingDaySlotInfo) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *ListDaySlotInfosResult_SettingDaySlotInfo) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *ListDaySlotInfosResult_SettingDaySlotInfo) GetPetCapacity() int32 {
	if x != nil {
		return x.PetCapacity
	}
	return 0
}

func (x *ListDaySlotInfosResult_SettingDaySlotInfo) GetUsedPetCapacity() int32 {
	if x != nil {
		return x.UsedPetCapacity
	}
	return 0
}

func (x *ListDaySlotInfosResult_SettingDaySlotInfo) GetAppointmentCount() int32 {
	if x != nil {
		return x.AppointmentCount
	}
	return 0
}

func (x *ListDaySlotInfosResult_SettingDaySlotInfo) GetLimitationGroups() []*v18.LimitationGroup {
	if x != nil {
		return x.LimitationGroups
	}
	return nil
}

var File_moego_api_appointment_v1_calendar_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_calendar_api_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6c, 0x65, 0x6e,
	0x64, 0x61, 0x72, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74,
	0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x64, 0x65, 0x66,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x48, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8b, 0x02, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2f,
	0x0a, 0x14, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x4e, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x26, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x4e, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x22, 0x9c, 0x27, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c,
	0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79,
	0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x1a, 0x9e, 0x11, 0x0a,
	0x19, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x4a, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x62, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x11, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x07,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x61, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x48, 0x02, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x55, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x43,
	0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x48, 0x03, 0x52, 0x0b, 0x70,
	0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x53, 0x69, 0x67, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x0a,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a,
	0x0f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x0e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x48, 0x07,
	0x52, 0x08, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x31, 0x0a, 0x12,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x48, 0x08, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x29, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x48, 0x09, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x75, 0x74,
	0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x88, 0x01, 0x01, 0x12, 0x5c, 0x0a, 0x0b, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x48, 0x0a, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x6f, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x69, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79,
	0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x0b, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x37, 0x0a, 0x15, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x01, 0x48,
	0x0c, 0x52, 0x13, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x61, 0x69,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0d,
	0x52, 0x0a, 0x70, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x2a, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0e, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x70, 0x61,
	0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x0d, 0x70,
	0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x0f, 0x52, 0x0c, 0x70, 0x72,
	0x65, 0x70, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a,
	0x0e, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x1b, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0c, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x49, 0x64, 0x73, 0x12, 0x6d, 0x0a, 0x0e, 0x64, 0x72, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61,
	0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x44, 0x72, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x64, 0x72, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x76, 0x0a, 0x18, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x17, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x1f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x48, 0x0a,
	0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x20, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x69,
	0x67, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x6e, 0x6f, 0x74,
	0x65, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x5f, 0x69, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x18,
	0x0a, 0x16, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x61, 0x69,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x72, 0x65,
	0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0xa4, 0x04,
	0x0a, 0x16, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77,
	0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69,
	0x73, 0x4e, 0x65, 0x77, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x75,
	0x6c, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x66, 0x75, 0x6c, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x5a, 0x0a,
	0x05, 0x61, 0x72, 0x65, 0x61, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x43,
	0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e,
	0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x43, 0x65, 0x72, 0x74, 0x61, 0x69, 0x6e, 0x41, 0x72,
	0x65, 0x61, 0x52, 0x05, 0x61, 0x72, 0x65, 0x61, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x32, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x33, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69,
	0x6e, 0x61, 0x74, 0x65, 0x1a, 0x6e, 0x0a, 0x17, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x43, 0x61, 0x72, 0x64, 0x43, 0x65, 0x72, 0x74, 0x61, 0x69, 0x6e, 0x41, 0x72, 0x65, 0x61, 0x12,
	0x17, 0x0a, 0x07, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x61, 0x72, 0x65, 0x61, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x72, 0x65, 0x61,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65,
	0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x1a, 0xae, 0x06, 0x0a, 0x13, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x43, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24,
	0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x68, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61,
	0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x67,
	0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x74, 0x0a, 0x0e, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x12, 0x69, 0x0a,
	0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x22, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x1f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x60, 0x0a, 0x08, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64,
	0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x65, 0x74, 0x53,
	0x69, 0x7a, 0x65, 0x49, 0x64, 0x1a, 0xa9, 0x01, 0x0a, 0x17, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x1a, 0xf7, 0x01, 0x0a, 0x1a, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61,
	0x72, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x30, 0x0a, 0x14, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x1a, 0xe6, 0x01, 0x0a, 0x17,
	0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x1a, 0x89, 0x01, 0x0a, 0x1c, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x43, 0x61, 0x72, 0x64, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65,
	0x1a, 0x84, 0x01, 0x0a, 0x17, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72,
	0x64, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x1a, 0x9c, 0x02, 0x0a, 0x19, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x44, 0x72, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x72, 0x61, 0x67, 0x67, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x64, 0x72, 0x61, 0x67, 0x67, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x0d, 0x65, 0x61, 0x72, 0x6c, 0x69, 0x65, 0x73, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x61,
	0x72, 0x6c, 0x69, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a,
	0x0b, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x01, 0x52, 0x0a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x16, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x14, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x19, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x5f, 0x6e, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x4e, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x61, 0x72, 0x6c, 0x69, 0x65, 0x73,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x81, 0x03, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x43, 0x61, 0x72, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x3a, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0f, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e,
	0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x14, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f,
	0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4e, 0x6f, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x5f, 0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4e, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x3c,
	0x0a, 0x09, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x09,
	0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x22, 0xca, 0x0a, 0x0a, 0x14, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x5b, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x53,
	0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73,
	0x1a, 0xd4, 0x09, 0x0a, 0x16, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72,
	0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x4a, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x62, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x11,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x10, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x08, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x75, 0x74,
	0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x12, 0x5c, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x56, 0x69, 0x65, 0x77, 0x48, 0x03, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0c, 0x70,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x70,
	0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12,
	0x24, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x04, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x05, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a,
	0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x6f, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x6e, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69,
	0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69,
	0x73, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x1d, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x16, 0x0a, 0x14,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xa3, 0x04, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x3b, 0x0a, 0x1b, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x61, 0x74, 0x5f, 0x73, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x61, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x73, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x41, 0x74, 0x53, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x78, 0x0a, 0x14,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x68, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x44, 0x65, 0x66,
	0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x34, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x01, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x65,
	0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x22, 0xad, 0x04,
	0x0a, 0x1a, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x33, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x1b, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74, 0x5f, 0x73, 0x61, 0x6d, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x61, 0x6c, 0x6c, 0x50,
	0x65, 0x74, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x53, 0x61, 0x6d, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x70, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x15, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66,
	0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x13, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x22, 0xcd, 0x10,
	0x0a, 0x10, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x68,
	0x61, 0x73, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x68, 0x61,
	0x73, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x73, 0x12, 0x4a, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x62,
	0x0a, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x11, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88,
	0x01, 0x01, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x61, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x48, 0x02, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x55, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74,
	0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x48, 0x03, 0x52, 0x0b, 0x70, 0x72,
	0x65, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53,
	0x69, 0x67, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f,
	0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x0a, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x0e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x48, 0x07, 0x52,
	0x08, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x11,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x31, 0x0a, 0x12, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x03, 0x48, 0x08, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e,
	0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x09, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x88, 0x01, 0x01, 0x12, 0x5c, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x56, 0x69, 0x65, 0x77, 0x48, 0x0a, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x5c, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x43, 0x61, 0x72, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x0b, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x15, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0c, 0x52, 0x13, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x24, 0x0a, 0x0b, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x52, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x50, 0x61, 0x79, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x48, 0x0e, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x50,
	0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x65, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0c, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x73, 0x12,
	0x5a, 0x0a, 0x0e, 0x64, 0x72, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x44,
	0x72, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x64, 0x72,
	0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x57, 0x0a, 0x12, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x7b, 0x0a, 0x18, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x48, 0x0f, 0x52, 0x17, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x44, 0x0a, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07,
	0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x69, 0x74, 0x5f, 0x70,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x68, 0x69, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x42, 0x15, 0x0a, 0x13,
	0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xf0, 0x02,
	0x0a, 0x18, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73,
	0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x67,
	0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x47, 0x0a, 0x05, 0x61, 0x72, 0x65, 0x61, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x43, 0x65,
	0x72, 0x74, 0x61, 0x69, 0x6e, 0x41, 0x72, 0x65, 0x61, 0x52, 0x05, 0x61, 0x72, 0x65, 0x61, 0x73,
	0x22, 0x6e, 0x0a, 0x17, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64,
	0x43, 0x65, 0x72, 0x74, 0x61, 0x69, 0x6e, 0x41, 0x72, 0x65, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x61,
	0x72, 0x65, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x72,
	0x65, 0x61, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0xce, 0x04, 0x0a, 0x13, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72,
	0x64, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64,
	0x12, 0x4e, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73,
	0x12, 0x4d, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12,
	0x4d, 0x0a, 0x08, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x56,
	0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x22, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x1f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1e, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x49,
	0x64, 0x22, 0x95, 0x01, 0x0a, 0x17, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61,
	0x72, 0x64, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a,
	0x0b, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xf7, 0x01, 0x0a, 0x1a, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x14, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x22, 0xc6, 0x02, 0x0a, 0x17, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x43, 0x61, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x22, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x84, 0x01, 0x0a,
	0x17, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x56, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x65, 0x22, 0xad, 0x04, 0x0a, 0x19, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x43, 0x61, 0x72, 0x64, 0x44, 0x72, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x72, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x64, 0x72, 0x61, 0x67, 0x67, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x28, 0x0a, 0x0d, 0x65, 0x61, 0x72, 0x6c, 0x69, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x61, 0x72, 0x6c, 0x69, 0x65,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x0a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x34, 0x0a, 0x16, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x14, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x19, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x5f, 0x6e, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x4e, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x6f, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x44, 0x72, 0x61, 0x67, 0x67,
	0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4e, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x69,
	0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0f, 0x6e, 0x6f, 0x74, 0x52, 0x65, 0x73,
	0x69, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x85, 0x01, 0x0a, 0x0f, 0x4e, 0x6f,
	0x74, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x1d, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x18, 0x0a, 0x14, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e,
	0x43, 0x4c, 0x55, 0x44, 0x45, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x53, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x53, 0x5f,
	0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10,
	0x03, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x61, 0x72, 0x6c, 0x69, 0x65, 0x73, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x22, 0xc3, 0x03, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x43,
	0x61, 0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x69, 0x78, 0x54, 0x79, 0x70, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x36, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x6f,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4e, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6e,
	0x6f, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x4e, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x3c, 0x0a, 0x09,
	0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x09, 0x70, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x51, 0x0a, 0x09, 0x76, 0x69,
	0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x65, 0x77,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x01,
	0x52, 0x08, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a,
	0x0a, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x61, 0x0a, 0x1d, 0x4c, 0x69, 0x73,
	0x74, 0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x69, 0x78,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x05, 0x63, 0x61,
	0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72,
	0x64, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x22, 0xb4, 0x01, 0x0a,
	0x16, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17,
	0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64,
	0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x22, 0x99, 0x07, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x53,
	0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5b,
	0x0a, 0x0a, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x61, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x09, 0x73, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x71, 0x0a, 0x12, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x44, 0x61, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x90,
	0x03, 0x0a, 0x0b, 0x44, 0x61, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70,
	0x65, 0x74, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73,
	0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x64, 0x50, 0x65, 0x74, 0x43, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x30, 0x0a, 0x14, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x66,
	0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x75, 0x73, 0x65, 0x64, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79,
	0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x5a, 0x0a, 0x11, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x10, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f,
	0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x1a, 0x9b, 0x02, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x79,
	0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70,
	0x65, 0x74, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73,
	0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x64, 0x50, 0x65, 0x74, 0x43, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x5a, 0x0a, 0x11, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x10, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x32,
	0xfa, 0x04, 0x0a, 0x0f, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x6a, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61,
	0x72, 0x64, 0x73, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x70, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x43, 0x61, 0x72, 0x64,
	0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x43, 0x61, 0x72, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x82, 0x01, 0x0a, 0x14, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x69, 0x78, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x69,
	0x78, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x43, 0x61,
	0x72, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x69, 0x78, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x53,
	0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x79, 0x53, 0x6c, 0x6f,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x84, 0x01, 0x0a,
	0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70,
	0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_appointment_v1_calendar_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_calendar_api_proto_rawDescData = file_moego_api_appointment_v1_calendar_api_proto_rawDesc
)

func file_moego_api_appointment_v1_calendar_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_calendar_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_calendar_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_calendar_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_calendar_api_proto_rawDescData
}

var file_moego_api_appointment_v1_calendar_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_api_appointment_v1_calendar_api_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_moego_api_appointment_v1_calendar_api_proto_goTypes = []interface{}{
	(CalendarCardDraggableInfo_NotResizeReason)(0),          // 0: moego.api.appointment.v1.CalendarCardDraggableInfo.NotResizeReason
	(*ListDayCardsParams)(nil),                              // 1: moego.api.appointment.v1.ListDayCardsParams
	(*ListDayCardsResult)(nil),                              // 2: moego.api.appointment.v1.ListDayCardsResult
	(*ListMonthCardsParams)(nil),                            // 3: moego.api.appointment.v1.ListMonthCardsParams
	(*ListMonthCardsResult)(nil),                            // 4: moego.api.appointment.v1.ListMonthCardsResult
	(*PreviewCalendarCardsParams)(nil),                      // 5: moego.api.appointment.v1.PreviewCalendarCardsParams
	(*PreviewCalendarCardsResult)(nil),                      // 6: moego.api.appointment.v1.PreviewCalendarCardsResult
	(*CalendarCardView)(nil),                                // 7: moego.api.appointment.v1.CalendarCardView
	(*CalendarCardCustomerInfo)(nil),                        // 8: moego.api.appointment.v1.CalendarCardCustomerInfo
	(*CalendarCardCertainArea)(nil),                         // 9: moego.api.appointment.v1.CalendarCardCertainArea
	(*CalendarCardPetInfo)(nil),                             // 10: moego.api.appointment.v1.CalendarCardPetInfo
	(*CalendarCardPetCodeInfo)(nil),                         // 11: moego.api.appointment.v1.CalendarCardPetCodeInfo
	(*CalendarCardEvaluationInfo)(nil),                      // 12: moego.api.appointment.v1.CalendarCardEvaluationInfo
	(*CalendarCardServiceInfo)(nil),                         // 13: moego.api.appointment.v1.CalendarCardServiceInfo
	(*CalendarCardVaccineInfo)(nil),                         // 14: moego.api.appointment.v1.CalendarCardVaccineInfo
	(*CalendarCardDraggableInfo)(nil),                       // 15: moego.api.appointment.v1.CalendarCardDraggableInfo
	(*ListDayCardsWithMixTypeParams)(nil),                   // 16: moego.api.appointment.v1.ListDayCardsWithMixTypeParams
	(*ListDayCardsWithMixTypeResult)(nil),                   // 17: moego.api.appointment.v1.ListDayCardsWithMixTypeResult
	(*ListDaySlotInfosParams)(nil),                          // 18: moego.api.appointment.v1.ListDaySlotInfosParams
	(*ListDaySlotInfosResult)(nil),                          // 19: moego.api.appointment.v1.ListDaySlotInfosResult
	(*ListDayCardsResult_CalendarCardCompositeView)(nil),    // 20: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView
	(*ListDayCardsResult_CalendarCardClientInfo)(nil),       // 21: moego.api.appointment.v1.ListDayCardsResult.CalendarCardClientInfo
	(*ListDayCardsResult_CalendarCardCertainArea)(nil),      // 22: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCertainArea
	(*ListDayCardsResult_CalendarCardPetInfo)(nil),          // 23: moego.api.appointment.v1.ListDayCardsResult.CalendarCardPetInfo
	(*ListDayCardsResult_CalendarCardPetCodeInfo)(nil),      // 24: moego.api.appointment.v1.ListDayCardsResult.CalendarCardPetCodeInfo
	(*ListDayCardsResult_CalendarCardEvaluationInfo)(nil),   // 25: moego.api.appointment.v1.ListDayCardsResult.CalendarCardEvaluationInfo
	(*ListDayCardsResult_CalendarCardServiceInfo)(nil),      // 26: moego.api.appointment.v1.ListDayCardsResult.CalendarCardServiceInfo
	(*ListDayCardsResult_CalendarCardVaccineAlertInfo)(nil), // 27: moego.api.appointment.v1.ListDayCardsResult.CalendarCardVaccineAlertInfo
	(*ListDayCardsResult_CalendarCardVaccineInfo)(nil),      // 28: moego.api.appointment.v1.ListDayCardsResult.CalendarCardVaccineInfo
	(*ListDayCardsResult_CalendarCardDraggableInfo)(nil),    // 29: moego.api.appointment.v1.ListDayCardsResult.CalendarCardDraggableInfo
	(*ListMonthCardsResult_CalendarCardSimpleView)(nil),     // 30: moego.api.appointment.v1.ListMonthCardsResult.CalendarCardSimpleView
	(*ListDaySlotInfosResult_DaySlotInfo)(nil),              // 31: moego.api.appointment.v1.ListDaySlotInfosResult.DaySlotInfo
	(*ListDaySlotInfosResult_SettingDaySlotInfo)(nil),       // 32: moego.api.appointment.v1.ListDaySlotInfosResult.SettingDaySlotInfo
	(*date.Date)(nil),                                       // 33: google.type.Date
	(*v2.Predicate)(nil),                                    // 34: moego.utils.v2.Predicate
	(*v1.AppointmentCalendarScheduleDef)(nil),               // 35: moego.models.appointment.v1.AppointmentCalendarScheduleDef
	(*v1.PetServiceCalendarDef)(nil),                        // 36: moego.models.appointment.v1.PetServiceCalendarDef
	(*v1.AppointmentScheduleDef)(nil),                       // 37: moego.models.appointment.v1.AppointmentScheduleDef
	(*v1.PetServiceCalendarScheduleDef)(nil),                // 38: moego.models.appointment.v1.PetServiceCalendarScheduleDef
	(v1.CalendarCardType)(0),                                // 39: moego.models.appointment.v1.CalendarCardType
	(v1.AppointmentStatus)(0),                               // 40: moego.models.appointment.v1.AppointmentStatus
	(v1.AppointmentPaymentStatus)(0),                        // 41: moego.models.appointment.v1.AppointmentPaymentStatus
	(*v11.PreAuthCalendarView)(nil),                         // 42: moego.models.payment.v1.PreAuthCalendarView
	(*v12.GroomingAutoAssignView)(nil),                      // 43: moego.models.online_booking.v1.GroomingAutoAssignView
	(*v11.PrePayCalendarView)(nil),                          // 44: moego.models.payment.v1.PrePayCalendarView
	(v13.ServiceItemType)(0),                                // 45: moego.models.offering.v1.ServiceItemType
	(*v14.MembershipSubscriptionListModel)(nil),             // 46: moego.models.membership.v1.MembershipSubscriptionListModel
	(*v15.InvoiceCalendarView)(nil),                         // 47: moego.models.order.v1.InvoiceCalendarView
	(*v16.BusinessCustomerAddressView)(nil),                 // 48: moego.models.business_customer.v1.BusinessCustomerAddressView
	(v17.PetType)(0),                                        // 49: moego.models.customer.v1.PetType
	(v1.ViewType)(0),                                        // 50: moego.models.appointment.v1.ViewType
	(v11.DepositStatus)(0),                                  // 51: moego.models.payment.v1.DepositStatus
	(*v15.OrderModelAppointmentView)(nil),                   // 52: moego.models.order.v1.OrderModelAppointmentView
	(*latlng.LatLng)(nil),                                   // 53: google.type.LatLng
	(*v18.LimitationGroup)(nil),                             // 54: moego.models.organization.v1.LimitationGroup
}
var file_moego_api_appointment_v1_calendar_api_proto_depIdxs = []int32{
	33, // 0: moego.api.appointment.v1.ListDayCardsParams.start_date:type_name -> google.type.Date
	33, // 1: moego.api.appointment.v1.ListDayCardsParams.end_date:type_name -> google.type.Date
	20, // 2: moego.api.appointment.v1.ListDayCardsResult.cards:type_name -> moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView
	33, // 3: moego.api.appointment.v1.ListMonthCardsParams.start_date:type_name -> google.type.Date
	33, // 4: moego.api.appointment.v1.ListMonthCardsParams.end_date:type_name -> google.type.Date
	34, // 5: moego.api.appointment.v1.ListMonthCardsParams.predicate:type_name -> moego.utils.v2.Predicate
	30, // 6: moego.api.appointment.v1.ListMonthCardsResult.cards:type_name -> moego.api.appointment.v1.ListMonthCardsResult.CalendarCardSimpleView
	35, // 7: moego.api.appointment.v1.PreviewCalendarCardsParams.appointment_schedule:type_name -> moego.models.appointment.v1.AppointmentCalendarScheduleDef
	36, // 8: moego.api.appointment.v1.PreviewCalendarCardsParams.pet_services:type_name -> moego.models.appointment.v1.PetServiceCalendarDef
	37, // 9: moego.api.appointment.v1.PreviewCalendarCardsResult.appointment_schedule:type_name -> moego.models.appointment.v1.AppointmentScheduleDef
	38, // 10: moego.api.appointment.v1.PreviewCalendarCardsResult.pet_service_schedules:type_name -> moego.models.appointment.v1.PetServiceCalendarScheduleDef
	7,  // 11: moego.api.appointment.v1.PreviewCalendarCardsResult.cards:type_name -> moego.api.appointment.v1.CalendarCardView
	39, // 12: moego.api.appointment.v1.CalendarCardView.card_type:type_name -> moego.models.appointment.v1.CalendarCardType
	40, // 13: moego.api.appointment.v1.CalendarCardView.appointment_status:type_name -> moego.models.appointment.v1.AppointmentStatus
	41, // 14: moego.api.appointment.v1.CalendarCardView.payment_status:type_name -> moego.models.appointment.v1.AppointmentPaymentStatus
	42, // 15: moego.api.appointment.v1.CalendarCardView.pre_auth_info:type_name -> moego.models.payment.v1.PreAuthCalendarView
	43, // 16: moego.api.appointment.v1.CalendarCardView.auto_assign:type_name -> moego.models.online_booking.v1.GroomingAutoAssignView
	8,  // 17: moego.api.appointment.v1.CalendarCardView.customer_info:type_name -> moego.api.appointment.v1.CalendarCardCustomerInfo
	10, // 18: moego.api.appointment.v1.CalendarCardView.pets:type_name -> moego.api.appointment.v1.CalendarCardPetInfo
	44, // 19: moego.api.appointment.v1.CalendarCardView.pre_pay_info:type_name -> moego.models.payment.v1.PrePayCalendarView
	15, // 20: moego.api.appointment.v1.CalendarCardView.draggable_info:type_name -> moego.api.appointment.v1.CalendarCardDraggableInfo
	45, // 21: moego.api.appointment.v1.CalendarCardView.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	46, // 22: moego.api.appointment.v1.CalendarCardView.membership_subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionListModel
	47, // 23: moego.api.appointment.v1.CalendarCardView.invoice:type_name -> moego.models.order.v1.InvoiceCalendarView
	48, // 24: moego.api.appointment.v1.CalendarCardCustomerInfo.primary_address:type_name -> moego.models.business_customer.v1.BusinessCustomerAddressView
	9,  // 25: moego.api.appointment.v1.CalendarCardCustomerInfo.areas:type_name -> moego.api.appointment.v1.CalendarCardCertainArea
	11, // 26: moego.api.appointment.v1.CalendarCardPetInfo.pet_codes:type_name -> moego.api.appointment.v1.CalendarCardPetCodeInfo
	13, // 27: moego.api.appointment.v1.CalendarCardPetInfo.services:type_name -> moego.api.appointment.v1.CalendarCardServiceInfo
	14, // 28: moego.api.appointment.v1.CalendarCardPetInfo.vaccines:type_name -> moego.api.appointment.v1.CalendarCardVaccineInfo
	12, // 29: moego.api.appointment.v1.CalendarCardPetInfo.evaluations:type_name -> moego.api.appointment.v1.CalendarCardEvaluationInfo
	49, // 30: moego.api.appointment.v1.CalendarCardPetInfo.pet_type:type_name -> moego.models.customer.v1.PetType
	0,  // 31: moego.api.appointment.v1.CalendarCardDraggableInfo.not_resize_reason:type_name -> moego.api.appointment.v1.CalendarCardDraggableInfo.NotResizeReason
	33, // 32: moego.api.appointment.v1.ListDayCardsWithMixTypeParams.start_date:type_name -> google.type.Date
	33, // 33: moego.api.appointment.v1.ListDayCardsWithMixTypeParams.end_date:type_name -> google.type.Date
	34, // 34: moego.api.appointment.v1.ListDayCardsWithMixTypeParams.predicate:type_name -> moego.utils.v2.Predicate
	50, // 35: moego.api.appointment.v1.ListDayCardsWithMixTypeParams.view_type:type_name -> moego.models.appointment.v1.ViewType
	7,  // 36: moego.api.appointment.v1.ListDayCardsWithMixTypeResult.cards:type_name -> moego.api.appointment.v1.CalendarCardView
	31, // 37: moego.api.appointment.v1.ListDaySlotInfosResult.slot_infos:type_name -> moego.api.appointment.v1.ListDaySlotInfosResult.DaySlotInfo
	32, // 38: moego.api.appointment.v1.ListDaySlotInfosResult.setting_slot_infos:type_name -> moego.api.appointment.v1.ListDaySlotInfosResult.SettingDaySlotInfo
	39, // 39: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.card_type:type_name -> moego.models.appointment.v1.CalendarCardType
	40, // 40: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.appointment_status:type_name -> moego.models.appointment.v1.AppointmentStatus
	41, // 41: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.payment_status:type_name -> moego.models.appointment.v1.AppointmentPaymentStatus
	42, // 42: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.pre_auth_info:type_name -> moego.models.payment.v1.PreAuthCalendarView
	43, // 43: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.auto_assign:type_name -> moego.models.online_booking.v1.GroomingAutoAssignView
	21, // 44: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.client_info:type_name -> moego.api.appointment.v1.ListDayCardsResult.CalendarCardClientInfo
	23, // 45: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.pet_list:type_name -> moego.api.appointment.v1.ListDayCardsResult.CalendarCardPetInfo
	51, // 46: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.prepay_status:type_name -> moego.models.payment.v1.DepositStatus
	29, // 47: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.draggable_info:type_name -> moego.api.appointment.v1.ListDayCardsResult.CalendarCardDraggableInfo
	45, // 48: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	46, // 49: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.membership_subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionListModel
	47, // 50: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.invoice:type_name -> moego.models.order.v1.InvoiceCalendarView
	52, // 51: moego.api.appointment.v1.ListDayCardsResult.CalendarCardCompositeView.orders:type_name -> moego.models.order.v1.OrderModelAppointmentView
	22, // 52: moego.api.appointment.v1.ListDayCardsResult.CalendarCardClientInfo.areas:type_name -> moego.api.appointment.v1.ListDayCardsResult.CalendarCardCertainArea
	53, // 53: moego.api.appointment.v1.ListDayCardsResult.CalendarCardClientInfo.coordinate:type_name -> google.type.LatLng
	24, // 54: moego.api.appointment.v1.ListDayCardsResult.CalendarCardPetInfo.pet_code_list:type_name -> moego.api.appointment.v1.ListDayCardsResult.CalendarCardPetCodeInfo
	26, // 55: moego.api.appointment.v1.ListDayCardsResult.CalendarCardPetInfo.service_list:type_name -> moego.api.appointment.v1.ListDayCardsResult.CalendarCardServiceInfo
	27, // 56: moego.api.appointment.v1.ListDayCardsResult.CalendarCardPetInfo.vaccine_alerts:type_name -> moego.api.appointment.v1.ListDayCardsResult.CalendarCardVaccineAlertInfo
	25, // 57: moego.api.appointment.v1.ListDayCardsResult.CalendarCardPetInfo.evaluations:type_name -> moego.api.appointment.v1.ListDayCardsResult.CalendarCardEvaluationInfo
	49, // 58: moego.api.appointment.v1.ListDayCardsResult.CalendarCardPetInfo.pet_type:type_name -> moego.models.customer.v1.PetType
	28, // 59: moego.api.appointment.v1.ListDayCardsResult.CalendarCardPetInfo.vaccines:type_name -> moego.api.appointment.v1.ListDayCardsResult.CalendarCardVaccineInfo
	39, // 60: moego.api.appointment.v1.ListMonthCardsResult.CalendarCardSimpleView.card_type:type_name -> moego.models.appointment.v1.CalendarCardType
	40, // 61: moego.api.appointment.v1.ListMonthCardsResult.CalendarCardSimpleView.appointment_status:type_name -> moego.models.appointment.v1.AppointmentStatus
	43, // 62: moego.api.appointment.v1.ListMonthCardsResult.CalendarCardSimpleView.auto_assign:type_name -> moego.models.online_booking.v1.GroomingAutoAssignView
	45, // 63: moego.api.appointment.v1.ListMonthCardsResult.CalendarCardSimpleView.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	54, // 64: moego.api.appointment.v1.ListDaySlotInfosResult.DaySlotInfo.limitation_groups:type_name -> moego.models.organization.v1.LimitationGroup
	54, // 65: moego.api.appointment.v1.ListDaySlotInfosResult.SettingDaySlotInfo.limitation_groups:type_name -> moego.models.organization.v1.LimitationGroup
	1,  // 66: moego.api.appointment.v1.CalendarService.ListDayCards:input_type -> moego.api.appointment.v1.ListDayCardsParams
	3,  // 67: moego.api.appointment.v1.CalendarService.ListMonthCards:input_type -> moego.api.appointment.v1.ListMonthCardsParams
	5,  // 68: moego.api.appointment.v1.CalendarService.PreviewCalendarCards:input_type -> moego.api.appointment.v1.PreviewCalendarCardsParams
	16, // 69: moego.api.appointment.v1.CalendarService.ListDayCardsWithMixType:input_type -> moego.api.appointment.v1.ListDayCardsWithMixTypeParams
	18, // 70: moego.api.appointment.v1.CalendarService.ListDaySlotInfos:input_type -> moego.api.appointment.v1.ListDaySlotInfosParams
	2,  // 71: moego.api.appointment.v1.CalendarService.ListDayCards:output_type -> moego.api.appointment.v1.ListDayCardsResult
	4,  // 72: moego.api.appointment.v1.CalendarService.ListMonthCards:output_type -> moego.api.appointment.v1.ListMonthCardsResult
	6,  // 73: moego.api.appointment.v1.CalendarService.PreviewCalendarCards:output_type -> moego.api.appointment.v1.PreviewCalendarCardsResult
	17, // 74: moego.api.appointment.v1.CalendarService.ListDayCardsWithMixType:output_type -> moego.api.appointment.v1.ListDayCardsWithMixTypeResult
	19, // 75: moego.api.appointment.v1.CalendarService.ListDaySlotInfos:output_type -> moego.api.appointment.v1.ListDaySlotInfosResult
	71, // [71:76] is the sub-list for method output_type
	66, // [66:71] is the sub-list for method input_type
	66, // [66:66] is the sub-list for extension type_name
	66, // [66:66] is the sub-list for extension extendee
	0,  // [0:66] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_calendar_api_proto_init() }
func file_moego_api_appointment_v1_calendar_api_proto_init() {
	if File_moego_api_appointment_v1_calendar_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMonthCardsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMonthCardsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewCalendarCardsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewCalendarCardsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarCardView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarCardCustomerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarCardCertainArea); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarCardPetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarCardPetCodeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarCardEvaluationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarCardServiceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarCardVaccineInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarCardDraggableInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsWithMixTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsWithMixTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDaySlotInfosParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDaySlotInfosResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult_CalendarCardCompositeView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult_CalendarCardClientInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult_CalendarCardCertainArea); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult_CalendarCardPetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult_CalendarCardPetCodeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult_CalendarCardEvaluationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult_CalendarCardServiceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult_CalendarCardVaccineAlertInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult_CalendarCardVaccineInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDayCardsResult_CalendarCardDraggableInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMonthCardsResult_CalendarCardSimpleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDaySlotInfosResult_DaySlotInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_calendar_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDaySlotInfosResult_SettingDaySlotInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_calendar_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_calendar_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_calendar_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_calendar_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_calendar_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_calendar_api_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_calendar_api_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_calendar_api_proto_msgTypes[19].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_calendar_api_proto_msgTypes[28].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_calendar_api_proto_msgTypes[29].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_calendar_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_calendar_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_calendar_api_proto_depIdxs,
		EnumInfos:         file_moego_api_appointment_v1_calendar_api_proto_enumTypes,
		MessageInfos:      file_moego_api_appointment_v1_calendar_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_calendar_api_proto = out.File
	file_moego_api_appointment_v1_calendar_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_calendar_api_proto_goTypes = nil
	file_moego_api_appointment_v1_calendar_api_proto_depIdxs = nil
}
