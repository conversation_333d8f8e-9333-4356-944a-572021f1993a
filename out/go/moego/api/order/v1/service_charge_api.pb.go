// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/order/v1/service_charge_api.proto

package orderapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get service charge request params
type GetServiceChargeListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is active
	IsActive *bool `protobuf:"varint,1,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// appointment id
	AppointmentId *int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
}

func (x *GetServiceChargeListRequest) Reset() {
	*x = GetServiceChargeListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceChargeListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceChargeListRequest) ProtoMessage() {}

func (x *GetServiceChargeListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceChargeListRequest.ProtoReflect.Descriptor instead.
func (*GetServiceChargeListRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_service_charge_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetServiceChargeListRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *GetServiceChargeListRequest) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

// add service charge request params
type AddServiceChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// split method
	Description *string `protobuf:"bytes,2,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// price, must be positive
	Price float64 `protobuf:"fixed64,3,opt,name=price,proto3" json:"price,omitempty"`
	// tax id, 0 or null means no tax
	TaxId *int32 `protobuf:"varint,4,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// is mandatory
	// deprecated by Freeman since 2024/9/25, use auto_apply_status instead
	//
	// Deprecated: Do not use.
	IsMandatory *bool `protobuf:"varint,5,opt,name=is_mandatory,json=isMandatory,proto3,oneof" json:"is_mandatory,omitempty"`
	// is active
	IsActive *bool `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// apply to upcoming
	ApplyUpcomingAppt *bool `protobuf:"varint,7,opt,name=apply_upcoming_appt,json=applyUpcomingAppt,proto3,oneof" json:"apply_upcoming_appt,omitempty"`
	// auto apply status
	AutoApplyStatus *v1.ServiceCharge_AutoApplyStatus `protobuf:"varint,8,opt,name=auto_apply_status,json=autoApplyStatus,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyStatus,oneof" json:"auto_apply_status,omitempty"`
	// auto apply condition
	AutoApplyCondition *v1.ServiceCharge_AutoApplyCondition `protobuf:"varint,9,opt,name=auto_apply_condition,json=autoApplyCondition,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyCondition,oneof" json:"auto_apply_condition,omitempty"`
	// auto apply time
	AutoApplyTime *int32 `protobuf:"varint,10,opt,name=auto_apply_time,json=autoApplyTime,proto3,oneof" json:"auto_apply_time,omitempty"`
	// source
	Source *v1.ServiceCharge_Source `protobuf:"varint,11,opt,name=source,proto3,enum=moego.models.order.v1.ServiceCharge_Source,oneof" json:"source,omitempty"`
}

func (x *AddServiceChargeRequest) Reset() {
	*x = AddServiceChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddServiceChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddServiceChargeRequest) ProtoMessage() {}

func (x *AddServiceChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddServiceChargeRequest.ProtoReflect.Descriptor instead.
func (*AddServiceChargeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_service_charge_api_proto_rawDescGZIP(), []int{1}
}

func (x *AddServiceChargeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddServiceChargeRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AddServiceChargeRequest) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *AddServiceChargeRequest) GetTaxId() int32 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

// Deprecated: Do not use.
func (x *AddServiceChargeRequest) GetIsMandatory() bool {
	if x != nil && x.IsMandatory != nil {
		return *x.IsMandatory
	}
	return false
}

func (x *AddServiceChargeRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *AddServiceChargeRequest) GetApplyUpcomingAppt() bool {
	if x != nil && x.ApplyUpcomingAppt != nil {
		return *x.ApplyUpcomingAppt
	}
	return false
}

func (x *AddServiceChargeRequest) GetAutoApplyStatus() v1.ServiceCharge_AutoApplyStatus {
	if x != nil && x.AutoApplyStatus != nil {
		return *x.AutoApplyStatus
	}
	return v1.ServiceCharge_AutoApplyStatus(0)
}

func (x *AddServiceChargeRequest) GetAutoApplyCondition() v1.ServiceCharge_AutoApplyCondition {
	if x != nil && x.AutoApplyCondition != nil {
		return *x.AutoApplyCondition
	}
	return v1.ServiceCharge_AutoApplyCondition(0)
}

func (x *AddServiceChargeRequest) GetAutoApplyTime() int32 {
	if x != nil && x.AutoApplyTime != nil {
		return *x.AutoApplyTime
	}
	return 0
}

func (x *AddServiceChargeRequest) GetSource() v1.ServiceCharge_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.ServiceCharge_Source(0)
}

// update service charge request params
type UpdateServiceChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id, exist for update
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// split method
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// price, must be positive
	Price *float64 `protobuf:"fixed64,4,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// tax id, 0 or null means no tax
	TaxId *int32 `protobuf:"varint,5,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// is mandatory, preserved 8-19 for future use
	// deprecated by Freeman since 2024/9/25, use auto_apply_status instead
	//
	// Deprecated: Do not use.
	IsMandatory *bool `protobuf:"varint,6,opt,name=is_mandatory,json=isMandatory,proto3,oneof" json:"is_mandatory,omitempty"`
	// is active
	IsActive *bool `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// apply to upcoming
	ApplyUpcomingAppt *bool `protobuf:"varint,8,opt,name=apply_upcoming_appt,json=applyUpcomingAppt,proto3,oneof" json:"apply_upcoming_appt,omitempty"`
	// auto apply status
	AutoApplyStatus *v1.ServiceCharge_AutoApplyStatus `protobuf:"varint,9,opt,name=auto_apply_status,json=autoApplyStatus,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyStatus,oneof" json:"auto_apply_status,omitempty"`
	// auto apply condition
	AutoApplyCondition *v1.ServiceCharge_AutoApplyCondition `protobuf:"varint,10,opt,name=auto_apply_condition,json=autoApplyCondition,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyCondition,oneof" json:"auto_apply_condition,omitempty"`
	// auto apply time
	AutoApplyTime *int32 `protobuf:"varint,11,opt,name=auto_apply_time,json=autoApplyTime,proto3,oneof" json:"auto_apply_time,omitempty"`
	// source
	Source *v1.ServiceCharge_Source `protobuf:"varint,12,opt,name=source,proto3,enum=moego.models.order.v1.ServiceCharge_Source,oneof" json:"source,omitempty"`
}

func (x *UpdateServiceChargeRequest) Reset() {
	*x = UpdateServiceChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceChargeRequest) ProtoMessage() {}

func (x *UpdateServiceChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceChargeRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceChargeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_service_charge_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateServiceChargeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateServiceChargeRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateServiceChargeRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateServiceChargeRequest) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *UpdateServiceChargeRequest) GetTaxId() int32 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

// Deprecated: Do not use.
func (x *UpdateServiceChargeRequest) GetIsMandatory() bool {
	if x != nil && x.IsMandatory != nil {
		return *x.IsMandatory
	}
	return false
}

func (x *UpdateServiceChargeRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *UpdateServiceChargeRequest) GetApplyUpcomingAppt() bool {
	if x != nil && x.ApplyUpcomingAppt != nil {
		return *x.ApplyUpcomingAppt
	}
	return false
}

func (x *UpdateServiceChargeRequest) GetAutoApplyStatus() v1.ServiceCharge_AutoApplyStatus {
	if x != nil && x.AutoApplyStatus != nil {
		return *x.AutoApplyStatus
	}
	return v1.ServiceCharge_AutoApplyStatus(0)
}

func (x *UpdateServiceChargeRequest) GetAutoApplyCondition() v1.ServiceCharge_AutoApplyCondition {
	if x != nil && x.AutoApplyCondition != nil {
		return *x.AutoApplyCondition
	}
	return v1.ServiceCharge_AutoApplyCondition(0)
}

func (x *UpdateServiceChargeRequest) GetAutoApplyTime() int32 {
	if x != nil && x.AutoApplyTime != nil {
		return *x.AutoApplyTime
	}
	return 0
}

func (x *UpdateServiceChargeRequest) GetSource() v1.ServiceCharge_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.ServiceCharge_Source(0)
}

// sort service charge request params
type SortServiceChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sorted id list from front-end
	SortedId []int64 `protobuf:"varint,1,rep,packed,name=sorted_id,json=sortedId,proto3" json:"sorted_id,omitempty"`
}

func (x *SortServiceChargeRequest) Reset() {
	*x = SortServiceChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortServiceChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortServiceChargeRequest) ProtoMessage() {}

func (x *SortServiceChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortServiceChargeRequest.ProtoReflect.Descriptor instead.
func (*SortServiceChargeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_service_charge_api_proto_rawDescGZIP(), []int{3}
}

func (x *SortServiceChargeRequest) GetSortedId() []int64 {
	if x != nil {
		return x.SortedId
	}
	return nil
}

// delete service charge request params
type DeleteServiceChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// to be deleted id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// apply to upcoming, if ture, will delete service charge from upcoming unconfirmed and no-fully-paid appointments
	ApplyUpcomingAppt *bool `protobuf:"varint,2,opt,name=apply_upcoming_appt,json=applyUpcomingAppt,proto3,oneof" json:"apply_upcoming_appt,omitempty"`
}

func (x *DeleteServiceChargeRequest) Reset() {
	*x = DeleteServiceChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteServiceChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceChargeRequest) ProtoMessage() {}

func (x *DeleteServiceChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceChargeRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceChargeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_service_charge_api_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteServiceChargeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteServiceChargeRequest) GetApplyUpcomingAppt() bool {
	if x != nil && x.ApplyUpcomingAppt != nil {
		return *x.ApplyUpcomingAppt
	}
	return false
}

// get service charge list response
type GetServiceChargeListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge list
	ServiceCharge []*v1.ServiceCharge `protobuf:"bytes,1,rep,name=service_charge,json=serviceCharge,proto3" json:"service_charge,omitempty"`
}

func (x *GetServiceChargeListResponse) Reset() {
	*x = GetServiceChargeListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceChargeListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceChargeListResponse) ProtoMessage() {}

func (x *GetServiceChargeListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceChargeListResponse.ProtoReflect.Descriptor instead.
func (*GetServiceChargeListResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_service_charge_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetServiceChargeListResponse) GetServiceCharge() []*v1.ServiceCharge {
	if x != nil {
		return x.ServiceCharge
	}
	return nil
}

// operate service charge response
type OperateServiceChargeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *OperateServiceChargeResponse) Reset() {
	*x = OperateServiceChargeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateServiceChargeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateServiceChargeResponse) ProtoMessage() {}

func (x *OperateServiceChargeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_service_charge_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateServiceChargeResponse.ProtoReflect.Descriptor instead.
func (*OperateServiceChargeResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_service_charge_api_proto_rawDescGZIP(), []int{6}
}

func (x *OperateServiceChargeResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

var File_moego_api_order_v1_service_charge_api_proto protoreflect.FileDescriptor

var file_moego_api_order_v1_service_charge_api_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x95, 0x01, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x09,
	0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x00, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x01, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x22, 0xd8, 0x06, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x96, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x31, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xe8,
	0x07, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x78,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x28, 0x00, 0x48, 0x01, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2a,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x48, 0x02, 0x52, 0x0b, 0x69, 0x73, 0x4d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52,
	0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x13,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61,
	0x70, 0x70, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x11, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x71, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x05,
	0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x7a, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x06, 0x52, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x37, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05,
	0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x07, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x48, 0x08, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x69,
	0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x42, 0x0c, 0x0a, 0x0a, 0x5f,
	0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x70,
	0x74, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x12, 0x0a, 0x10, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22,
	0x91, 0x07, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x96,
	0x01, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xe8, 0x07, 0x48, 0x01, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12,
	0x29, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e,
	0xfa, 0x42, 0x0b, 0x12, 0x09, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x02,
	0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61,
	0x78, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x00, 0x48, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x2a, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x48, 0x04, 0x52, 0x0b, 0x69, 0x73, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05,
	0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a,
	0x13, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f,
	0x61, 0x70, 0x70, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x11, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x71, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48,
	0x07, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x7a, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x08, 0x52, 0x12, 0x61, 0x75, 0x74, 0x6f,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x37, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a,
	0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x09, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x48, 0x0a, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x16, 0x0a,
	0x14, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x70, 0x70, 0x74, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x22, 0x45, 0x0a, 0x18, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x29, 0x0a, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x08, 0x73, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x49, 0x64, 0x22, 0x79, 0x0a, 0x1a, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x6c,
	0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x55, 0x70,
	0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x74, 0x88, 0x01, 0x01, 0x42, 0x16, 0x0a,
	0x14, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x70, 0x70, 0x74, 0x22, 0x6b, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x22, 0x36, 0x0a, 0x1c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xd3, 0x04, 0x0a, 0x14, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x79, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65,
	0x0a, 0x10, 0x41, 0x64, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x6b, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x12, 0x73, 0x0a, 0x11, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x77, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x72, 0x0a, 0x1a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_order_v1_service_charge_api_proto_rawDescOnce sync.Once
	file_moego_api_order_v1_service_charge_api_proto_rawDescData = file_moego_api_order_v1_service_charge_api_proto_rawDesc
)

func file_moego_api_order_v1_service_charge_api_proto_rawDescGZIP() []byte {
	file_moego_api_order_v1_service_charge_api_proto_rawDescOnce.Do(func() {
		file_moego_api_order_v1_service_charge_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_order_v1_service_charge_api_proto_rawDescData)
	})
	return file_moego_api_order_v1_service_charge_api_proto_rawDescData
}

var file_moego_api_order_v1_service_charge_api_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_api_order_v1_service_charge_api_proto_goTypes = []interface{}{
	(*GetServiceChargeListRequest)(nil),      // 0: moego.api.order.v1.GetServiceChargeListRequest
	(*AddServiceChargeRequest)(nil),          // 1: moego.api.order.v1.AddServiceChargeRequest
	(*UpdateServiceChargeRequest)(nil),       // 2: moego.api.order.v1.UpdateServiceChargeRequest
	(*SortServiceChargeRequest)(nil),         // 3: moego.api.order.v1.SortServiceChargeRequest
	(*DeleteServiceChargeRequest)(nil),       // 4: moego.api.order.v1.DeleteServiceChargeRequest
	(*GetServiceChargeListResponse)(nil),     // 5: moego.api.order.v1.GetServiceChargeListResponse
	(*OperateServiceChargeResponse)(nil),     // 6: moego.api.order.v1.OperateServiceChargeResponse
	(v1.ServiceCharge_AutoApplyStatus)(0),    // 7: moego.models.order.v1.ServiceCharge.AutoApplyStatus
	(v1.ServiceCharge_AutoApplyCondition)(0), // 8: moego.models.order.v1.ServiceCharge.AutoApplyCondition
	(v1.ServiceCharge_Source)(0),             // 9: moego.models.order.v1.ServiceCharge.Source
	(*v1.ServiceCharge)(nil),                 // 10: moego.models.order.v1.ServiceCharge
}
var file_moego_api_order_v1_service_charge_api_proto_depIdxs = []int32{
	7,  // 0: moego.api.order.v1.AddServiceChargeRequest.auto_apply_status:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyStatus
	8,  // 1: moego.api.order.v1.AddServiceChargeRequest.auto_apply_condition:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyCondition
	9,  // 2: moego.api.order.v1.AddServiceChargeRequest.source:type_name -> moego.models.order.v1.ServiceCharge.Source
	7,  // 3: moego.api.order.v1.UpdateServiceChargeRequest.auto_apply_status:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyStatus
	8,  // 4: moego.api.order.v1.UpdateServiceChargeRequest.auto_apply_condition:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyCondition
	9,  // 5: moego.api.order.v1.UpdateServiceChargeRequest.source:type_name -> moego.models.order.v1.ServiceCharge.Source
	10, // 6: moego.api.order.v1.GetServiceChargeListResponse.service_charge:type_name -> moego.models.order.v1.ServiceCharge
	0,  // 7: moego.api.order.v1.ServiceChargeService.GetServiceChargeList:input_type -> moego.api.order.v1.GetServiceChargeListRequest
	1,  // 8: moego.api.order.v1.ServiceChargeService.AddServiceCharge:input_type -> moego.api.order.v1.AddServiceChargeRequest
	2,  // 9: moego.api.order.v1.ServiceChargeService.UpdateServiceCharge:input_type -> moego.api.order.v1.UpdateServiceChargeRequest
	3,  // 10: moego.api.order.v1.ServiceChargeService.SortServiceCharge:input_type -> moego.api.order.v1.SortServiceChargeRequest
	4,  // 11: moego.api.order.v1.ServiceChargeService.DeleteServiceCharge:input_type -> moego.api.order.v1.DeleteServiceChargeRequest
	5,  // 12: moego.api.order.v1.ServiceChargeService.GetServiceChargeList:output_type -> moego.api.order.v1.GetServiceChargeListResponse
	10, // 13: moego.api.order.v1.ServiceChargeService.AddServiceCharge:output_type -> moego.models.order.v1.ServiceCharge
	10, // 14: moego.api.order.v1.ServiceChargeService.UpdateServiceCharge:output_type -> moego.models.order.v1.ServiceCharge
	6,  // 15: moego.api.order.v1.ServiceChargeService.SortServiceCharge:output_type -> moego.api.order.v1.OperateServiceChargeResponse
	6,  // 16: moego.api.order.v1.ServiceChargeService.DeleteServiceCharge:output_type -> moego.api.order.v1.OperateServiceChargeResponse
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_api_order_v1_service_charge_api_proto_init() }
func file_moego_api_order_v1_service_charge_api_proto_init() {
	if File_moego_api_order_v1_service_charge_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_order_v1_service_charge_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceChargeListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_service_charge_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddServiceChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_service_charge_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_service_charge_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortServiceChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_service_charge_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteServiceChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_service_charge_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceChargeListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_service_charge_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateServiceChargeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_order_v1_service_charge_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_service_charge_api_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_service_charge_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_service_charge_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_order_v1_service_charge_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_order_v1_service_charge_api_proto_goTypes,
		DependencyIndexes: file_moego_api_order_v1_service_charge_api_proto_depIdxs,
		MessageInfos:      file_moego_api_order_v1_service_charge_api_proto_msgTypes,
	}.Build()
	File_moego_api_order_v1_service_charge_api_proto = out.File
	file_moego_api_order_v1_service_charge_api_proto_rawDesc = nil
	file_moego_api_order_v1_service_charge_api_proto_goTypes = nil
	file_moego_api_order_v1_service_charge_api_proto_depIdxs = nil
}
