// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/appointment/v1/group_class_api.proto

package appointmentapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// status
type ListSessionsParams_Status int32

const (
	// status is unspecified
	ListSessionsParams_STATUS_UNSPECIFIED ListSessionsParams_Status = 0
	// past
	ListSessionsParams_PAST ListSessionsParams_Status = 1
	// upcoming
	ListSessionsParams_UPCOMING ListSessionsParams_Status = 2
)

// Enum value maps for ListSessionsParams_Status.
var (
	ListSessionsParams_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PAST",
		2: "UPCOMING",
	}
	ListSessionsParams_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PAST":               1,
		"UPCOMING":           2,
	}
)

func (x ListSessionsParams_Status) Enum() *ListSessionsParams_Status {
	p := new(ListSessionsParams_Status)
	*p = x
	return p
}

func (x ListSessionsParams_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListSessionsParams_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_client_appointment_v1_group_class_api_proto_enumTypes[0].Descriptor()
}

func (ListSessionsParams_Status) Type() protoreflect.EnumType {
	return &file_moego_client_appointment_v1_group_class_api_proto_enumTypes[0]
}

func (x ListSessionsParams_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListSessionsParams_Status.Descriptor instead.
func (ListSessionsParams_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{2, 0}
}

// list customer group class instances request
type ListInstancesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ListInstancesParams_Name
	//	*ListInstancesParams_Domain
	Anonymous isListInstancesParams_Anonymous `protobuf_oneof:"anonymous"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListInstancesParams) Reset() {
	*x = ListInstancesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInstancesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInstancesParams) ProtoMessage() {}

func (x *ListInstancesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInstancesParams.ProtoReflect.Descriptor instead.
func (*ListInstancesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{0}
}

func (m *ListInstancesParams) GetAnonymous() isListInstancesParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ListInstancesParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ListInstancesParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ListInstancesParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ListInstancesParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *ListInstancesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type isListInstancesParams_Anonymous interface {
	isListInstancesParams_Anonymous()
}

type ListInstancesParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ListInstancesParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ListInstancesParams_Name) isListInstancesParams_Anonymous() {}

func (*ListInstancesParams_Domain) isListInstancesParams_Anonymous() {}

// list customer group class instances result
type ListInstancesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group class instances
	GroupInstances []*ListInstancesResult_GroupInstanceView `protobuf:"bytes,1,rep,name=group_instances,json=groupInstances,proto3" json:"group_instances,omitempty"`
	// group classes
	GroupClasses []*v1.GroupClassModel `protobuf:"bytes,2,rep,name=group_classes,json=groupClasses,proto3" json:"group_classes,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListInstancesResult) Reset() {
	*x = ListInstancesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInstancesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInstancesResult) ProtoMessage() {}

func (x *ListInstancesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInstancesResult.ProtoReflect.Descriptor instead.
func (*ListInstancesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListInstancesResult) GetGroupInstances() []*ListInstancesResult_GroupInstanceView {
	if x != nil {
		return x.GroupInstances
	}
	return nil
}

func (x *ListInstancesResult) GetGroupClasses() []*v1.GroupClassModel {
	if x != nil {
		return x.GroupClasses
	}
	return nil
}

func (x *ListInstancesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list customer group class sessions request
type ListSessionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ListSessionsParams_Name
	//	*ListSessionsParams_Domain
	Anonymous isListSessionsParams_Anonymous `protobuf_oneof:"anonymous"`
	// status
	Status ListSessionsParams_Status `protobuf:"varint,3,opt,name=status,proto3,enum=moego.client.appointment.v1.ListSessionsParams_Status" json:"status,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListSessionsParams) Reset() {
	*x = ListSessionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSessionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSessionsParams) ProtoMessage() {}

func (x *ListSessionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSessionsParams.ProtoReflect.Descriptor instead.
func (*ListSessionsParams) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{2}
}

func (m *ListSessionsParams) GetAnonymous() isListSessionsParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ListSessionsParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ListSessionsParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ListSessionsParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ListSessionsParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *ListSessionsParams) GetStatus() ListSessionsParams_Status {
	if x != nil {
		return x.Status
	}
	return ListSessionsParams_STATUS_UNSPECIFIED
}

func (x *ListSessionsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type isListSessionsParams_Anonymous interface {
	isListSessionsParams_Anonymous()
}

type ListSessionsParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ListSessionsParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ListSessionsParams_Name) isListSessionsParams_Anonymous() {}

func (*ListSessionsParams_Domain) isListSessionsParams_Anonymous() {}

// list customer group class sessions result
type ListSessionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group class sessions
	Sessions []*ListSessionsResult_GroupSessionView `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
	// group class instance
	GroupInstances []*GroupInstanceView `protobuf:"bytes,2,rep,name=group_instances,json=groupInstances,proto3" json:"group_instances,omitempty"`
	// group class
	GroupClasses []*v1.GroupClassModel `protobuf:"bytes,3,rep,name=group_classes,json=groupClasses,proto3" json:"group_classes,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListSessionsResult) Reset() {
	*x = ListSessionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSessionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSessionsResult) ProtoMessage() {}

func (x *ListSessionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSessionsResult.ProtoReflect.Descriptor instead.
func (*ListSessionsResult) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListSessionsResult) GetSessions() []*ListSessionsResult_GroupSessionView {
	if x != nil {
		return x.Sessions
	}
	return nil
}

func (x *ListSessionsResult) GetGroupInstances() []*GroupInstanceView {
	if x != nil {
		return x.GroupInstances
	}
	return nil
}

func (x *ListSessionsResult) GetGroupClasses() []*v1.GroupClassModel {
	if x != nil {
		return x.GroupClasses
	}
	return nil
}

func (x *ListSessionsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// group instance with sessions
type GroupInstanceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group class instance
	GroupInstance *v1.GroupClassInstance `protobuf:"bytes,1,opt,name=group_instance,json=groupInstance,proto3" json:"group_instance,omitempty"`
	// group session
	Sessions []*v1.GroupClassSession `protobuf:"bytes,2,rep,name=sessions,proto3" json:"sessions,omitempty"`
}

func (x *GroupInstanceView) Reset() {
	*x = GroupInstanceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupInstanceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupInstanceView) ProtoMessage() {}

func (x *GroupInstanceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupInstanceView.ProtoReflect.Descriptor instead.
func (*GroupInstanceView) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{4}
}

func (x *GroupInstanceView) GetGroupInstance() *v1.GroupClassInstance {
	if x != nil {
		return x.GroupInstance
	}
	return nil
}

func (x *GroupInstanceView) GetSessions() []*v1.GroupClassSession {
	if x != nil {
		return x.Sessions
	}
	return nil
}

// get instance detail params
type GetInstanceDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetInstanceDetailParams_Name
	//	*GetInstanceDetailParams_Domain
	Anonymous isGetInstanceDetailParams_Anonymous `protobuf_oneof:"anonymous"`
	// group class instance id
	GroupInstanceId int64 `protobuf:"varint,3,opt,name=group_instance_id,json=groupInstanceId,proto3" json:"group_instance_id,omitempty"`
	// pet ids
	PetIds []int64 `protobuf:"varint,4,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
}

func (x *GetInstanceDetailParams) Reset() {
	*x = GetInstanceDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstanceDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstanceDetailParams) ProtoMessage() {}

func (x *GetInstanceDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstanceDetailParams.ProtoReflect.Descriptor instead.
func (*GetInstanceDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{5}
}

func (m *GetInstanceDetailParams) GetAnonymous() isGetInstanceDetailParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetInstanceDetailParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetInstanceDetailParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetInstanceDetailParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetInstanceDetailParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetInstanceDetailParams) GetGroupInstanceId() int64 {
	if x != nil {
		return x.GroupInstanceId
	}
	return 0
}

func (x *GetInstanceDetailParams) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

type isGetInstanceDetailParams_Anonymous interface {
	isGetInstanceDetailParams_Anonymous()
}

type GetInstanceDetailParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetInstanceDetailParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetInstanceDetailParams_Name) isGetInstanceDetailParams_Anonymous() {}

func (*GetInstanceDetailParams_Domain) isGetInstanceDetailParams_Anonymous() {}

// get instance detail result
type GetInstanceDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group class session
	Sessions []*v1.GroupClassSession `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
	// group class instance
	GroupInstance *v1.GroupClassInstance `protobuf:"bytes,2,opt,name=group_instance,json=groupInstance,proto3" json:"group_instance,omitempty"`
	// group class
	GroupClass *v1.GroupClassModel `protobuf:"bytes,3,opt,name=group_class,json=groupClass,proto3" json:"group_class,omitempty"`
	// trainer
	Trainer *GetInstanceDetailResult_TrainerView `protobuf:"bytes,4,opt,name=trainer,proto3" json:"trainer,omitempty"`
	// pet info
	Pets []*v11.BusinessCustomerPetInfoModel `protobuf:"bytes,5,rep,name=pets,proto3" json:"pets,omitempty"`
}

func (x *GetInstanceDetailResult) Reset() {
	*x = GetInstanceDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstanceDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstanceDetailResult) ProtoMessage() {}

func (x *GetInstanceDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstanceDetailResult.ProtoReflect.Descriptor instead.
func (*GetInstanceDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetInstanceDetailResult) GetSessions() []*v1.GroupClassSession {
	if x != nil {
		return x.Sessions
	}
	return nil
}

func (x *GetInstanceDetailResult) GetGroupInstance() *v1.GroupClassInstance {
	if x != nil {
		return x.GroupInstance
	}
	return nil
}

func (x *GetInstanceDetailResult) GetGroupClass() *v1.GroupClassModel {
	if x != nil {
		return x.GroupClass
	}
	return nil
}

func (x *GetInstanceDetailResult) GetTrainer() *GetInstanceDetailResult_TrainerView {
	if x != nil {
		return x.Trainer
	}
	return nil
}

func (x *GetInstanceDetailResult) GetPets() []*v11.BusinessCustomerPetInfoModel {
	if x != nil {
		return x.Pets
	}
	return nil
}

// remove pet params
type RemovePetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*RemovePetParams_Name
	//	*RemovePetParams_Domain
	Anonymous isRemovePetParams_Anonymous `protobuf_oneof:"anonymous"`
	// group class instance id
	GroupInstanceId int64 `protobuf:"varint,3,opt,name=group_instance_id,json=groupInstanceId,proto3" json:"group_instance_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,4,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
}

func (x *RemovePetParams) Reset() {
	*x = RemovePetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemovePetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePetParams) ProtoMessage() {}

func (x *RemovePetParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePetParams.ProtoReflect.Descriptor instead.
func (*RemovePetParams) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{7}
}

func (m *RemovePetParams) GetAnonymous() isRemovePetParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *RemovePetParams) GetName() string {
	if x, ok := x.GetAnonymous().(*RemovePetParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *RemovePetParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*RemovePetParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *RemovePetParams) GetGroupInstanceId() int64 {
	if x != nil {
		return x.GroupInstanceId
	}
	return 0
}

func (x *RemovePetParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

type isRemovePetParams_Anonymous interface {
	isRemovePetParams_Anonymous()
}

type RemovePetParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type RemovePetParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*RemovePetParams_Name) isRemovePetParams_Anonymous() {}

func (*RemovePetParams_Domain) isRemovePetParams_Anonymous() {}

// remove pet result
type RemovePetResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemovePetResult) Reset() {
	*x = RemovePetResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemovePetResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePetResult) ProtoMessage() {}

func (x *RemovePetResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePetResult.ProtoReflect.Descriptor instead.
func (*RemovePetResult) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{8}
}

// group class instance view
type ListInstancesResult_GroupInstanceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group class instance
	GroupClassInstance *v1.GroupClassInstance `protobuf:"bytes,1,opt,name=group_class_instance,json=groupClassInstance,proto3" json:"group_class_instance,omitempty"`
	// sessions
	Sessions []*v1.GroupClassSession `protobuf:"bytes,2,rep,name=sessions,proto3" json:"sessions,omitempty"`
	// pets
	Pets []*v11.BusinessCustomerPetInfoModel `protobuf:"bytes,3,rep,name=pets,proto3" json:"pets,omitempty"`
}

func (x *ListInstancesResult_GroupInstanceView) Reset() {
	*x = ListInstancesResult_GroupInstanceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInstancesResult_GroupInstanceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInstancesResult_GroupInstanceView) ProtoMessage() {}

func (x *ListInstancesResult_GroupInstanceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInstancesResult_GroupInstanceView.ProtoReflect.Descriptor instead.
func (*ListInstancesResult_GroupInstanceView) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListInstancesResult_GroupInstanceView) GetGroupClassInstance() *v1.GroupClassInstance {
	if x != nil {
		return x.GroupClassInstance
	}
	return nil
}

func (x *ListInstancesResult_GroupInstanceView) GetSessions() []*v1.GroupClassSession {
	if x != nil {
		return x.Sessions
	}
	return nil
}

func (x *ListInstancesResult_GroupInstanceView) GetPets() []*v11.BusinessCustomerPetInfoModel {
	if x != nil {
		return x.Pets
	}
	return nil
}

// group class session view
type ListSessionsResult_GroupSessionView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group class session
	Session *v1.GroupClassSession `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	// pets
	Pets []*v11.BusinessCustomerPetInfoModel `protobuf:"bytes,2,rep,name=pets,proto3" json:"pets,omitempty"`
}

func (x *ListSessionsResult_GroupSessionView) Reset() {
	*x = ListSessionsResult_GroupSessionView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSessionsResult_GroupSessionView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSessionsResult_GroupSessionView) ProtoMessage() {}

func (x *ListSessionsResult_GroupSessionView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSessionsResult_GroupSessionView.ProtoReflect.Descriptor instead.
func (*ListSessionsResult_GroupSessionView) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ListSessionsResult_GroupSessionView) GetSession() *v1.GroupClassSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *ListSessionsResult_GroupSessionView) GetPets() []*v11.BusinessCustomerPetInfoModel {
	if x != nil {
		return x.Pets
	}
	return nil
}

// Trainer
type GetInstanceDetailResult_TrainerView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// avatar
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
}

func (x *GetInstanceDetailResult_TrainerView) Reset() {
	*x = GetInstanceDetailResult_TrainerView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstanceDetailResult_TrainerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstanceDetailResult_TrainerView) ProtoMessage() {}

func (x *GetInstanceDetailResult_TrainerView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_group_class_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstanceDetailResult_TrainerView.ProtoReflect.Descriptor instead.
func (*GetInstanceDetailResult_TrainerView) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP(), []int{6, 0}
}

func (x *GetInstanceDetailResult_TrainerView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetInstanceDetailResult_TrainerView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *GetInstanceDetailResult_TrainerView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *GetInstanceDetailResult_TrainerView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

var File_moego_client_appointment_v1_group_class_api_proto protoreflect.FileDescriptor

var file_moego_client_appointment_v1_group_class_api_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4, 0x01, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03,
	0xf8, 0x42, 0x01, 0x22, 0xaa, 0x04, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6b, 0x0a, 0x0f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x91, 0x02, 0x0a,
	0x11, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x5e, 0x0a, 0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x12,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x53, 0x0a, 0x04, 0x70,
	0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73,
	0x22, 0xb7, 0x02, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x58, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x38,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x53, 0x54, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50,
	0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e,
	0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x90, 0x04, 0x0a, 0x12, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x5c, 0x0a, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x57, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xae, 0x01, 0x0a,
	0x10, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x45, 0x0a, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x22, 0xb1, 0x01,
	0x0a, 0x11, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x53, 0x0a, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0xa9, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x33, 0x0a,
	0x11, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x42, 0x10, 0x0a, 0x09, 0x61,
	0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xb0, 0x04,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x47, 0x0a, 0x08, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x53, 0x0a, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x12, 0x5a, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12,
	0x53, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04,
	0x70, 0x65, 0x74, 0x73, 0x1a, 0x7a, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68,
	0x22, 0xa8, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x33, 0x0a, 0x11, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f,
	0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x11, 0x0a, 0x0f, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xe4,
	0x03, 0x0a, 0x11, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x73, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x0c, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x09,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x8a, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x61, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_appointment_v1_group_class_api_proto_rawDescOnce sync.Once
	file_moego_client_appointment_v1_group_class_api_proto_rawDescData = file_moego_client_appointment_v1_group_class_api_proto_rawDesc
)

func file_moego_client_appointment_v1_group_class_api_proto_rawDescGZIP() []byte {
	file_moego_client_appointment_v1_group_class_api_proto_rawDescOnce.Do(func() {
		file_moego_client_appointment_v1_group_class_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_appointment_v1_group_class_api_proto_rawDescData)
	})
	return file_moego_client_appointment_v1_group_class_api_proto_rawDescData
}

var file_moego_client_appointment_v1_group_class_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_client_appointment_v1_group_class_api_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_client_appointment_v1_group_class_api_proto_goTypes = []interface{}{
	(ListSessionsParams_Status)(0),                // 0: moego.client.appointment.v1.ListSessionsParams.Status
	(*ListInstancesParams)(nil),                   // 1: moego.client.appointment.v1.ListInstancesParams
	(*ListInstancesResult)(nil),                   // 2: moego.client.appointment.v1.ListInstancesResult
	(*ListSessionsParams)(nil),                    // 3: moego.client.appointment.v1.ListSessionsParams
	(*ListSessionsResult)(nil),                    // 4: moego.client.appointment.v1.ListSessionsResult
	(*GroupInstanceView)(nil),                     // 5: moego.client.appointment.v1.GroupInstanceView
	(*GetInstanceDetailParams)(nil),               // 6: moego.client.appointment.v1.GetInstanceDetailParams
	(*GetInstanceDetailResult)(nil),               // 7: moego.client.appointment.v1.GetInstanceDetailResult
	(*RemovePetParams)(nil),                       // 8: moego.client.appointment.v1.RemovePetParams
	(*RemovePetResult)(nil),                       // 9: moego.client.appointment.v1.RemovePetResult
	(*ListInstancesResult_GroupInstanceView)(nil), // 10: moego.client.appointment.v1.ListInstancesResult.GroupInstanceView
	(*ListSessionsResult_GroupSessionView)(nil),   // 11: moego.client.appointment.v1.ListSessionsResult.GroupSessionView
	(*GetInstanceDetailResult_TrainerView)(nil),   // 12: moego.client.appointment.v1.GetInstanceDetailResult.TrainerView
	(*v2.PaginationRequest)(nil),                  // 13: moego.utils.v2.PaginationRequest
	(*v1.GroupClassModel)(nil),                    // 14: moego.models.offering.v1.GroupClassModel
	(*v2.PaginationResponse)(nil),                 // 15: moego.utils.v2.PaginationResponse
	(*v1.GroupClassInstance)(nil),                 // 16: moego.models.offering.v1.GroupClassInstance
	(*v1.GroupClassSession)(nil),                  // 17: moego.models.offering.v1.GroupClassSession
	(*v11.BusinessCustomerPetInfoModel)(nil),      // 18: moego.models.business_customer.v1.BusinessCustomerPetInfoModel
}
var file_moego_client_appointment_v1_group_class_api_proto_depIdxs = []int32{
	13, // 0: moego.client.appointment.v1.ListInstancesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	10, // 1: moego.client.appointment.v1.ListInstancesResult.group_instances:type_name -> moego.client.appointment.v1.ListInstancesResult.GroupInstanceView
	14, // 2: moego.client.appointment.v1.ListInstancesResult.group_classes:type_name -> moego.models.offering.v1.GroupClassModel
	15, // 3: moego.client.appointment.v1.ListInstancesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	0,  // 4: moego.client.appointment.v1.ListSessionsParams.status:type_name -> moego.client.appointment.v1.ListSessionsParams.Status
	13, // 5: moego.client.appointment.v1.ListSessionsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	11, // 6: moego.client.appointment.v1.ListSessionsResult.sessions:type_name -> moego.client.appointment.v1.ListSessionsResult.GroupSessionView
	5,  // 7: moego.client.appointment.v1.ListSessionsResult.group_instances:type_name -> moego.client.appointment.v1.GroupInstanceView
	14, // 8: moego.client.appointment.v1.ListSessionsResult.group_classes:type_name -> moego.models.offering.v1.GroupClassModel
	15, // 9: moego.client.appointment.v1.ListSessionsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	16, // 10: moego.client.appointment.v1.GroupInstanceView.group_instance:type_name -> moego.models.offering.v1.GroupClassInstance
	17, // 11: moego.client.appointment.v1.GroupInstanceView.sessions:type_name -> moego.models.offering.v1.GroupClassSession
	17, // 12: moego.client.appointment.v1.GetInstanceDetailResult.sessions:type_name -> moego.models.offering.v1.GroupClassSession
	16, // 13: moego.client.appointment.v1.GetInstanceDetailResult.group_instance:type_name -> moego.models.offering.v1.GroupClassInstance
	14, // 14: moego.client.appointment.v1.GetInstanceDetailResult.group_class:type_name -> moego.models.offering.v1.GroupClassModel
	12, // 15: moego.client.appointment.v1.GetInstanceDetailResult.trainer:type_name -> moego.client.appointment.v1.GetInstanceDetailResult.TrainerView
	18, // 16: moego.client.appointment.v1.GetInstanceDetailResult.pets:type_name -> moego.models.business_customer.v1.BusinessCustomerPetInfoModel
	16, // 17: moego.client.appointment.v1.ListInstancesResult.GroupInstanceView.group_class_instance:type_name -> moego.models.offering.v1.GroupClassInstance
	17, // 18: moego.client.appointment.v1.ListInstancesResult.GroupInstanceView.sessions:type_name -> moego.models.offering.v1.GroupClassSession
	18, // 19: moego.client.appointment.v1.ListInstancesResult.GroupInstanceView.pets:type_name -> moego.models.business_customer.v1.BusinessCustomerPetInfoModel
	17, // 20: moego.client.appointment.v1.ListSessionsResult.GroupSessionView.session:type_name -> moego.models.offering.v1.GroupClassSession
	18, // 21: moego.client.appointment.v1.ListSessionsResult.GroupSessionView.pets:type_name -> moego.models.business_customer.v1.BusinessCustomerPetInfoModel
	1,  // 22: moego.client.appointment.v1.GroupClassService.ListInstances:input_type -> moego.client.appointment.v1.ListInstancesParams
	3,  // 23: moego.client.appointment.v1.GroupClassService.ListSessions:input_type -> moego.client.appointment.v1.ListSessionsParams
	6,  // 24: moego.client.appointment.v1.GroupClassService.GetInstanceDetail:input_type -> moego.client.appointment.v1.GetInstanceDetailParams
	8,  // 25: moego.client.appointment.v1.GroupClassService.RemovePet:input_type -> moego.client.appointment.v1.RemovePetParams
	2,  // 26: moego.client.appointment.v1.GroupClassService.ListInstances:output_type -> moego.client.appointment.v1.ListInstancesResult
	4,  // 27: moego.client.appointment.v1.GroupClassService.ListSessions:output_type -> moego.client.appointment.v1.ListSessionsResult
	7,  // 28: moego.client.appointment.v1.GroupClassService.GetInstanceDetail:output_type -> moego.client.appointment.v1.GetInstanceDetailResult
	9,  // 29: moego.client.appointment.v1.GroupClassService.RemovePet:output_type -> moego.client.appointment.v1.RemovePetResult
	26, // [26:30] is the sub-list for method output_type
	22, // [22:26] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_moego_client_appointment_v1_group_class_api_proto_init() }
func file_moego_client_appointment_v1_group_class_api_proto_init() {
	if File_moego_client_appointment_v1_group_class_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInstancesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInstancesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSessionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSessionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupInstanceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstanceDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstanceDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemovePetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemovePetResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInstancesResult_GroupInstanceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSessionsResult_GroupSessionView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_group_class_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstanceDetailResult_TrainerView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_appointment_v1_group_class_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ListInstancesParams_Name)(nil),
		(*ListInstancesParams_Domain)(nil),
	}
	file_moego_client_appointment_v1_group_class_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*ListSessionsParams_Name)(nil),
		(*ListSessionsParams_Domain)(nil),
	}
	file_moego_client_appointment_v1_group_class_api_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*GetInstanceDetailParams_Name)(nil),
		(*GetInstanceDetailParams_Domain)(nil),
	}
	file_moego_client_appointment_v1_group_class_api_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*RemovePetParams_Name)(nil),
		(*RemovePetParams_Domain)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_appointment_v1_group_class_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_appointment_v1_group_class_api_proto_goTypes,
		DependencyIndexes: file_moego_client_appointment_v1_group_class_api_proto_depIdxs,
		EnumInfos:         file_moego_client_appointment_v1_group_class_api_proto_enumTypes,
		MessageInfos:      file_moego_client_appointment_v1_group_class_api_proto_msgTypes,
	}.Build()
	File_moego_client_appointment_v1_group_class_api_proto = out.File
	file_moego_client_appointment_v1_group_class_api_proto_rawDesc = nil
	file_moego_client_appointment_v1_group_class_api_proto_goTypes = nil
	file_moego_client_appointment_v1_group_class_api_proto_depIdxs = nil
}
