// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/appointment/v1/group_class_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GroupClassServiceClient is the client API for GroupClassService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GroupClassServiceClient interface {
	// Get customer group class
	ListInstances(ctx context.Context, in *ListInstancesParams, opts ...grpc.CallOption) (*ListInstancesResult, error)
	// Get customer group class session
	ListSessions(ctx context.Context, in *ListSessionsParams, opts ...grpc.CallOption) (*ListSessionsResult, error)
	// Get instance detail
	GetInstanceDetail(ctx context.Context, in *GetInstanceDetailParams, opts ...grpc.CallOption) (*GetInstanceDetailResult, error)
	// Remove pet from instance
	RemovePet(ctx context.Context, in *RemovePetParams, opts ...grpc.CallOption) (*RemovePetResult, error)
}

type groupClassServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGroupClassServiceClient(cc grpc.ClientConnInterface) GroupClassServiceClient {
	return &groupClassServiceClient{cc}
}

func (c *groupClassServiceClient) ListInstances(ctx context.Context, in *ListInstancesParams, opts ...grpc.CallOption) (*ListInstancesResult, error) {
	out := new(ListInstancesResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.GroupClassService/ListInstances", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) ListSessions(ctx context.Context, in *ListSessionsParams, opts ...grpc.CallOption) (*ListSessionsResult, error) {
	out := new(ListSessionsResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.GroupClassService/ListSessions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) GetInstanceDetail(ctx context.Context, in *GetInstanceDetailParams, opts ...grpc.CallOption) (*GetInstanceDetailResult, error) {
	out := new(GetInstanceDetailResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.GroupClassService/GetInstanceDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) RemovePet(ctx context.Context, in *RemovePetParams, opts ...grpc.CallOption) (*RemovePetResult, error) {
	out := new(RemovePetResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.GroupClassService/RemovePet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroupClassServiceServer is the server API for GroupClassService service.
// All implementations must embed UnimplementedGroupClassServiceServer
// for forward compatibility
type GroupClassServiceServer interface {
	// Get customer group class
	ListInstances(context.Context, *ListInstancesParams) (*ListInstancesResult, error)
	// Get customer group class session
	ListSessions(context.Context, *ListSessionsParams) (*ListSessionsResult, error)
	// Get instance detail
	GetInstanceDetail(context.Context, *GetInstanceDetailParams) (*GetInstanceDetailResult, error)
	// Remove pet from instance
	RemovePet(context.Context, *RemovePetParams) (*RemovePetResult, error)
	mustEmbedUnimplementedGroupClassServiceServer()
}

// UnimplementedGroupClassServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGroupClassServiceServer struct {
}

func (UnimplementedGroupClassServiceServer) ListInstances(context.Context, *ListInstancesParams) (*ListInstancesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstances not implemented")
}
func (UnimplementedGroupClassServiceServer) ListSessions(context.Context, *ListSessionsParams) (*ListSessionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSessions not implemented")
}
func (UnimplementedGroupClassServiceServer) GetInstanceDetail(context.Context, *GetInstanceDetailParams) (*GetInstanceDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceDetail not implemented")
}
func (UnimplementedGroupClassServiceServer) RemovePet(context.Context, *RemovePetParams) (*RemovePetResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePet not implemented")
}
func (UnimplementedGroupClassServiceServer) mustEmbedUnimplementedGroupClassServiceServer() {}

// UnsafeGroupClassServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroupClassServiceServer will
// result in compilation errors.
type UnsafeGroupClassServiceServer interface {
	mustEmbedUnimplementedGroupClassServiceServer()
}

func RegisterGroupClassServiceServer(s grpc.ServiceRegistrar, srv GroupClassServiceServer) {
	s.RegisterService(&GroupClassService_ServiceDesc, srv)
}

func _GroupClassService_ListInstances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstancesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).ListInstances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.GroupClassService/ListInstances",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).ListInstances(ctx, req.(*ListInstancesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_ListSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSessionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).ListSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.GroupClassService/ListSessions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).ListSessions(ctx, req.(*ListSessionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_GetInstanceDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).GetInstanceDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.GroupClassService/GetInstanceDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).GetInstanceDetail(ctx, req.(*GetInstanceDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_RemovePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePetParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).RemovePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.GroupClassService/RemovePet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).RemovePet(ctx, req.(*RemovePetParams))
	}
	return interceptor(ctx, in, info, handler)
}

// GroupClassService_ServiceDesc is the grpc.ServiceDesc for GroupClassService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroupClassService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.appointment.v1.GroupClassService",
	HandlerType: (*GroupClassServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListInstances",
			Handler:    _GroupClassService_ListInstances_Handler,
		},
		{
			MethodName: "ListSessions",
			Handler:    _GroupClassService_ListSessions_Handler,
		},
		{
			MethodName: "GetInstanceDetail",
			Handler:    _GroupClassService_GetInstanceDetail_Handler,
		},
		{
			MethodName: "RemovePet",
			Handler:    _GroupClassService_RemovePet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/appointment/v1/group_class_api.proto",
}
