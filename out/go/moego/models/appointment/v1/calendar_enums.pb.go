// @since 2025-08-17 09:20:43
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/calendar_enums.proto

package appointmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ViewType defines the supported view types for the calendar
type ViewType int32

const (
	// Unspecified view, serves as the default value
	ViewType_VIEW_TYPE_UNSPECIFIED ViewType = 0
	// Day view
	ViewType_DAY ViewType = 1
	// Week view
	ViewType_WEEK ViewType = 2
	// Month view
	ViewType_MONTH ViewType = 3
	// Agenda list view
	ViewType_AGENDA ViewType = 4
)

// Enum value maps for ViewType.
var (
	ViewType_name = map[int32]string{
		0: "VIEW_TYPE_UNSPECIFIED",
		1: "DAY",
		2: "WEEK",
		3: "MONTH",
		4: "AGENDA",
	}
	ViewType_value = map[string]int32{
		"VIEW_TYPE_UNSPECIFIED": 0,
		"DAY":                   1,
		"WEEK":                  2,
		"MONTH":                 3,
		"AGENDA":                4,
	}
)

func (x ViewType) Enum() *ViewType {
	p := new(ViewType)
	*p = x
	return p
}

func (x ViewType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ViewType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_calendar_enums_proto_enumTypes[0].Descriptor()
}

func (ViewType) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_calendar_enums_proto_enumTypes[0]
}

func (x ViewType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ViewType.Descriptor instead.
func (ViewType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_calendar_enums_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_appointment_v1_calendar_enums_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_calendar_enums_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2a,
	0x4f, 0x0a, 0x08, 0x56, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x56,
	0x49, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x44, 0x41, 0x59, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x57, 0x45, 0x45, 0x4b, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x4f, 0x4e,
	0x54, 0x48, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x47, 0x45, 0x4e, 0x44, 0x41, 0x10, 0x04,
	0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_calendar_enums_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_calendar_enums_proto_rawDescData = file_moego_models_appointment_v1_calendar_enums_proto_rawDesc
)

func file_moego_models_appointment_v1_calendar_enums_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_calendar_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_calendar_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_calendar_enums_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_calendar_enums_proto_rawDescData
}

var file_moego_models_appointment_v1_calendar_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_appointment_v1_calendar_enums_proto_goTypes = []interface{}{
	(ViewType)(0), // 0: moego.models.appointment.v1.ViewType
}
var file_moego_models_appointment_v1_calendar_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_calendar_enums_proto_init() }
func file_moego_models_appointment_v1_calendar_enums_proto_init() {
	if File_moego_models_appointment_v1_calendar_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_calendar_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_calendar_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_calendar_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_appointment_v1_calendar_enums_proto_enumTypes,
	}.Build()
	File_moego_models_appointment_v1_calendar_enums_proto = out.File
	file_moego_models_appointment_v1_calendar_enums_proto_rawDesc = nil
	file_moego_models_appointment_v1_calendar_enums_proto_goTypes = nil
	file_moego_models_appointment_v1_calendar_enums_proto_depIdxs = nil
}
