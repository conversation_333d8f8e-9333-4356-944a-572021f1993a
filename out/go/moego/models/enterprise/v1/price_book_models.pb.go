// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/price_book_models.proto

package enterprisepb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// price book
type PriceBook struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *PriceBook) Reset() {
	*x = PriceBook{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceBook) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceBook) ProtoMessage() {}

func (x *PriceBook) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceBook.ProtoReflect.Descriptor instead.
func (*PriceBook) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{0}
}

func (x *PriceBook) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PriceBook) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// category
type ServiceCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// price book
	PriceBook *PriceBook `protobuf:"bytes,3,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,5,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// sort
	Sort int64 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	// service type
	ServiceType v1.ServiceType `protobuf:"varint,7,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
}

func (x *ServiceCategory) Reset() {
	*x = ServiceCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCategory) ProtoMessage() {}

func (x *ServiceCategory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCategory.ProtoReflect.Descriptor instead.
func (*ServiceCategory) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceCategory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceCategory) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ServiceCategory) GetPriceBook() *PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

func (x *ServiceCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceCategory) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *ServiceCategory) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ServiceCategory) GetServiceType() v1.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v1.ServiceType(0)
}

// service model
// 原有 service 模型是企业级设计，这里先简化
type Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// price book
	PriceBook *PriceBook `protobuf:"bytes,3,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,5,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// category
	Category *ServiceCategory `protobuf:"bytes,6,opt,name=category,proto3" json:"category,omitempty"`
	// description
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,8,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// color
	Color string `protobuf:"bytes,9,opt,name=color,proto3" json:"color,omitempty"`
	// sort
	Sort int64 `protobuf:"varint,10,opt,name=sort,proto3" json:"sort,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,11,opt,name=price,proto3" json:"price,omitempty"`
	// service price unit
	ServicePriceUnit v1.ServicePriceUnit `protobuf:"varint,12,opt,name=service_price_unit,json=servicePriceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"service_price_unit,omitempty"`
	// 万分位税率
	TaxRate int64 `protobuf:"varint,13,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,14,opt,name=duration,proto3" json:"duration,omitempty"`
	// max duration
	MaxDuration *durationpb.Duration `protobuf:"bytes,15,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// limitation
	Limitation *Service_Limitation `protobuf:"bytes,16,opt,name=limitation,proto3" json:"limitation,omitempty"`
	// service type
	ServiceType v1.ServiceType `protobuf:"varint,17,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// images
	Images []string `protobuf:"bytes,18,rep,name=images,proto3" json:"images,omitempty"`
	// require dedicated staff, only for add-on
	RequireDedicatedStaff bool `protobuf:"varint,19,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// auto rule
	AutoRule *Service_AutoRule `protobuf:"bytes,20,opt,name=auto_rule,json=autoRule,proto3" json:"auto_rule,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// pushed at
	PushedAt *timestamppb.Timestamp `protobuf:"bytes,23,opt,name=pushed_at,json=pushedAt,proto3,oneof" json:"pushed_at,omitempty"`
}

func (x *Service) Reset() {
	*x = Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{2}
}

func (x *Service) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Service) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *Service) GetPriceBook() *PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

func (x *Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Service) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *Service) GetCategory() *ServiceCategory {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *Service) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Service) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *Service) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *Service) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *Service) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *Service) GetServicePriceUnit() v1.ServicePriceUnit {
	if x != nil {
		return x.ServicePriceUnit
	}
	return v1.ServicePriceUnit(0)
}

func (x *Service) GetTaxRate() int64 {
	if x != nil {
		return x.TaxRate
	}
	return 0
}

func (x *Service) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *Service) GetMaxDuration() *durationpb.Duration {
	if x != nil {
		return x.MaxDuration
	}
	return nil
}

func (x *Service) GetLimitation() *Service_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

func (x *Service) GetServiceType() v1.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v1.ServiceType(0)
}

func (x *Service) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *Service) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *Service) GetAutoRule() *Service_AutoRule {
	if x != nil {
		return x.AutoRule
	}
	return nil
}

func (x *Service) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Service) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Service) GetPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PushedAt
	}
	return nil
}

// bundle service rule
type BundleServiceRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enabled
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// bundle sell service id
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *BundleServiceRule) Reset() {
	*x = BundleServiceRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BundleServiceRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BundleServiceRule) ProtoMessage() {}

func (x *BundleServiceRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BundleServiceRule.ProtoReflect.Descriptor instead.
func (*BundleServiceRule) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{3}
}

func (x *BundleServiceRule) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *BundleServiceRule) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// services (only for add on/pricing rule)
type ServiceLimitationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether the add on is available for all services(only for add on/pricing rule)
	EnableServiceFilter *bool `protobuf:"varint,1,opt,name=enable_service_filter,json=enableServiceFilter,proto3,oneof" json:"enable_service_filter,omitempty"`
	// service filters(only for add on/pricing rule)
	ServiceFilterList []*v1.ServiceFilter `protobuf:"bytes,2,rep,name=service_filter_list,json=serviceFilterList,proto3" json:"service_filter_list,omitempty"`
}

func (x *ServiceLimitationDef) Reset() {
	*x = ServiceLimitationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceLimitationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceLimitationDef) ProtoMessage() {}

func (x *ServiceLimitationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceLimitationDef.ProtoReflect.Descriptor instead.
func (*ServiceLimitationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{4}
}

func (x *ServiceLimitationDef) GetEnableServiceFilter() bool {
	if x != nil && x.EnableServiceFilter != nil {
		return *x.EnableServiceFilter
	}
	return false
}

func (x *ServiceLimitationDef) GetServiceFilterList() []*v1.ServiceFilter {
	if x != nil {
		return x.ServiceFilterList
	}
	return nil
}

// evaluation limitation
type EvaluationLimitationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is required
	IsRequired bool `protobuf:"varint,1,opt,name=is_required,json=isRequired,proto3" json:"is_required,omitempty"`
	// is required for OB
	IsRequiredForOb bool `protobuf:"varint,2,opt,name=is_required_for_ob,json=isRequiredForOb,proto3" json:"is_required_for_ob,omitempty"`
	// evaluation ids
	EvaluationIds []int64 `protobuf:"varint,3,rep,packed,name=evaluation_ids,json=evaluationIds,proto3" json:"evaluation_ids,omitempty"`
}

func (x *EvaluationLimitationDef) Reset() {
	*x = EvaluationLimitationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationLimitationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationLimitationDef) ProtoMessage() {}

func (x *EvaluationLimitationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationLimitationDef.ProtoReflect.Descriptor instead.
func (*EvaluationLimitationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{5}
}

func (x *EvaluationLimitationDef) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *EvaluationLimitationDef) GetIsRequiredForOb() bool {
	if x != nil {
		return x.IsRequiredForOb
	}
	return false
}

func (x *EvaluationLimitationDef) GetEvaluationIds() []int64 {
	if x != nil {
		return x.EvaluationIds
	}
	return nil
}

// pet type breeds limitation
type PetTypeBreedsLimitationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all pet types and breeds
	AllPetTypesAndBreeds bool `protobuf:"varint,1,opt,name=all_pet_types_and_breeds,json=allPetTypesAndBreeds,proto3" json:"all_pet_types_and_breeds,omitempty"`
	// required pet types
	PetTypeBreeds []*PetTypeBreeds `protobuf:"bytes,2,rep,name=pet_type_breeds,json=petTypeBreeds,proto3" json:"pet_type_breeds,omitempty"`
}

func (x *PetTypeBreedsLimitationDef) Reset() {
	*x = PetTypeBreedsLimitationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetTypeBreedsLimitationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetTypeBreedsLimitationDef) ProtoMessage() {}

func (x *PetTypeBreedsLimitationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetTypeBreedsLimitationDef.ProtoReflect.Descriptor instead.
func (*PetTypeBreedsLimitationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{6}
}

func (x *PetTypeBreedsLimitationDef) GetAllPetTypesAndBreeds() bool {
	if x != nil {
		return x.AllPetTypesAndBreeds
	}
	return false
}

func (x *PetTypeBreedsLimitationDef) GetPetTypeBreeds() []*PetTypeBreeds {
	if x != nil {
		return x.PetTypeBreeds
	}
	return nil
}

// lodging types limitation
type LodgingTypesLimitationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all lodging types
	AllLodgingTypes bool `protobuf:"varint,1,opt,name=all_lodging_types,json=allLodgingTypes,proto3" json:"all_lodging_types,omitempty"`
	// required lodging types
	LodgingTypeIds []int64 `protobuf:"varint,2,rep,packed,name=lodging_type_ids,json=lodgingTypeIds,proto3" json:"lodging_type_ids,omitempty"`
}

func (x *LodgingTypesLimitationDef) Reset() {
	*x = LodgingTypesLimitationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingTypesLimitationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingTypesLimitationDef) ProtoMessage() {}

func (x *LodgingTypesLimitationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingTypesLimitationDef.ProtoReflect.Descriptor instead.
func (*LodgingTypesLimitationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{7}
}

func (x *LodgingTypesLimitationDef) GetAllLodgingTypes() bool {
	if x != nil {
		return x.AllLodgingTypes
	}
	return false
}

func (x *LodgingTypesLimitationDef) GetLodgingTypeIds() []int64 {
	if x != nil {
		return x.LodgingTypeIds
	}
	return nil
}

// pet type breeds
type PetTypeBreeds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all breeds
	AllBreeds bool `protobuf:"varint,1,opt,name=all_breeds,json=allBreeds,proto3" json:"all_breeds,omitempty"`
	// pet type id
	PetTypeId v11.PetType `protobuf:"varint,2,opt,name=pet_type_id,json=petTypeId,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type_id,omitempty"`
	// pet type name
	PetTypeName string `protobuf:"bytes,3,opt,name=pet_type_name,json=petTypeName,proto3" json:"pet_type_name,omitempty"`
	// required breeds
	PetBreeds []*PetBreed `protobuf:"bytes,4,rep,name=pet_breeds,json=petBreeds,proto3" json:"pet_breeds,omitempty"`
}

func (x *PetTypeBreeds) Reset() {
	*x = PetTypeBreeds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetTypeBreeds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetTypeBreeds) ProtoMessage() {}

func (x *PetTypeBreeds) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetTypeBreeds.ProtoReflect.Descriptor instead.
func (*PetTypeBreeds) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{8}
}

func (x *PetTypeBreeds) GetAllBreeds() bool {
	if x != nil {
		return x.AllBreeds
	}
	return false
}

func (x *PetTypeBreeds) GetPetTypeId() v11.PetType {
	if x != nil {
		return x.PetTypeId
	}
	return v11.PetType(0)
}

func (x *PetTypeBreeds) GetPetTypeName() string {
	if x != nil {
		return x.PetTypeName
	}
	return ""
}

func (x *PetTypeBreeds) GetPetBreeds() []*PetBreed {
	if x != nil {
		return x.PetBreeds
	}
	return nil
}

// pet breed
type PetBreed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type id
	PetTypeId v11.PetType `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type_id,omitempty"`
	// pet type name
	PetTypeName string `protobuf:"bytes,2,opt,name=pet_type_name,json=petTypeName,proto3" json:"pet_type_name,omitempty"`
	// pet breed name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *PetBreed) Reset() {
	*x = PetBreed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetBreed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetBreed) ProtoMessage() {}

func (x *PetBreed) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetBreed.ProtoReflect.Descriptor instead.
func (*PetBreed) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{9}
}

func (x *PetBreed) GetPetTypeId() v11.PetType {
	if x != nil {
		return x.PetTypeId
	}
	return v11.PetType(0)
}

func (x *PetBreed) GetPetTypeName() string {
	if x != nil {
		return x.PetTypeName
	}
	return ""
}

func (x *PetBreed) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// pet type model
// Pet type can not be deleted, only can be set to unavailable.
type PetType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id, primary key of pet type record in database
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet type id
	PetTypeId v11.PetType `protobuf:"varint,2,opt,name=pet_type_id,json=petTypeId,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type_id,omitempty"`
	// pet type name, e.g. Dog, Cat, etc.
	PetTypeName string `protobuf:"bytes,3,opt,name=pet_type_name,json=petTypeName,proto3" json:"pet_type_name,omitempty"`
	// if the pet type is available
	IsAvailable bool `protobuf:"varint,4,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// pet type sort. The larger the sort number, the higher the priority.
	Sort int32 `protobuf:"varint,5,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *PetType) Reset() {
	*x = PetType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetType) ProtoMessage() {}

func (x *PetType) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetType.ProtoReflect.Descriptor instead.
func (*PetType) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{10}
}

func (x *PetType) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetType) GetPetTypeId() v11.PetType {
	if x != nil {
		return x.PetTypeId
	}
	return v11.PetType(0)
}

func (x *PetType) GetPetTypeName() string {
	if x != nil {
		return x.PetTypeName
	}
	return ""
}

func (x *PetType) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *PetType) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// weight range
type WeightRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// min weight
	Min *Weight `protobuf:"bytes,1,opt,name=min,proto3" json:"min,omitempty"`
	// max weight
	Max *Weight `protobuf:"bytes,2,opt,name=max,proto3" json:"max,omitempty"`
}

func (x *WeightRange) Reset() {
	*x = WeightRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeightRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeightRange) ProtoMessage() {}

func (x *WeightRange) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeightRange.ProtoReflect.Descriptor instead.
func (*WeightRange) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{11}
}

func (x *WeightRange) GetMin() *Weight {
	if x != nil {
		return x.Min
	}
	return nil
}

func (x *WeightRange) GetMax() *Weight {
	if x != nil {
		return x.Max
	}
	return nil
}

// weight
type Weight struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// weight
	Weight int64 `protobuf:"varint,1,opt,name=weight,proto3" json:"weight,omitempty"`
	// unit
	Unit WeightUnit `protobuf:"varint,2,opt,name=unit,proto3,enum=moego.models.enterprise.v1.WeightUnit" json:"unit,omitempty"`
}

func (x *Weight) Reset() {
	*x = Weight{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Weight) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Weight) ProtoMessage() {}

func (x *Weight) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Weight.ProtoReflect.Descriptor instead.
func (*Weight) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{12}
}

func (x *Weight) GetWeight() int64 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *Weight) GetUnit() WeightUnit {
	if x != nil {
		return x.Unit
	}
	return WeightUnit_WEIGHT_UNIT_UNSPECIFIED
}

// service change history
type ServiceChangeHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// impacted tenants
	ImpactedTenants int64 `protobuf:"varint,4,opt,name=impacted_tenants,json=impactedTenants,proto3" json:"impacted_tenants,omitempty"`
	// roll out at
	RollOutAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=roll_out_at,json=rollOutAt,proto3" json:"roll_out_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// impacted tenant ids
	ImpactedTenantIds []int64 `protobuf:"varint,7,rep,packed,name=impacted_tenant_ids,json=impactedTenantIds,proto3" json:"impacted_tenant_ids,omitempty"`
	// push result
	PushResult *TemplatePushResult `protobuf:"bytes,8,opt,name=push_result,json=pushResult,proto3" json:"push_result,omitempty"`
}

func (x *ServiceChangeHistory) Reset() {
	*x = ServiceChangeHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceChangeHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceChangeHistory) ProtoMessage() {}

func (x *ServiceChangeHistory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceChangeHistory.ProtoReflect.Descriptor instead.
func (*ServiceChangeHistory) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{13}
}

func (x *ServiceChangeHistory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceChangeHistory) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ServiceChangeHistory) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceChangeHistory) GetImpactedTenants() int64 {
	if x != nil {
		return x.ImpactedTenants
	}
	return 0
}

func (x *ServiceChangeHistory) GetRollOutAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RollOutAt
	}
	return nil
}

func (x *ServiceChangeHistory) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ServiceChangeHistory) GetImpactedTenantIds() []int64 {
	if x != nil {
		return x.ImpactedTenantIds
	}
	return nil
}

func (x *ServiceChangeHistory) GetPushResult() *TemplatePushResult {
	if x != nil {
		return x.PushResult
	}
	return nil
}

// service change
type ServiceChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// history id
	HistoryId int64 `protobuf:"varint,3,opt,name=history_id,json=historyId,proto3" json:"history_id,omitempty"`
	// target
	Target *TenantObject `protobuf:"bytes,4,opt,name=target,proto3" json:"target,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,6,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// details
	DetailCategories []*DetailCategory `protobuf:"bytes,7,rep,name=detail_categories,json=detailCategories,proto3" json:"detail_categories,omitempty"`
}

func (x *ServiceChange) Reset() {
	*x = ServiceChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceChange) ProtoMessage() {}

func (x *ServiceChange) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceChange.ProtoReflect.Descriptor instead.
func (*ServiceChange) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{14}
}

func (x *ServiceChange) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceChange) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ServiceChange) GetHistoryId() int64 {
	if x != nil {
		return x.HistoryId
	}
	return 0
}

func (x *ServiceChange) GetTarget() *TenantObject {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *ServiceChange) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceChange) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceChange) GetDetailCategories() []*DetailCategory {
	if x != nil {
		return x.DetailCategories
	}
	return nil
}

// The Evaluation model
type Evaluation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise_id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// price book
	PriceBook *PriceBook `protobuf:"bytes,3,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
	// service item types that require evaluation
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,4,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,5,opt,name=price,proto3" json:"price,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,6,opt,name=duration,proto3" json:"duration,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// name
	Name string `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,9,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// description
	Description string `protobuf:"bytes,10,opt,name=description,proto3" json:"description,omitempty"`
	// name shown in ob flow
	AliasForOnlineBooking string `protobuf:"bytes,11,opt,name=alias_for_online_booking,json=aliasForOnlineBooking,proto3" json:"alias_for_online_booking,omitempty"`
	// is allow staff auto assign. default is false
	AllowStaffAutoAssign bool `protobuf:"varint,12,opt,name=allow_staff_auto_assign,json=allowStaffAutoAssign,proto3" json:"allow_staff_auto_assign,omitempty"`
	// is resettable
	IsResettable bool `protobuf:"varint,13,opt,name=is_resettable,json=isResettable,proto3" json:"is_resettable,omitempty"`
	// reset interval days
	ResetIntervalDays int64 `protobuf:"varint,14,opt,name=reset_interval_days,json=resetIntervalDays,proto3" json:"reset_interval_days,omitempty"`
	// 万分位税率
	TaxRate int64 `protobuf:"varint,15,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// sort
	Sort int64 `protobuf:"varint,16,opt,name=sort,proto3" json:"sort,omitempty"`
	// limitation
	Limitation *Evaluation_Limitation `protobuf:"bytes,17,opt,name=limitation,proto3" json:"limitation,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// pushed at
	PushedAt *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=pushed_at,json=pushedAt,proto3,oneof" json:"pushed_at,omitempty"`
}

func (x *Evaluation) Reset() {
	*x = Evaluation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Evaluation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Evaluation) ProtoMessage() {}

func (x *Evaluation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Evaluation.ProtoReflect.Descriptor instead.
func (*Evaluation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{15}
}

func (x *Evaluation) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Evaluation) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *Evaluation) GetPriceBook() *PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

func (x *Evaluation) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *Evaluation) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *Evaluation) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *Evaluation) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *Evaluation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Evaluation) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Evaluation) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Evaluation) GetAliasForOnlineBooking() string {
	if x != nil {
		return x.AliasForOnlineBooking
	}
	return ""
}

func (x *Evaluation) GetAllowStaffAutoAssign() bool {
	if x != nil {
		return x.AllowStaffAutoAssign
	}
	return false
}

func (x *Evaluation) GetIsResettable() bool {
	if x != nil {
		return x.IsResettable
	}
	return false
}

func (x *Evaluation) GetResetIntervalDays() int64 {
	if x != nil {
		return x.ResetIntervalDays
	}
	return 0
}

func (x *Evaluation) GetTaxRate() int64 {
	if x != nil {
		return x.TaxRate
	}
	return 0
}

func (x *Evaluation) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *Evaluation) GetLimitation() *Evaluation_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

func (x *Evaluation) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Evaluation) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Evaluation) GetPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PushedAt
	}
	return nil
}

// create evaluation def
type CreateEvaluationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book id
	PriceBookId int64 `protobuf:"varint,1,opt,name=price_book_id,json=priceBookId,proto3" json:"price_book_id,omitempty"`
	// service item types that require evaluation
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,2,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,3,opt,name=price,proto3" json:"price,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,4,opt,name=duration,proto3" json:"duration,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,5,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// name
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// description
	Description string `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
	// name shown in ob flow
	AliasForOnlineBooking string `protobuf:"bytes,9,opt,name=alias_for_online_booking,json=aliasForOnlineBooking,proto3" json:"alias_for_online_booking,omitempty"`
	// is allow staff auto assign. default is false
	AllowStaffAutoAssign bool `protobuf:"varint,10,opt,name=allow_staff_auto_assign,json=allowStaffAutoAssign,proto3" json:"allow_staff_auto_assign,omitempty"`
	// is resettable
	IsResettable bool `protobuf:"varint,11,opt,name=is_resettable,json=isResettable,proto3" json:"is_resettable,omitempty"`
	// reset interval days
	ResetIntervalDays int64 `protobuf:"varint,12,opt,name=reset_interval_days,json=resetIntervalDays,proto3" json:"reset_interval_days,omitempty"`
	// 万分位税率
	TaxRate int64 `protobuf:"varint,13,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// limitation
	Limitation *Evaluation_Limitation `protobuf:"bytes,14,opt,name=limitation,proto3" json:"limitation,omitempty"`
}

func (x *CreateEvaluationDef) Reset() {
	*x = CreateEvaluationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationDef) ProtoMessage() {}

func (x *CreateEvaluationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationDef.ProtoReflect.Descriptor instead.
func (*CreateEvaluationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{16}
}

func (x *CreateEvaluationDef) GetPriceBookId() int64 {
	if x != nil {
		return x.PriceBookId
	}
	return 0
}

func (x *CreateEvaluationDef) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *CreateEvaluationDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *CreateEvaluationDef) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *CreateEvaluationDef) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *CreateEvaluationDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateEvaluationDef) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CreateEvaluationDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateEvaluationDef) GetAliasForOnlineBooking() string {
	if x != nil {
		return x.AliasForOnlineBooking
	}
	return ""
}

func (x *CreateEvaluationDef) GetAllowStaffAutoAssign() bool {
	if x != nil {
		return x.AllowStaffAutoAssign
	}
	return false
}

func (x *CreateEvaluationDef) GetIsResettable() bool {
	if x != nil {
		return x.IsResettable
	}
	return false
}

func (x *CreateEvaluationDef) GetResetIntervalDays() int64 {
	if x != nil {
		return x.ResetIntervalDays
	}
	return 0
}

func (x *CreateEvaluationDef) GetTaxRate() int64 {
	if x != nil {
		return x.TaxRate
	}
	return 0
}

func (x *CreateEvaluationDef) GetLimitation() *Evaluation_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

// update evaluation def
type UpdateEvaluationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item types that require evaluation
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,1,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,2,opt,name=price,proto3" json:"price,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,3,opt,name=duration,proto3" json:"duration,omitempty"`
	// color code
	ColorCode *string `protobuf:"bytes,4,opt,name=color_code,json=colorCode,proto3,oneof" json:"color_code,omitempty"`
	// name
	Name *string `protobuf:"bytes,5,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// is active
	IsActive *bool `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// description
	Description *string `protobuf:"bytes,7,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// name shown in ob flow
	AliasForOnlineBooking *string `protobuf:"bytes,8,opt,name=alias_for_online_booking,json=aliasForOnlineBooking,proto3,oneof" json:"alias_for_online_booking,omitempty"`
	// is allow staff auto assign. default is false
	AllowStaffAutoAssign *bool `protobuf:"varint,9,opt,name=allow_staff_auto_assign,json=allowStaffAutoAssign,proto3,oneof" json:"allow_staff_auto_assign,omitempty"`
	// is resettable
	IsResettable *bool `protobuf:"varint,10,opt,name=is_resettable,json=isResettable,proto3,oneof" json:"is_resettable,omitempty"`
	// reset interval days
	ResetIntervalDays *int64 `protobuf:"varint,11,opt,name=reset_interval_days,json=resetIntervalDays,proto3,oneof" json:"reset_interval_days,omitempty"`
	// 万分位税率
	TaxRate *int64 `protobuf:"varint,12,opt,name=tax_rate,json=taxRate,proto3,oneof" json:"tax_rate,omitempty"`
	// limitation
	Limitation *Evaluation_Limitation `protobuf:"bytes,13,opt,name=limitation,proto3" json:"limitation,omitempty"`
}

func (x *UpdateEvaluationDef) Reset() {
	*x = UpdateEvaluationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationDef) ProtoMessage() {}

func (x *UpdateEvaluationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationDef.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateEvaluationDef) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *UpdateEvaluationDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *UpdateEvaluationDef) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *UpdateEvaluationDef) GetColorCode() string {
	if x != nil && x.ColorCode != nil {
		return *x.ColorCode
	}
	return ""
}

func (x *UpdateEvaluationDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateEvaluationDef) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *UpdateEvaluationDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateEvaluationDef) GetAliasForOnlineBooking() string {
	if x != nil && x.AliasForOnlineBooking != nil {
		return *x.AliasForOnlineBooking
	}
	return ""
}

func (x *UpdateEvaluationDef) GetAllowStaffAutoAssign() bool {
	if x != nil && x.AllowStaffAutoAssign != nil {
		return *x.AllowStaffAutoAssign
	}
	return false
}

func (x *UpdateEvaluationDef) GetIsResettable() bool {
	if x != nil && x.IsResettable != nil {
		return *x.IsResettable
	}
	return false
}

func (x *UpdateEvaluationDef) GetResetIntervalDays() int64 {
	if x != nil && x.ResetIntervalDays != nil {
		return *x.ResetIntervalDays
	}
	return 0
}

func (x *UpdateEvaluationDef) GetTaxRate() int64 {
	if x != nil && x.TaxRate != nil {
		return *x.TaxRate
	}
	return 0
}

func (x *UpdateEvaluationDef) GetLimitation() *Evaluation_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

// create lodging type def
type CreateLodgingTypeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the lodging type
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// description of the lodging type
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// images of this lodging type
	PhotoList []string `protobuf:"bytes,3,rep,name=photo_list,json=photoList,proto3" json:"photo_list,omitempty"`
	// max pet number of this lodging type
	MaxPetNum int32 `protobuf:"varint,4,opt,name=max_pet_num,json=maxPetNum,proto3" json:"max_pet_num,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	// moe_pet_size.id list
	PetSizeIds []int64 `protobuf:"varint,5,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// lodging unit type in this lodging type
	LodgingUnitType v1.LodgingUnitType `protobuf:"varint,6,opt,name=lodging_unit_type,json=lodgingUnitType,proto3,enum=moego.models.offering.v1.LodgingUnitType" json:"lodging_unit_type,omitempty"`
	// whether the lodging type is available for all pet size
	PetSizeFilter bool `protobuf:"varint,7,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
}

func (x *CreateLodgingTypeDef) Reset() {
	*x = CreateLodgingTypeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLodgingTypeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLodgingTypeDef) ProtoMessage() {}

func (x *CreateLodgingTypeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLodgingTypeDef.ProtoReflect.Descriptor instead.
func (*CreateLodgingTypeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{18}
}

func (x *CreateLodgingTypeDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateLodgingTypeDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateLodgingTypeDef) GetPhotoList() []string {
	if x != nil {
		return x.PhotoList
	}
	return nil
}

func (x *CreateLodgingTypeDef) GetMaxPetNum() int32 {
	if x != nil {
		return x.MaxPetNum
	}
	return 0
}

func (x *CreateLodgingTypeDef) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *CreateLodgingTypeDef) GetLodgingUnitType() v1.LodgingUnitType {
	if x != nil {
		return x.LodgingUnitType
	}
	return v1.LodgingUnitType(0)
}

func (x *CreateLodgingTypeDef) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

// update lodging type def
type UpdateLodgingTypeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the lodging type
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// description of the lodging type
	Description *string `protobuf:"bytes,2,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// images of this lodging type
	PhotoList *PhotoList `protobuf:"bytes,3,opt,name=photo_list,json=photoList,proto3,oneof" json:"photo_list,omitempty"`
	// max pet number of this lodging type
	MaxPetNum *int32 `protobuf:"varint,4,opt,name=max_pet_num,json=maxPetNum,proto3,oneof" json:"max_pet_num,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	// moe_pet_size.id list
	PetSizeIds []int64 `protobuf:"varint,5,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// lodging unit type in this lodging type
	LodgingUnitType *v1.LodgingUnitType `protobuf:"varint,6,opt,name=lodging_unit_type,json=lodgingUnitType,proto3,enum=moego.models.offering.v1.LodgingUnitType,oneof" json:"lodging_unit_type,omitempty"`
	// whether the lodging type is available for all pet size
	PetSizeFilter *bool `protobuf:"varint,7,opt,name=pet_size_filter,json=petSizeFilter,proto3,oneof" json:"pet_size_filter,omitempty"`
}

func (x *UpdateLodgingTypeDef) Reset() {
	*x = UpdateLodgingTypeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLodgingTypeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLodgingTypeDef) ProtoMessage() {}

func (x *UpdateLodgingTypeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLodgingTypeDef.ProtoReflect.Descriptor instead.
func (*UpdateLodgingTypeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateLodgingTypeDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateLodgingTypeDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateLodgingTypeDef) GetPhotoList() *PhotoList {
	if x != nil {
		return x.PhotoList
	}
	return nil
}

func (x *UpdateLodgingTypeDef) GetMaxPetNum() int32 {
	if x != nil && x.MaxPetNum != nil {
		return *x.MaxPetNum
	}
	return 0
}

func (x *UpdateLodgingTypeDef) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *UpdateLodgingTypeDef) GetLodgingUnitType() v1.LodgingUnitType {
	if x != nil && x.LodgingUnitType != nil {
		return *x.LodgingUnitType
	}
	return v1.LodgingUnitType(0)
}

func (x *UpdateLodgingTypeDef) GetPetSizeFilter() bool {
	if x != nil && x.PetSizeFilter != nil {
		return *x.PetSizeFilter
	}
	return false
}

// photo list
type PhotoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// image list
	PhotoList []string `protobuf:"bytes,1,rep,name=photo_list,json=photoList,proto3" json:"photo_list,omitempty"`
}

func (x *PhotoList) Reset() {
	*x = PhotoList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhotoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhotoList) ProtoMessage() {}

func (x *PhotoList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhotoList.ProtoReflect.Descriptor instead.
func (*PhotoList) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{20}
}

func (x *PhotoList) GetPhotoList() []string {
	if x != nil {
		return x.PhotoList
	}
	return nil
}

// Lodging Type model
type LodgingType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging type
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name of the lodging type
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description of the lodging type
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// images of this lodging type
	PhotoList []string `protobuf:"bytes,4,rep,name=photo_list,json=photoList,proto3" json:"photo_list,omitempty"`
	// max pet number of this lodging type
	MaxPetNum int32 `protobuf:"varint,5,opt,name=max_pet_num,json=maxPetNum,proto3" json:"max_pet_num,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	// moe_pet_size.id list
	PetSizeIds []int64 `protobuf:"varint,6,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// lodging unit type in this lodging type
	LodgingUnitType v1.LodgingUnitType `protobuf:"varint,7,opt,name=lodging_unit_type,json=lodgingUnitType,proto3,enum=moego.models.offering.v1.LodgingUnitType" json:"lodging_unit_type,omitempty"`
	// whether the lodging type is available for all pet size
	PetSizeFilter bool `protobuf:"varint,8,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// Sort for the lodging type
	Sort int32 `protobuf:"varint,9,opt,name=sort,proto3" json:"sort,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,10,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// pushed at
	PushedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=pushed_at,json=pushedAt,proto3,oneof" json:"pushed_at,omitempty"`
}

func (x *LodgingType) Reset() {
	*x = LodgingType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingType) ProtoMessage() {}

func (x *LodgingType) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingType.ProtoReflect.Descriptor instead.
func (*LodgingType) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{21}
}

func (x *LodgingType) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingType) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LodgingType) GetPhotoList() []string {
	if x != nil {
		return x.PhotoList
	}
	return nil
}

func (x *LodgingType) GetMaxPetNum() int32 {
	if x != nil {
		return x.MaxPetNum
	}
	return 0
}

func (x *LodgingType) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *LodgingType) GetLodgingUnitType() v1.LodgingUnitType {
	if x != nil {
		return x.LodgingUnitType
	}
	return v1.LodgingUnitType(0)
}

func (x *LodgingType) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *LodgingType) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *LodgingType) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *LodgingType) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LodgingType) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LodgingType) GetPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PushedAt
	}
	return nil
}

// pricing rule
type PricingRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// price book
	PriceBook *PriceBook `protobuf:"bytes,3,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
	// rule type
	Type v2.RuleType `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.offering.v2.RuleType" json:"type,omitempty"`
	// rule name
	RuleName string `protobuf:"bytes,5,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	// active, true means active, false means inactive
	IsActive bool `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// rule apply type, apply to each one/apply to additional
	RuleApplyType v2.RuleApplyType `protobuf:"varint,7,opt,name=rule_apply_type,json=ruleApplyType,proto3,enum=moego.models.offering.v2.RuleApplyType" json:"rule_apply_type,omitempty"`
	// same lodging unit, only effective when rule_item_type is multiple pet
	NeedInSameLodging bool `protobuf:"varint,8,opt,name=need_in_same_lodging,json=needInSameLodging,proto3" json:"need_in_same_lodging,omitempty"`
	// rule configuration
	RuleConfiguration *v2.PricingRuleConfiguration `protobuf:"bytes,9,opt,name=rule_configuration,json=ruleConfiguration,proto3" json:"rule_configuration,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// pushed at
	PushedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=pushed_at,json=pushedAt,proto3,oneof" json:"pushed_at,omitempty"`
	// is charge per lodging, only effective when rule_item_type is peak date
	IsChargePerLodging bool `protobuf:"varint,13,opt,name=is_charge_per_lodging,json=isChargePerLodging,proto3" json:"is_charge_per_lodging,omitempty"`
	// limitation
	Limitation *PricingRule_Limitation `protobuf:"bytes,14,opt,name=limitation,proto3" json:"limitation,omitempty"`
}

func (x *PricingRule) Reset() {
	*x = PricingRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricingRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingRule) ProtoMessage() {}

func (x *PricingRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingRule.ProtoReflect.Descriptor instead.
func (*PricingRule) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{22}
}

func (x *PricingRule) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PricingRule) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *PricingRule) GetPriceBook() *PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

func (x *PricingRule) GetType() v2.RuleType {
	if x != nil {
		return x.Type
	}
	return v2.RuleType(0)
}

func (x *PricingRule) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *PricingRule) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *PricingRule) GetRuleApplyType() v2.RuleApplyType {
	if x != nil {
		return x.RuleApplyType
	}
	return v2.RuleApplyType(0)
}

func (x *PricingRule) GetNeedInSameLodging() bool {
	if x != nil {
		return x.NeedInSameLodging
	}
	return false
}

func (x *PricingRule) GetRuleConfiguration() *v2.PricingRuleConfiguration {
	if x != nil {
		return x.RuleConfiguration
	}
	return nil
}

func (x *PricingRule) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PricingRule) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *PricingRule) GetPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PushedAt
	}
	return nil
}

func (x *PricingRule) GetIsChargePerLodging() bool {
	if x != nil {
		return x.IsChargePerLodging
	}
	return false
}

func (x *PricingRule) GetLimitation() *PricingRule_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

// // create pricing rule def
type CreatePricingRuleDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book id
	PriceBookId int64 `protobuf:"varint,1,opt,name=price_book_id,json=priceBookId,proto3" json:"price_book_id,omitempty"`
	// rule type
	Type v2.RuleType `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.offering.v2.RuleType" json:"type,omitempty"`
	// rule name
	RuleName string `protobuf:"bytes,3,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	// active, true means active, false means inactive
	IsActive bool `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// rule apply type, apply to each one/apply to additional
	RuleApplyType v2.RuleApplyType `protobuf:"varint,5,opt,name=rule_apply_type,json=ruleApplyType,proto3,enum=moego.models.offering.v2.RuleApplyType" json:"rule_apply_type,omitempty"`
	// same lodging unit, only effective when rule_item_type is multiple pet
	NeedInSameLodging bool `protobuf:"varint,6,opt,name=need_in_same_lodging,json=needInSameLodging,proto3" json:"need_in_same_lodging,omitempty"`
	// rule configuration
	RuleConfiguration *v2.PricingRuleConfiguration `protobuf:"bytes,7,opt,name=rule_configuration,json=ruleConfiguration,proto3" json:"rule_configuration,omitempty"`
	// is charge per lodging, only effective when rule_item_type is peak date
	IsChargePerLodging bool `protobuf:"varint,8,opt,name=is_charge_per_lodging,json=isChargePerLodging,proto3" json:"is_charge_per_lodging,omitempty"`
	// limitation
	Limitation *PricingRule_Limitation `protobuf:"bytes,9,opt,name=limitation,proto3" json:"limitation,omitempty"`
}

func (x *CreatePricingRuleDef) Reset() {
	*x = CreatePricingRuleDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePricingRuleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePricingRuleDef) ProtoMessage() {}

func (x *CreatePricingRuleDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePricingRuleDef.ProtoReflect.Descriptor instead.
func (*CreatePricingRuleDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{23}
}

func (x *CreatePricingRuleDef) GetPriceBookId() int64 {
	if x != nil {
		return x.PriceBookId
	}
	return 0
}

func (x *CreatePricingRuleDef) GetType() v2.RuleType {
	if x != nil {
		return x.Type
	}
	return v2.RuleType(0)
}

func (x *CreatePricingRuleDef) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *CreatePricingRuleDef) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CreatePricingRuleDef) GetRuleApplyType() v2.RuleApplyType {
	if x != nil {
		return x.RuleApplyType
	}
	return v2.RuleApplyType(0)
}

func (x *CreatePricingRuleDef) GetNeedInSameLodging() bool {
	if x != nil {
		return x.NeedInSameLodging
	}
	return false
}

func (x *CreatePricingRuleDef) GetRuleConfiguration() *v2.PricingRuleConfiguration {
	if x != nil {
		return x.RuleConfiguration
	}
	return nil
}

func (x *CreatePricingRuleDef) GetIsChargePerLodging() bool {
	if x != nil {
		return x.IsChargePerLodging
	}
	return false
}

func (x *CreatePricingRuleDef) GetLimitation() *PricingRule_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

// update pricing rule def
type UpdatePricingRuleDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule type
	Type v2.RuleType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.offering.v2.RuleType" json:"type,omitempty"`
	// rule name
	RuleName *string `protobuf:"bytes,2,opt,name=rule_name,json=ruleName,proto3,oneof" json:"rule_name,omitempty"`
	// active, true means active, false means inactive
	IsActive *bool `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// rule apply type, apply to each one/apply to additional
	RuleApplyType *v2.RuleApplyType `protobuf:"varint,4,opt,name=rule_apply_type,json=ruleApplyType,proto3,enum=moego.models.offering.v2.RuleApplyType,oneof" json:"rule_apply_type,omitempty"`
	// same lodging unit, only effective when rule_item_type is multiple pet
	NeedInSameLodging *bool `protobuf:"varint,5,opt,name=need_in_same_lodging,json=needInSameLodging,proto3,oneof" json:"need_in_same_lodging,omitempty"`
	// rule configuration
	RuleConfiguration *v2.PricingRuleConfiguration `protobuf:"bytes,6,opt,name=rule_configuration,json=ruleConfiguration,proto3,oneof" json:"rule_configuration,omitempty"`
	// is charge per lodging, only effective when rule_item_type is peak date
	IsChargePerLodging *bool `protobuf:"varint,7,opt,name=is_charge_per_lodging,json=isChargePerLodging,proto3,oneof" json:"is_charge_per_lodging,omitempty"`
	// limitation
	Limitation *PricingRule_Limitation `protobuf:"bytes,8,opt,name=limitation,proto3,oneof" json:"limitation,omitempty"`
}

func (x *UpdatePricingRuleDef) Reset() {
	*x = UpdatePricingRuleDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePricingRuleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePricingRuleDef) ProtoMessage() {}

func (x *UpdatePricingRuleDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePricingRuleDef.ProtoReflect.Descriptor instead.
func (*UpdatePricingRuleDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{24}
}

func (x *UpdatePricingRuleDef) GetType() v2.RuleType {
	if x != nil {
		return x.Type
	}
	return v2.RuleType(0)
}

func (x *UpdatePricingRuleDef) GetRuleName() string {
	if x != nil && x.RuleName != nil {
		return *x.RuleName
	}
	return ""
}

func (x *UpdatePricingRuleDef) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *UpdatePricingRuleDef) GetRuleApplyType() v2.RuleApplyType {
	if x != nil && x.RuleApplyType != nil {
		return *x.RuleApplyType
	}
	return v2.RuleApplyType(0)
}

func (x *UpdatePricingRuleDef) GetNeedInSameLodging() bool {
	if x != nil && x.NeedInSameLodging != nil {
		return *x.NeedInSameLodging
	}
	return false
}

func (x *UpdatePricingRuleDef) GetRuleConfiguration() *v2.PricingRuleConfiguration {
	if x != nil {
		return x.RuleConfiguration
	}
	return nil
}

func (x *UpdatePricingRuleDef) GetIsChargePerLodging() bool {
	if x != nil && x.IsChargePerLodging != nil {
		return *x.IsChargePerLodging
	}
	return false
}

func (x *UpdatePricingRuleDef) GetLimitation() *PricingRule_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

// service charge charge
type ServiceCharge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// price book
	PriceBook *PriceBook `protobuf:"bytes,3,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// split method
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,6,opt,name=price,proto3" json:"price,omitempty"`
	// 万分位税率
	TaxRate int64 `protobuf:"varint,7,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// sort
	Sort int32 `protobuf:"varint,8,opt,name=sort,proto3" json:"sort,omitempty"`
	// auto apply status
	AutoApplyStatus v12.ServiceCharge_AutoApplyStatus `protobuf:"varint,9,opt,name=auto_apply_status,json=autoApplyStatus,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyStatus" json:"auto_apply_status,omitempty"`
	// auto apply condition
	AutoApplyCondition v12.ServiceCharge_AutoApplyCondition `protobuf:"varint,10,opt,name=auto_apply_condition,json=autoApplyCondition,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyCondition" json:"auto_apply_condition,omitempty"`
	// auto apply time, unit: minute
	// Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
	AutoApplyTime int32 `protobuf:"varint,11,opt,name=auto_apply_time,json=autoApplyTime,proto3" json:"auto_apply_time,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,12,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// apply type
	ApplyType v12.ServiceCharge_ApplyType `protobuf:"varint,13,opt,name=apply_type,json=applyType,proto3,enum=moego.models.order.v1.ServiceCharge_ApplyType" json:"apply_type,omitempty"`
	// auto apply time
	AutoApplyTimeType *v12.ServiceCharge_AutoApplyTimeType `protobuf:"varint,14,opt,name=auto_apply_time_type,json=autoApplyTimeType,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyTimeType,oneof" json:"auto_apply_time_type,omitempty"`
	// surcharge type
	SurchargeType *v12.SurchargeType `protobuf:"varint,15,opt,name=surcharge_type,json=surchargeType,proto3,enum=moego.models.order.v1.SurchargeType,oneof" json:"surcharge_type,omitempty"`
	// charge method
	ChargeMethod v12.ChargeMethod `protobuf:"varint,16,opt,name=charge_method,json=chargeMethod,proto3,enum=moego.models.order.v1.ChargeMethod" json:"charge_method,omitempty"`
	// Only applicable when surcharge_type == CHARGE_24_HOUR
	TimeBasedPricingType *v12.ServiceCharge_TimeBasedPricingType `protobuf:"varint,17,opt,name=time_based_pricing_type,json=timeBasedPricingType,proto3,enum=moego.models.order.v1.ServiceCharge_TimeBasedPricingType,oneof" json:"time_based_pricing_type,omitempty"`
	// Applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
	MultiplePetsChargeType *v12.ServiceCharge_MultiplePetsChargeType `protobuf:"varint,18,opt,name=multiple_pets_charge_type,json=multiplePetsChargeType,proto3,enum=moego.models.order.v1.ServiceCharge_MultiplePetsChargeType,oneof" json:"multiple_pets_charge_type,omitempty"`
	// List of rules for hourly exceed charges, applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
	HourlyExceedRules *HourlyExceedRules `protobuf:"bytes,19,opt,name=hourly_exceed_rules,json=hourlyExceedRules,proto3" json:"hourly_exceed_rules,omitempty"`
	// limitation
	Limitation *ServiceCharge_Limitation `protobuf:"bytes,20,opt,name=limitation,proto3" json:"limitation,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// pushed at
	PushedAt *timestamppb.Timestamp `protobuf:"bytes,23,opt,name=pushed_at,json=pushedAt,proto3,oneof" json:"pushed_at,omitempty"`
	// service item types
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,24,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
}

func (x *ServiceCharge) Reset() {
	*x = ServiceCharge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCharge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCharge) ProtoMessage() {}

func (x *ServiceCharge) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCharge.ProtoReflect.Descriptor instead.
func (*ServiceCharge) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{25}
}

func (x *ServiceCharge) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceCharge) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ServiceCharge) GetPriceBook() *PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

func (x *ServiceCharge) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceCharge) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceCharge) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *ServiceCharge) GetTaxRate() int64 {
	if x != nil {
		return x.TaxRate
	}
	return 0
}

func (x *ServiceCharge) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ServiceCharge) GetAutoApplyStatus() v12.ServiceCharge_AutoApplyStatus {
	if x != nil {
		return x.AutoApplyStatus
	}
	return v12.ServiceCharge_AutoApplyStatus(0)
}

func (x *ServiceCharge) GetAutoApplyCondition() v12.ServiceCharge_AutoApplyCondition {
	if x != nil {
		return x.AutoApplyCondition
	}
	return v12.ServiceCharge_AutoApplyCondition(0)
}

func (x *ServiceCharge) GetAutoApplyTime() int32 {
	if x != nil {
		return x.AutoApplyTime
	}
	return 0
}

func (x *ServiceCharge) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *ServiceCharge) GetApplyType() v12.ServiceCharge_ApplyType {
	if x != nil {
		return x.ApplyType
	}
	return v12.ServiceCharge_ApplyType(0)
}

func (x *ServiceCharge) GetAutoApplyTimeType() v12.ServiceCharge_AutoApplyTimeType {
	if x != nil && x.AutoApplyTimeType != nil {
		return *x.AutoApplyTimeType
	}
	return v12.ServiceCharge_AutoApplyTimeType(0)
}

func (x *ServiceCharge) GetSurchargeType() v12.SurchargeType {
	if x != nil && x.SurchargeType != nil {
		return *x.SurchargeType
	}
	return v12.SurchargeType(0)
}

func (x *ServiceCharge) GetChargeMethod() v12.ChargeMethod {
	if x != nil {
		return x.ChargeMethod
	}
	return v12.ChargeMethod(0)
}

func (x *ServiceCharge) GetTimeBasedPricingType() v12.ServiceCharge_TimeBasedPricingType {
	if x != nil && x.TimeBasedPricingType != nil {
		return *x.TimeBasedPricingType
	}
	return v12.ServiceCharge_TimeBasedPricingType(0)
}

func (x *ServiceCharge) GetMultiplePetsChargeType() v12.ServiceCharge_MultiplePetsChargeType {
	if x != nil && x.MultiplePetsChargeType != nil {
		return *x.MultiplePetsChargeType
	}
	return v12.ServiceCharge_MultiplePetsChargeType(0)
}

func (x *ServiceCharge) GetHourlyExceedRules() *HourlyExceedRules {
	if x != nil {
		return x.HourlyExceedRules
	}
	return nil
}

func (x *ServiceCharge) GetLimitation() *ServiceCharge_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

func (x *ServiceCharge) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ServiceCharge) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ServiceCharge) GetPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PushedAt
	}
	return nil
}

func (x *ServiceCharge) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// hourly exceed rules
type HourlyExceedRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of rules for hourly exceed charges, applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
	HourlyExceedRules []*v12.ServiceChargeExceedHourRule `protobuf:"bytes,1,rep,name=hourly_exceed_rules,json=hourlyExceedRules,proto3" json:"hourly_exceed_rules,omitempty"`
}

func (x *HourlyExceedRules) Reset() {
	*x = HourlyExceedRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HourlyExceedRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HourlyExceedRules) ProtoMessage() {}

func (x *HourlyExceedRules) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HourlyExceedRules.ProtoReflect.Descriptor instead.
func (*HourlyExceedRules) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{26}
}

func (x *HourlyExceedRules) GetHourlyExceedRules() []*v12.ServiceChargeExceedHourRule {
	if x != nil {
		return x.HourlyExceedRules
	}
	return nil
}

// food source limitation
type FoodSourceLimitationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether the food source is available for all food sources
	IsAllFoodSources bool `protobuf:"varint,1,opt,name=is_all_food_sources,json=isAllFoodSources,proto3" json:"is_all_food_sources,omitempty"`
	// food source ids
	FoodSourceIds []int64 `protobuf:"varint,2,rep,packed,name=food_source_ids,json=foodSourceIds,proto3" json:"food_source_ids,omitempty"`
}

func (x *FoodSourceLimitationDef) Reset() {
	*x = FoodSourceLimitationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FoodSourceLimitationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FoodSourceLimitationDef) ProtoMessage() {}

func (x *FoodSourceLimitationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FoodSourceLimitationDef.ProtoReflect.Descriptor instead.
func (*FoodSourceLimitationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{27}
}

func (x *FoodSourceLimitationDef) GetIsAllFoodSources() bool {
	if x != nil {
		return x.IsAllFoodSources
	}
	return false
}

func (x *FoodSourceLimitationDef) GetFoodSourceIds() []int64 {
	if x != nil {
		return x.FoodSourceIds
	}
	return nil
}

// create service charge def
type CreateServiceChargeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book id
	PriceBookId int64 `protobuf:"varint,1,opt,name=price_book_id,json=priceBookId,proto3" json:"price_book_id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// split method
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,4,opt,name=price,proto3" json:"price,omitempty"`
	// 万分位税率
	TaxRate int64 `protobuf:"varint,5,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// sort
	Sort int32 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	// auto apply status
	AutoApplyStatus v12.ServiceCharge_AutoApplyStatus `protobuf:"varint,7,opt,name=auto_apply_status,json=autoApplyStatus,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyStatus" json:"auto_apply_status,omitempty"`
	// auto apply condition
	AutoApplyCondition v12.ServiceCharge_AutoApplyCondition `protobuf:"varint,8,opt,name=auto_apply_condition,json=autoApplyCondition,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyCondition" json:"auto_apply_condition,omitempty"`
	// auto apply time, unit: minute
	// Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
	AutoApplyTime *int32 `protobuf:"varint,9,opt,name=auto_apply_time,json=autoApplyTime,proto3,oneof" json:"auto_apply_time,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,10,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// apply type
	ApplyType v12.ServiceCharge_ApplyType `protobuf:"varint,11,opt,name=apply_type,json=applyType,proto3,enum=moego.models.order.v1.ServiceCharge_ApplyType" json:"apply_type,omitempty"`
	// auto apply time type
	AutoApplyTimeType *v12.ServiceCharge_AutoApplyTimeType `protobuf:"varint,12,opt,name=auto_apply_time_type,json=autoApplyTimeType,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyTimeType,oneof" json:"auto_apply_time_type,omitempty"`
	// surcharge type
	SurchargeType *v12.SurchargeType `protobuf:"varint,13,opt,name=surcharge_type,json=surchargeType,proto3,enum=moego.models.order.v1.SurchargeType,oneof" json:"surcharge_type,omitempty"`
	// charge method
	ChargeMethod v12.ChargeMethod `protobuf:"varint,14,opt,name=charge_method,json=chargeMethod,proto3,enum=moego.models.order.v1.ChargeMethod" json:"charge_method,omitempty"`
	// Only applicable when surcharge_type == CHARGE_24_HOUR
	TimeBasedPricingType *v12.ServiceCharge_TimeBasedPricingType `protobuf:"varint,15,opt,name=time_based_pricing_type,json=timeBasedPricingType,proto3,enum=moego.models.order.v1.ServiceCharge_TimeBasedPricingType,oneof" json:"time_based_pricing_type,omitempty"`
	// Applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
	MultiplePetsChargeType *v12.ServiceCharge_MultiplePetsChargeType `protobuf:"varint,16,opt,name=multiple_pets_charge_type,json=multiplePetsChargeType,proto3,enum=moego.models.order.v1.ServiceCharge_MultiplePetsChargeType,oneof" json:"multiple_pets_charge_type,omitempty"`
	// List of rules for hourly exceed charges, applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
	HourlyExceedRules *HourlyExceedRules `protobuf:"bytes,17,opt,name=hourly_exceed_rules,json=hourlyExceedRules,proto3" json:"hourly_exceed_rules,omitempty"`
	// limitation
	Limitation *ServiceCharge_Limitation `protobuf:"bytes,18,opt,name=limitation,proto3" json:"limitation,omitempty"`
	// service item types
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,19,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
}

func (x *CreateServiceChargeDef) Reset() {
	*x = CreateServiceChargeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceChargeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceChargeDef) ProtoMessage() {}

func (x *CreateServiceChargeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceChargeDef.ProtoReflect.Descriptor instead.
func (*CreateServiceChargeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{28}
}

func (x *CreateServiceChargeDef) GetPriceBookId() int64 {
	if x != nil {
		return x.PriceBookId
	}
	return 0
}

func (x *CreateServiceChargeDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateServiceChargeDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateServiceChargeDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *CreateServiceChargeDef) GetTaxRate() int64 {
	if x != nil {
		return x.TaxRate
	}
	return 0
}

func (x *CreateServiceChargeDef) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *CreateServiceChargeDef) GetAutoApplyStatus() v12.ServiceCharge_AutoApplyStatus {
	if x != nil {
		return x.AutoApplyStatus
	}
	return v12.ServiceCharge_AutoApplyStatus(0)
}

func (x *CreateServiceChargeDef) GetAutoApplyCondition() v12.ServiceCharge_AutoApplyCondition {
	if x != nil {
		return x.AutoApplyCondition
	}
	return v12.ServiceCharge_AutoApplyCondition(0)
}

func (x *CreateServiceChargeDef) GetAutoApplyTime() int32 {
	if x != nil && x.AutoApplyTime != nil {
		return *x.AutoApplyTime
	}
	return 0
}

func (x *CreateServiceChargeDef) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CreateServiceChargeDef) GetApplyType() v12.ServiceCharge_ApplyType {
	if x != nil {
		return x.ApplyType
	}
	return v12.ServiceCharge_ApplyType(0)
}

func (x *CreateServiceChargeDef) GetAutoApplyTimeType() v12.ServiceCharge_AutoApplyTimeType {
	if x != nil && x.AutoApplyTimeType != nil {
		return *x.AutoApplyTimeType
	}
	return v12.ServiceCharge_AutoApplyTimeType(0)
}

func (x *CreateServiceChargeDef) GetSurchargeType() v12.SurchargeType {
	if x != nil && x.SurchargeType != nil {
		return *x.SurchargeType
	}
	return v12.SurchargeType(0)
}

func (x *CreateServiceChargeDef) GetChargeMethod() v12.ChargeMethod {
	if x != nil {
		return x.ChargeMethod
	}
	return v12.ChargeMethod(0)
}

func (x *CreateServiceChargeDef) GetTimeBasedPricingType() v12.ServiceCharge_TimeBasedPricingType {
	if x != nil && x.TimeBasedPricingType != nil {
		return *x.TimeBasedPricingType
	}
	return v12.ServiceCharge_TimeBasedPricingType(0)
}

func (x *CreateServiceChargeDef) GetMultiplePetsChargeType() v12.ServiceCharge_MultiplePetsChargeType {
	if x != nil && x.MultiplePetsChargeType != nil {
		return *x.MultiplePetsChargeType
	}
	return v12.ServiceCharge_MultiplePetsChargeType(0)
}

func (x *CreateServiceChargeDef) GetHourlyExceedRules() *HourlyExceedRules {
	if x != nil {
		return x.HourlyExceedRules
	}
	return nil
}

func (x *CreateServiceChargeDef) GetLimitation() *ServiceCharge_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

func (x *CreateServiceChargeDef) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// update service charge def
type UpdateServiceChargeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// split method
	Description *string `protobuf:"bytes,2,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,3,opt,name=price,proto3" json:"price,omitempty"`
	// 万分位税率
	TaxRate *int64 `protobuf:"varint,4,opt,name=tax_rate,json=taxRate,proto3,oneof" json:"tax_rate,omitempty"`
	// sort
	Sort *int32 `protobuf:"varint,5,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// auto apply status
	AutoApplyStatus *v12.ServiceCharge_AutoApplyStatus `protobuf:"varint,6,opt,name=auto_apply_status,json=autoApplyStatus,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyStatus,oneof" json:"auto_apply_status,omitempty"`
	// auto apply condition
	AutoApplyCondition *v12.ServiceCharge_AutoApplyCondition `protobuf:"varint,7,opt,name=auto_apply_condition,json=autoApplyCondition,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyCondition,oneof" json:"auto_apply_condition,omitempty"`
	// auto apply time, unit: minute
	// Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
	AutoApplyTime *int32 `protobuf:"varint,8,opt,name=auto_apply_time,json=autoApplyTime,proto3,oneof" json:"auto_apply_time,omitempty"`
	// is active
	IsActive *bool `protobuf:"varint,9,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// apply type
	ApplyType *v12.ServiceCharge_ApplyType `protobuf:"varint,10,opt,name=apply_type,json=applyType,proto3,enum=moego.models.order.v1.ServiceCharge_ApplyType,oneof" json:"apply_type,omitempty"`
	// auto apply time type
	AutoApplyTimeType *v12.ServiceCharge_AutoApplyTimeType `protobuf:"varint,11,opt,name=auto_apply_time_type,json=autoApplyTimeType,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyTimeType,oneof" json:"auto_apply_time_type,omitempty"`
	// surcharge type
	SurchargeType *v12.SurchargeType `protobuf:"varint,12,opt,name=surcharge_type,json=surchargeType,proto3,enum=moego.models.order.v1.SurchargeType,oneof" json:"surcharge_type,omitempty"`
	// charge method
	ChargeMethod *v12.ChargeMethod `protobuf:"varint,13,opt,name=charge_method,json=chargeMethod,proto3,enum=moego.models.order.v1.ChargeMethod,oneof" json:"charge_method,omitempty"`
	// Only applicable when surcharge_type == CHARGE_24_HOUR
	TimeBasedPricingType *v12.ServiceCharge_TimeBasedPricingType `protobuf:"varint,14,opt,name=time_based_pricing_type,json=timeBasedPricingType,proto3,enum=moego.models.order.v1.ServiceCharge_TimeBasedPricingType,oneof" json:"time_based_pricing_type,omitempty"`
	// Applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
	MultiplePetsChargeType *v12.ServiceCharge_MultiplePetsChargeType `protobuf:"varint,15,opt,name=multiple_pets_charge_type,json=multiplePetsChargeType,proto3,enum=moego.models.order.v1.ServiceCharge_MultiplePetsChargeType,oneof" json:"multiple_pets_charge_type,omitempty"`
	// List of rules for hourly exceed charges, applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
	HourlyExceedRules *HourlyExceedRules `protobuf:"bytes,16,opt,name=hourly_exceed_rules,json=hourlyExceedRules,proto3" json:"hourly_exceed_rules,omitempty"`
	// limitation
	Limitation *ServiceCharge_Limitation `protobuf:"bytes,17,opt,name=limitation,proto3" json:"limitation,omitempty"`
	// service item types
	ServiceItemTypes *UpdateServiceChargeDef_ServiceItemTypes `protobuf:"bytes,18,opt,name=service_item_types,json=serviceItemTypes,proto3" json:"service_item_types,omitempty"`
}

func (x *UpdateServiceChargeDef) Reset() {
	*x = UpdateServiceChargeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceChargeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceChargeDef) ProtoMessage() {}

func (x *UpdateServiceChargeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceChargeDef.ProtoReflect.Descriptor instead.
func (*UpdateServiceChargeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateServiceChargeDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateServiceChargeDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateServiceChargeDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *UpdateServiceChargeDef) GetTaxRate() int64 {
	if x != nil && x.TaxRate != nil {
		return *x.TaxRate
	}
	return 0
}

func (x *UpdateServiceChargeDef) GetSort() int32 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *UpdateServiceChargeDef) GetAutoApplyStatus() v12.ServiceCharge_AutoApplyStatus {
	if x != nil && x.AutoApplyStatus != nil {
		return *x.AutoApplyStatus
	}
	return v12.ServiceCharge_AutoApplyStatus(0)
}

func (x *UpdateServiceChargeDef) GetAutoApplyCondition() v12.ServiceCharge_AutoApplyCondition {
	if x != nil && x.AutoApplyCondition != nil {
		return *x.AutoApplyCondition
	}
	return v12.ServiceCharge_AutoApplyCondition(0)
}

func (x *UpdateServiceChargeDef) GetAutoApplyTime() int32 {
	if x != nil && x.AutoApplyTime != nil {
		return *x.AutoApplyTime
	}
	return 0
}

func (x *UpdateServiceChargeDef) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *UpdateServiceChargeDef) GetApplyType() v12.ServiceCharge_ApplyType {
	if x != nil && x.ApplyType != nil {
		return *x.ApplyType
	}
	return v12.ServiceCharge_ApplyType(0)
}

func (x *UpdateServiceChargeDef) GetAutoApplyTimeType() v12.ServiceCharge_AutoApplyTimeType {
	if x != nil && x.AutoApplyTimeType != nil {
		return *x.AutoApplyTimeType
	}
	return v12.ServiceCharge_AutoApplyTimeType(0)
}

func (x *UpdateServiceChargeDef) GetSurchargeType() v12.SurchargeType {
	if x != nil && x.SurchargeType != nil {
		return *x.SurchargeType
	}
	return v12.SurchargeType(0)
}

func (x *UpdateServiceChargeDef) GetChargeMethod() v12.ChargeMethod {
	if x != nil && x.ChargeMethod != nil {
		return *x.ChargeMethod
	}
	return v12.ChargeMethod(0)
}

func (x *UpdateServiceChargeDef) GetTimeBasedPricingType() v12.ServiceCharge_TimeBasedPricingType {
	if x != nil && x.TimeBasedPricingType != nil {
		return *x.TimeBasedPricingType
	}
	return v12.ServiceCharge_TimeBasedPricingType(0)
}

func (x *UpdateServiceChargeDef) GetMultiplePetsChargeType() v12.ServiceCharge_MultiplePetsChargeType {
	if x != nil && x.MultiplePetsChargeType != nil {
		return *x.MultiplePetsChargeType
	}
	return v12.ServiceCharge_MultiplePetsChargeType(0)
}

func (x *UpdateServiceChargeDef) GetHourlyExceedRules() *HourlyExceedRules {
	if x != nil {
		return x.HourlyExceedRules
	}
	return nil
}

func (x *UpdateServiceChargeDef) GetLimitation() *ServiceCharge_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

func (x *UpdateServiceChargeDef) GetServiceItemTypes() *UpdateServiceChargeDef_ServiceItemTypes {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// auto rule
type Service_AutoRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto rollover, now only for daycare
	AutoRolloverRule *v1.AutoRolloverRule `protobuf:"bytes,1,opt,name=auto_rollover_rule,json=autoRolloverRule,proto3" json:"auto_rollover_rule,omitempty"`
	// bundle service rule, now only for grooming, only contains add-on
	BundleServiceRule *BundleServiceRule `protobuf:"bytes,2,opt,name=bundle_service_rule,json=bundleServiceRule,proto3" json:"bundle_service_rule,omitempty"`
	// AdditionalServiceRule, now only for boarding
	AdditionalServiceRule *v1.AdditionalServiceRule `protobuf:"bytes,3,opt,name=additional_service_rule,json=additionalServiceRule,proto3" json:"additional_service_rule,omitempty"`
}

func (x *Service_AutoRule) Reset() {
	*x = Service_AutoRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service_AutoRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service_AutoRule) ProtoMessage() {}

func (x *Service_AutoRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service_AutoRule.ProtoReflect.Descriptor instead.
func (*Service_AutoRule) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Service_AutoRule) GetAutoRolloverRule() *v1.AutoRolloverRule {
	if x != nil {
		return x.AutoRolloverRule
	}
	return nil
}

func (x *Service_AutoRule) GetBundleServiceRule() *BundleServiceRule {
	if x != nil {
		return x.BundleServiceRule
	}
	return nil
}

func (x *Service_AutoRule) GetAdditionalServiceRule() *v1.AdditionalServiceRule {
	if x != nil {
		return x.AdditionalServiceRule
	}
	return nil
}

// limitation
type Service_Limitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all pet types and breeds
	AllPetTypesAndBreeds bool `protobuf:"varint,1,opt,name=all_pet_types_and_breeds,json=allPetTypesAndBreeds,proto3" json:"all_pet_types_and_breeds,omitempty"`
	// required pet types
	PetTypeBreeds []*PetTypeBreeds `protobuf:"bytes,2,rep,name=pet_type_breeds,json=petTypeBreeds,proto3" json:"pet_type_breeds,omitempty"`
	// all pet sizes
	AllPetSizes bool `protobuf:"varint,3,opt,name=all_pet_sizes,json=allPetSizes,proto3" json:"all_pet_sizes,omitempty"`
	// pet weight range
	PetWeightRange *WeightRange `protobuf:"bytes,4,opt,name=pet_weight_range,json=petWeightRange,proto3" json:"pet_weight_range,omitempty"`
	// all coat types
	AllCoatTypes bool `protobuf:"varint,5,opt,name=all_coat_types,json=allCoatTypes,proto3" json:"all_coat_types,omitempty"`
	// required coat types
	RequiredCoatTypes []string `protobuf:"bytes,6,rep,name=required_coat_types,json=requiredCoatTypes,proto3" json:"required_coat_types,omitempty"`
	// pet codes
	PetCodes *PetCodeLimitationDef `protobuf:"bytes,7,opt,name=pet_codes,json=petCodes,proto3" json:"pet_codes,omitempty"`
	// services (only for add on)
	Services *ServiceLimitationDef `protobuf:"bytes,8,opt,name=services,proto3" json:"services,omitempty"`
	// evaluations
	Evaluations *EvaluationLimitationDef `protobuf:"bytes,9,opt,name=evaluations,proto3" json:"evaluations,omitempty"`
	// lodging types
	LodgingTypes *LodgingTypesLimitationDef `protobuf:"bytes,10,opt,name=lodging_types,json=lodgingTypes,proto3" json:"lodging_types,omitempty"`
}

func (x *Service_Limitation) Reset() {
	*x = Service_Limitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service_Limitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service_Limitation) ProtoMessage() {}

func (x *Service_Limitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service_Limitation.ProtoReflect.Descriptor instead.
func (*Service_Limitation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Service_Limitation) GetAllPetTypesAndBreeds() bool {
	if x != nil {
		return x.AllPetTypesAndBreeds
	}
	return false
}

func (x *Service_Limitation) GetPetTypeBreeds() []*PetTypeBreeds {
	if x != nil {
		return x.PetTypeBreeds
	}
	return nil
}

func (x *Service_Limitation) GetAllPetSizes() bool {
	if x != nil {
		return x.AllPetSizes
	}
	return false
}

func (x *Service_Limitation) GetPetWeightRange() *WeightRange {
	if x != nil {
		return x.PetWeightRange
	}
	return nil
}

func (x *Service_Limitation) GetAllCoatTypes() bool {
	if x != nil {
		return x.AllCoatTypes
	}
	return false
}

func (x *Service_Limitation) GetRequiredCoatTypes() []string {
	if x != nil {
		return x.RequiredCoatTypes
	}
	return nil
}

func (x *Service_Limitation) GetPetCodes() *PetCodeLimitationDef {
	if x != nil {
		return x.PetCodes
	}
	return nil
}

func (x *Service_Limitation) GetServices() *ServiceLimitationDef {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *Service_Limitation) GetEvaluations() *EvaluationLimitationDef {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

func (x *Service_Limitation) GetLodgingTypes() *LodgingTypesLimitationDef {
	if x != nil {
		return x.LodgingTypes
	}
	return nil
}

// limitation
type Evaluation_Limitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet types and breeds
	PetTypeBreeds *PetTypeBreedsLimitationDef `protobuf:"bytes,1,opt,name=pet_type_breeds,json=petTypeBreeds,proto3" json:"pet_type_breeds,omitempty"`
	// lodging types
	LodgingTypes *LodgingTypesLimitationDef `protobuf:"bytes,2,opt,name=lodging_types,json=lodgingTypes,proto3" json:"lodging_types,omitempty"`
}

func (x *Evaluation_Limitation) Reset() {
	*x = Evaluation_Limitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Evaluation_Limitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Evaluation_Limitation) ProtoMessage() {}

func (x *Evaluation_Limitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Evaluation_Limitation.ProtoReflect.Descriptor instead.
func (*Evaluation_Limitation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{15, 0}
}

func (x *Evaluation_Limitation) GetPetTypeBreeds() *PetTypeBreedsLimitationDef {
	if x != nil {
		return x.PetTypeBreeds
	}
	return nil
}

func (x *Evaluation_Limitation) GetLodgingTypes() *LodgingTypesLimitationDef {
	if x != nil {
		return x.LodgingTypes
	}
	return nil
}

// limitation
type PricingRule_Limitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service limitation
	Services *ServiceLimitationDef `protobuf:"bytes,1,opt,name=services,proto3" json:"services,omitempty"`
}

func (x *PricingRule_Limitation) Reset() {
	*x = PricingRule_Limitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricingRule_Limitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingRule_Limitation) ProtoMessage() {}

func (x *PricingRule_Limitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingRule_Limitation.ProtoReflect.Descriptor instead.
func (*PricingRule_Limitation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{22, 0}
}

func (x *PricingRule_Limitation) GetServices() *ServiceLimitationDef {
	if x != nil {
		return x.Services
	}
	return nil
}

// limitation
type ServiceCharge_Limitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// food source limitation
	FoodSources *FoodSourceLimitationDef `protobuf:"bytes,1,opt,name=food_sources,json=foodSources,proto3" json:"food_sources,omitempty"`
	// service
	Services *ServiceLimitationDef `protobuf:"bytes,2,opt,name=services,proto3" json:"services,omitempty"`
}

func (x *ServiceCharge_Limitation) Reset() {
	*x = ServiceCharge_Limitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCharge_Limitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCharge_Limitation) ProtoMessage() {}

func (x *ServiceCharge_Limitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCharge_Limitation.ProtoReflect.Descriptor instead.
func (*ServiceCharge_Limitation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{25, 0}
}

func (x *ServiceCharge_Limitation) GetFoodSources() *FoodSourceLimitationDef {
	if x != nil {
		return x.FoodSources
	}
	return nil
}

func (x *ServiceCharge_Limitation) GetServices() *ServiceLimitationDef {
	if x != nil {
		return x.Services
	}
	return nil
}

// message service item types
type UpdateServiceChargeDef_ServiceItemTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item types
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,1,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
}

func (x *UpdateServiceChargeDef_ServiceItemTypes) Reset() {
	*x = UpdateServiceChargeDef_ServiceItemTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceChargeDef_ServiceItemTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceChargeDef_ServiceItemTypes) ProtoMessage() {}

func (x *UpdateServiceChargeDef_ServiceItemTypes) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceChargeDef_ServiceItemTypes.ProtoReflect.Descriptor instead.
func (*UpdateServiceChargeDef_ServiceItemTypes) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP(), []int{29, 0}
}

func (x *UpdateServiceChargeDef_ServiceItemTypes) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

var File_moego_models_enterprise_v1_price_book_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_price_book_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x2f, 0x0a, 0x09, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0xdf, 0x02, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0a, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x12, 0x52, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x91, 0x11, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x58, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74,
	0x61, 0x78, 0x52, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a,
	0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b,
	0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x0a, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x12, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x36, 0x0a,
	0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x49, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x75,
	0x6c, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x75,
	0x74, 0x6f, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x08, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x41,
	0x74, 0x88, 0x01, 0x01, 0x1a, 0xac, 0x02, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x75, 0x6c,
	0x65, 0x12, 0x58, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x6f, 0x76,
	0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x6f, 0x6c,
	0x6c, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x52,
	0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x5d, 0x0a, 0x13, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x11, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x67, 0x0a, 0x17, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x15, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x1a, 0xb4, 0x05, 0x0a, 0x0a, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x18, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x41, 0x6e, 0x64, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x51, 0x0a, 0x0f, 0x70, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x52, 0x0d,
	0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x22, 0x0a,
	0x0d, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x73, 0x12, 0x51, 0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x61, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x61, 0x6c,
	0x6c, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x4d, 0x0a, 0x09, 0x70, 0x65,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52,
	0x08, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x08, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x08, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x55, 0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x66, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x5a,
	0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x0c, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70,
	0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x4e, 0x0a, 0x11, 0x42, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x22, 0xc2, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x66, 0x12, 0x37, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x13, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x8e, 0x01,
	0x0a, 0x17, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x62,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x46, 0x6f, 0x72, 0x4f, 0x62, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0xa7,
	0x01, 0x0a, 0x1a, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x36, 0x0a,
	0x18, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x5f, 0x61,
	0x6e, 0x64, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x14, 0x61, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x41, 0x6e, 0x64, 0x42,
	0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x51, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x22, 0x71, 0x0a, 0x19, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x73, 0x22, 0xda, 0x01, 0x0a, 0x0d,
	0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x61, 0x6c, 0x6c, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x41, 0x0a, 0x0b,
	0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x09, 0x70,
	0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x08, 0x50, 0x65, 0x74,
	0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x41, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0xb7, 0x01, 0x0a, 0x07, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x0b,
	0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x22, 0x79, 0x0a, 0x0b, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x03, 0x6d, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12,
	0x34, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x52, 0x03, 0x6d, 0x61, 0x78, 0x22, 0x5c, 0x0a, 0x06, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x3a, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x04, 0x75,
	0x6e, 0x69, 0x74, 0x22, 0x8d, 0x03, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x29, 0x0a, 0x10, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x69, 0x6d, 0x70, 0x61,
	0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x0b, 0x72,
	0x6f, 0x6c, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x72, 0x6f,
	0x6c, 0x6c, 0x4f, 0x75, 0x74, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x11, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x73, 0x12, 0x4f, 0x0a, 0x0b, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a, 0x70, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0xc0, 0x02, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x06, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x57, 0x0a,
	0x11, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x52, 0x10, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0x87, 0x09, 0x0a, 0x0a, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x18,
	0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15,
	0x61, 0x6c, 0x69, 0x61, 0x73, 0x46, 0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x23, 0x0a, 0x0d,
	0x69, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x52, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x44, 0x61, 0x79,
	0x73, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x12, 0x51, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x70, 0x75, 0x73,
	0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x08, 0x70, 0x75, 0x73, 0x68,
	0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x1a, 0xc8, 0x01, 0x0a, 0x0a, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5e, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x5a, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x66, 0x52, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x22, 0xa1, 0x05, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x2b, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x28,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x37, 0x0a, 0x18, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x46, 0x6f, 0x72, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x11, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x44, 0x61, 0x79, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61,
	0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74,
	0x65, 0x12, 0x51, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xc7, 0x06, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x57, 0x0a, 0x12,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x18,
	0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04,
	0x52, 0x15, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x46, 0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x17, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x14, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x73,
	0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52,
	0x0c, 0x69, 0x73, 0x52, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x48, 0x07, 0x52,
	0x11, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x44, 0x61,
	0x79, 0x73, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x48, 0x08, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x51, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x1b,
	0x0a, 0x19, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x42, 0x1a, 0x0a, 0x18, 0x5f,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x69, 0x73, 0x5f, 0x72,
	0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x72, 0x65,
	0x73, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x79,
	0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x22, 0xcb,
	0x02, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff,
	0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0x80, 0x08, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x27, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0c, 0x70,
	0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x73, 0x12, 0x55, 0x0a,
	0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70,
	0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xf2, 0x03, 0x0a,
	0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x44, 0x65, 0x66, 0x12, 0x23, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48,
	0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x08, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x49, 0x0a, 0x0a, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x68, 0x6f,
	0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x02, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x4c,
	0x69, 0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x1a, 0x02, 0x20, 0x00, 0x48, 0x03, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x53,
	0x69, 0x7a, 0x65, 0x49, 0x64, 0x73, 0x12, 0x5a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48, 0x04, 0x52, 0x0f,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x0d, 0x70,
	0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x68, 0x6f,
	0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6d, 0x61, 0x78, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x22, 0x34, 0x0a, 0x09, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27,
	0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x00, 0x52, 0x09, 0x70, 0x68,
	0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xae, 0x04, 0x0a, 0x0b, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0b,
	0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0c,
	0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x73, 0x12, 0x55,
	0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72,
	0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3c, 0x0a, 0x09,
	0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x08, 0x70,
	0x75, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70,
	0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x84, 0x07, 0x0a, 0x0b, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a,
	0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x4f, 0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x69,
	0x6e, 0x5f, 0x73, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x6e, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x53, 0x61, 0x6d, 0x65,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x61, 0x0a, 0x12, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x72, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x3c, 0x0a, 0x09, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48,
	0x00, 0x52, 0x08, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x31,
	0x0a, 0x15, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69,
	0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x65, 0x72, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x12, 0x52, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x5a, 0x0a, 0x0a, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22,
	0xcf, 0x04, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x12, 0x2b, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x09, 0x72, 0x75, 0x6c,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x5b, 0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0d, 0x72,
	0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x14,
	0x6e, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x6e, 0x65, 0x65, 0x64,
	0x49, 0x6e, 0x53, 0x61, 0x6d, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x6b, 0x0a,
	0x12, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x11, 0x72, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x15, 0x69, 0x73,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x50, 0x65, 0x72, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x52, 0x0a,
	0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xce, 0x05, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x12, 0x42, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2c,
	0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52,
	0x08, 0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09,
	0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x01, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x60,
	0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x0d,
	0x72, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x34, 0x0a, 0x14, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x61, 0x6d, 0x65,
	0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03,
	0x52, 0x11, 0x6e, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x53, 0x61, 0x6d, 0x65, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x70, 0x0a, 0x12, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x48, 0x04, 0x52, 0x11, 0x72, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x12, 0x69, 0x73, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x50, 0x65, 0x72, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01,
	0x12, 0x57, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x06, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x72, 0x75,
	0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x6e, 0x65,
	0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x69, 0x73,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xe3, 0x0e, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f,
	0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x60,
	0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e,
	0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0f, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x69, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x4d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x6c, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a,
	0x0e, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x01, 0x52, 0x0d, 0x73,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x48, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0c, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x75, 0x0a, 0x17, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x48, 0x02, 0x52, 0x14, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x7b, 0x0a, 0x19, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74,
	0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70,
	0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x48, 0x03, 0x52, 0x16, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x73,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5d, 0x0a,
	0x13, 0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x5f, 0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x45, 0x78,
	0x63, 0x65, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x11, 0x68, 0x6f, 0x75, 0x72, 0x6c,
	0x79, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x54, 0x0a, 0x0a,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x70, 0x75, 0x73, 0x68,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x04, 0x52, 0x08, 0x70, 0x75, 0x73, 0x68, 0x65,
	0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x18, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x1a,
	0xb2, 0x01, 0x0a, 0x0a, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56,
	0x0a, 0x0c, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x0b, 0x66, 0x6f, 0x6f, 0x64, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x11, 0x0a,
	0x0f, 0x5f, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x1c, 0x0a, 0x1a,
	0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70,
	0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x83, 0x01, 0x0a, 0x11, 0x48, 0x6f, 0x75,
	0x72, 0x6c, 0x79, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x6e,
	0x0a, 0x13, 0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x5f, 0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x00, 0x10, 0x0a, 0x52, 0x11, 0x68, 0x6f, 0x75,
	0x72, 0x6c, 0x79, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x22, 0x70,
	0x0a, 0x17, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73, 0x5f,
	0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x46, 0x6f, 0x6f,
	0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x6f, 0x6f, 0x64,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0d, 0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x73,
	0x22, 0xd6, 0x0b, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x65, 0x66, 0x12, 0x2b, 0x0a, 0x0d, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18,
	0xff, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x08, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x60, 0x0a,
	0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41,
	0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f,
	0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x69, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x0f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x6c, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c,
	0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x01, 0x52, 0x11, 0x61, 0x75, 0x74,
	0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x50, 0x0a, 0x0e, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x02, 0x52, 0x0d, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52,
	0x0c, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x75, 0x0a,
	0x17, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x48, 0x03, 0x52, 0x14, 0x74, 0x69, 0x6d,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x7b, 0x0a, 0x19, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65,
	0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x48, 0x04, 0x52, 0x16, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65,
	0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x5d, 0x0a, 0x13, 0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x5f, 0x65, 0x78, 0x63, 0x65,
	0x65, 0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x75, 0x72,
	0x6c, 0x79, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x11, 0x68,
	0x6f, 0x75, 0x72, 0x6c, 0x79, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x54, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x13, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42,
	0x1a, 0x0a, 0x18, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x1c, 0x0a, 0x1a, 0x5f,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xa8, 0x0e, 0x0a, 0x16, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x44, 0x65, 0x66, 0x12, 0x23, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x00,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x08, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x48, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a,
	0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41,
	0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x04,
	0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x6e, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x05, 0x52, 0x12, 0x61,
	0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x48, 0x06, 0x52,
	0x0d, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x07, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x52, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x48, 0x08, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x78, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x09, 0x52, 0x11, 0x61, 0x75, 0x74,
	0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x5c, 0x0a, 0x0e, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0a, 0x52, 0x0d, 0x73,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x59, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0b, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x81, 0x01, 0x0a, 0x17, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x48, 0x0c, 0x52, 0x14, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x87,
	0x01, 0x0a, 0x19, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x73,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c,
	0x65, 0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0d, 0x52, 0x16, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5d, 0x0a, 0x13, 0x68, 0x6f, 0x75, 0x72,
	0x6c, 0x79, 0x5f, 0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x48, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x52, 0x11, 0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x45, 0x78, 0x63, 0x65,
	0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x54, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x71, 0x0a,
	0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x1a, 0x6b, 0x0a, 0x10, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x42, 0x07, 0x0a,
	0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x73, 0x6f, 0x72, 0x74, 0x42, 0x14, 0x0a, 0x12,
	0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c,
	0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x17, 0x0a, 0x15,
	0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x70, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_price_book_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_price_book_models_proto_rawDescData = file_moego_models_enterprise_v1_price_book_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_price_book_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_price_book_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_price_book_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_price_book_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_price_book_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_price_book_models_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_moego_models_enterprise_v1_price_book_models_proto_goTypes = []interface{}{
	(*PriceBook)(nil),                               // 0: moego.models.enterprise.v1.PriceBook
	(*ServiceCategory)(nil),                         // 1: moego.models.enterprise.v1.ServiceCategory
	(*Service)(nil),                                 // 2: moego.models.enterprise.v1.Service
	(*BundleServiceRule)(nil),                       // 3: moego.models.enterprise.v1.BundleServiceRule
	(*ServiceLimitationDef)(nil),                    // 4: moego.models.enterprise.v1.ServiceLimitationDef
	(*EvaluationLimitationDef)(nil),                 // 5: moego.models.enterprise.v1.EvaluationLimitationDef
	(*PetTypeBreedsLimitationDef)(nil),              // 6: moego.models.enterprise.v1.PetTypeBreedsLimitationDef
	(*LodgingTypesLimitationDef)(nil),               // 7: moego.models.enterprise.v1.LodgingTypesLimitationDef
	(*PetTypeBreeds)(nil),                           // 8: moego.models.enterprise.v1.PetTypeBreeds
	(*PetBreed)(nil),                                // 9: moego.models.enterprise.v1.PetBreed
	(*PetType)(nil),                                 // 10: moego.models.enterprise.v1.PetType
	(*WeightRange)(nil),                             // 11: moego.models.enterprise.v1.WeightRange
	(*Weight)(nil),                                  // 12: moego.models.enterprise.v1.Weight
	(*ServiceChangeHistory)(nil),                    // 13: moego.models.enterprise.v1.ServiceChangeHistory
	(*ServiceChange)(nil),                           // 14: moego.models.enterprise.v1.ServiceChange
	(*Evaluation)(nil),                              // 15: moego.models.enterprise.v1.Evaluation
	(*CreateEvaluationDef)(nil),                     // 16: moego.models.enterprise.v1.CreateEvaluationDef
	(*UpdateEvaluationDef)(nil),                     // 17: moego.models.enterprise.v1.UpdateEvaluationDef
	(*CreateLodgingTypeDef)(nil),                    // 18: moego.models.enterprise.v1.CreateLodgingTypeDef
	(*UpdateLodgingTypeDef)(nil),                    // 19: moego.models.enterprise.v1.UpdateLodgingTypeDef
	(*PhotoList)(nil),                               // 20: moego.models.enterprise.v1.PhotoList
	(*LodgingType)(nil),                             // 21: moego.models.enterprise.v1.LodgingType
	(*PricingRule)(nil),                             // 22: moego.models.enterprise.v1.PricingRule
	(*CreatePricingRuleDef)(nil),                    // 23: moego.models.enterprise.v1.CreatePricingRuleDef
	(*UpdatePricingRuleDef)(nil),                    // 24: moego.models.enterprise.v1.UpdatePricingRuleDef
	(*ServiceCharge)(nil),                           // 25: moego.models.enterprise.v1.ServiceCharge
	(*HourlyExceedRules)(nil),                       // 26: moego.models.enterprise.v1.HourlyExceedRules
	(*FoodSourceLimitationDef)(nil),                 // 27: moego.models.enterprise.v1.FoodSourceLimitationDef
	(*CreateServiceChargeDef)(nil),                  // 28: moego.models.enterprise.v1.CreateServiceChargeDef
	(*UpdateServiceChargeDef)(nil),                  // 29: moego.models.enterprise.v1.UpdateServiceChargeDef
	(*Service_AutoRule)(nil),                        // 30: moego.models.enterprise.v1.Service.AutoRule
	(*Service_Limitation)(nil),                      // 31: moego.models.enterprise.v1.Service.Limitation
	(*Evaluation_Limitation)(nil),                   // 32: moego.models.enterprise.v1.Evaluation.Limitation
	(*PricingRule_Limitation)(nil),                  // 33: moego.models.enterprise.v1.PricingRule.Limitation
	(*ServiceCharge_Limitation)(nil),                // 34: moego.models.enterprise.v1.ServiceCharge.Limitation
	(*UpdateServiceChargeDef_ServiceItemTypes)(nil), // 35: moego.models.enterprise.v1.UpdateServiceChargeDef.ServiceItemTypes
	(v1.ServiceItemType)(0),                         // 36: moego.models.offering.v1.ServiceItemType
	(v1.ServiceType)(0),                             // 37: moego.models.offering.v1.ServiceType
	(*money.Money)(nil),                             // 38: google.type.Money
	(v1.ServicePriceUnit)(0),                        // 39: moego.models.offering.v1.ServicePriceUnit
	(*durationpb.Duration)(nil),                     // 40: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),                   // 41: google.protobuf.Timestamp
	(*v1.ServiceFilter)(nil),                        // 42: moego.models.offering.v1.ServiceFilter
	(v11.PetType)(0),                                // 43: moego.models.customer.v1.PetType
	(WeightUnit)(0),                                 // 44: moego.models.enterprise.v1.WeightUnit
	(*TemplatePushResult)(nil),                      // 45: moego.models.enterprise.v1.TemplatePushResult
	(*TenantObject)(nil),                            // 46: moego.models.enterprise.v1.TenantObject
	(*DetailCategory)(nil),                          // 47: moego.models.enterprise.v1.DetailCategory
	(v1.LodgingUnitType)(0),                         // 48: moego.models.offering.v1.LodgingUnitType
	(v2.RuleType)(0),                                // 49: moego.models.offering.v2.RuleType
	(v2.RuleApplyType)(0),                           // 50: moego.models.offering.v2.RuleApplyType
	(*v2.PricingRuleConfiguration)(nil),             // 51: moego.models.offering.v2.PricingRuleConfiguration
	(v12.ServiceCharge_AutoApplyStatus)(0),          // 52: moego.models.order.v1.ServiceCharge.AutoApplyStatus
	(v12.ServiceCharge_AutoApplyCondition)(0),       // 53: moego.models.order.v1.ServiceCharge.AutoApplyCondition
	(v12.ServiceCharge_ApplyType)(0),                // 54: moego.models.order.v1.ServiceCharge.ApplyType
	(v12.ServiceCharge_AutoApplyTimeType)(0),        // 55: moego.models.order.v1.ServiceCharge.AutoApplyTimeType
	(v12.SurchargeType)(0),                          // 56: moego.models.order.v1.SurchargeType
	(v12.ChargeMethod)(0),                           // 57: moego.models.order.v1.ChargeMethod
	(v12.ServiceCharge_TimeBasedPricingType)(0),     // 58: moego.models.order.v1.ServiceCharge.TimeBasedPricingType
	(v12.ServiceCharge_MultiplePetsChargeType)(0),   // 59: moego.models.order.v1.ServiceCharge.MultiplePetsChargeType
	(*v12.ServiceChargeExceedHourRule)(nil),         // 60: moego.models.order.v1.ServiceChargeExceedHourRule
	(*v1.AutoRolloverRule)(nil),                     // 61: moego.models.offering.v1.AutoRolloverRule
	(*v1.AdditionalServiceRule)(nil),                // 62: moego.models.offering.v1.AdditionalServiceRule
	(*PetCodeLimitationDef)(nil),                    // 63: moego.models.enterprise.v1.PetCodeLimitationDef
}
var file_moego_models_enterprise_v1_price_book_models_proto_depIdxs = []int32{
	0,   // 0: moego.models.enterprise.v1.ServiceCategory.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	36,  // 1: moego.models.enterprise.v1.ServiceCategory.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	37,  // 2: moego.models.enterprise.v1.ServiceCategory.service_type:type_name -> moego.models.offering.v1.ServiceType
	0,   // 3: moego.models.enterprise.v1.Service.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	36,  // 4: moego.models.enterprise.v1.Service.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	1,   // 5: moego.models.enterprise.v1.Service.category:type_name -> moego.models.enterprise.v1.ServiceCategory
	38,  // 6: moego.models.enterprise.v1.Service.price:type_name -> google.type.Money
	39,  // 7: moego.models.enterprise.v1.Service.service_price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	40,  // 8: moego.models.enterprise.v1.Service.duration:type_name -> google.protobuf.Duration
	40,  // 9: moego.models.enterprise.v1.Service.max_duration:type_name -> google.protobuf.Duration
	31,  // 10: moego.models.enterprise.v1.Service.limitation:type_name -> moego.models.enterprise.v1.Service.Limitation
	37,  // 11: moego.models.enterprise.v1.Service.service_type:type_name -> moego.models.offering.v1.ServiceType
	30,  // 12: moego.models.enterprise.v1.Service.auto_rule:type_name -> moego.models.enterprise.v1.Service.AutoRule
	41,  // 13: moego.models.enterprise.v1.Service.created_at:type_name -> google.protobuf.Timestamp
	41,  // 14: moego.models.enterprise.v1.Service.updated_at:type_name -> google.protobuf.Timestamp
	41,  // 15: moego.models.enterprise.v1.Service.pushed_at:type_name -> google.protobuf.Timestamp
	42,  // 16: moego.models.enterprise.v1.ServiceLimitationDef.service_filter_list:type_name -> moego.models.offering.v1.ServiceFilter
	8,   // 17: moego.models.enterprise.v1.PetTypeBreedsLimitationDef.pet_type_breeds:type_name -> moego.models.enterprise.v1.PetTypeBreeds
	43,  // 18: moego.models.enterprise.v1.PetTypeBreeds.pet_type_id:type_name -> moego.models.customer.v1.PetType
	9,   // 19: moego.models.enterprise.v1.PetTypeBreeds.pet_breeds:type_name -> moego.models.enterprise.v1.PetBreed
	43,  // 20: moego.models.enterprise.v1.PetBreed.pet_type_id:type_name -> moego.models.customer.v1.PetType
	43,  // 21: moego.models.enterprise.v1.PetType.pet_type_id:type_name -> moego.models.customer.v1.PetType
	12,  // 22: moego.models.enterprise.v1.WeightRange.min:type_name -> moego.models.enterprise.v1.Weight
	12,  // 23: moego.models.enterprise.v1.WeightRange.max:type_name -> moego.models.enterprise.v1.Weight
	44,  // 24: moego.models.enterprise.v1.Weight.unit:type_name -> moego.models.enterprise.v1.WeightUnit
	41,  // 25: moego.models.enterprise.v1.ServiceChangeHistory.roll_out_at:type_name -> google.protobuf.Timestamp
	41,  // 26: moego.models.enterprise.v1.ServiceChangeHistory.updated_at:type_name -> google.protobuf.Timestamp
	45,  // 27: moego.models.enterprise.v1.ServiceChangeHistory.push_result:type_name -> moego.models.enterprise.v1.TemplatePushResult
	46,  // 28: moego.models.enterprise.v1.ServiceChange.target:type_name -> moego.models.enterprise.v1.TenantObject
	47,  // 29: moego.models.enterprise.v1.ServiceChange.detail_categories:type_name -> moego.models.enterprise.v1.DetailCategory
	0,   // 30: moego.models.enterprise.v1.Evaluation.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	36,  // 31: moego.models.enterprise.v1.Evaluation.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	38,  // 32: moego.models.enterprise.v1.Evaluation.price:type_name -> google.type.Money
	40,  // 33: moego.models.enterprise.v1.Evaluation.duration:type_name -> google.protobuf.Duration
	32,  // 34: moego.models.enterprise.v1.Evaluation.limitation:type_name -> moego.models.enterprise.v1.Evaluation.Limitation
	41,  // 35: moego.models.enterprise.v1.Evaluation.created_at:type_name -> google.protobuf.Timestamp
	41,  // 36: moego.models.enterprise.v1.Evaluation.updated_at:type_name -> google.protobuf.Timestamp
	41,  // 37: moego.models.enterprise.v1.Evaluation.pushed_at:type_name -> google.protobuf.Timestamp
	36,  // 38: moego.models.enterprise.v1.CreateEvaluationDef.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	38,  // 39: moego.models.enterprise.v1.CreateEvaluationDef.price:type_name -> google.type.Money
	40,  // 40: moego.models.enterprise.v1.CreateEvaluationDef.duration:type_name -> google.protobuf.Duration
	32,  // 41: moego.models.enterprise.v1.CreateEvaluationDef.limitation:type_name -> moego.models.enterprise.v1.Evaluation.Limitation
	36,  // 42: moego.models.enterprise.v1.UpdateEvaluationDef.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	38,  // 43: moego.models.enterprise.v1.UpdateEvaluationDef.price:type_name -> google.type.Money
	40,  // 44: moego.models.enterprise.v1.UpdateEvaluationDef.duration:type_name -> google.protobuf.Duration
	32,  // 45: moego.models.enterprise.v1.UpdateEvaluationDef.limitation:type_name -> moego.models.enterprise.v1.Evaluation.Limitation
	48,  // 46: moego.models.enterprise.v1.CreateLodgingTypeDef.lodging_unit_type:type_name -> moego.models.offering.v1.LodgingUnitType
	20,  // 47: moego.models.enterprise.v1.UpdateLodgingTypeDef.photo_list:type_name -> moego.models.enterprise.v1.PhotoList
	48,  // 48: moego.models.enterprise.v1.UpdateLodgingTypeDef.lodging_unit_type:type_name -> moego.models.offering.v1.LodgingUnitType
	48,  // 49: moego.models.enterprise.v1.LodgingType.lodging_unit_type:type_name -> moego.models.offering.v1.LodgingUnitType
	41,  // 50: moego.models.enterprise.v1.LodgingType.created_at:type_name -> google.protobuf.Timestamp
	41,  // 51: moego.models.enterprise.v1.LodgingType.updated_at:type_name -> google.protobuf.Timestamp
	41,  // 52: moego.models.enterprise.v1.LodgingType.pushed_at:type_name -> google.protobuf.Timestamp
	0,   // 53: moego.models.enterprise.v1.PricingRule.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	49,  // 54: moego.models.enterprise.v1.PricingRule.type:type_name -> moego.models.offering.v2.RuleType
	50,  // 55: moego.models.enterprise.v1.PricingRule.rule_apply_type:type_name -> moego.models.offering.v2.RuleApplyType
	51,  // 56: moego.models.enterprise.v1.PricingRule.rule_configuration:type_name -> moego.models.offering.v2.PricingRuleConfiguration
	41,  // 57: moego.models.enterprise.v1.PricingRule.created_at:type_name -> google.protobuf.Timestamp
	41,  // 58: moego.models.enterprise.v1.PricingRule.updated_at:type_name -> google.protobuf.Timestamp
	41,  // 59: moego.models.enterprise.v1.PricingRule.pushed_at:type_name -> google.protobuf.Timestamp
	33,  // 60: moego.models.enterprise.v1.PricingRule.limitation:type_name -> moego.models.enterprise.v1.PricingRule.Limitation
	49,  // 61: moego.models.enterprise.v1.CreatePricingRuleDef.type:type_name -> moego.models.offering.v2.RuleType
	50,  // 62: moego.models.enterprise.v1.CreatePricingRuleDef.rule_apply_type:type_name -> moego.models.offering.v2.RuleApplyType
	51,  // 63: moego.models.enterprise.v1.CreatePricingRuleDef.rule_configuration:type_name -> moego.models.offering.v2.PricingRuleConfiguration
	33,  // 64: moego.models.enterprise.v1.CreatePricingRuleDef.limitation:type_name -> moego.models.enterprise.v1.PricingRule.Limitation
	49,  // 65: moego.models.enterprise.v1.UpdatePricingRuleDef.type:type_name -> moego.models.offering.v2.RuleType
	50,  // 66: moego.models.enterprise.v1.UpdatePricingRuleDef.rule_apply_type:type_name -> moego.models.offering.v2.RuleApplyType
	51,  // 67: moego.models.enterprise.v1.UpdatePricingRuleDef.rule_configuration:type_name -> moego.models.offering.v2.PricingRuleConfiguration
	33,  // 68: moego.models.enterprise.v1.UpdatePricingRuleDef.limitation:type_name -> moego.models.enterprise.v1.PricingRule.Limitation
	0,   // 69: moego.models.enterprise.v1.ServiceCharge.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	38,  // 70: moego.models.enterprise.v1.ServiceCharge.price:type_name -> google.type.Money
	52,  // 71: moego.models.enterprise.v1.ServiceCharge.auto_apply_status:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyStatus
	53,  // 72: moego.models.enterprise.v1.ServiceCharge.auto_apply_condition:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyCondition
	54,  // 73: moego.models.enterprise.v1.ServiceCharge.apply_type:type_name -> moego.models.order.v1.ServiceCharge.ApplyType
	55,  // 74: moego.models.enterprise.v1.ServiceCharge.auto_apply_time_type:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyTimeType
	56,  // 75: moego.models.enterprise.v1.ServiceCharge.surcharge_type:type_name -> moego.models.order.v1.SurchargeType
	57,  // 76: moego.models.enterprise.v1.ServiceCharge.charge_method:type_name -> moego.models.order.v1.ChargeMethod
	58,  // 77: moego.models.enterprise.v1.ServiceCharge.time_based_pricing_type:type_name -> moego.models.order.v1.ServiceCharge.TimeBasedPricingType
	59,  // 78: moego.models.enterprise.v1.ServiceCharge.multiple_pets_charge_type:type_name -> moego.models.order.v1.ServiceCharge.MultiplePetsChargeType
	26,  // 79: moego.models.enterprise.v1.ServiceCharge.hourly_exceed_rules:type_name -> moego.models.enterprise.v1.HourlyExceedRules
	34,  // 80: moego.models.enterprise.v1.ServiceCharge.limitation:type_name -> moego.models.enterprise.v1.ServiceCharge.Limitation
	41,  // 81: moego.models.enterprise.v1.ServiceCharge.created_at:type_name -> google.protobuf.Timestamp
	41,  // 82: moego.models.enterprise.v1.ServiceCharge.updated_at:type_name -> google.protobuf.Timestamp
	41,  // 83: moego.models.enterprise.v1.ServiceCharge.pushed_at:type_name -> google.protobuf.Timestamp
	36,  // 84: moego.models.enterprise.v1.ServiceCharge.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	60,  // 85: moego.models.enterprise.v1.HourlyExceedRules.hourly_exceed_rules:type_name -> moego.models.order.v1.ServiceChargeExceedHourRule
	38,  // 86: moego.models.enterprise.v1.CreateServiceChargeDef.price:type_name -> google.type.Money
	52,  // 87: moego.models.enterprise.v1.CreateServiceChargeDef.auto_apply_status:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyStatus
	53,  // 88: moego.models.enterprise.v1.CreateServiceChargeDef.auto_apply_condition:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyCondition
	54,  // 89: moego.models.enterprise.v1.CreateServiceChargeDef.apply_type:type_name -> moego.models.order.v1.ServiceCharge.ApplyType
	55,  // 90: moego.models.enterprise.v1.CreateServiceChargeDef.auto_apply_time_type:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyTimeType
	56,  // 91: moego.models.enterprise.v1.CreateServiceChargeDef.surcharge_type:type_name -> moego.models.order.v1.SurchargeType
	57,  // 92: moego.models.enterprise.v1.CreateServiceChargeDef.charge_method:type_name -> moego.models.order.v1.ChargeMethod
	58,  // 93: moego.models.enterprise.v1.CreateServiceChargeDef.time_based_pricing_type:type_name -> moego.models.order.v1.ServiceCharge.TimeBasedPricingType
	59,  // 94: moego.models.enterprise.v1.CreateServiceChargeDef.multiple_pets_charge_type:type_name -> moego.models.order.v1.ServiceCharge.MultiplePetsChargeType
	26,  // 95: moego.models.enterprise.v1.CreateServiceChargeDef.hourly_exceed_rules:type_name -> moego.models.enterprise.v1.HourlyExceedRules
	34,  // 96: moego.models.enterprise.v1.CreateServiceChargeDef.limitation:type_name -> moego.models.enterprise.v1.ServiceCharge.Limitation
	36,  // 97: moego.models.enterprise.v1.CreateServiceChargeDef.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	38,  // 98: moego.models.enterprise.v1.UpdateServiceChargeDef.price:type_name -> google.type.Money
	52,  // 99: moego.models.enterprise.v1.UpdateServiceChargeDef.auto_apply_status:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyStatus
	53,  // 100: moego.models.enterprise.v1.UpdateServiceChargeDef.auto_apply_condition:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyCondition
	54,  // 101: moego.models.enterprise.v1.UpdateServiceChargeDef.apply_type:type_name -> moego.models.order.v1.ServiceCharge.ApplyType
	55,  // 102: moego.models.enterprise.v1.UpdateServiceChargeDef.auto_apply_time_type:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyTimeType
	56,  // 103: moego.models.enterprise.v1.UpdateServiceChargeDef.surcharge_type:type_name -> moego.models.order.v1.SurchargeType
	57,  // 104: moego.models.enterprise.v1.UpdateServiceChargeDef.charge_method:type_name -> moego.models.order.v1.ChargeMethod
	58,  // 105: moego.models.enterprise.v1.UpdateServiceChargeDef.time_based_pricing_type:type_name -> moego.models.order.v1.ServiceCharge.TimeBasedPricingType
	59,  // 106: moego.models.enterprise.v1.UpdateServiceChargeDef.multiple_pets_charge_type:type_name -> moego.models.order.v1.ServiceCharge.MultiplePetsChargeType
	26,  // 107: moego.models.enterprise.v1.UpdateServiceChargeDef.hourly_exceed_rules:type_name -> moego.models.enterprise.v1.HourlyExceedRules
	34,  // 108: moego.models.enterprise.v1.UpdateServiceChargeDef.limitation:type_name -> moego.models.enterprise.v1.ServiceCharge.Limitation
	35,  // 109: moego.models.enterprise.v1.UpdateServiceChargeDef.service_item_types:type_name -> moego.models.enterprise.v1.UpdateServiceChargeDef.ServiceItemTypes
	61,  // 110: moego.models.enterprise.v1.Service.AutoRule.auto_rollover_rule:type_name -> moego.models.offering.v1.AutoRolloverRule
	3,   // 111: moego.models.enterprise.v1.Service.AutoRule.bundle_service_rule:type_name -> moego.models.enterprise.v1.BundleServiceRule
	62,  // 112: moego.models.enterprise.v1.Service.AutoRule.additional_service_rule:type_name -> moego.models.offering.v1.AdditionalServiceRule
	8,   // 113: moego.models.enterprise.v1.Service.Limitation.pet_type_breeds:type_name -> moego.models.enterprise.v1.PetTypeBreeds
	11,  // 114: moego.models.enterprise.v1.Service.Limitation.pet_weight_range:type_name -> moego.models.enterprise.v1.WeightRange
	63,  // 115: moego.models.enterprise.v1.Service.Limitation.pet_codes:type_name -> moego.models.enterprise.v1.PetCodeLimitationDef
	4,   // 116: moego.models.enterprise.v1.Service.Limitation.services:type_name -> moego.models.enterprise.v1.ServiceLimitationDef
	5,   // 117: moego.models.enterprise.v1.Service.Limitation.evaluations:type_name -> moego.models.enterprise.v1.EvaluationLimitationDef
	7,   // 118: moego.models.enterprise.v1.Service.Limitation.lodging_types:type_name -> moego.models.enterprise.v1.LodgingTypesLimitationDef
	6,   // 119: moego.models.enterprise.v1.Evaluation.Limitation.pet_type_breeds:type_name -> moego.models.enterprise.v1.PetTypeBreedsLimitationDef
	7,   // 120: moego.models.enterprise.v1.Evaluation.Limitation.lodging_types:type_name -> moego.models.enterprise.v1.LodgingTypesLimitationDef
	4,   // 121: moego.models.enterprise.v1.PricingRule.Limitation.services:type_name -> moego.models.enterprise.v1.ServiceLimitationDef
	27,  // 122: moego.models.enterprise.v1.ServiceCharge.Limitation.food_sources:type_name -> moego.models.enterprise.v1.FoodSourceLimitationDef
	4,   // 123: moego.models.enterprise.v1.ServiceCharge.Limitation.services:type_name -> moego.models.enterprise.v1.ServiceLimitationDef
	36,  // 124: moego.models.enterprise.v1.UpdateServiceChargeDef.ServiceItemTypes.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	125, // [125:125] is the sub-list for method output_type
	125, // [125:125] is the sub-list for method input_type
	125, // [125:125] is the sub-list for extension type_name
	125, // [125:125] is the sub-list for extension extendee
	0,   // [0:125] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_price_book_models_proto_init() }
func file_moego_models_enterprise_v1_price_book_models_proto_init() {
	if File_moego_models_enterprise_v1_price_book_models_proto != nil {
		return
	}
	file_moego_models_enterprise_v1_enterprise_enums_proto_init()
	file_moego_models_enterprise_v1_pet_settings_models_proto_init()
	file_moego_models_enterprise_v1_template_push_proto_init()
	file_moego_models_enterprise_v1_tenant_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceBook); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BundleServiceRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceLimitationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationLimitationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetTypeBreedsLimitationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingTypesLimitationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetTypeBreeds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetBreed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeightRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Weight); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceChangeHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Evaluation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLodgingTypeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLodgingTypeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhotoList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricingRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePricingRuleDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePricingRuleDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCharge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HourlyExceedRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FoodSourceLimitationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceChargeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceChargeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service_AutoRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service_Limitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Evaluation_Limitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricingRule_Limitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCharge_Limitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceChargeDef_ServiceItemTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[19].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[21].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[22].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[24].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[25].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[28].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_price_book_models_proto_msgTypes[29].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_price_book_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_price_book_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_price_book_models_proto_depIdxs,
		MessageInfos:      file_moego_models_enterprise_v1_price_book_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_price_book_models_proto = out.File
	file_moego_models_enterprise_v1_price_book_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_price_book_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_price_book_models_proto_depIdxs = nil
}
