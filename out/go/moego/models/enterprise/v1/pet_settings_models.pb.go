// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/pet_settings_models.proto

package enterprisepb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// limit type
type PetCodeLimitationDef_LimitType int32

const (
	// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
	// all pet codes
	PetCodeLimitationDef_ALL_PET_CODES PetCodeLimitationDef_LimitType = 0
	// required pet codes
	PetCodeLimitationDef_REQUIRED_PET_CODES PetCodeLimitationDef_LimitType = 1
	// excluded pet codes
	PetCodeLimitationDef_EXCLUDED_PET_CODES PetCodeLimitationDef_LimitType = 2
)

// Enum value maps for PetCodeLimitationDef_LimitType.
var (
	PetCodeLimitationDef_LimitType_name = map[int32]string{
		0: "ALL_PET_CODES",
		1: "REQUIRED_PET_CODES",
		2: "EXCLUDED_PET_CODES",
	}
	PetCodeLimitationDef_LimitType_value = map[string]int32{
		"ALL_PET_CODES":      0,
		"REQUIRED_PET_CODES": 1,
		"EXCLUDED_PET_CODES": 2,
	}
)

func (x PetCodeLimitationDef_LimitType) Enum() *PetCodeLimitationDef_LimitType {
	p := new(PetCodeLimitationDef_LimitType)
	*p = x
	return p
}

func (x PetCodeLimitationDef_LimitType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PetCodeLimitationDef_LimitType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_pet_settings_models_proto_enumTypes[0].Descriptor()
}

func (PetCodeLimitationDef_LimitType) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_pet_settings_models_proto_enumTypes[0]
}

func (x PetCodeLimitationDef_LimitType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PetCodeLimitationDef_LimitType.Descriptor instead.
func (PetCodeLimitationDef_LimitType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescGZIP(), []int{3, 0}
}

// pet code
type PetCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet code abbreviation
	Abbreviation string `protobuf:"bytes,2,opt,name=abbreviation,proto3" json:"abbreviation,omitempty"`
	// pet code description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// pet code color
	Color string `protobuf:"bytes,4,opt,name=color,proto3" json:"color,omitempty"`
	// pet code sort. The larger the sort number, the higher the priority.
	Sort int32 `protobuf:"varint,5,opt,name=sort,proto3" json:"sort,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// pushed at
	PushedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=pushed_at,json=pushedAt,proto3,oneof" json:"pushed_at,omitempty"`
}

func (x *PetCode) Reset() {
	*x = PetCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCode) ProtoMessage() {}

func (x *PetCode) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCode.ProtoReflect.Descriptor instead.
func (*PetCode) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescGZIP(), []int{0}
}

func (x *PetCode) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetCode) GetAbbreviation() string {
	if x != nil {
		return x.Abbreviation
	}
	return ""
}

func (x *PetCode) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PetCode) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *PetCode) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *PetCode) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *PetCode) GetPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PushedAt
	}
	return nil
}

// pet code update def
type PetCodeUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code abbreviation
	Abbreviation *string `protobuf:"bytes,1,opt,name=abbreviation,proto3,oneof" json:"abbreviation,omitempty"`
	// pet code description
	Description *string `protobuf:"bytes,2,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// pet code color
	Color *string `protobuf:"bytes,3,opt,name=color,proto3,oneof" json:"color,omitempty"`
}

func (x *PetCodeUpdateDef) Reset() {
	*x = PetCodeUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetCodeUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCodeUpdateDef) ProtoMessage() {}

func (x *PetCodeUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCodeUpdateDef.ProtoReflect.Descriptor instead.
func (*PetCodeUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescGZIP(), []int{1}
}

func (x *PetCodeUpdateDef) GetAbbreviation() string {
	if x != nil && x.Abbreviation != nil {
		return *x.Abbreviation
	}
	return ""
}

func (x *PetCodeUpdateDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *PetCodeUpdateDef) GetColor() string {
	if x != nil && x.Color != nil {
		return *x.Color
	}
	return ""
}

// pet code create def
type PetCodeCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code abbreviation
	Abbreviation string `protobuf:"bytes,1,opt,name=abbreviation,proto3" json:"abbreviation,omitempty"`
	// pet code description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// pet code color
	Color string `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *PetCodeCreateDef) Reset() {
	*x = PetCodeCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetCodeCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCodeCreateDef) ProtoMessage() {}

func (x *PetCodeCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCodeCreateDef.ProtoReflect.Descriptor instead.
func (*PetCodeCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescGZIP(), []int{2}
}

func (x *PetCodeCreateDef) GetAbbreviation() string {
	if x != nil {
		return x.Abbreviation
	}
	return ""
}

func (x *PetCodeCreateDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PetCodeCreateDef) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// pet codes
type PetCodeLimitationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// limit type
	LimitType PetCodeLimitationDef_LimitType `protobuf:"varint,1,opt,name=limit_type,json=limitType,proto3,enum=moego.models.enterprise.v1.PetCodeLimitationDef_LimitType" json:"limit_type,omitempty"`
	// pet codes
	PetCodeIds []int64 `protobuf:"varint,2,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
}

func (x *PetCodeLimitationDef) Reset() {
	*x = PetCodeLimitationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetCodeLimitationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCodeLimitationDef) ProtoMessage() {}

func (x *PetCodeLimitationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCodeLimitationDef.ProtoReflect.Descriptor instead.
func (*PetCodeLimitationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescGZIP(), []int{3}
}

func (x *PetCodeLimitationDef) GetLimitType() PetCodeLimitationDef_LimitType {
	if x != nil {
		return x.LimitType
	}
	return PetCodeLimitationDef_ALL_PET_CODES
}

func (x *PetCodeLimitationDef) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

// pet metadata
type PetMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// metadata name
	MetadataName v1.BusinessPetMetadataName `protobuf:"varint,3,opt,name=metadata_name,json=metadataName,proto3,enum=moego.models.business_customer.v1.BusinessPetMetadataName" json:"metadata_name,omitempty"`
	// metadata value
	MetadataValue string `protobuf:"bytes,4,opt,name=metadata_value,json=metadataValue,proto3" json:"metadata_value,omitempty"`
	// extra json data, can customize additional metadata information.
	ExtraJson map[string]string `protobuf:"bytes,5,rep,name=extra_json,json=extraJson,proto3" json:"extra_json,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// sort number
	Sort int32 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// pushed at
	PushedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=pushed_at,json=pushedAt,proto3,oneof" json:"pushed_at,omitempty"`
}

func (x *PetMetadata) Reset() {
	*x = PetMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetMetadata) ProtoMessage() {}

func (x *PetMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetMetadata.ProtoReflect.Descriptor instead.
func (*PetMetadata) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescGZIP(), []int{4}
}

func (x *PetMetadata) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetMetadata) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *PetMetadata) GetMetadataName() v1.BusinessPetMetadataName {
	if x != nil {
		return x.MetadataName
	}
	return v1.BusinessPetMetadataName(0)
}

func (x *PetMetadata) GetMetadataValue() string {
	if x != nil {
		return x.MetadataValue
	}
	return ""
}

func (x *PetMetadata) GetExtraJson() map[string]string {
	if x != nil {
		return x.ExtraJson
	}
	return nil
}

func (x *PetMetadata) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *PetMetadata) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PetMetadata) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *PetMetadata) GetPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PushedAt
	}
	return nil
}

// pet metadata update def
type PetMetadataUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// metadata value
	MetadataValue *string `protobuf:"bytes,1,opt,name=metadata_value,json=metadataValue,proto3,oneof" json:"metadata_value,omitempty"`
	// use to check whether extra_json should be updated
	UpdateExtraJson bool `protobuf:"varint,2,opt,name=update_extra_json,json=updateExtraJson,proto3" json:"update_extra_json,omitempty"`
	// extra json data, can customize additional metadata information.
	ExtraJson map[string]string `protobuf:"bytes,3,rep,name=extra_json,json=extraJson,proto3" json:"extra_json,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *PetMetadataUpdateDef) Reset() {
	*x = PetMetadataUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetMetadataUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetMetadataUpdateDef) ProtoMessage() {}

func (x *PetMetadataUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetMetadataUpdateDef.ProtoReflect.Descriptor instead.
func (*PetMetadataUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescGZIP(), []int{5}
}

func (x *PetMetadataUpdateDef) GetMetadataValue() string {
	if x != nil && x.MetadataValue != nil {
		return *x.MetadataValue
	}
	return ""
}

func (x *PetMetadataUpdateDef) GetUpdateExtraJson() bool {
	if x != nil {
		return x.UpdateExtraJson
	}
	return false
}

func (x *PetMetadataUpdateDef) GetExtraJson() map[string]string {
	if x != nil {
		return x.ExtraJson
	}
	return nil
}

// pet metadata create def
type PetMetadataCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// metadata name
	MetadataName v1.BusinessPetMetadataName `protobuf:"varint,1,opt,name=metadata_name,json=metadataName,proto3,enum=moego.models.business_customer.v1.BusinessPetMetadataName" json:"metadata_name,omitempty"`
	// metadata value
	MetadataValue string `protobuf:"bytes,2,opt,name=metadata_value,json=metadataValue,proto3" json:"metadata_value,omitempty"`
	// extra json data, can customize additional metadata information.
	ExtraJson map[string]string `protobuf:"bytes,3,rep,name=extra_json,json=extraJson,proto3" json:"extra_json,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *PetMetadataCreateDef) Reset() {
	*x = PetMetadataCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetMetadataCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetMetadataCreateDef) ProtoMessage() {}

func (x *PetMetadataCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetMetadataCreateDef.ProtoReflect.Descriptor instead.
func (*PetMetadataCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescGZIP(), []int{6}
}

func (x *PetMetadataCreateDef) GetMetadataName() v1.BusinessPetMetadataName {
	if x != nil {
		return x.MetadataName
	}
	return v1.BusinessPetMetadataName(0)
}

func (x *PetMetadataCreateDef) GetMetadataValue() string {
	if x != nil {
		return x.MetadataValue
	}
	return ""
}

func (x *PetMetadataCreateDef) GetExtraJson() map[string]string {
	if x != nil {
		return x.ExtraJson
	}
	return nil
}

var File_moego_models_enterprise_v1_pet_settings_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_pet_settings_models_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90, 0x02, 0x0a, 0x07, 0x50, 0x65, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x62, 0x62, 0x72,
	0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x3c, 0x0a, 0x09, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00,
	0x52, 0x08, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a,
	0x0a, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0xa8, 0x01, 0x0a, 0x10,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x12, 0x27, 0x0a, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x19, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x02, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0x0a, 0x06,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x6e, 0x0a, 0x10, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x62,
	0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xe3, 0x01, 0x0a, 0x14, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12,
	0x59, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x66, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0a, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x73, 0x22, 0x4e, 0x0a, 0x09,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x4c, 0x4c,
	0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x53, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12,
	0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x53, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x58, 0x43, 0x4c, 0x55, 0x44, 0x45, 0x44,
	0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x53, 0x10, 0x02, 0x22, 0xb5, 0x04, 0x0a,
	0x0b, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x5f, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x55, 0x0a, 0x0a, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4a, 0x73, 0x6f, 0x6e,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x4a, 0x73, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x70, 0x75,
	0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x08, 0x70, 0x75, 0x73,
	0x68, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x4a, 0x73, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x22, 0x9f, 0x02, 0x0a, 0x14, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x2a, 0x0a,
	0x0e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x5e, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x6a,
	0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x4a, 0x73, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x4a, 0x73, 0x6f, 0x6e, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4a, 0x73,
	0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xbc, 0x02, 0x0a, 0x14, 0x50, 0x65, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12,
	0x5f, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x5e, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x4a, 0x73, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x4a, 0x73, 0x6f, 0x6e, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x4a, 0x73, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescData = file_moego_models_enterprise_v1_pet_settings_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_pet_settings_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_pet_settings_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_models_enterprise_v1_pet_settings_models_proto_goTypes = []interface{}{
	(PetCodeLimitationDef_LimitType)(0), // 0: moego.models.enterprise.v1.PetCodeLimitationDef.LimitType
	(*PetCode)(nil),                     // 1: moego.models.enterprise.v1.PetCode
	(*PetCodeUpdateDef)(nil),            // 2: moego.models.enterprise.v1.PetCodeUpdateDef
	(*PetCodeCreateDef)(nil),            // 3: moego.models.enterprise.v1.PetCodeCreateDef
	(*PetCodeLimitationDef)(nil),        // 4: moego.models.enterprise.v1.PetCodeLimitationDef
	(*PetMetadata)(nil),                 // 5: moego.models.enterprise.v1.PetMetadata
	(*PetMetadataUpdateDef)(nil),        // 6: moego.models.enterprise.v1.PetMetadataUpdateDef
	(*PetMetadataCreateDef)(nil),        // 7: moego.models.enterprise.v1.PetMetadataCreateDef
	nil,                                 // 8: moego.models.enterprise.v1.PetMetadata.ExtraJsonEntry
	nil,                                 // 9: moego.models.enterprise.v1.PetMetadataUpdateDef.ExtraJsonEntry
	nil,                                 // 10: moego.models.enterprise.v1.PetMetadataCreateDef.ExtraJsonEntry
	(*timestamppb.Timestamp)(nil),       // 11: google.protobuf.Timestamp
	(v1.BusinessPetMetadataName)(0),     // 12: moego.models.business_customer.v1.BusinessPetMetadataName
}
var file_moego_models_enterprise_v1_pet_settings_models_proto_depIdxs = []int32{
	11, // 0: moego.models.enterprise.v1.PetCode.updated_at:type_name -> google.protobuf.Timestamp
	11, // 1: moego.models.enterprise.v1.PetCode.pushed_at:type_name -> google.protobuf.Timestamp
	0,  // 2: moego.models.enterprise.v1.PetCodeLimitationDef.limit_type:type_name -> moego.models.enterprise.v1.PetCodeLimitationDef.LimitType
	12, // 3: moego.models.enterprise.v1.PetMetadata.metadata_name:type_name -> moego.models.business_customer.v1.BusinessPetMetadataName
	8,  // 4: moego.models.enterprise.v1.PetMetadata.extra_json:type_name -> moego.models.enterprise.v1.PetMetadata.ExtraJsonEntry
	11, // 5: moego.models.enterprise.v1.PetMetadata.created_at:type_name -> google.protobuf.Timestamp
	11, // 6: moego.models.enterprise.v1.PetMetadata.updated_at:type_name -> google.protobuf.Timestamp
	11, // 7: moego.models.enterprise.v1.PetMetadata.pushed_at:type_name -> google.protobuf.Timestamp
	9,  // 8: moego.models.enterprise.v1.PetMetadataUpdateDef.extra_json:type_name -> moego.models.enterprise.v1.PetMetadataUpdateDef.ExtraJsonEntry
	12, // 9: moego.models.enterprise.v1.PetMetadataCreateDef.metadata_name:type_name -> moego.models.business_customer.v1.BusinessPetMetadataName
	10, // 10: moego.models.enterprise.v1.PetMetadataCreateDef.extra_json:type_name -> moego.models.enterprise.v1.PetMetadataCreateDef.ExtraJsonEntry
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_pet_settings_models_proto_init() }
func file_moego_models_enterprise_v1_pet_settings_models_proto_init() {
	if File_moego_models_enterprise_v1_pet_settings_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetCodeUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetCodeCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetCodeLimitationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetMetadataUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetMetadataCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_pet_settings_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_pet_settings_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_pet_settings_models_proto_depIdxs,
		EnumInfos:         file_moego_models_enterprise_v1_pet_settings_models_proto_enumTypes,
		MessageInfos:      file_moego_models_enterprise_v1_pet_settings_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_pet_settings_models_proto = out.File
	file_moego_models_enterprise_v1_pet_settings_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_pet_settings_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_pet_settings_models_proto_depIdxs = nil
}
