// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/template_push.proto

package enterprisepb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TemplateType
type TemplateType int32

const (
	// Unspecified template type
	TemplateType_TEMPLATE_TYPE_UNSPECIFIED TemplateType = 0
	// service
	TemplateType_SERVICE TemplateType = 1
	// workflow
	TemplateType_WORKFLOW TemplateType = 2
	// lodging
	TemplateType_LODGING TemplateType = 3
	// evaluation
	TemplateType_EVALUATION TemplateType = 4
	// pet code
	TemplateType_PET_CODE TemplateType = 5
	// pricing rule
	TemplateType_PRICING_RULE TemplateType = 6
	// service charge
	TemplateType_SERVICE_CHARGE TemplateType = 7
	// pet metadata
	TemplateType_PET_METADATA TemplateType = 8
	// membership
	TemplateType_MEMBERSHIP TemplateType = 9
	// package
	TemplateType_PACKAGE TemplateType = 10
	// discount
	TemplateType_DISCOUNT TemplateType = 11
)

// Enum value maps for TemplateType.
var (
	TemplateType_name = map[int32]string{
		0:  "TEMPLATE_TYPE_UNSPECIFIED",
		1:  "SERVICE",
		2:  "WORKFLOW",
		3:  "LODGING",
		4:  "EVALUATION",
		5:  "PET_CODE",
		6:  "PRICING_RULE",
		7:  "SERVICE_CHARGE",
		8:  "PET_METADATA",
		9:  "MEMBERSHIP",
		10: "PACKAGE",
		11: "DISCOUNT",
	}
	TemplateType_value = map[string]int32{
		"TEMPLATE_TYPE_UNSPECIFIED": 0,
		"SERVICE":                   1,
		"WORKFLOW":                  2,
		"LODGING":                   3,
		"EVALUATION":                4,
		"PET_CODE":                  5,
		"PRICING_RULE":              6,
		"SERVICE_CHARGE":            7,
		"PET_METADATA":              8,
		"MEMBERSHIP":                9,
		"PACKAGE":                   10,
		"DISCOUNT":                  11,
	}
)

func (x TemplateType) Enum() *TemplateType {
	p := new(TemplateType)
	*p = x
	return p
}

func (x TemplateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplateType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_template_push_proto_enumTypes[0].Descriptor()
}

func (TemplateType) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_template_push_proto_enumTypes[0]
}

func (x TemplateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplateType.Descriptor instead.
func (TemplateType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{0}
}

// TemplatePushConflictType
type TemplatePushConflictType int32

const (
	// Unspecified conflict type
	TemplatePushConflictType_TEMPLATE_PUSH_CONFLICT_TYPE_UNSPECIFIED TemplatePushConflictType = 0
	// name conflict
	TemplatePushConflictType_NAME_CONFLICT TemplatePushConflictType = 1
)

// Enum value maps for TemplatePushConflictType.
var (
	TemplatePushConflictType_name = map[int32]string{
		0: "TEMPLATE_PUSH_CONFLICT_TYPE_UNSPECIFIED",
		1: "NAME_CONFLICT",
	}
	TemplatePushConflictType_value = map[string]int32{
		"TEMPLATE_PUSH_CONFLICT_TYPE_UNSPECIFIED": 0,
		"NAME_CONFLICT": 1,
	}
)

func (x TemplatePushConflictType) Enum() *TemplatePushConflictType {
	p := new(TemplatePushConflictType)
	*p = x
	return p
}

func (x TemplatePushConflictType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplatePushConflictType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_template_push_proto_enumTypes[1].Descriptor()
}

func (TemplatePushConflictType) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_template_push_proto_enumTypes[1]
}

func (x TemplatePushConflictType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplatePushConflictType.Descriptor instead.
func (TemplatePushConflictType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{1}
}

// field
type TemplatePushChangeHistoryOrderBy_Field int32

const (
	// 未指定
	TemplatePushChangeHistoryOrderBy_FIELD_UNSPECIFIED TemplatePushChangeHistoryOrderBy_Field = 0
	// 创建时间
	TemplatePushChangeHistoryOrderBy_UPDATED_AT TemplatePushChangeHistoryOrderBy_Field = 1
)

// Enum value maps for TemplatePushChangeHistoryOrderBy_Field.
var (
	TemplatePushChangeHistoryOrderBy_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "UPDATED_AT",
	}
	TemplatePushChangeHistoryOrderBy_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"UPDATED_AT":        1,
	}
)

func (x TemplatePushChangeHistoryOrderBy_Field) Enum() *TemplatePushChangeHistoryOrderBy_Field {
	p := new(TemplatePushChangeHistoryOrderBy_Field)
	*p = x
	return p
}

func (x TemplatePushChangeHistoryOrderBy_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplatePushChangeHistoryOrderBy_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_template_push_proto_enumTypes[2].Descriptor()
}

func (TemplatePushChangeHistoryOrderBy_Field) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_template_push_proto_enumTypes[2]
}

func (x TemplatePushChangeHistoryOrderBy_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplatePushChangeHistoryOrderBy_Field.Descriptor instead.
func (TemplatePushChangeHistoryOrderBy_Field) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{2, 0}
}

// TemplatePushMapping
type TemplatePushMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// template type
	TemplateType TemplateType `protobuf:"varint,2,opt,name=template_type,json=templateType,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_type,omitempty"`
	// template id
	TemplateId int64 `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// target organization type
	TargetOrganizationType v1.OrganizationType `protobuf:"varint,4,opt,name=target_organization_type,json=targetOrganizationType,proto3,enum=moego.models.organization.v1.OrganizationType" json:"target_organization_type,omitempty"`
	// target organization id
	TargetOrganizationId int64 `protobuf:"varint,5,opt,name=target_organization_id,json=targetOrganizationId,proto3" json:"target_organization_id,omitempty"`
	// target id
	TargetId int64 `protobuf:"varint,6,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
}

func (x *TemplatePushMapping) Reset() {
	*x = TemplatePushMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplatePushMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplatePushMapping) ProtoMessage() {}

func (x *TemplatePushMapping) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplatePushMapping.ProtoReflect.Descriptor instead.
func (*TemplatePushMapping) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{0}
}

func (x *TemplatePushMapping) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TemplatePushMapping) GetTemplateType() TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return TemplateType_TEMPLATE_TYPE_UNSPECIFIED
}

func (x *TemplatePushMapping) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *TemplatePushMapping) GetTargetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.TargetOrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *TemplatePushMapping) GetTargetOrganizationId() int64 {
	if x != nil {
		return x.TargetOrganizationId
	}
	return 0
}

func (x *TemplatePushMapping) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

// template push setting
type TemplatePushSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// template type
	TemplateType TemplateType `protobuf:"varint,2,opt,name=template_type,json=templateType,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_type,omitempty"`
	// template id
	TemplateId int64 `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// attributes
	Attributes []*TemplatePushSetting_Attribute `protobuf:"bytes,4,rep,name=attributes,proto3" json:"attributes,omitempty"`
}

func (x *TemplatePushSetting) Reset() {
	*x = TemplatePushSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplatePushSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplatePushSetting) ProtoMessage() {}

func (x *TemplatePushSetting) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplatePushSetting.ProtoReflect.Descriptor instead.
func (*TemplatePushSetting) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{1}
}

func (x *TemplatePushSetting) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TemplatePushSetting) GetTemplateType() TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return TemplateType_TEMPLATE_TYPE_UNSPECIFIED
}

func (x *TemplatePushSetting) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *TemplatePushSetting) GetAttributes() []*TemplatePushSetting_Attribute {
	if x != nil {
		return x.Attributes
	}
	return nil
}

// order by
type TemplatePushChangeHistoryOrderBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// field
	Field TemplatePushChangeHistoryOrderBy_Field `protobuf:"varint,1,opt,name=field,proto3,enum=moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy_Field" json:"field,omitempty"`
	// asc
	Asc bool `protobuf:"varint,2,opt,name=asc,proto3" json:"asc,omitempty"`
}

func (x *TemplatePushChangeHistoryOrderBy) Reset() {
	*x = TemplatePushChangeHistoryOrderBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplatePushChangeHistoryOrderBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplatePushChangeHistoryOrderBy) ProtoMessage() {}

func (x *TemplatePushChangeHistoryOrderBy) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplatePushChangeHistoryOrderBy.ProtoReflect.Descriptor instead.
func (*TemplatePushChangeHistoryOrderBy) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{2}
}

func (x *TemplatePushChangeHistoryOrderBy) GetField() TemplatePushChangeHistoryOrderBy_Field {
	if x != nil {
		return x.Field
	}
	return TemplatePushChangeHistoryOrderBy_FIELD_UNSPECIFIED
}

func (x *TemplatePushChangeHistoryOrderBy) GetAsc() bool {
	if x != nil {
		return x.Asc
	}
	return false
}

// template push result
type TemplatePushResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
	// success tenant ids
	SuccessTenantIds []int64 `protobuf:"varint,3,rep,packed,name=success_tenant_ids,json=successTenantIds,proto3" json:"success_tenant_ids,omitempty"`
	// failed tenant ids
	FailedTenantIds []int64 `protobuf:"varint,4,rep,packed,name=failed_tenant_ids,json=failedTenantIds,proto3" json:"failed_tenant_ids,omitempty"`
}

func (x *TemplatePushResult) Reset() {
	*x = TemplatePushResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplatePushResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplatePushResult) ProtoMessage() {}

func (x *TemplatePushResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplatePushResult.ProtoReflect.Descriptor instead.
func (*TemplatePushResult) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{3}
}

func (x *TemplatePushResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *TemplatePushResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

func (x *TemplatePushResult) GetSuccessTenantIds() []int64 {
	if x != nil {
		return x.SuccessTenantIds
	}
	return nil
}

func (x *TemplatePushResult) GetFailedTenantIds() []int64 {
	if x != nil {
		return x.FailedTenantIds
	}
	return nil
}

// change history
type TemplatePushHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// template type
	TemplateType TemplateType `protobuf:"varint,3,opt,name=template_type,json=templateType,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_type,omitempty"`
	// template id
	TemplateId int64 `protobuf:"varint,4,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// impacted tenants
	ImpactedTenants int64 `protobuf:"varint,5,opt,name=impacted_tenants,json=impactedTenants,proto3" json:"impacted_tenants,omitempty"`
	// roll out at
	RollOutAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=roll_out_at,json=rollOutAt,proto3" json:"roll_out_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// impacted tenant ids
	ImpactedTenantIds []int64 `protobuf:"varint,8,rep,packed,name=impacted_tenant_ids,json=impactedTenantIds,proto3" json:"impacted_tenant_ids,omitempty"`
	// push result
	PushResult *TemplatePushResult `protobuf:"bytes,9,opt,name=push_result,json=pushResult,proto3" json:"push_result,omitempty"`
}

func (x *TemplatePushHistory) Reset() {
	*x = TemplatePushHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplatePushHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplatePushHistory) ProtoMessage() {}

func (x *TemplatePushHistory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplatePushHistory.ProtoReflect.Descriptor instead.
func (*TemplatePushHistory) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{4}
}

func (x *TemplatePushHistory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TemplatePushHistory) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *TemplatePushHistory) GetTemplateType() TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return TemplateType_TEMPLATE_TYPE_UNSPECIFIED
}

func (x *TemplatePushHistory) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *TemplatePushHistory) GetImpactedTenants() int64 {
	if x != nil {
		return x.ImpactedTenants
	}
	return 0
}

func (x *TemplatePushHistory) GetRollOutAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RollOutAt
	}
	return nil
}

func (x *TemplatePushHistory) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *TemplatePushHistory) GetImpactedTenantIds() []int64 {
	if x != nil {
		return x.ImpactedTenantIds
	}
	return nil
}

func (x *TemplatePushHistory) GetPushResult() *TemplatePushResult {
	if x != nil {
		return x.PushResult
	}
	return nil
}

// change
type TemplatePushChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// history id
	HistoryId int64 `protobuf:"varint,3,opt,name=history_id,json=historyId,proto3" json:"history_id,omitempty"`
	// target
	Target *TenantObject `protobuf:"bytes,4,opt,name=target,proto3" json:"target,omitempty"`
	// template type
	TemplateType TemplateType `protobuf:"varint,5,opt,name=template_type,json=templateType,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_type,omitempty"`
	// template id
	TemplateId int64 `protobuf:"varint,6,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// template name
	TemplateName string `protobuf:"bytes,7,opt,name=template_name,json=templateName,proto3" json:"template_name,omitempty"`
	// details
	DetailCategories []*DetailCategory `protobuf:"bytes,8,rep,name=detail_categories,json=detailCategories,proto3" json:"detail_categories,omitempty"`
}

func (x *TemplatePushChange) Reset() {
	*x = TemplatePushChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplatePushChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplatePushChange) ProtoMessage() {}

func (x *TemplatePushChange) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplatePushChange.ProtoReflect.Descriptor instead.
func (*TemplatePushChange) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{5}
}

func (x *TemplatePushChange) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TemplatePushChange) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *TemplatePushChange) GetHistoryId() int64 {
	if x != nil {
		return x.HistoryId
	}
	return 0
}

func (x *TemplatePushChange) GetTarget() *TenantObject {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *TemplatePushChange) GetTemplateType() TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return TemplateType_TEMPLATE_TYPE_UNSPECIFIED
}

func (x *TemplatePushChange) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *TemplatePushChange) GetTemplateName() string {
	if x != nil {
		return x.TemplateName
	}
	return ""
}

func (x *TemplatePushChange) GetDetailCategories() []*DetailCategory {
	if x != nil {
		return x.DetailCategories
	}
	return nil
}

// detail category
type DetailCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// details
	Details []*Detail `protobuf:"bytes,2,rep,name=details,proto3" json:"details,omitempty"`
}

func (x *DetailCategory) Reset() {
	*x = DetailCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailCategory) ProtoMessage() {}

func (x *DetailCategory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailCategory.ProtoReflect.Descriptor instead.
func (*DetailCategory) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{6}
}

func (x *DetailCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DetailCategory) GetDetails() []*Detail {
	if x != nil {
		return x.Details
	}
	return nil
}

// detail
type Detail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// field name
	FieldName string `protobuf:"bytes,1,opt,name=field_name,json=fieldName,proto3" json:"field_name,omitempty"`
	// field type
	Type v2.Field_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.reporting.v2.Field_Type" json:"type,omitempty"`
	// old value
	Old *v2.Value `protobuf:"bytes,3,opt,name=old,proto3" json:"old,omitempty"`
	// new value
	New *v2.Value `protobuf:"bytes,4,opt,name=new,proto3" json:"new,omitempty"`
	// is changed
	IsChanged bool `protobuf:"varint,5,opt,name=is_changed,json=isChanged,proto3" json:"is_changed,omitempty"`
}

func (x *Detail) Reset() {
	*x = Detail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Detail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Detail) ProtoMessage() {}

func (x *Detail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Detail.ProtoReflect.Descriptor instead.
func (*Detail) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{7}
}

func (x *Detail) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *Detail) GetType() v2.Field_Type {
	if x != nil {
		return x.Type
	}
	return v2.Field_Type(0)
}

func (x *Detail) GetOld() *v2.Value {
	if x != nil {
		return x.Old
	}
	return nil
}

func (x *Detail) GetNew() *v2.Value {
	if x != nil {
		return x.New
	}
	return nil
}

func (x *Detail) GetIsChanged() bool {
	if x != nil {
		return x.IsChanged
	}
	return false
}

// conflict
type TemplatePushConflict struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// target tenant ids
	TargetTenantIds []int64 `protobuf:"varint,2,rep,packed,name=target_tenant_ids,json=targetTenantIds,proto3" json:"target_tenant_ids,omitempty"`
	// conflict type
	ConflictType TemplatePushConflictType `protobuf:"varint,3,opt,name=conflict_type,json=conflictType,proto3,enum=moego.models.enterprise.v1.TemplatePushConflictType" json:"conflict_type,omitempty"`
	// conflict message
	ConflictMessage string `protobuf:"bytes,4,opt,name=conflict_message,json=conflictMessage,proto3" json:"conflict_message,omitempty"`
}

func (x *TemplatePushConflict) Reset() {
	*x = TemplatePushConflict{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplatePushConflict) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplatePushConflict) ProtoMessage() {}

func (x *TemplatePushConflict) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplatePushConflict.ProtoReflect.Descriptor instead.
func (*TemplatePushConflict) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{8}
}

func (x *TemplatePushConflict) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TemplatePushConflict) GetTargetTenantIds() []int64 {
	if x != nil {
		return x.TargetTenantIds
	}
	return nil
}

func (x *TemplatePushConflict) GetConflictType() TemplatePushConflictType {
	if x != nil {
		return x.ConflictType
	}
	return TemplatePushConflictType_TEMPLATE_PUSH_CONFLICT_TYPE_UNSPECIFIED
}

func (x *TemplatePushConflict) GetConflictMessage() string {
	if x != nil {
		return x.ConflictMessage
	}
	return ""
}

// Attribute
type TemplatePushSetting_Attribute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// field name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// is locked
	IsLocked bool `protobuf:"varint,2,opt,name=is_locked,json=isLocked,proto3" json:"is_locked,omitempty"`
}

func (x *TemplatePushSetting_Attribute) Reset() {
	*x = TemplatePushSetting_Attribute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplatePushSetting_Attribute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplatePushSetting_Attribute) ProtoMessage() {}

func (x *TemplatePushSetting_Attribute) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_template_push_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplatePushSetting_Attribute.ProtoReflect.Descriptor instead.
func (*TemplatePushSetting_Attribute) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP(), []int{1, 0}
}

func (x *TemplatePushSetting_Attribute) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TemplatePushSetting_Attribute) GetIsLocked() bool {
	if x != nil {
		return x.IsLocked
	}
	return false
}

var File_moego_models_enterprise_v1_template_push_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_template_push_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xd2, 0x02, 0x0a, 0x13, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4d, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x68, 0x0a, 0x18, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x16, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x14, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x49, 0x64, 0x22, 0xae, 0x02, 0x0a, 0x13, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4d, 0x0a,
	0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x59, 0x0a,
	0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x0a, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x1a, 0x3c, 0x0a, 0x09, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f,
	0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73,
	0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x22, 0xbe, 0x01, 0x0a, 0x20, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x58, 0x0a, 0x05, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x05,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x03, 0x61, 0x73, 0x63, 0x22, 0x2e, 0x0a, 0x05, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x01, 0x22, 0xcc, 0x01, 0x0a, 0x12, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2e,
	0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c,
	0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xdd, 0x03, 0x0a, 0x13, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x69,
	0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x3a,
	0x0a, 0x0b, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x72, 0x6f, 0x6c, 0x6c, 0x4f, 0x75, 0x74, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x11, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x4f, 0x0a, 0x0b, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a, 0x70, 0x75, 0x73, 0x68,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x98, 0x03, 0x0a, 0x12, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x40, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x12, 0x4d, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x57, 0x0a, 0x11, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52,
	0x10, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x22, 0x62, 0x0a, 0x0e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xe9, 0x01, 0x0a, 0x06, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x03, 0x6f, 0x6c,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x6f, 0x6c, 0x64, 0x12, 0x32,
	0x0a, 0x03, 0x6e, 0x65, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x6e,
	0x65, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x64, 0x22, 0xd8, 0x01, 0x0a, 0x14, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x59, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69,
	0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e,
	0x66, 0x6c, 0x69, 0x63, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0xd6, 0x01, 0x0a,
	0x0c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x19, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x4f, 0x52,
	0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x4f, 0x44, 0x47, 0x49,
	0x4e, 0x47, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0x05, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x52, 0x49, 0x43, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x55,
	0x4c, 0x45, 0x10, 0x06, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x45, 0x54, 0x5f,
	0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x10, 0x08, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x45,
	0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x10, 0x09, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41,
	0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x0a, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x10, 0x0b, 0x2a, 0x5a, 0x0a, 0x18, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2b, 0x0a, 0x27, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x55,
	0x53, 0x48, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11,
	0x0a, 0x0d, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x10,
	0x01, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_template_push_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_template_push_proto_rawDescData = file_moego_models_enterprise_v1_template_push_proto_rawDesc
)

func file_moego_models_enterprise_v1_template_push_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_template_push_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_template_push_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_template_push_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_template_push_proto_rawDescData
}

var file_moego_models_enterprise_v1_template_push_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_enterprise_v1_template_push_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_models_enterprise_v1_template_push_proto_goTypes = []interface{}{
	(TemplateType)(0),                           // 0: moego.models.enterprise.v1.TemplateType
	(TemplatePushConflictType)(0),               // 1: moego.models.enterprise.v1.TemplatePushConflictType
	(TemplatePushChangeHistoryOrderBy_Field)(0), // 2: moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy.Field
	(*TemplatePushMapping)(nil),                 // 3: moego.models.enterprise.v1.TemplatePushMapping
	(*TemplatePushSetting)(nil),                 // 4: moego.models.enterprise.v1.TemplatePushSetting
	(*TemplatePushChangeHistoryOrderBy)(nil),    // 5: moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy
	(*TemplatePushResult)(nil),                  // 6: moego.models.enterprise.v1.TemplatePushResult
	(*TemplatePushHistory)(nil),                 // 7: moego.models.enterprise.v1.TemplatePushHistory
	(*TemplatePushChange)(nil),                  // 8: moego.models.enterprise.v1.TemplatePushChange
	(*DetailCategory)(nil),                      // 9: moego.models.enterprise.v1.DetailCategory
	(*Detail)(nil),                              // 10: moego.models.enterprise.v1.Detail
	(*TemplatePushConflict)(nil),                // 11: moego.models.enterprise.v1.TemplatePushConflict
	(*TemplatePushSetting_Attribute)(nil),       // 12: moego.models.enterprise.v1.TemplatePushSetting.Attribute
	(v1.OrganizationType)(0),                    // 13: moego.models.organization.v1.OrganizationType
	(*timestamppb.Timestamp)(nil),               // 14: google.protobuf.Timestamp
	(*TenantObject)(nil),                        // 15: moego.models.enterprise.v1.TenantObject
	(v2.Field_Type)(0),                          // 16: moego.models.reporting.v2.Field.Type
	(*v2.Value)(nil),                            // 17: moego.models.reporting.v2.Value
}
var file_moego_models_enterprise_v1_template_push_proto_depIdxs = []int32{
	0,  // 0: moego.models.enterprise.v1.TemplatePushMapping.template_type:type_name -> moego.models.enterprise.v1.TemplateType
	13, // 1: moego.models.enterprise.v1.TemplatePushMapping.target_organization_type:type_name -> moego.models.organization.v1.OrganizationType
	0,  // 2: moego.models.enterprise.v1.TemplatePushSetting.template_type:type_name -> moego.models.enterprise.v1.TemplateType
	12, // 3: moego.models.enterprise.v1.TemplatePushSetting.attributes:type_name -> moego.models.enterprise.v1.TemplatePushSetting.Attribute
	2,  // 4: moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy.field:type_name -> moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy.Field
	0,  // 5: moego.models.enterprise.v1.TemplatePushHistory.template_type:type_name -> moego.models.enterprise.v1.TemplateType
	14, // 6: moego.models.enterprise.v1.TemplatePushHistory.roll_out_at:type_name -> google.protobuf.Timestamp
	14, // 7: moego.models.enterprise.v1.TemplatePushHistory.updated_at:type_name -> google.protobuf.Timestamp
	6,  // 8: moego.models.enterprise.v1.TemplatePushHistory.push_result:type_name -> moego.models.enterprise.v1.TemplatePushResult
	15, // 9: moego.models.enterprise.v1.TemplatePushChange.target:type_name -> moego.models.enterprise.v1.TenantObject
	0,  // 10: moego.models.enterprise.v1.TemplatePushChange.template_type:type_name -> moego.models.enterprise.v1.TemplateType
	9,  // 11: moego.models.enterprise.v1.TemplatePushChange.detail_categories:type_name -> moego.models.enterprise.v1.DetailCategory
	10, // 12: moego.models.enterprise.v1.DetailCategory.details:type_name -> moego.models.enterprise.v1.Detail
	16, // 13: moego.models.enterprise.v1.Detail.type:type_name -> moego.models.reporting.v2.Field.Type
	17, // 14: moego.models.enterprise.v1.Detail.old:type_name -> moego.models.reporting.v2.Value
	17, // 15: moego.models.enterprise.v1.Detail.new:type_name -> moego.models.reporting.v2.Value
	1,  // 16: moego.models.enterprise.v1.TemplatePushConflict.conflict_type:type_name -> moego.models.enterprise.v1.TemplatePushConflictType
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_template_push_proto_init() }
func file_moego_models_enterprise_v1_template_push_proto_init() {
	if File_moego_models_enterprise_v1_template_push_proto != nil {
		return
	}
	file_moego_models_enterprise_v1_tenant_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_template_push_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplatePushMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_template_push_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplatePushSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_template_push_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplatePushChangeHistoryOrderBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_template_push_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplatePushResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_template_push_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplatePushHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_template_push_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplatePushChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_template_push_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_template_push_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Detail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_template_push_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplatePushConflict); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_template_push_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplatePushSetting_Attribute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_template_push_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_template_push_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_template_push_proto_depIdxs,
		EnumInfos:         file_moego_models_enterprise_v1_template_push_proto_enumTypes,
		MessageInfos:      file_moego_models_enterprise_v1_template_push_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_template_push_proto = out.File
	file_moego_models_enterprise_v1_template_push_proto_rawDesc = nil
	file_moego_models_enterprise_v1_template_push_proto_goTypes = nil
	file_moego_models_enterprise_v1_template_push_proto_depIdxs = nil
}
