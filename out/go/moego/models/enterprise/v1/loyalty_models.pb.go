// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/loyalty_models.proto

package enterprisepb

import (
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/marketing/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CreateMembershipDef
type CreateMembershipDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// status
	Status v1.MembershipModel_Status `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.membership.v1.MembershipModel_Status" json:"status,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,4,opt,name=price,proto3" json:"price,omitempty"`
	// billing cycle
	BillingCyclePeriod *v11.TimePeriod `protobuf:"bytes,5,opt,name=billing_cycle_period,json=billingCyclePeriod,proto3" json:"billing_cycle_period,omitempty"`
	// policy
	Policy string `protobuf:"bytes,6,opt,name=policy,proto3" json:"policy,omitempty"`
	// enable discount benefits
	EnableDiscountBenefits bool `protobuf:"varint,7,opt,name=enable_discount_benefits,json=enableDiscountBenefits,proto3" json:"enable_discount_benefits,omitempty"`
	// discount benefits
	DiscountBenefits []*CreateMembershipDiscountBenefitDef `protobuf:"bytes,9,rep,name=discount_benefits,json=discountBenefits,proto3" json:"discount_benefits,omitempty"`
	// enable quantity benefits
	EnableQuantityBenefits bool `protobuf:"varint,8,opt,name=enable_quantity_benefits,json=enableQuantityBenefits,proto3" json:"enable_quantity_benefits,omitempty"`
	// quantity benefits
	QuantityBenefits []*CreateMembershipQuantityBenefitDef `protobuf:"bytes,10,rep,name=quantity_benefits,json=quantityBenefits,proto3" json:"quantity_benefits,omitempty"`
	// quantity benefits period type
	QuantityBenefitsPeriodType v1.PeriodType `protobuf:"varint,11,opt,name=quantity_benefits_period_type,json=quantityBenefitsPeriodType,proto3,enum=moego.models.membership.v1.PeriodType" json:"quantity_benefits_period_type,omitempty"`
	// quantity benefits specified period
	QuantityBenefitsSpecifiedPeriod *v11.TimePeriod `protobuf:"bytes,12,opt,name=quantity_benefits_specified_period,json=quantityBenefitsSpecifiedPeriod,proto3" json:"quantity_benefits_specified_period,omitempty"`
}

func (x *CreateMembershipDef) Reset() {
	*x = CreateMembershipDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMembershipDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMembershipDef) ProtoMessage() {}

func (x *CreateMembershipDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMembershipDef.ProtoReflect.Descriptor instead.
func (*CreateMembershipDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{0}
}

func (x *CreateMembershipDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateMembershipDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateMembershipDef) GetStatus() v1.MembershipModel_Status {
	if x != nil {
		return x.Status
	}
	return v1.MembershipModel_Status(0)
}

func (x *CreateMembershipDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *CreateMembershipDef) GetBillingCyclePeriod() *v11.TimePeriod {
	if x != nil {
		return x.BillingCyclePeriod
	}
	return nil
}

func (x *CreateMembershipDef) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *CreateMembershipDef) GetEnableDiscountBenefits() bool {
	if x != nil {
		return x.EnableDiscountBenefits
	}
	return false
}

func (x *CreateMembershipDef) GetDiscountBenefits() []*CreateMembershipDiscountBenefitDef {
	if x != nil {
		return x.DiscountBenefits
	}
	return nil
}

func (x *CreateMembershipDef) GetEnableQuantityBenefits() bool {
	if x != nil {
		return x.EnableQuantityBenefits
	}
	return false
}

func (x *CreateMembershipDef) GetQuantityBenefits() []*CreateMembershipQuantityBenefitDef {
	if x != nil {
		return x.QuantityBenefits
	}
	return nil
}

func (x *CreateMembershipDef) GetQuantityBenefitsPeriodType() v1.PeriodType {
	if x != nil {
		return x.QuantityBenefitsPeriodType
	}
	return v1.PeriodType(0)
}

func (x *CreateMembershipDef) GetQuantityBenefitsSpecifiedPeriod() *v11.TimePeriod {
	if x != nil {
		return x.QuantityBenefitsSpecifiedPeriod
	}
	return nil
}

// CreateMembershipDiscountBenefitDef
type CreateMembershipDiscountBenefitDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is all
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// ids
	TargetIds []int64 `protobuf:"varint,2,rep,packed,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`
	// value
	Value float64 `protobuf:"fixed64,3,opt,name=value,proto3" json:"value,omitempty"`
	// unit
	Unit v1.DiscountUnit `protobuf:"varint,4,opt,name=unit,proto3,enum=moego.models.membership.v1.DiscountUnit" json:"unit,omitempty"`
	// target type
	TargetType v1.TargetType `protobuf:"varint,5,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
}

func (x *CreateMembershipDiscountBenefitDef) Reset() {
	*x = CreateMembershipDiscountBenefitDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMembershipDiscountBenefitDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMembershipDiscountBenefitDef) ProtoMessage() {}

func (x *CreateMembershipDiscountBenefitDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMembershipDiscountBenefitDef.ProtoReflect.Descriptor instead.
func (*CreateMembershipDiscountBenefitDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{1}
}

func (x *CreateMembershipDiscountBenefitDef) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *CreateMembershipDiscountBenefitDef) GetTargetIds() []int64 {
	if x != nil {
		return x.TargetIds
	}
	return nil
}

func (x *CreateMembershipDiscountBenefitDef) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *CreateMembershipDiscountBenefitDef) GetUnit() v1.DiscountUnit {
	if x != nil {
		return x.Unit
	}
	return v1.DiscountUnit(0)
}

func (x *CreateMembershipDiscountBenefitDef) GetTargetType() v1.TargetType {
	if x != nil {
		return x.TargetType
	}
	return v1.TargetType(0)
}

// CreateMembershipQuantityBenefitDef
type CreateMembershipQuantityBenefitDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is limit
	IsLimited bool `protobuf:"varint,1,opt,name=is_limited,json=isLimited,proto3" json:"is_limited,omitempty"`
	// count
	LimitedValue int64 `protobuf:"varint,2,opt,name=limited_value,json=limitedValue,proto3" json:"limited_value,omitempty"`
	// id
	TargetId int64 `protobuf:"varint,3,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// target type
	TargetType v1.TargetType `protobuf:"varint,4,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// target subtype
	TargetSubType v1.TargetSubType `protobuf:"varint,5,opt,name=target_sub_type,json=targetSubType,proto3,enum=moego.models.membership.v1.TargetSubType" json:"target_sub_type,omitempty"`
}

func (x *CreateMembershipQuantityBenefitDef) Reset() {
	*x = CreateMembershipQuantityBenefitDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMembershipQuantityBenefitDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMembershipQuantityBenefitDef) ProtoMessage() {}

func (x *CreateMembershipQuantityBenefitDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMembershipQuantityBenefitDef.ProtoReflect.Descriptor instead.
func (*CreateMembershipQuantityBenefitDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{2}
}

func (x *CreateMembershipQuantityBenefitDef) GetIsLimited() bool {
	if x != nil {
		return x.IsLimited
	}
	return false
}

func (x *CreateMembershipQuantityBenefitDef) GetLimitedValue() int64 {
	if x != nil {
		return x.LimitedValue
	}
	return 0
}

func (x *CreateMembershipQuantityBenefitDef) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *CreateMembershipQuantityBenefitDef) GetTargetType() v1.TargetType {
	if x != nil {
		return x.TargetType
	}
	return v1.TargetType(0)
}

func (x *CreateMembershipQuantityBenefitDef) GetTargetSubType() v1.TargetSubType {
	if x != nil {
		return x.TargetSubType
	}
	return v1.TargetSubType(0)
}

// UpdateMembershipDef
type UpdateMembershipDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// description
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// status
	Status *v1.MembershipModel_Status `protobuf:"varint,4,opt,name=status,proto3,enum=moego.models.membership.v1.MembershipModel_Status,oneof" json:"status,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,5,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// billing cycle
	BillingCyclePeriod *v11.TimePeriod `protobuf:"bytes,6,opt,name=billing_cycle_period,json=billingCyclePeriod,proto3,oneof" json:"billing_cycle_period,omitempty"`
	// policy
	Policy *string `protobuf:"bytes,7,opt,name=policy,proto3,oneof" json:"policy,omitempty"`
	// enable discount benefits
	EnableDiscountBenefits *bool `protobuf:"varint,8,opt,name=enable_discount_benefits,json=enableDiscountBenefits,proto3,oneof" json:"enable_discount_benefits,omitempty"`
	// discount benefits
	DiscountBenefits []*UpdateMembershipDiscountBenefitDef `protobuf:"bytes,9,rep,name=discount_benefits,json=discountBenefits,proto3" json:"discount_benefits,omitempty"`
	// enable quantity benefits
	EnableQuantityBenefits *bool `protobuf:"varint,10,opt,name=enable_quantity_benefits,json=enableQuantityBenefits,proto3,oneof" json:"enable_quantity_benefits,omitempty"`
	// quantity benefits
	QuantityBenefits []*UpdateMembershipQuantityBenefitDef `protobuf:"bytes,11,rep,name=quantity_benefits,json=quantityBenefits,proto3" json:"quantity_benefits,omitempty"`
	// quantity benefits period type
	QuantityBenefitsPeriodType *v1.PeriodType `protobuf:"varint,12,opt,name=quantity_benefits_period_type,json=quantityBenefitsPeriodType,proto3,enum=moego.models.membership.v1.PeriodType,oneof" json:"quantity_benefits_period_type,omitempty"`
	// quantity benefits specified period
	QuantityBenefitsSpecifiedPeriod *v11.TimePeriod `protobuf:"bytes,13,opt,name=quantity_benefits_specified_period,json=quantityBenefitsSpecifiedPeriod,proto3,oneof" json:"quantity_benefits_specified_period,omitempty"`
}

func (x *UpdateMembershipDef) Reset() {
	*x = UpdateMembershipDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMembershipDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMembershipDef) ProtoMessage() {}

func (x *UpdateMembershipDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMembershipDef.ProtoReflect.Descriptor instead.
func (*UpdateMembershipDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateMembershipDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateMembershipDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateMembershipDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateMembershipDef) GetStatus() v1.MembershipModel_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.MembershipModel_Status(0)
}

func (x *UpdateMembershipDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *UpdateMembershipDef) GetBillingCyclePeriod() *v11.TimePeriod {
	if x != nil {
		return x.BillingCyclePeriod
	}
	return nil
}

func (x *UpdateMembershipDef) GetPolicy() string {
	if x != nil && x.Policy != nil {
		return *x.Policy
	}
	return ""
}

func (x *UpdateMembershipDef) GetEnableDiscountBenefits() bool {
	if x != nil && x.EnableDiscountBenefits != nil {
		return *x.EnableDiscountBenefits
	}
	return false
}

func (x *UpdateMembershipDef) GetDiscountBenefits() []*UpdateMembershipDiscountBenefitDef {
	if x != nil {
		return x.DiscountBenefits
	}
	return nil
}

func (x *UpdateMembershipDef) GetEnableQuantityBenefits() bool {
	if x != nil && x.EnableQuantityBenefits != nil {
		return *x.EnableQuantityBenefits
	}
	return false
}

func (x *UpdateMembershipDef) GetQuantityBenefits() []*UpdateMembershipQuantityBenefitDef {
	if x != nil {
		return x.QuantityBenefits
	}
	return nil
}

func (x *UpdateMembershipDef) GetQuantityBenefitsPeriodType() v1.PeriodType {
	if x != nil && x.QuantityBenefitsPeriodType != nil {
		return *x.QuantityBenefitsPeriodType
	}
	return v1.PeriodType(0)
}

func (x *UpdateMembershipDef) GetQuantityBenefitsSpecifiedPeriod() *v11.TimePeriod {
	if x != nil {
		return x.QuantityBenefitsSpecifiedPeriod
	}
	return nil
}

// UpdateMembershipDiscountBenefitDef
type UpdateMembershipDiscountBenefitDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// is all
	IsAll *bool `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3,oneof" json:"is_all,omitempty"`
	// ids
	TargetIds []int64 `protobuf:"varint,3,rep,packed,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`
	// value
	Value *float64 `protobuf:"fixed64,4,opt,name=value,proto3,oneof" json:"value,omitempty"`
	// unit
	Unit v1.DiscountUnit `protobuf:"varint,5,opt,name=unit,proto3,enum=moego.models.membership.v1.DiscountUnit" json:"unit,omitempty"`
	// target type
	TargetType v1.TargetType `protobuf:"varint,6,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
}

func (x *UpdateMembershipDiscountBenefitDef) Reset() {
	*x = UpdateMembershipDiscountBenefitDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMembershipDiscountBenefitDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMembershipDiscountBenefitDef) ProtoMessage() {}

func (x *UpdateMembershipDiscountBenefitDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMembershipDiscountBenefitDef.ProtoReflect.Descriptor instead.
func (*UpdateMembershipDiscountBenefitDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateMembershipDiscountBenefitDef) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *UpdateMembershipDiscountBenefitDef) GetIsAll() bool {
	if x != nil && x.IsAll != nil {
		return *x.IsAll
	}
	return false
}

func (x *UpdateMembershipDiscountBenefitDef) GetTargetIds() []int64 {
	if x != nil {
		return x.TargetIds
	}
	return nil
}

func (x *UpdateMembershipDiscountBenefitDef) GetValue() float64 {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return 0
}

func (x *UpdateMembershipDiscountBenefitDef) GetUnit() v1.DiscountUnit {
	if x != nil {
		return x.Unit
	}
	return v1.DiscountUnit(0)
}

func (x *UpdateMembershipDiscountBenefitDef) GetTargetType() v1.TargetType {
	if x != nil {
		return x.TargetType
	}
	return v1.TargetType(0)
}

// UpdateMembershipQuantityBenefitDef
type UpdateMembershipQuantityBenefitDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// is limit
	IsLimited *bool `protobuf:"varint,2,opt,name=is_limited,json=isLimited,proto3,oneof" json:"is_limited,omitempty"`
	// count
	LimitedValue *int64 `protobuf:"varint,3,opt,name=limited_value,json=limitedValue,proto3,oneof" json:"limited_value,omitempty"`
	// id
	TargetId *int64 `protobuf:"varint,4,opt,name=target_id,json=targetId,proto3,oneof" json:"target_id,omitempty"`
	// target type
	TargetType v1.TargetType `protobuf:"varint,5,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// target sub type
	TargetSubType v1.TargetSubType `protobuf:"varint,6,opt,name=target_sub_type,json=targetSubType,proto3,enum=moego.models.membership.v1.TargetSubType" json:"target_sub_type,omitempty"`
}

func (x *UpdateMembershipQuantityBenefitDef) Reset() {
	*x = UpdateMembershipQuantityBenefitDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMembershipQuantityBenefitDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMembershipQuantityBenefitDef) ProtoMessage() {}

func (x *UpdateMembershipQuantityBenefitDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMembershipQuantityBenefitDef.ProtoReflect.Descriptor instead.
func (*UpdateMembershipQuantityBenefitDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateMembershipQuantityBenefitDef) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *UpdateMembershipQuantityBenefitDef) GetIsLimited() bool {
	if x != nil && x.IsLimited != nil {
		return *x.IsLimited
	}
	return false
}

func (x *UpdateMembershipQuantityBenefitDef) GetLimitedValue() int64 {
	if x != nil && x.LimitedValue != nil {
		return *x.LimitedValue
	}
	return 0
}

func (x *UpdateMembershipQuantityBenefitDef) GetTargetId() int64 {
	if x != nil && x.TargetId != nil {
		return *x.TargetId
	}
	return 0
}

func (x *UpdateMembershipQuantityBenefitDef) GetTargetType() v1.TargetType {
	if x != nil {
		return x.TargetType
	}
	return v1.TargetType(0)
}

func (x *UpdateMembershipQuantityBenefitDef) GetTargetSubType() v1.TargetSubType {
	if x != nil {
		return x.TargetSubType
	}
	return v1.TargetSubType(0)
}

// Membership
type Membership struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// status
	Status v1.MembershipModel_Status `protobuf:"varint,5,opt,name=status,proto3,enum=moego.models.membership.v1.MembershipModel_Status" json:"status,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,6,opt,name=price,proto3" json:"price,omitempty"`
	// billing cycle
	BillingCyclePeriod *v11.TimePeriod `protobuf:"bytes,7,opt,name=billing_cycle_period,json=billingCyclePeriod,proto3" json:"billing_cycle_period,omitempty"`
	// policy
	Policy string `protobuf:"bytes,8,opt,name=policy,proto3" json:"policy,omitempty"`
	// enable discount benefits
	EnableDiscountBenefits bool `protobuf:"varint,9,opt,name=enable_discount_benefits,json=enableDiscountBenefits,proto3" json:"enable_discount_benefits,omitempty"`
	// discount benefits
	DiscountBenefits []*MembershipDiscountBenefit `protobuf:"bytes,10,rep,name=discount_benefits,json=discountBenefits,proto3" json:"discount_benefits,omitempty"`
	// enable quantity benefits
	EnableQuantityBenefits bool `protobuf:"varint,11,opt,name=enable_quantity_benefits,json=enableQuantityBenefits,proto3" json:"enable_quantity_benefits,omitempty"`
	// quantity benefits
	QuantityBenefits []*MembershipQuantityBenefit `protobuf:"bytes,12,rep,name=quantity_benefits,json=quantityBenefits,proto3" json:"quantity_benefits,omitempty"`
	// quantity benefits period type
	QuantityBenefitsPeriodType v1.PeriodType `protobuf:"varint,13,opt,name=quantity_benefits_period_type,json=quantityBenefitsPeriodType,proto3,enum=moego.models.membership.v1.PeriodType" json:"quantity_benefits_period_type,omitempty"`
	// quantity benefits specified period
	QuantityBenefitsSpecifiedPeriod *v11.TimePeriod `protobuf:"bytes,14,opt,name=quantity_benefits_specified_period,json=quantityBenefitsSpecifiedPeriod,proto3" json:"quantity_benefits_specified_period,omitempty"`
	// create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// push time
	PushedAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=pushed_at,json=pushedAt,proto3" json:"pushed_at,omitempty"`
}

func (x *Membership) Reset() {
	*x = Membership{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Membership) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Membership) ProtoMessage() {}

func (x *Membership) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Membership.ProtoReflect.Descriptor instead.
func (*Membership) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{6}
}

func (x *Membership) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Membership) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *Membership) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Membership) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Membership) GetStatus() v1.MembershipModel_Status {
	if x != nil {
		return x.Status
	}
	return v1.MembershipModel_Status(0)
}

func (x *Membership) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *Membership) GetBillingCyclePeriod() *v11.TimePeriod {
	if x != nil {
		return x.BillingCyclePeriod
	}
	return nil
}

func (x *Membership) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *Membership) GetEnableDiscountBenefits() bool {
	if x != nil {
		return x.EnableDiscountBenefits
	}
	return false
}

func (x *Membership) GetDiscountBenefits() []*MembershipDiscountBenefit {
	if x != nil {
		return x.DiscountBenefits
	}
	return nil
}

func (x *Membership) GetEnableQuantityBenefits() bool {
	if x != nil {
		return x.EnableQuantityBenefits
	}
	return false
}

func (x *Membership) GetQuantityBenefits() []*MembershipQuantityBenefit {
	if x != nil {
		return x.QuantityBenefits
	}
	return nil
}

func (x *Membership) GetQuantityBenefitsPeriodType() v1.PeriodType {
	if x != nil {
		return x.QuantityBenefitsPeriodType
	}
	return v1.PeriodType(0)
}

func (x *Membership) GetQuantityBenefitsSpecifiedPeriod() *v11.TimePeriod {
	if x != nil {
		return x.QuantityBenefitsSpecifiedPeriod
	}
	return nil
}

func (x *Membership) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Membership) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Membership) GetPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PushedAt
	}
	return nil
}

// MembershipDiscountBenefit
type MembershipDiscountBenefit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// is all
	IsAll bool `protobuf:"varint,3,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// ids
	TargetIds []int64 `protobuf:"varint,4,rep,packed,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`
	// value
	Value float64 `protobuf:"fixed64,5,opt,name=value,proto3" json:"value,omitempty"`
	// unit
	Unit v1.DiscountUnit `protobuf:"varint,6,opt,name=unit,proto3,enum=moego.models.membership.v1.DiscountUnit" json:"unit,omitempty"`
	// target type
	TargetType v1.TargetType `protobuf:"varint,7,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
}

func (x *MembershipDiscountBenefit) Reset() {
	*x = MembershipDiscountBenefit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipDiscountBenefit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipDiscountBenefit) ProtoMessage() {}

func (x *MembershipDiscountBenefit) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipDiscountBenefit.ProtoReflect.Descriptor instead.
func (*MembershipDiscountBenefit) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{7}
}

func (x *MembershipDiscountBenefit) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MembershipDiscountBenefit) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *MembershipDiscountBenefit) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *MembershipDiscountBenefit) GetTargetIds() []int64 {
	if x != nil {
		return x.TargetIds
	}
	return nil
}

func (x *MembershipDiscountBenefit) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *MembershipDiscountBenefit) GetUnit() v1.DiscountUnit {
	if x != nil {
		return x.Unit
	}
	return v1.DiscountUnit(0)
}

func (x *MembershipDiscountBenefit) GetTargetType() v1.TargetType {
	if x != nil {
		return x.TargetType
	}
	return v1.TargetType(0)
}

// MembershipQuantityBenefit
type MembershipQuantityBenefit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// is limit
	IsLimited bool `protobuf:"varint,3,opt,name=is_limited,json=isLimited,proto3" json:"is_limited,omitempty"`
	// count
	LimitedValue int64 `protobuf:"varint,4,opt,name=limited_value,json=limitedValue,proto3" json:"limited_value,omitempty"`
	// target id
	TargetId int64 `protobuf:"varint,5,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// target type
	TargetType v1.TargetType `protobuf:"varint,6,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// target sub type
	TargetSubType v1.TargetSubType `protobuf:"varint,7,opt,name=target_sub_type,json=targetSubType,proto3,enum=moego.models.membership.v1.TargetSubType" json:"target_sub_type,omitempty"`
}

func (x *MembershipQuantityBenefit) Reset() {
	*x = MembershipQuantityBenefit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipQuantityBenefit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipQuantityBenefit) ProtoMessage() {}

func (x *MembershipQuantityBenefit) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipQuantityBenefit.ProtoReflect.Descriptor instead.
func (*MembershipQuantityBenefit) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{8}
}

func (x *MembershipQuantityBenefit) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MembershipQuantityBenefit) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *MembershipQuantityBenefit) GetIsLimited() bool {
	if x != nil {
		return x.IsLimited
	}
	return false
}

func (x *MembershipQuantityBenefit) GetLimitedValue() int64 {
	if x != nil {
		return x.LimitedValue
	}
	return 0
}

func (x *MembershipQuantityBenefit) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *MembershipQuantityBenefit) GetTargetType() v1.TargetType {
	if x != nil {
		return x.TargetType
	}
	return v1.TargetType(0)
}

func (x *MembershipQuantityBenefit) GetTargetSubType() v1.TargetSubType {
	if x != nil {
		return x.TargetSubType
	}
	return v1.TargetSubType(0)
}

// CreatePackageDef
type CreatePackageDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,3,opt,name=price,proto3" json:"price,omitempty"`
	// total value
	TotalValue *money.Money `protobuf:"bytes,4,opt,name=total_value,json=totalValue,proto3" json:"total_value,omitempty"`
	// tax id
	TaxId *int64 `protobuf:"varint,5,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// enable online booking
	EnableOnlineBooking bool `protobuf:"varint,7,opt,name=enable_online_booking,json=enableOnlineBooking,proto3" json:"enable_online_booking,omitempty"`
	// expiration days
	ExpirationDays *int32 `protobuf:"varint,8,opt,name=expiration_days,json=expirationDays,proto3,oneof" json:"expiration_days,omitempty"`
	// items
	Items []*CreatePackageItemDef `protobuf:"bytes,9,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *CreatePackageDef) Reset() {
	*x = CreatePackageDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePackageDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePackageDef) ProtoMessage() {}

func (x *CreatePackageDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePackageDef.ProtoReflect.Descriptor instead.
func (*CreatePackageDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{9}
}

func (x *CreatePackageDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreatePackageDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreatePackageDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *CreatePackageDef) GetTotalValue() *money.Money {
	if x != nil {
		return x.TotalValue
	}
	return nil
}

func (x *CreatePackageDef) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

func (x *CreatePackageDef) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CreatePackageDef) GetEnableOnlineBooking() bool {
	if x != nil {
		return x.EnableOnlineBooking
	}
	return false
}

func (x *CreatePackageDef) GetExpirationDays() int32 {
	if x != nil && x.ExpirationDays != nil {
		return *x.ExpirationDays
	}
	return 0
}

func (x *CreatePackageDef) GetItems() []*CreatePackageItemDef {
	if x != nil {
		return x.Items
	}
	return nil
}

// CreatePackageItemDef
type CreatePackageItemDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// quantity
	Quantity int32 `protobuf:"varint,1,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *CreatePackageItemDef) Reset() {
	*x = CreatePackageItemDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePackageItemDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePackageItemDef) ProtoMessage() {}

func (x *CreatePackageItemDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePackageItemDef.ProtoReflect.Descriptor instead.
func (*CreatePackageItemDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{10}
}

func (x *CreatePackageItemDef) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *CreatePackageItemDef) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// UpdatePackageDef
type UpdatePackageDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// description
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,4,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// total value
	TotalValue *money.Money `protobuf:"bytes,5,opt,name=total_value,json=totalValue,proto3,oneof" json:"total_value,omitempty"`
	// tax id
	TaxId *int64 `protobuf:"varint,6,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// is active
	IsActive *bool `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// enable online booking
	EnableOnlineBooking *bool `protobuf:"varint,8,opt,name=enable_online_booking,json=enableOnlineBooking,proto3,oneof" json:"enable_online_booking,omitempty"`
	// expiration days
	ExpirationDays *int32 `protobuf:"varint,9,opt,name=expiration_days,json=expirationDays,proto3,oneof" json:"expiration_days,omitempty"`
	// items
	Items []*UpdatePackageItemDef `protobuf:"bytes,10,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *UpdatePackageDef) Reset() {
	*x = UpdatePackageDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePackageDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePackageDef) ProtoMessage() {}

func (x *UpdatePackageDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePackageDef.ProtoReflect.Descriptor instead.
func (*UpdatePackageDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{11}
}

func (x *UpdatePackageDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePackageDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdatePackageDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdatePackageDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *UpdatePackageDef) GetTotalValue() *money.Money {
	if x != nil {
		return x.TotalValue
	}
	return nil
}

func (x *UpdatePackageDef) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

func (x *UpdatePackageDef) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *UpdatePackageDef) GetEnableOnlineBooking() bool {
	if x != nil && x.EnableOnlineBooking != nil {
		return *x.EnableOnlineBooking
	}
	return false
}

func (x *UpdatePackageDef) GetExpirationDays() int32 {
	if x != nil && x.ExpirationDays != nil {
		return *x.ExpirationDays
	}
	return 0
}

func (x *UpdatePackageDef) GetItems() []*UpdatePackageItemDef {
	if x != nil {
		return x.Items
	}
	return nil
}

// UpdatePackageItemDef
type UpdatePackageItemDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// quantity
	Quantity *int32 `protobuf:"varint,2,opt,name=quantity,proto3,oneof" json:"quantity,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,3,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *UpdatePackageItemDef) Reset() {
	*x = UpdatePackageItemDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePackageItemDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePackageItemDef) ProtoMessage() {}

func (x *UpdatePackageItemDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePackageItemDef.ProtoReflect.Descriptor instead.
func (*UpdatePackageItemDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{12}
}

func (x *UpdatePackageItemDef) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *UpdatePackageItemDef) GetQuantity() int32 {
	if x != nil && x.Quantity != nil {
		return *x.Quantity
	}
	return 0
}

func (x *UpdatePackageItemDef) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// Package
type Package struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,5,opt,name=price,proto3" json:"price,omitempty"`
	// total value
	TotalValue *money.Money `protobuf:"bytes,6,opt,name=total_value,json=totalValue,proto3" json:"total_value,omitempty"`
	// tax id
	TaxId *int64 `protobuf:"varint,7,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// enable online booking
	EnableOnlineBooking bool `protobuf:"varint,9,opt,name=enable_online_booking,json=enableOnlineBooking,proto3" json:"enable_online_booking,omitempty"`
	// expiration days
	ExpirationDays *int32 `protobuf:"varint,10,opt,name=expiration_days,json=expirationDays,proto3,oneof" json:"expiration_days,omitempty"`
	// items
	Items []*PackageItem `protobuf:"bytes,11,rep,name=items,proto3" json:"items,omitempty"`
	// create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// push time
	PushedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=pushed_at,json=pushedAt,proto3" json:"pushed_at,omitempty"`
}

func (x *Package) Reset() {
	*x = Package{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package) ProtoMessage() {}

func (x *Package) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package.ProtoReflect.Descriptor instead.
func (*Package) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{13}
}

func (x *Package) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Package) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *Package) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Package) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Package) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *Package) GetTotalValue() *money.Money {
	if x != nil {
		return x.TotalValue
	}
	return nil
}

func (x *Package) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

func (x *Package) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Package) GetEnableOnlineBooking() bool {
	if x != nil {
		return x.EnableOnlineBooking
	}
	return false
}

func (x *Package) GetExpirationDays() int32 {
	if x != nil && x.ExpirationDays != nil {
		return *x.ExpirationDays
	}
	return 0
}

func (x *Package) GetItems() []*PackageItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Package) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Package) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Package) GetPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PushedAt
	}
	return nil
}

// PackageItem
type PackageItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// package id
	PackageId int64 `protobuf:"varint,3,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	// quantity
	Quantity int32 `protobuf:"varint,4,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,5,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *PackageItem) Reset() {
	*x = PackageItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PackageItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageItem) ProtoMessage() {}

func (x *PackageItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageItem.ProtoReflect.Descriptor instead.
func (*PackageItem) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{14}
}

func (x *PackageItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PackageItem) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *PackageItem) GetPackageId() int64 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *PackageItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *PackageItem) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// CreateDiscountDef
type CreateDiscountDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount code
	DiscountCode string `protobuf:"bytes,1,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
	// description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// discount amount
	Amount *money.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// discount type
	Type v12.DiscountCodeType `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.marketing.v1.DiscountCodeType" json:"type,omitempty"`
	// start date
	StartDate *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// limit usage
	LimitUsage int32 `protobuf:"varint,7,opt,name=limit_usage,json=limitUsage,proto3" json:"limit_usage,omitempty"`
	// limit number per client
	LimitNumberPerClient int32 `protobuf:"varint,8,opt,name=limit_number_per_client,json=limitNumberPerClient,proto3" json:"limit_number_per_client,omitempty"`
	// limit budget
	LimitBudget int32 `protobuf:"varint,9,opt,name=limit_budget,json=limitBudget,proto3" json:"limit_budget,omitempty"`
	// auto apply association
	AutoApplyAssociation bool `protobuf:"varint,10,opt,name=auto_apply_association,json=autoApplyAssociation,proto3" json:"auto_apply_association,omitempty"`
	// enable online booking
	EnableOnlineBooking bool `protobuf:"varint,11,opt,name=enable_online_booking,json=enableOnlineBooking,proto3" json:"enable_online_booking,omitempty"`
	// expiry type
	ExpiryType v12.ExpiryType `protobuf:"varint,12,opt,name=expiry_type,json=expiryType,proto3,enum=moego.models.marketing.v1.ExpiryType" json:"expiry_type,omitempty"`
	// expiry time
	ExpiryTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=expiry_time,json=expiryTime,proto3,oneof" json:"expiry_time,omitempty"`
	// limitation
	Limitation *Discount_Limitation `protobuf:"bytes,14,opt,name=limitation,proto3" json:"limitation,omitempty"`
}

func (x *CreateDiscountDef) Reset() {
	*x = CreateDiscountDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDiscountDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDiscountDef) ProtoMessage() {}

func (x *CreateDiscountDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDiscountDef.ProtoReflect.Descriptor instead.
func (*CreateDiscountDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{15}
}

func (x *CreateDiscountDef) GetDiscountCode() string {
	if x != nil {
		return x.DiscountCode
	}
	return ""
}

func (x *CreateDiscountDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateDiscountDef) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *CreateDiscountDef) GetType() v12.DiscountCodeType {
	if x != nil {
		return x.Type
	}
	return v12.DiscountCodeType(0)
}

func (x *CreateDiscountDef) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *CreateDiscountDef) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *CreateDiscountDef) GetLimitUsage() int32 {
	if x != nil {
		return x.LimitUsage
	}
	return 0
}

func (x *CreateDiscountDef) GetLimitNumberPerClient() int32 {
	if x != nil {
		return x.LimitNumberPerClient
	}
	return 0
}

func (x *CreateDiscountDef) GetLimitBudget() int32 {
	if x != nil {
		return x.LimitBudget
	}
	return 0
}

func (x *CreateDiscountDef) GetAutoApplyAssociation() bool {
	if x != nil {
		return x.AutoApplyAssociation
	}
	return false
}

func (x *CreateDiscountDef) GetEnableOnlineBooking() bool {
	if x != nil {
		return x.EnableOnlineBooking
	}
	return false
}

func (x *CreateDiscountDef) GetExpiryType() v12.ExpiryType {
	if x != nil {
		return x.ExpiryType
	}
	return v12.ExpiryType(0)
}

func (x *CreateDiscountDef) GetExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTime
	}
	return nil
}

func (x *CreateDiscountDef) GetLimitation() *Discount_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

// UpdateDiscountDef
type UpdateDiscountDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount code
	DiscountCode *string `protobuf:"bytes,1,opt,name=discount_code,json=discountCode,proto3,oneof" json:"discount_code,omitempty"`
	// description
	Description *string `protobuf:"bytes,2,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// discount amount
	Amount *money.Money `protobuf:"bytes,3,opt,name=amount,proto3,oneof" json:"amount,omitempty"`
	// discount type
	Type v12.DiscountCodeType `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.marketing.v1.DiscountCodeType" json:"type,omitempty"`
	// start date
	StartDate *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// limit usage
	LimitUsage *int32 `protobuf:"varint,7,opt,name=limit_usage,json=limitUsage,proto3,oneof" json:"limit_usage,omitempty"`
	// limit number per client
	LimitNumberPerClient *int32 `protobuf:"varint,8,opt,name=limit_number_per_client,json=limitNumberPerClient,proto3,oneof" json:"limit_number_per_client,omitempty"`
	// limit budget
	LimitBudget *int32 `protobuf:"varint,9,opt,name=limit_budget,json=limitBudget,proto3,oneof" json:"limit_budget,omitempty"`
	// auto apply association
	AutoApplyAssociation *bool `protobuf:"varint,10,opt,name=auto_apply_association,json=autoApplyAssociation,proto3,oneof" json:"auto_apply_association,omitempty"`
	// enable online booking
	EnableOnlineBooking *bool `protobuf:"varint,11,opt,name=enable_online_booking,json=enableOnlineBooking,proto3,oneof" json:"enable_online_booking,omitempty"`
	// expiry type
	ExpiryType v12.ExpiryType `protobuf:"varint,12,opt,name=expiry_type,json=expiryType,proto3,enum=moego.models.marketing.v1.ExpiryType" json:"expiry_type,omitempty"`
	// expiry time
	ExpiryTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=expiry_time,json=expiryTime,proto3,oneof" json:"expiry_time,omitempty"`
	// status
	Status v12.DiscountCodeStatus `protobuf:"varint,14,opt,name=status,proto3,enum=moego.models.marketing.v1.DiscountCodeStatus" json:"status,omitempty"`
	// limitation
	Limitation *Discount_Limitation `protobuf:"bytes,15,opt,name=limitation,proto3" json:"limitation,omitempty"`
}

func (x *UpdateDiscountDef) Reset() {
	*x = UpdateDiscountDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDiscountDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDiscountDef) ProtoMessage() {}

func (x *UpdateDiscountDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDiscountDef.ProtoReflect.Descriptor instead.
func (*UpdateDiscountDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateDiscountDef) GetDiscountCode() string {
	if x != nil && x.DiscountCode != nil {
		return *x.DiscountCode
	}
	return ""
}

func (x *UpdateDiscountDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateDiscountDef) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *UpdateDiscountDef) GetType() v12.DiscountCodeType {
	if x != nil {
		return x.Type
	}
	return v12.DiscountCodeType(0)
}

func (x *UpdateDiscountDef) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *UpdateDiscountDef) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *UpdateDiscountDef) GetLimitUsage() int32 {
	if x != nil && x.LimitUsage != nil {
		return *x.LimitUsage
	}
	return 0
}

func (x *UpdateDiscountDef) GetLimitNumberPerClient() int32 {
	if x != nil && x.LimitNumberPerClient != nil {
		return *x.LimitNumberPerClient
	}
	return 0
}

func (x *UpdateDiscountDef) GetLimitBudget() int32 {
	if x != nil && x.LimitBudget != nil {
		return *x.LimitBudget
	}
	return 0
}

func (x *UpdateDiscountDef) GetAutoApplyAssociation() bool {
	if x != nil && x.AutoApplyAssociation != nil {
		return *x.AutoApplyAssociation
	}
	return false
}

func (x *UpdateDiscountDef) GetEnableOnlineBooking() bool {
	if x != nil && x.EnableOnlineBooking != nil {
		return *x.EnableOnlineBooking
	}
	return false
}

func (x *UpdateDiscountDef) GetExpiryType() v12.ExpiryType {
	if x != nil {
		return x.ExpiryType
	}
	return v12.ExpiryType(0)
}

func (x *UpdateDiscountDef) GetExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTime
	}
	return nil
}

func (x *UpdateDiscountDef) GetStatus() v12.DiscountCodeStatus {
	if x != nil {
		return x.Status
	}
	return v12.DiscountCodeStatus(0)
}

func (x *UpdateDiscountDef) GetLimitation() *Discount_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

// Discount
type Discount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// discount code
	DiscountCode string `protobuf:"bytes,3,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
	// description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// discount amount
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// discount type
	Type v12.DiscountCodeType `protobuf:"varint,6,opt,name=type,proto3,enum=moego.models.marketing.v1.DiscountCodeType" json:"type,omitempty"`
	// start date
	StartDate *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// limit usage
	LimitUsage int32 `protobuf:"varint,9,opt,name=limit_usage,json=limitUsage,proto3" json:"limit_usage,omitempty"`
	// limit number per client
	LimitNumberPerClient int32 `protobuf:"varint,10,opt,name=limit_number_per_client,json=limitNumberPerClient,proto3" json:"limit_number_per_client,omitempty"`
	// limit budget
	LimitBudget int32 `protobuf:"varint,11,opt,name=limit_budget,json=limitBudget,proto3" json:"limit_budget,omitempty"`
	// auto apply association
	AutoApplyAssociation bool `protobuf:"varint,12,opt,name=auto_apply_association,json=autoApplyAssociation,proto3" json:"auto_apply_association,omitempty"`
	// enable online booking
	EnableOnlineBooking bool `protobuf:"varint,13,opt,name=enable_online_booking,json=enableOnlineBooking,proto3" json:"enable_online_booking,omitempty"`
	// status
	Status v12.DiscountCodeStatus `protobuf:"varint,14,opt,name=status,proto3,enum=moego.models.marketing.v1.DiscountCodeStatus" json:"status,omitempty"`
	// expiry type
	ExpiryType v12.ExpiryType `protobuf:"varint,15,opt,name=expiry_type,json=expiryType,proto3,enum=moego.models.marketing.v1.ExpiryType" json:"expiry_type,omitempty"`
	// expiry time
	ExpiryTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=expiry_time,json=expiryTime,proto3" json:"expiry_time,omitempty"`
	// create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// push time
	PushedAt *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=pushed_at,json=pushedAt,proto3" json:"pushed_at,omitempty"`
	// limitation
	Limitation *Discount_Limitation `protobuf:"bytes,20,opt,name=limitation,proto3" json:"limitation,omitempty"`
}

func (x *Discount) Reset() {
	*x = Discount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Discount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Discount) ProtoMessage() {}

func (x *Discount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Discount.ProtoReflect.Descriptor instead.
func (*Discount) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{17}
}

func (x *Discount) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Discount) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *Discount) GetDiscountCode() string {
	if x != nil {
		return x.DiscountCode
	}
	return ""
}

func (x *Discount) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Discount) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *Discount) GetType() v12.DiscountCodeType {
	if x != nil {
		return x.Type
	}
	return v12.DiscountCodeType(0)
}

func (x *Discount) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *Discount) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *Discount) GetLimitUsage() int32 {
	if x != nil {
		return x.LimitUsage
	}
	return 0
}

func (x *Discount) GetLimitNumberPerClient() int32 {
	if x != nil {
		return x.LimitNumberPerClient
	}
	return 0
}

func (x *Discount) GetLimitBudget() int32 {
	if x != nil {
		return x.LimitBudget
	}
	return 0
}

func (x *Discount) GetAutoApplyAssociation() bool {
	if x != nil {
		return x.AutoApplyAssociation
	}
	return false
}

func (x *Discount) GetEnableOnlineBooking() bool {
	if x != nil {
		return x.EnableOnlineBooking
	}
	return false
}

func (x *Discount) GetStatus() v12.DiscountCodeStatus {
	if x != nil {
		return x.Status
	}
	return v12.DiscountCodeStatus(0)
}

func (x *Discount) GetExpiryType() v12.ExpiryType {
	if x != nil {
		return x.ExpiryType
	}
	return v12.ExpiryType(0)
}

func (x *Discount) GetExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTime
	}
	return nil
}

func (x *Discount) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Discount) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Discount) GetPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PushedAt
	}
	return nil
}

func (x *Discount) GetLimitation() *Discount_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

// service & add-on limitation
type ServiceAddOnLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// allowed all services
	AllowedAllServices bool `protobuf:"varint,1,opt,name=allowed_all_services,json=allowedAllServices,proto3" json:"allowed_all_services,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// add-on ids
	AddOnIds []int64 `protobuf:"varint,3,rep,packed,name=add_on_ids,json=addOnIds,proto3" json:"add_on_ids,omitempty"`
}

func (x *ServiceAddOnLimitation) Reset() {
	*x = ServiceAddOnLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceAddOnLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAddOnLimitation) ProtoMessage() {}

func (x *ServiceAddOnLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAddOnLimitation.ProtoReflect.Descriptor instead.
func (*ServiceAddOnLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{18}
}

func (x *ServiceAddOnLimitation) GetAllowedAllServices() bool {
	if x != nil {
		return x.AllowedAllServices
	}
	return false
}

func (x *ServiceAddOnLimitation) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *ServiceAddOnLimitation) GetAddOnIds() []int64 {
	if x != nil {
		return x.AddOnIds
	}
	return nil
}

// product limitation
type ProductLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// allowed all products
	AllowedAllProducts bool `protobuf:"varint,1,opt,name=allowed_all_products,json=allowedAllProducts,proto3" json:"allowed_all_products,omitempty"`
	// product ids
	ProductIds []int64 `protobuf:"varint,2,rep,packed,name=product_ids,json=productIds,proto3" json:"product_ids,omitempty"`
}

func (x *ProductLimitation) Reset() {
	*x = ProductLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductLimitation) ProtoMessage() {}

func (x *ProductLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductLimitation.ProtoReflect.Descriptor instead.
func (*ProductLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{19}
}

func (x *ProductLimitation) GetAllowedAllProducts() bool {
	if x != nil {
		return x.AllowedAllProducts
	}
	return false
}

func (x *ProductLimitation) GetProductIds() []int64 {
	if x != nil {
		return x.ProductIds
	}
	return nil
}

// client limitation
type ClientLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// allowed all clients
	AllowedAllClients bool `protobuf:"varint,1,opt,name=allowed_all_clients,json=allowedAllClients,proto3" json:"allowed_all_clients,omitempty"`
	// allowed new clients
	AllowedNewClients bool `protobuf:"varint,2,opt,name=allowed_new_clients,json=allowedNewClients,proto3" json:"allowed_new_clients,omitempty"`
	// client groups, json format
	ClientGroups string `protobuf:"bytes,3,opt,name=client_groups,json=clientGroups,proto3" json:"client_groups,omitempty"`
}

func (x *ClientLimitation) Reset() {
	*x = ClientLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientLimitation) ProtoMessage() {}

func (x *ClientLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientLimitation.ProtoReflect.Descriptor instead.
func (*ClientLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{20}
}

func (x *ClientLimitation) GetAllowedAllClients() bool {
	if x != nil {
		return x.AllowedAllClients
	}
	return false
}

func (x *ClientLimitation) GetAllowedNewClients() bool {
	if x != nil {
		return x.AllowedNewClients
	}
	return false
}

func (x *ClientLimitation) GetClientGroups() string {
	if x != nil {
		return x.ClientGroups
	}
	return ""
}

// limitation
type Discount_Limitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// item limitation
	ItemLimitation *Discount_Limitation_ItemLimitation `protobuf:"bytes,1,opt,name=item_limitation,json=itemLimitation,proto3" json:"item_limitation,omitempty"`
	// client limitation
	ClientLimitation *ClientLimitation `protobuf:"bytes,2,opt,name=client_limitation,json=clientLimitation,proto3" json:"client_limitation,omitempty"`
}

func (x *Discount_Limitation) Reset() {
	*x = Discount_Limitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Discount_Limitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Discount_Limitation) ProtoMessage() {}

func (x *Discount_Limitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Discount_Limitation.ProtoReflect.Descriptor instead.
func (*Discount_Limitation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{17, 0}
}

func (x *Discount_Limitation) GetItemLimitation() *Discount_Limitation_ItemLimitation {
	if x != nil {
		return x.ItemLimitation
	}
	return nil
}

func (x *Discount_Limitation) GetClientLimitation() *ClientLimitation {
	if x != nil {
		return x.ClientLimitation
	}
	return nil
}

// item limitation
type Discount_Limitation_ItemLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// allowed all items
	AllowedAllItems bool `protobuf:"varint,1,opt,name=allowed_all_items,json=allowedAllItems,proto3" json:"allowed_all_items,omitempty"`
	// service_add_on_limitation
	ServiceAddOnLimitation *ServiceAddOnLimitation `protobuf:"bytes,2,opt,name=service_add_on_limitation,json=serviceAddOnLimitation,proto3" json:"service_add_on_limitation,omitempty"`
	// product limitation
	ProductLimitation *ProductLimitation `protobuf:"bytes,3,opt,name=product_limitation,json=productLimitation,proto3" json:"product_limitation,omitempty"`
}

func (x *Discount_Limitation_ItemLimitation) Reset() {
	*x = Discount_Limitation_ItemLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Discount_Limitation_ItemLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Discount_Limitation_ItemLimitation) ProtoMessage() {}

func (x *Discount_Limitation_ItemLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Discount_Limitation_ItemLimitation.ProtoReflect.Descriptor instead.
func (*Discount_Limitation_ItemLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP(), []int{17, 0, 0}
}

func (x *Discount_Limitation_ItemLimitation) GetAllowedAllItems() bool {
	if x != nil {
		return x.AllowedAllItems
	}
	return false
}

func (x *Discount_Limitation_ItemLimitation) GetServiceAddOnLimitation() *ServiceAddOnLimitation {
	if x != nil {
		return x.ServiceAddOnLimitation
	}
	return nil
}

func (x *Discount_Limitation_ItemLimitation) GetProductLimitation() *ProductLimitation {
	if x != nil {
		return x.ProductLimitation
	}
	return nil
}

var File_moego_models_enterprise_v1_loyalty_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_loyalty_models_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x79,
	0x61, 0x6c, 0x74, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x2f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe0, 0x06,
	0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x44, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x4c, 0x0a, 0x14, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x52, 0x12, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x38,
	0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x6b, 0x0a, 0x11, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x44, 0x65, 0x66, 0x52, 0x10, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x51,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12,
	0x6b, 0x0a, 0x11, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x65, 0x66, 0x52, 0x10, 0x71, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x69, 0x0a, 0x1d,
	0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1a, 0x71, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x67, 0x0a, 0x22, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52,
	0x1f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x22, 0x8f, 0x02, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x44, 0x65, 0x66, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x6c,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x48, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x53, 0x0a,
	0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xb7, 0x02, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0b, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x5b, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x75, 0x62, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x22, 0xe0, 0x08, 0x0a,
	0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x44, 0x65, 0x66, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x4f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x02, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x51, 0x0a, 0x14, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x48, 0x04,
	0x52, 0x12, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x88, 0x01, 0x01, 0x12, 0x3d, 0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x6b, 0x0a, 0x11, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x65, 0x66, 0x52, 0x10,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x12, 0x3d, 0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x07, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x51, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x6b, 0x0a, 0x11, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x65, 0x66, 0x52, 0x10, 0x71, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x6e, 0x0a, 0x1d,
	0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x48, 0x08, 0x52, 0x1a, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x6c, 0x0a, 0x22,
	0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x48, 0x09, 0x52, 0x1f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0x1b, 0x0a, 0x19,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x42, 0x20, 0x0a, 0x1e, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x25, 0x0a, 0x23, 0x5f, 0x71, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22,
	0xc6, 0x02, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x44, 0x65, 0x66, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x69,
	0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x05, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x46, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x51, 0x0a, 0x0b, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x05, 0x0a, 0x03,
	0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x91, 0x03, 0x0a, 0x22, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x51, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x65, 0x66, 0x12,
	0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x09, 0x69, 0x73, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x02, 0x52, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x53, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5b, 0x0a, 0x0f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53,
	0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x22, 0x92, 0x08, 0x0a,
	0x0a, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x4c, 0x0a, 0x14,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x12, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43,
	0x79, 0x63, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x62, 0x0a, 0x11,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52, 0x10,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x12, 0x38, 0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x62, 0x0a, 0x11, 0x71, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18,
	0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x51, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52, 0x10, 0x71, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x69,
	0x0a, 0x1d, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1a, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x67, 0x0a, 0x22, 0x71, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x52, 0x1f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x73, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x37, 0x0a, 0x09, 0x70, 0x75, 0x73, 0x68,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x41,
	0x74, 0x22, 0xa3, 0x02, 0x0a, 0x19, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x3c, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x47,
	0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xcd, 0x02, 0x0a, 0x19, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0b, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73,
	0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd1, 0x03, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xf4, 0x03, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x12, 0x35, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x22, 0x66, 0x0a, 0x14, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x44, 0x65, 0x66, 0x12, 0x23, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x08,
	0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x73, 0x22, 0xd0, 0x04, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x66, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x64, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xf4, 0x03, 0x48, 0x01, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x48, 0x02, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x04, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a,
	0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x79, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48,
	0x07, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x79,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x44, 0x65, 0x66, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69,
	0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x12,
	0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48,
	0x01, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x42,
	0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x22, 0xfb, 0x04, 0x0a, 0x07, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x06, 0x74, 0x61, 0x78,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05, 0x74, 0x61, 0x78,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x01, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x79,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x3d, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x37, 0x0a, 0x09, 0x70, 0x75, 0x73,
	0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64,
	0x41, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x79,
	0x73, 0x22, 0x9e, 0x01, 0x0a, 0x0b, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x73, 0x22, 0xdb, 0x06, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x12, 0x2e, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x72, 0x03, 0x18, 0xf4, 0x03, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x4b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x17, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x14, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x50, 0x65, 0x72, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x62, 0x75, 0x64,
	0x67, 0x65, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x28, 0x00, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x12,
	0x34, 0x0a, 0x16, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x61, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x14, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x52, 0x0a, 0x0b, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a,
	0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00,
	0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x4f, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x22, 0xeb, 0x08, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x12, 0x33, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x48, 0x00, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xf4, 0x03, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x48, 0x02, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x49, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x0b, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x28, 0x00, 0x40, 0x01, 0x48, 0x03, 0x52, 0x0a, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x55, 0x73, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x17, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x5f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x1a, 0x04, 0x28, 0x00, 0x40, 0x01, 0x48, 0x04, 0x52, 0x14, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x50, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x31, 0x0a, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x62, 0x75, 0x64, 0x67,
	0x65, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x28,
	0x00, 0x40, 0x01, 0x48, 0x05, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x75, 0x64, 0x67,
	0x65, 0x74, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x16, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x37, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x07, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x0b, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x08, 0x52, 0x0a,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4f, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4f,
	0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x42, 0x1a, 0x0a, 0x18,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x70, 0x65,
	0x72, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xf5,
	0x0b, 0x0a, 0x08, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f,
	0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x50, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x75, 0x64, 0x67, 0x65,
	0x74, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x45, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x46, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x37, 0x0a,
	0x09, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x70, 0x75,
	0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x12, 0x4f, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xdc, 0x03, 0x0a, 0x0a, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x67, 0x0a, 0x0f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0e, 0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x59, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x89, 0x02, 0x0a, 0x0e, 0x49,
	0x74, 0x65, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a,
	0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x41, 0x6c, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x6d, 0x0a, 0x19, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x16, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x89, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x61, 0x64, 0x64, 0x4f, 0x6e, 0x49,
	0x64, 0x73, 0x22, 0x66, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6c,
	0x6c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x10, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2e, 0x0a, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x2e, 0x0a, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x4e, 0x65, 0x77, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_loyalty_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_loyalty_models_proto_rawDescData = file_moego_models_enterprise_v1_loyalty_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_loyalty_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_loyalty_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_loyalty_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_loyalty_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_loyalty_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_moego_models_enterprise_v1_loyalty_models_proto_goTypes = []interface{}{
	(*CreateMembershipDef)(nil),                // 0: moego.models.enterprise.v1.CreateMembershipDef
	(*CreateMembershipDiscountBenefitDef)(nil), // 1: moego.models.enterprise.v1.CreateMembershipDiscountBenefitDef
	(*CreateMembershipQuantityBenefitDef)(nil), // 2: moego.models.enterprise.v1.CreateMembershipQuantityBenefitDef
	(*UpdateMembershipDef)(nil),                // 3: moego.models.enterprise.v1.UpdateMembershipDef
	(*UpdateMembershipDiscountBenefitDef)(nil), // 4: moego.models.enterprise.v1.UpdateMembershipDiscountBenefitDef
	(*UpdateMembershipQuantityBenefitDef)(nil), // 5: moego.models.enterprise.v1.UpdateMembershipQuantityBenefitDef
	(*Membership)(nil),                         // 6: moego.models.enterprise.v1.Membership
	(*MembershipDiscountBenefit)(nil),          // 7: moego.models.enterprise.v1.MembershipDiscountBenefit
	(*MembershipQuantityBenefit)(nil),          // 8: moego.models.enterprise.v1.MembershipQuantityBenefit
	(*CreatePackageDef)(nil),                   // 9: moego.models.enterprise.v1.CreatePackageDef
	(*CreatePackageItemDef)(nil),               // 10: moego.models.enterprise.v1.CreatePackageItemDef
	(*UpdatePackageDef)(nil),                   // 11: moego.models.enterprise.v1.UpdatePackageDef
	(*UpdatePackageItemDef)(nil),               // 12: moego.models.enterprise.v1.UpdatePackageItemDef
	(*Package)(nil),                            // 13: moego.models.enterprise.v1.Package
	(*PackageItem)(nil),                        // 14: moego.models.enterprise.v1.PackageItem
	(*CreateDiscountDef)(nil),                  // 15: moego.models.enterprise.v1.CreateDiscountDef
	(*UpdateDiscountDef)(nil),                  // 16: moego.models.enterprise.v1.UpdateDiscountDef
	(*Discount)(nil),                           // 17: moego.models.enterprise.v1.Discount
	(*ServiceAddOnLimitation)(nil),             // 18: moego.models.enterprise.v1.ServiceAddOnLimitation
	(*ProductLimitation)(nil),                  // 19: moego.models.enterprise.v1.ProductLimitation
	(*ClientLimitation)(nil),                   // 20: moego.models.enterprise.v1.ClientLimitation
	(*Discount_Limitation)(nil),                // 21: moego.models.enterprise.v1.Discount.Limitation
	(*Discount_Limitation_ItemLimitation)(nil), // 22: moego.models.enterprise.v1.Discount.Limitation.ItemLimitation
	(v1.MembershipModel_Status)(0),             // 23: moego.models.membership.v1.MembershipModel.Status
	(*money.Money)(nil),                        // 24: google.type.Money
	(*v11.TimePeriod)(nil),                     // 25: moego.utils.v1.TimePeriod
	(v1.PeriodType)(0),                         // 26: moego.models.membership.v1.PeriodType
	(v1.DiscountUnit)(0),                       // 27: moego.models.membership.v1.DiscountUnit
	(v1.TargetType)(0),                         // 28: moego.models.membership.v1.TargetType
	(v1.TargetSubType)(0),                      // 29: moego.models.membership.v1.TargetSubType
	(*timestamppb.Timestamp)(nil),              // 30: google.protobuf.Timestamp
	(v12.DiscountCodeType)(0),                  // 31: moego.models.marketing.v1.DiscountCodeType
	(v12.ExpiryType)(0),                        // 32: moego.models.marketing.v1.ExpiryType
	(v12.DiscountCodeStatus)(0),                // 33: moego.models.marketing.v1.DiscountCodeStatus
}
var file_moego_models_enterprise_v1_loyalty_models_proto_depIdxs = []int32{
	23, // 0: moego.models.enterprise.v1.CreateMembershipDef.status:type_name -> moego.models.membership.v1.MembershipModel.Status
	24, // 1: moego.models.enterprise.v1.CreateMembershipDef.price:type_name -> google.type.Money
	25, // 2: moego.models.enterprise.v1.CreateMembershipDef.billing_cycle_period:type_name -> moego.utils.v1.TimePeriod
	1,  // 3: moego.models.enterprise.v1.CreateMembershipDef.discount_benefits:type_name -> moego.models.enterprise.v1.CreateMembershipDiscountBenefitDef
	2,  // 4: moego.models.enterprise.v1.CreateMembershipDef.quantity_benefits:type_name -> moego.models.enterprise.v1.CreateMembershipQuantityBenefitDef
	26, // 5: moego.models.enterprise.v1.CreateMembershipDef.quantity_benefits_period_type:type_name -> moego.models.membership.v1.PeriodType
	25, // 6: moego.models.enterprise.v1.CreateMembershipDef.quantity_benefits_specified_period:type_name -> moego.utils.v1.TimePeriod
	27, // 7: moego.models.enterprise.v1.CreateMembershipDiscountBenefitDef.unit:type_name -> moego.models.membership.v1.DiscountUnit
	28, // 8: moego.models.enterprise.v1.CreateMembershipDiscountBenefitDef.target_type:type_name -> moego.models.membership.v1.TargetType
	28, // 9: moego.models.enterprise.v1.CreateMembershipQuantityBenefitDef.target_type:type_name -> moego.models.membership.v1.TargetType
	29, // 10: moego.models.enterprise.v1.CreateMembershipQuantityBenefitDef.target_sub_type:type_name -> moego.models.membership.v1.TargetSubType
	23, // 11: moego.models.enterprise.v1.UpdateMembershipDef.status:type_name -> moego.models.membership.v1.MembershipModel.Status
	24, // 12: moego.models.enterprise.v1.UpdateMembershipDef.price:type_name -> google.type.Money
	25, // 13: moego.models.enterprise.v1.UpdateMembershipDef.billing_cycle_period:type_name -> moego.utils.v1.TimePeriod
	4,  // 14: moego.models.enterprise.v1.UpdateMembershipDef.discount_benefits:type_name -> moego.models.enterprise.v1.UpdateMembershipDiscountBenefitDef
	5,  // 15: moego.models.enterprise.v1.UpdateMembershipDef.quantity_benefits:type_name -> moego.models.enterprise.v1.UpdateMembershipQuantityBenefitDef
	26, // 16: moego.models.enterprise.v1.UpdateMembershipDef.quantity_benefits_period_type:type_name -> moego.models.membership.v1.PeriodType
	25, // 17: moego.models.enterprise.v1.UpdateMembershipDef.quantity_benefits_specified_period:type_name -> moego.utils.v1.TimePeriod
	27, // 18: moego.models.enterprise.v1.UpdateMembershipDiscountBenefitDef.unit:type_name -> moego.models.membership.v1.DiscountUnit
	28, // 19: moego.models.enterprise.v1.UpdateMembershipDiscountBenefitDef.target_type:type_name -> moego.models.membership.v1.TargetType
	28, // 20: moego.models.enterprise.v1.UpdateMembershipQuantityBenefitDef.target_type:type_name -> moego.models.membership.v1.TargetType
	29, // 21: moego.models.enterprise.v1.UpdateMembershipQuantityBenefitDef.target_sub_type:type_name -> moego.models.membership.v1.TargetSubType
	23, // 22: moego.models.enterprise.v1.Membership.status:type_name -> moego.models.membership.v1.MembershipModel.Status
	24, // 23: moego.models.enterprise.v1.Membership.price:type_name -> google.type.Money
	25, // 24: moego.models.enterprise.v1.Membership.billing_cycle_period:type_name -> moego.utils.v1.TimePeriod
	7,  // 25: moego.models.enterprise.v1.Membership.discount_benefits:type_name -> moego.models.enterprise.v1.MembershipDiscountBenefit
	8,  // 26: moego.models.enterprise.v1.Membership.quantity_benefits:type_name -> moego.models.enterprise.v1.MembershipQuantityBenefit
	26, // 27: moego.models.enterprise.v1.Membership.quantity_benefits_period_type:type_name -> moego.models.membership.v1.PeriodType
	25, // 28: moego.models.enterprise.v1.Membership.quantity_benefits_specified_period:type_name -> moego.utils.v1.TimePeriod
	30, // 29: moego.models.enterprise.v1.Membership.created_at:type_name -> google.protobuf.Timestamp
	30, // 30: moego.models.enterprise.v1.Membership.updated_at:type_name -> google.protobuf.Timestamp
	30, // 31: moego.models.enterprise.v1.Membership.pushed_at:type_name -> google.protobuf.Timestamp
	27, // 32: moego.models.enterprise.v1.MembershipDiscountBenefit.unit:type_name -> moego.models.membership.v1.DiscountUnit
	28, // 33: moego.models.enterprise.v1.MembershipDiscountBenefit.target_type:type_name -> moego.models.membership.v1.TargetType
	28, // 34: moego.models.enterprise.v1.MembershipQuantityBenefit.target_type:type_name -> moego.models.membership.v1.TargetType
	29, // 35: moego.models.enterprise.v1.MembershipQuantityBenefit.target_sub_type:type_name -> moego.models.membership.v1.TargetSubType
	24, // 36: moego.models.enterprise.v1.CreatePackageDef.price:type_name -> google.type.Money
	24, // 37: moego.models.enterprise.v1.CreatePackageDef.total_value:type_name -> google.type.Money
	10, // 38: moego.models.enterprise.v1.CreatePackageDef.items:type_name -> moego.models.enterprise.v1.CreatePackageItemDef
	24, // 39: moego.models.enterprise.v1.UpdatePackageDef.price:type_name -> google.type.Money
	24, // 40: moego.models.enterprise.v1.UpdatePackageDef.total_value:type_name -> google.type.Money
	12, // 41: moego.models.enterprise.v1.UpdatePackageDef.items:type_name -> moego.models.enterprise.v1.UpdatePackageItemDef
	24, // 42: moego.models.enterprise.v1.Package.price:type_name -> google.type.Money
	24, // 43: moego.models.enterprise.v1.Package.total_value:type_name -> google.type.Money
	14, // 44: moego.models.enterprise.v1.Package.items:type_name -> moego.models.enterprise.v1.PackageItem
	30, // 45: moego.models.enterprise.v1.Package.created_at:type_name -> google.protobuf.Timestamp
	30, // 46: moego.models.enterprise.v1.Package.updated_at:type_name -> google.protobuf.Timestamp
	30, // 47: moego.models.enterprise.v1.Package.pushed_at:type_name -> google.protobuf.Timestamp
	24, // 48: moego.models.enterprise.v1.CreateDiscountDef.amount:type_name -> google.type.Money
	31, // 49: moego.models.enterprise.v1.CreateDiscountDef.type:type_name -> moego.models.marketing.v1.DiscountCodeType
	30, // 50: moego.models.enterprise.v1.CreateDiscountDef.start_date:type_name -> google.protobuf.Timestamp
	30, // 51: moego.models.enterprise.v1.CreateDiscountDef.end_date:type_name -> google.protobuf.Timestamp
	32, // 52: moego.models.enterprise.v1.CreateDiscountDef.expiry_type:type_name -> moego.models.marketing.v1.ExpiryType
	30, // 53: moego.models.enterprise.v1.CreateDiscountDef.expiry_time:type_name -> google.protobuf.Timestamp
	21, // 54: moego.models.enterprise.v1.CreateDiscountDef.limitation:type_name -> moego.models.enterprise.v1.Discount.Limitation
	24, // 55: moego.models.enterprise.v1.UpdateDiscountDef.amount:type_name -> google.type.Money
	31, // 56: moego.models.enterprise.v1.UpdateDiscountDef.type:type_name -> moego.models.marketing.v1.DiscountCodeType
	30, // 57: moego.models.enterprise.v1.UpdateDiscountDef.start_date:type_name -> google.protobuf.Timestamp
	30, // 58: moego.models.enterprise.v1.UpdateDiscountDef.end_date:type_name -> google.protobuf.Timestamp
	32, // 59: moego.models.enterprise.v1.UpdateDiscountDef.expiry_type:type_name -> moego.models.marketing.v1.ExpiryType
	30, // 60: moego.models.enterprise.v1.UpdateDiscountDef.expiry_time:type_name -> google.protobuf.Timestamp
	33, // 61: moego.models.enterprise.v1.UpdateDiscountDef.status:type_name -> moego.models.marketing.v1.DiscountCodeStatus
	21, // 62: moego.models.enterprise.v1.UpdateDiscountDef.limitation:type_name -> moego.models.enterprise.v1.Discount.Limitation
	24, // 63: moego.models.enterprise.v1.Discount.amount:type_name -> google.type.Money
	31, // 64: moego.models.enterprise.v1.Discount.type:type_name -> moego.models.marketing.v1.DiscountCodeType
	30, // 65: moego.models.enterprise.v1.Discount.start_date:type_name -> google.protobuf.Timestamp
	30, // 66: moego.models.enterprise.v1.Discount.end_date:type_name -> google.protobuf.Timestamp
	33, // 67: moego.models.enterprise.v1.Discount.status:type_name -> moego.models.marketing.v1.DiscountCodeStatus
	32, // 68: moego.models.enterprise.v1.Discount.expiry_type:type_name -> moego.models.marketing.v1.ExpiryType
	30, // 69: moego.models.enterprise.v1.Discount.expiry_time:type_name -> google.protobuf.Timestamp
	30, // 70: moego.models.enterprise.v1.Discount.created_at:type_name -> google.protobuf.Timestamp
	30, // 71: moego.models.enterprise.v1.Discount.updated_at:type_name -> google.protobuf.Timestamp
	30, // 72: moego.models.enterprise.v1.Discount.pushed_at:type_name -> google.protobuf.Timestamp
	21, // 73: moego.models.enterprise.v1.Discount.limitation:type_name -> moego.models.enterprise.v1.Discount.Limitation
	22, // 74: moego.models.enterprise.v1.Discount.Limitation.item_limitation:type_name -> moego.models.enterprise.v1.Discount.Limitation.ItemLimitation
	20, // 75: moego.models.enterprise.v1.Discount.Limitation.client_limitation:type_name -> moego.models.enterprise.v1.ClientLimitation
	18, // 76: moego.models.enterprise.v1.Discount.Limitation.ItemLimitation.service_add_on_limitation:type_name -> moego.models.enterprise.v1.ServiceAddOnLimitation
	19, // 77: moego.models.enterprise.v1.Discount.Limitation.ItemLimitation.product_limitation:type_name -> moego.models.enterprise.v1.ProductLimitation
	78, // [78:78] is the sub-list for method output_type
	78, // [78:78] is the sub-list for method input_type
	78, // [78:78] is the sub-list for extension type_name
	78, // [78:78] is the sub-list for extension extendee
	0,  // [0:78] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_loyalty_models_proto_init() }
func file_moego_models_enterprise_v1_loyalty_models_proto_init() {
	if File_moego_models_enterprise_v1_loyalty_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMembershipDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMembershipDiscountBenefitDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMembershipQuantityBenefitDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMembershipDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMembershipDiscountBenefitDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMembershipQuantityBenefitDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Membership); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipDiscountBenefit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipQuantityBenefit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePackageDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePackageItemDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePackageDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePackageItemDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PackageItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDiscountDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDiscountDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Discount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceAddOnLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Discount_Limitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Discount_Limitation_ItemLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes[16].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_loyalty_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_loyalty_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_loyalty_models_proto_depIdxs,
		MessageInfos:      file_moego_models_enterprise_v1_loyalty_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_loyalty_models_proto = out.File
	file_moego_models_enterprise_v1_loyalty_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_loyalty_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_loyalty_models_proto_depIdxs = nil
}
