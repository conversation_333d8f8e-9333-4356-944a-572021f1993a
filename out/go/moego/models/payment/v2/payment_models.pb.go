// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v2/payment_models.proto

package paymentpb

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PaymentStatus 支付状态
type PaymentModel_PaymentStatus int32

const (
	// Unspecified
	PaymentModel_PAYMENT_STATUS_UNSPECIFIED PaymentModel_PaymentStatus = 0
	// Created，这是初始化状态
	PaymentModel_CREATED PaymentModel_PaymentStatus = 1
	// ACCEPTED，支付已被受理，在提交支付凭据后就会变成这个状态，无法撤销
	PaymentModel_ACCEPTED PaymentModel_PaymentStatus = 2
	// Submitted，已向支付渠道提交支付请求
	PaymentModel_SUBMITTED PaymentModel_PaymentStatus = 3
	// SUCCEEDED，支付成功，这是最终状态
	PaymentModel_SUCCEEDED PaymentModel_PaymentStatus = 4
	// Cancelled，支付取消，这是最终状态
	PaymentModel_CANCELLED PaymentModel_PaymentStatus = 5
	// Failed，支付失败，这是最终状态
	PaymentModel_FAILED PaymentModel_PaymentStatus = 6
	// Authorized，只出现于 pre-auth 场景，表示已授权
	PaymentModel_AUTHORIZED PaymentModel_PaymentStatus = 7
)

// Enum value maps for PaymentModel_PaymentStatus.
var (
	PaymentModel_PaymentStatus_name = map[int32]string{
		0: "PAYMENT_STATUS_UNSPECIFIED",
		1: "CREATED",
		2: "ACCEPTED",
		3: "SUBMITTED",
		4: "SUCCEEDED",
		5: "CANCELLED",
		6: "FAILED",
		7: "AUTHORIZED",
	}
	PaymentModel_PaymentStatus_value = map[string]int32{
		"PAYMENT_STATUS_UNSPECIFIED": 0,
		"CREATED":                    1,
		"ACCEPTED":                   2,
		"SUBMITTED":                  3,
		"SUCCEEDED":                  4,
		"CANCELLED":                  5,
		"FAILED":                     6,
		"AUTHORIZED":                 7,
	}
)

func (x PaymentModel_PaymentStatus) Enum() *PaymentModel_PaymentStatus {
	p := new(PaymentModel_PaymentStatus)
	*p = x
	return p
}

func (x PaymentModel_PaymentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentModel_PaymentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_models_proto_enumTypes[0].Descriptor()
}

func (PaymentModel_PaymentStatus) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_models_proto_enumTypes[0]
}

func (x PaymentModel_PaymentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentModel_PaymentStatus.Descriptor instead.
func (PaymentModel_PaymentStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{1, 0}
}

// 支付类型
type PaymentModel_PaymentType int32

const (
	// 未指定
	PaymentModel_PAYMENT_TYPE_UNSPECIFIED PaymentModel_PaymentType = 0
	// Standard 支付
	PaymentModel_STANDARD PaymentModel_PaymentType = 1
	// Pre-Auth 支付
	PaymentModel_PRE_AUTH PaymentModel_PaymentType = 2
)

// Enum value maps for PaymentModel_PaymentType.
var (
	PaymentModel_PaymentType_name = map[int32]string{
		0: "PAYMENT_TYPE_UNSPECIFIED",
		1: "STANDARD",
		2: "PRE_AUTH",
	}
	PaymentModel_PaymentType_value = map[string]int32{
		"PAYMENT_TYPE_UNSPECIFIED": 0,
		"STANDARD":                 1,
		"PRE_AUTH":                 2,
	}
)

func (x PaymentModel_PaymentType) Enum() *PaymentModel_PaymentType {
	p := new(PaymentModel_PaymentType)
	*p = x
	return p
}

func (x PaymentModel_PaymentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentModel_PaymentType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_models_proto_enumTypes[1].Descriptor()
}

func (PaymentModel_PaymentType) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_models_proto_enumTypes[1]
}

func (x PaymentModel_PaymentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentModel_PaymentType.Descriptor instead.
func (PaymentModel_PaymentType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{1, 1}
}

// 退款状态
type PaymentModel_Refund_Status int32

const (
	// 未退款
	PaymentModel_Refund_STATUS_UNSPECIFIED PaymentModel_Refund_Status = 0
	// 部分退款
	PaymentModel_Refund_PARTIAL_REFUND PaymentModel_Refund_Status = 1
	// 全额退款
	PaymentModel_Refund_FULL_REFUND PaymentModel_Refund_Status = 2
)

// Enum value maps for PaymentModel_Refund_Status.
var (
	PaymentModel_Refund_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PARTIAL_REFUND",
		2: "FULL_REFUND",
	}
	PaymentModel_Refund_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PARTIAL_REFUND":     1,
		"FULL_REFUND":        2,
	}
)

func (x PaymentModel_Refund_Status) Enum() *PaymentModel_Refund_Status {
	p := new(PaymentModel_Refund_Status)
	*p = x
	return p
}

func (x PaymentModel_Refund_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentModel_Refund_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_models_proto_enumTypes[2].Descriptor()
}

func (PaymentModel_Refund_Status) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_models_proto_enumTypes[2]
}

func (x PaymentModel_Refund_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentModel_Refund_Status.Descriptor instead.
func (PaymentModel_Refund_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{1, 0, 0}
}

// 支付方式类型
type PaymentMethod_MethodType int32

const (
	// 未指定
	PaymentMethod_METHOD_TYPE_UNSPECIFIED PaymentMethod_MethodType = 0
	// 卡支付
	PaymentMethod_CARD PaymentMethod_MethodType = 1
	// 已经存储了的支付方式
	PaymentMethod_RECURRING_PAYMENT_METHOD PaymentMethod_MethodType = 2
	// 纯记账支付
	PaymentMethod_BOOK_ENTRY PaymentMethod_MethodType = 3
	// terminal
	PaymentMethod_TERMINAL PaymentMethod_MethodType = 4
)

// Enum value maps for PaymentMethod_MethodType.
var (
	PaymentMethod_MethodType_name = map[int32]string{
		0: "METHOD_TYPE_UNSPECIFIED",
		1: "CARD",
		2: "RECURRING_PAYMENT_METHOD",
		3: "BOOK_ENTRY",
		4: "TERMINAL",
	}
	PaymentMethod_MethodType_value = map[string]int32{
		"METHOD_TYPE_UNSPECIFIED":  0,
		"CARD":                     1,
		"RECURRING_PAYMENT_METHOD": 2,
		"BOOK_ENTRY":               3,
		"TERMINAL":                 4,
	}
)

func (x PaymentMethod_MethodType) Enum() *PaymentMethod_MethodType {
	p := new(PaymentMethod_MethodType)
	*p = x
	return p
}

func (x PaymentMethod_MethodType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentMethod_MethodType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_models_proto_enumTypes[3].Descriptor()
}

func (PaymentMethod_MethodType) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_models_proto_enumTypes[3]
}

func (x PaymentMethod_MethodType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentMethod_MethodType.Descriptor instead.
func (PaymentMethod_MethodType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3, 0}
}

// 退款状态
type RefundModel_RefundStatus int32

const (
	// Unspecified
	RefundModel_REFUND_STATUS_UNSPECIFIED RefundModel_RefundStatus = 0
	// Created，这是初始化状态
	RefundModel_CREATED RefundModel_RefundStatus = 1
	// Submitted，已向支付渠道提交退款请求
	RefundModel_SUBMITTED RefundModel_RefundStatus = 2
	// SUCCEEDED，退款成功，这是最终状态
	RefundModel_SUCCEEDED RefundModel_RefundStatus = 3
	// Failed，退款失败，这是最终状态
	RefundModel_FAILED RefundModel_RefundStatus = 4
)

// Enum value maps for RefundModel_RefundStatus.
var (
	RefundModel_RefundStatus_name = map[int32]string{
		0: "REFUND_STATUS_UNSPECIFIED",
		1: "CREATED",
		2: "SUBMITTED",
		3: "SUCCEEDED",
		4: "FAILED",
	}
	RefundModel_RefundStatus_value = map[string]int32{
		"REFUND_STATUS_UNSPECIFIED": 0,
		"CREATED":                   1,
		"SUBMITTED":                 2,
		"SUCCEEDED":                 3,
		"FAILED":                    4,
	}
)

func (x RefundModel_RefundStatus) Enum() *RefundModel_RefundStatus {
	p := new(RefundModel_RefundStatus)
	*p = x
	return p
}

func (x RefundModel_RefundStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundModel_RefundStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_models_proto_enumTypes[4].Descriptor()
}

func (RefundModel_RefundStatus) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_models_proto_enumTypes[4]
}

func (x RefundModel_RefundStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundModel_RefundStatus.Descriptor instead.
func (RefundModel_RefundStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{4, 0}
}

// method type
type RecurringPaymentMethodModel_MethodType int32

const (
	// unspecified
	RecurringPaymentMethodModel_METHOD_TYPE_UNSPECIFIED RecurringPaymentMethodModel_MethodType = 0
	// card on file
	RecurringPaymentMethodModel_COF RecurringPaymentMethodModel_MethodType = 1
)

// Enum value maps for RecurringPaymentMethodModel_MethodType.
var (
	RecurringPaymentMethodModel_MethodType_name = map[int32]string{
		0: "METHOD_TYPE_UNSPECIFIED",
		1: "COF",
	}
	RecurringPaymentMethodModel_MethodType_value = map[string]int32{
		"METHOD_TYPE_UNSPECIFIED": 0,
		"COF":                     1,
	}
)

func (x RecurringPaymentMethodModel_MethodType) Enum() *RecurringPaymentMethodModel_MethodType {
	p := new(RecurringPaymentMethodModel_MethodType)
	*p = x
	return p
}

func (x RecurringPaymentMethodModel_MethodType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecurringPaymentMethodModel_MethodType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_models_proto_enumTypes[5].Descriptor()
}

func (RecurringPaymentMethodModel_MethodType) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_models_proto_enumTypes[5]
}

func (x RecurringPaymentMethodModel_MethodType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecurringPaymentMethodModel_MethodType.Descriptor instead.
func (RecurringPaymentMethodModel_MethodType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{6, 0}
}

// TransactionStatus
type PaymentTransactionModel_TransactionStatus int32

const (
	// Unspecified
	PaymentTransactionModel_TRANSACTION_STATUS_UNSPECIFIED PaymentTransactionModel_TransactionStatus = 0
	// Created，这是初始化状态
	PaymentTransactionModel_CREATED PaymentTransactionModel_TransactionStatus = 1
	// ACCEPTED，支付已被受理，在提交支付凭据后就会变成这个状态，无法撤销
	PaymentTransactionModel_ACCEPTED PaymentTransactionModel_TransactionStatus = 2
	// Submitted，已向支付渠道提交支付请求
	PaymentTransactionModel_SUBMITTED PaymentTransactionModel_TransactionStatus = 3
	// SUCCEEDED，支付成功，这是最终状态
	PaymentTransactionModel_SUCCEEDED PaymentTransactionModel_TransactionStatus = 4
	// Cancelled，支付取消，这是最终状态
	PaymentTransactionModel_CANCELLED PaymentTransactionModel_TransactionStatus = 5
	// Failed，支付失败，这是最终状态
	PaymentTransactionModel_FAILED PaymentTransactionModel_TransactionStatus = 6
	// Authorized，只出现于 pre-auth 场景，表示已授权
	PaymentTransactionModel_AUTHORIZED PaymentTransactionModel_TransactionStatus = 7
)

// Enum value maps for PaymentTransactionModel_TransactionStatus.
var (
	PaymentTransactionModel_TransactionStatus_name = map[int32]string{
		0: "TRANSACTION_STATUS_UNSPECIFIED",
		1: "CREATED",
		2: "ACCEPTED",
		3: "SUBMITTED",
		4: "SUCCEEDED",
		5: "CANCELLED",
		6: "FAILED",
		7: "AUTHORIZED",
	}
	PaymentTransactionModel_TransactionStatus_value = map[string]int32{
		"TRANSACTION_STATUS_UNSPECIFIED": 0,
		"CREATED":                        1,
		"ACCEPTED":                       2,
		"SUBMITTED":                      3,
		"SUCCEEDED":                      4,
		"CANCELLED":                      5,
		"FAILED":                         6,
		"AUTHORIZED":                     7,
	}
)

func (x PaymentTransactionModel_TransactionStatus) Enum() *PaymentTransactionModel_TransactionStatus {
	p := new(PaymentTransactionModel_TransactionStatus)
	*p = x
	return p
}

func (x PaymentTransactionModel_TransactionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentTransactionModel_TransactionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_models_proto_enumTypes[6].Descriptor()
}

func (PaymentTransactionModel_TransactionStatus) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_models_proto_enumTypes[6]
}

func (x PaymentTransactionModel_TransactionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentTransactionModel_TransactionStatus.Descriptor instead.
func (PaymentTransactionModel_TransactionStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{8, 0}
}

// Terminal 类型
type Terminal_TerminalType int32

const (
	// Unspecified
	Terminal_TERMINAL_TYPE_UNSPECIFIED Terminal_TerminalType = 0
	// Smart reader
	Terminal_SMART_READER Terminal_TerminalType = 1
	// BT reader
	Terminal_BLUETOOTH_READER Terminal_TerminalType = 2
	// Tap-to-pay reader
	Terminal_TAP_TO_PAY_READER Terminal_TerminalType = 3
)

// Enum value maps for Terminal_TerminalType.
var (
	Terminal_TerminalType_name = map[int32]string{
		0: "TERMINAL_TYPE_UNSPECIFIED",
		1: "SMART_READER",
		2: "BLUETOOTH_READER",
		3: "TAP_TO_PAY_READER",
	}
	Terminal_TerminalType_value = map[string]int32{
		"TERMINAL_TYPE_UNSPECIFIED": 0,
		"SMART_READER":              1,
		"BLUETOOTH_READER":          2,
		"TAP_TO_PAY_READER":         3,
	}
)

func (x Terminal_TerminalType) Enum() *Terminal_TerminalType {
	p := new(Terminal_TerminalType)
	*p = x
	return p
}

func (x Terminal_TerminalType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Terminal_TerminalType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_models_proto_enumTypes[7].Descriptor()
}

func (Terminal_TerminalType) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_models_proto_enumTypes[7]
}

func (x Terminal_TerminalType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Terminal_TerminalType.Descriptor instead.
func (Terminal_TerminalType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{10, 0}
}

// Terminal 状态
type Terminal_State int32

const (
	// Unspecified
	Terminal_STATE_UNSPECIFIED Terminal_State = 0
	// Idle
	Terminal_IDLE Terminal_State = 1
	// Busy
	Terminal_BUSY Terminal_State = 2
)

// Enum value maps for Terminal_State.
var (
	Terminal_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "IDLE",
		2: "BUSY",
	}
	Terminal_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"IDLE":              1,
		"BUSY":              2,
	}
)

func (x Terminal_State) Enum() *Terminal_State {
	p := new(Terminal_State)
	*p = x
	return p
}

func (x Terminal_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Terminal_State) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_models_proto_enumTypes[8].Descriptor()
}

func (Terminal_State) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_models_proto_enumTypes[8]
}

func (x Terminal_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Terminal_State.Descriptor instead.
func (Terminal_State) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{10, 1}
}

// User Payment 参与方
// (-- api-linter: core::0123::resource-annotation=disabled
//
//	aip.dev/not-precedent: Not applicable for MoeGo. --)
type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户 类型
	EntityType EntityType `protobuf:"varint,1,opt,name=entity_type,json=entityType,proto3,enum=moego.models.payment.v2.EntityType" json:"entity_type,omitempty"`
	// 用户 ID，当 entity_type 为 MoeGo 时，entity_id 为 0
	EntityId int64 `protobuf:"varint,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// 用户名
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetEntityType() EntityType {
	if x != nil {
		return x.EntityType
	}
	return EntityType_ENTITY_TYPE_UNSPECIFIED
}

func (x *User) GetEntityId() int64 {
	if x != nil {
		return x.EntityId
	}
	return 0
}

func (x *User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Payment 实体
type PaymentModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// payer 支付方
	Payer *User `protobuf:"bytes,2,opt,name=payer,proto3" json:"payer,omitempty"`
	// payee 收款方
	Payee *User `protobuf:"bytes,3,opt,name=payee,proto3" json:"payee,omitempty"`
	// 调用支付的上层业务系统类型
	ExternalType ExternalType `protobuf:"varint,4,opt,name=external_type,json=externalType,proto3,enum=moego.models.payment.v2.ExternalType" json:"external_type,omitempty"`
	// 调用支付的上层业务系统内单据 ID，用于关联支付单据，与 id 一一对应
	// 支付系统会将此字段作为幂等 key，上层业务系统需要保证此字段的唯一性
	ExternalId string `protobuf:"bytes,5,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 支付渠道
	ChannelType ChannelType `protobuf:"varint,6,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 渠道支付单据 ID，与 id 一一对应
	ChannelPaymentId string `protobuf:"bytes,7,opt,name=channel_payment_id,json=channelPaymentId,proto3" json:"channel_payment_id,omitempty"`
	// 支付金额，创建支付单据时传入的金额
	Amount *money.Money `protobuf:"bytes,8,opt,name=amount,proto3" json:"amount,omitempty"`
	// 支付时关联的 tips 金额
	TipsAmount *money.Money `protobuf:"bytes,9,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// 手续费
	ProcessingFee *money.Money `protobuf:"bytes,10,opt,name=processing_fee,json=processingFee,proto3" json:"processing_fee,omitempty"`
	// convenience fee
	ConvenienceFee *money.Money `protobuf:"bytes,11,opt,name=convenience_fee,json=convenienceFee,proto3" json:"convenience_fee,omitempty"`
	// 优惠金额
	DiscountAmount *money.Money `protobuf:"bytes,12,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	// 支付状态
	Status PaymentModel_PaymentStatus `protobuf:"varint,13,opt,name=status,proto3,enum=moego.models.payment.v2.PaymentModel_PaymentStatus" json:"status,omitempty"`
	// 退款信息
	Refund *PaymentModel_Refund `protobuf:"bytes,14,opt,name=refund,proto3" json:"refund,omitempty"`
	// 支付类型
	PaymentType PaymentModel_PaymentType `protobuf:"varint,15,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.payment.v2.PaymentModel_PaymentType" json:"payment_type,omitempty"`
	// 支付方式类型
	MethodType PaymentMethod_MethodType `protobuf:"varint,16,opt,name=method_type,json=methodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"method_type,omitempty"`
	// transaction id
	TransactionId int64 `protobuf:"varint,17,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// method id
	MethodId int64 `protobuf:"varint,18,opt,name=method_id,json=methodId,proto3" json:"method_id,omitempty"`
}

func (x *PaymentModel) Reset() {
	*x = PaymentModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentModel) ProtoMessage() {}

func (x *PaymentModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentModel.ProtoReflect.Descriptor instead.
func (*PaymentModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{1}
}

func (x *PaymentModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PaymentModel) GetPayer() *User {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *PaymentModel) GetPayee() *User {
	if x != nil {
		return x.Payee
	}
	return nil
}

func (x *PaymentModel) GetExternalType() ExternalType {
	if x != nil {
		return x.ExternalType
	}
	return ExternalType_EXTERNAL_TYPE_UNSPECIFIED
}

func (x *PaymentModel) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *PaymentModel) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *PaymentModel) GetChannelPaymentId() string {
	if x != nil {
		return x.ChannelPaymentId
	}
	return ""
}

func (x *PaymentModel) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PaymentModel) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

func (x *PaymentModel) GetProcessingFee() *money.Money {
	if x != nil {
		return x.ProcessingFee
	}
	return nil
}

func (x *PaymentModel) GetConvenienceFee() *money.Money {
	if x != nil {
		return x.ConvenienceFee
	}
	return nil
}

func (x *PaymentModel) GetDiscountAmount() *money.Money {
	if x != nil {
		return x.DiscountAmount
	}
	return nil
}

func (x *PaymentModel) GetStatus() PaymentModel_PaymentStatus {
	if x != nil {
		return x.Status
	}
	return PaymentModel_PAYMENT_STATUS_UNSPECIFIED
}

func (x *PaymentModel) GetRefund() *PaymentModel_Refund {
	if x != nil {
		return x.Refund
	}
	return nil
}

func (x *PaymentModel) GetPaymentType() PaymentModel_PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return PaymentModel_PAYMENT_TYPE_UNSPECIFIED
}

func (x *PaymentModel) GetMethodType() PaymentMethod_MethodType {
	if x != nil {
		return x.MethodType
	}
	return PaymentMethod_METHOD_TYPE_UNSPECIFIED
}

func (x *PaymentModel) GetTransactionId() int64 {
	if x != nil {
		return x.TransactionId
	}
	return 0
}

func (x *PaymentModel) GetMethodId() int64 {
	if x != nil {
		return x.MethodId
	}
	return 0
}

// payment view
type PaymentView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// payer 支付方
	Payer *User `protobuf:"bytes,2,opt,name=payer,proto3" json:"payer,omitempty"`
	// payee 收款方
	Payee *User `protobuf:"bytes,3,opt,name=payee,proto3" json:"payee,omitempty"`
	// 调用支付的上层业务系统类型
	ExternalType ExternalType `protobuf:"varint,4,opt,name=external_type,json=externalType,proto3,enum=moego.models.payment.v2.ExternalType" json:"external_type,omitempty"`
	// 调用支付的上层业务系统内单据 ID，用于关联支付单据，与 id 一一对应
	// 支付系统会将此字段作为幂等 key，上层业务系统需要保证此字段的唯一性
	ExternalId string `protobuf:"bytes,5,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 支付渠道
	ChannelType ChannelType `protobuf:"varint,6,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 渠道支付单据 ID，与 id 一一对应
	ChannelPaymentId string `protobuf:"bytes,7,opt,name=channel_payment_id,json=channelPaymentId,proto3" json:"channel_payment_id,omitempty"`
	// 支付金额，创建支付单据时传入的金额
	Amount *money.Money `protobuf:"bytes,8,opt,name=amount,proto3" json:"amount,omitempty"`
	// 支付时关联的 tips 金额
	TipsAmount *money.Money `protobuf:"bytes,9,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// 手续费
	ProcessingFee *money.Money `protobuf:"bytes,10,opt,name=processing_fee,json=processingFee,proto3" json:"processing_fee,omitempty"`
	// convenience fee
	ConvenienceFee *money.Money `protobuf:"bytes,11,opt,name=convenience_fee,json=convenienceFee,proto3" json:"convenience_fee,omitempty"`
	// 优惠金额
	DiscountAmount *money.Money `protobuf:"bytes,12,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	// 支付类型
	PaymentType PaymentModel_PaymentType `protobuf:"varint,13,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.payment.v2.PaymentModel_PaymentType" json:"payment_type,omitempty"`
	// 支付方式类型
	MethodType PaymentMethod_MethodType `protobuf:"varint,14,opt,name=method_type,json=methodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"method_type,omitempty"`
	// 支付状态
	Status PaymentModel_PaymentStatus `protobuf:"varint,15,opt,name=status,proto3,enum=moego.models.payment.v2.PaymentModel_PaymentStatus" json:"status,omitempty"`
	// payment实体对应的退款
	Refund *PaymentModel_Refund `protobuf:"bytes,16,opt,name=refund,proto3" json:"refund,omitempty"`
	// 退款列表
	RefundViews []*RefundView `protobuf:"bytes,17,rep,name=refund_views,json=refundViews,proto3" json:"refund_views,omitempty"`
	// 关联的 payout id
	PayoutId int64 `protobuf:"varint,18,opt,name=payout_id,json=payoutId,proto3" json:"payout_id,omitempty"`
	// 渠道payout id
	ChannelPayoutId string `protobuf:"bytes,19,opt,name=channel_payout_id,json=channelPayoutId,proto3" json:"channel_payout_id,omitempty"`
	// 支付描述
	Description string `protobuf:"bytes,20,opt,name=description,proto3" json:"description,omitempty"`
	// 剩余可退款金额
	RefundableAmount *money.Money `protobuf:"bytes,21,opt,name=refundable_amount,json=refundableAmount,proto3" json:"refundable_amount,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,23,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 展示的支付方式
	PaymentMethod string `protobuf:"bytes,24,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	// module
	Module string `protobuf:"bytes,25,opt,name=module,proto3" json:"module,omitempty"`
	// module id , e.g. grooming id
	ModuleId string `protobuf:"bytes,26,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	// paid by
	PaidBy string `protobuf:"bytes,27,opt,name=paid_by,json=paidBy,proto3" json:"paid_by,omitempty"`
	// transaction id
	TransactionId int64 `protobuf:"varint,28,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// invoice id
	InvoiceId int64 `protobuf:"varint,29,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
}

func (x *PaymentView) Reset() {
	*x = PaymentView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentView) ProtoMessage() {}

func (x *PaymentView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentView.ProtoReflect.Descriptor instead.
func (*PaymentView) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{2}
}

func (x *PaymentView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PaymentView) GetPayer() *User {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *PaymentView) GetPayee() *User {
	if x != nil {
		return x.Payee
	}
	return nil
}

func (x *PaymentView) GetExternalType() ExternalType {
	if x != nil {
		return x.ExternalType
	}
	return ExternalType_EXTERNAL_TYPE_UNSPECIFIED
}

func (x *PaymentView) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *PaymentView) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *PaymentView) GetChannelPaymentId() string {
	if x != nil {
		return x.ChannelPaymentId
	}
	return ""
}

func (x *PaymentView) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PaymentView) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

func (x *PaymentView) GetProcessingFee() *money.Money {
	if x != nil {
		return x.ProcessingFee
	}
	return nil
}

func (x *PaymentView) GetConvenienceFee() *money.Money {
	if x != nil {
		return x.ConvenienceFee
	}
	return nil
}

func (x *PaymentView) GetDiscountAmount() *money.Money {
	if x != nil {
		return x.DiscountAmount
	}
	return nil
}

func (x *PaymentView) GetPaymentType() PaymentModel_PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return PaymentModel_PAYMENT_TYPE_UNSPECIFIED
}

func (x *PaymentView) GetMethodType() PaymentMethod_MethodType {
	if x != nil {
		return x.MethodType
	}
	return PaymentMethod_METHOD_TYPE_UNSPECIFIED
}

func (x *PaymentView) GetStatus() PaymentModel_PaymentStatus {
	if x != nil {
		return x.Status
	}
	return PaymentModel_PAYMENT_STATUS_UNSPECIFIED
}

func (x *PaymentView) GetRefund() *PaymentModel_Refund {
	if x != nil {
		return x.Refund
	}
	return nil
}

func (x *PaymentView) GetRefundViews() []*RefundView {
	if x != nil {
		return x.RefundViews
	}
	return nil
}

func (x *PaymentView) GetPayoutId() int64 {
	if x != nil {
		return x.PayoutId
	}
	return 0
}

func (x *PaymentView) GetChannelPayoutId() string {
	if x != nil {
		return x.ChannelPayoutId
	}
	return ""
}

func (x *PaymentView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PaymentView) GetRefundableAmount() *money.Money {
	if x != nil {
		return x.RefundableAmount
	}
	return nil
}

func (x *PaymentView) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *PaymentView) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *PaymentView) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *PaymentView) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *PaymentView) GetModuleId() string {
	if x != nil {
		return x.ModuleId
	}
	return ""
}

func (x *PaymentView) GetPaidBy() string {
	if x != nil {
		return x.PaidBy
	}
	return ""
}

func (x *PaymentView) GetTransactionId() int64 {
	if x != nil {
		return x.TransactionId
	}
	return 0
}

func (x *PaymentView) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

// PaymentMethod 支付方式，会和 Payment 一一对应
type PaymentMethod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 对应支付单据 ID, deprecated
	PaymentId int64 `protobuf:"varint,2,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// 支付方式类型，这里是业务逻辑上的类型，如 Card支付、Online支付、Reader 等
	MethodType PaymentMethod_MethodType `protobuf:"varint,3,opt,name=method_type,json=methodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"method_type,omitempty"`
	// 渠道
	ChannelType ChannelType `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 渠道侧支付方式 ID
	ChannelPaymentMethodId string `protobuf:"bytes,5,opt,name=channel_payment_method_id,json=channelPaymentMethodId,proto3" json:"channel_payment_method_id,omitempty"`
	// 支付凭证
	Detail *PaymentMethod_Detail `protobuf:"bytes,6,opt,name=detail,proto3" json:"detail,omitempty"`
}

func (x *PaymentMethod) Reset() {
	*x = PaymentMethod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod) ProtoMessage() {}

func (x *PaymentMethod) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod.ProtoReflect.Descriptor instead.
func (*PaymentMethod) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3}
}

func (x *PaymentMethod) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PaymentMethod) GetPaymentId() int64 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

func (x *PaymentMethod) GetMethodType() PaymentMethod_MethodType {
	if x != nil {
		return x.MethodType
	}
	return PaymentMethod_METHOD_TYPE_UNSPECIFIED
}

func (x *PaymentMethod) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *PaymentMethod) GetChannelPaymentMethodId() string {
	if x != nil {
		return x.ChannelPaymentMethodId
	}
	return ""
}

func (x *PaymentMethod) GetDetail() *PaymentMethod_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

// 退款模型
type RefundModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 退款单据id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 原支付付款方
	Payer *User `protobuf:"bytes,2,opt,name=payer,proto3" json:"payer,omitempty"`
	// 原支付收款方
	Payee *User `protobuf:"bytes,3,opt,name=payee,proto3" json:"payee,omitempty"`
	// 外部调用方类型，如ORDER
	ExternalType ExternalType `protobuf:"varint,4,opt,name=external_type,json=externalType,proto3,enum=moego.models.payment.v2.ExternalType" json:"external_type,omitempty"`
	// 外部调用方关联id
	ExternalId string `protobuf:"bytes,5,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 支付渠道类型，如Adyen、Stripe
	ChannelType ChannelType `protobuf:"varint,6,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 渠道退款id
	ChannelRefundId string `protobuf:"bytes,7,opt,name=channel_refund_id,json=channelRefundId,proto3" json:"channel_refund_id,omitempty"`
	// 退款金额
	Amount *money.Money `protobuf:"bytes,8,opt,name=amount,proto3" json:"amount,omitempty"`
	// 支付单据id
	PaymentId int64 `protobuf:"varint,9,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// 退款状态
	Status RefundModel_RefundStatus `protobuf:"varint,10,opt,name=status,proto3,enum=moego.models.payment.v2.RefundModel_RefundStatus" json:"status,omitempty"`
	// 退款原因
	Reason string `protobuf:"bytes,11,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *RefundModel) Reset() {
	*x = RefundModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundModel) ProtoMessage() {}

func (x *RefundModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundModel.ProtoReflect.Descriptor instead.
func (*RefundModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{4}
}

func (x *RefundModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RefundModel) GetPayer() *User {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *RefundModel) GetPayee() *User {
	if x != nil {
		return x.Payee
	}
	return nil
}

func (x *RefundModel) GetExternalType() ExternalType {
	if x != nil {
		return x.ExternalType
	}
	return ExternalType_EXTERNAL_TYPE_UNSPECIFIED
}

func (x *RefundModel) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *RefundModel) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *RefundModel) GetChannelRefundId() string {
	if x != nil {
		return x.ChannelRefundId
	}
	return ""
}

func (x *RefundModel) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *RefundModel) GetPaymentId() int64 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

func (x *RefundModel) GetStatus() RefundModel_RefundStatus {
	if x != nil {
		return x.Status
	}
	return RefundModel_REFUND_STATUS_UNSPECIFIED
}

func (x *RefundModel) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// refund view
type RefundView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 退款单据id
	RefundId int64 `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
	// 买家
	Payer *User `protobuf:"bytes,2,opt,name=payer,proto3" json:"payer,omitempty"`
	// 卖家
	Payee *User `protobuf:"bytes,3,opt,name=payee,proto3" json:"payee,omitempty"`
	// 外部调用方类型
	ExternalType ExternalType `protobuf:"varint,4,opt,name=external_type,json=externalType,proto3,enum=moego.models.payment.v2.ExternalType" json:"external_type,omitempty"`
	// 外部调用方关联id
	ExternalId string `protobuf:"bytes,5,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 渠道类型
	ChannelType ChannelType `protobuf:"varint,6,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 渠道退款id
	ChannelRefundId string `protobuf:"bytes,7,opt,name=channel_refund_id,json=channelRefundId,proto3" json:"channel_refund_id,omitempty"`
	// 退款金额
	Amount *money.Money `protobuf:"bytes,8,opt,name=amount,proto3" json:"amount,omitempty"`
	// 支付单据id
	PaymentId int64 `protobuf:"varint,9,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// 渠道支付id
	ChannelPaymentId string `protobuf:"bytes,10,opt,name=channel_payment_id,json=channelPaymentId,proto3" json:"channel_payment_id,omitempty"`
	// 退款状态
	Status RefundModel_RefundStatus `protobuf:"varint,11,opt,name=status,proto3,enum=moego.models.payment.v2.RefundModel_RefundStatus" json:"status,omitempty"`
	// 退款原因
	Reason string `protobuf:"bytes,12,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *RefundView) Reset() {
	*x = RefundView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundView) ProtoMessage() {}

func (x *RefundView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundView.ProtoReflect.Descriptor instead.
func (*RefundView) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{5}
}

func (x *RefundView) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

func (x *RefundView) GetPayer() *User {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *RefundView) GetPayee() *User {
	if x != nil {
		return x.Payee
	}
	return nil
}

func (x *RefundView) GetExternalType() ExternalType {
	if x != nil {
		return x.ExternalType
	}
	return ExternalType_EXTERNAL_TYPE_UNSPECIFIED
}

func (x *RefundView) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *RefundView) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *RefundView) GetChannelRefundId() string {
	if x != nil {
		return x.ChannelRefundId
	}
	return ""
}

func (x *RefundView) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *RefundView) GetPaymentId() int64 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

func (x *RefundView) GetChannelPaymentId() string {
	if x != nil {
		return x.ChannelPaymentId
	}
	return ""
}

func (x *RefundView) GetStatus() RefundModel_RefundStatus {
	if x != nil {
		return x.Status
	}
	return RefundModel_REFUND_STATUS_UNSPECIFIED
}

func (x *RefundView) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 已存储的支付方式
type RecurringPaymentMethodModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 渠道类型
	ChannelType ChannelType `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 用户
	User *User `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	// 渠道 customer id
	ChannelCustomerId string `protobuf:"bytes,4,opt,name=channel_customer_id,json=channelCustomerId,proto3" json:"channel_customer_id,omitempty"`
	// 渠道保存的payment method
	ChannelPaymentMethod *RecurringPaymentMethodModel_ChannelPaymentMethod `protobuf:"bytes,5,opt,name=channel_payment_method,json=channelPaymentMethod,proto3" json:"channel_payment_method,omitempty"`
	// 存储的 payment method 类型
	MethodType RecurringPaymentMethodModel_MethodType `protobuf:"varint,6,opt,name=method_type,json=methodType,proto3,enum=moego.models.payment.v2.RecurringPaymentMethodModel_MethodType" json:"method_type,omitempty"`
	// extra
	Extra *RecurringPaymentMethodModel_Extra `protobuf:"bytes,7,opt,name=extra,proto3,oneof" json:"extra,omitempty"`
	// 是否primary
	IsPrimary bool `protobuf:"varint,8,opt,name=is_primary,json=isPrimary,proto3" json:"is_primary,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *RecurringPaymentMethodModel) Reset() {
	*x = RecurringPaymentMethodModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecurringPaymentMethodModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecurringPaymentMethodModel) ProtoMessage() {}

func (x *RecurringPaymentMethodModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecurringPaymentMethodModel.ProtoReflect.Descriptor instead.
func (*RecurringPaymentMethodModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{6}
}

func (x *RecurringPaymentMethodModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RecurringPaymentMethodModel) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *RecurringPaymentMethodModel) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *RecurringPaymentMethodModel) GetChannelCustomerId() string {
	if x != nil {
		return x.ChannelCustomerId
	}
	return ""
}

func (x *RecurringPaymentMethodModel) GetChannelPaymentMethod() *RecurringPaymentMethodModel_ChannelPaymentMethod {
	if x != nil {
		return x.ChannelPaymentMethod
	}
	return nil
}

func (x *RecurringPaymentMethodModel) GetMethodType() RecurringPaymentMethodModel_MethodType {
	if x != nil {
		return x.MethodType
	}
	return RecurringPaymentMethodModel_METHOD_TYPE_UNSPECIFIED
}

func (x *RecurringPaymentMethodModel) GetExtra() *RecurringPaymentMethodModel_Extra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *RecurringPaymentMethodModel) GetIsPrimary() bool {
	if x != nil {
		return x.IsPrimary
	}
	return false
}

func (x *RecurringPaymentMethodModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// recurring payment method view
type RecurringPaymentMethodView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 渠道类型
	ChannelType ChannelType `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 用户
	User *User `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	// 渠道 customer id
	ChannelCustomerId string `protobuf:"bytes,4,opt,name=channel_customer_id,json=channelCustomerId,proto3" json:"channel_customer_id,omitempty"`
	// 渠道保存的payment method
	ChannelPaymentMethod *RecurringPaymentMethodModel_ChannelPaymentMethod `protobuf:"bytes,5,opt,name=channel_payment_method,json=channelPaymentMethod,proto3" json:"channel_payment_method,omitempty"`
	// 存储的 payment method 类型
	MethodType RecurringPaymentMethodModel_MethodType `protobuf:"varint,6,opt,name=method_type,json=methodType,proto3,enum=moego.models.payment.v2.RecurringPaymentMethodModel_MethodType" json:"method_type,omitempty"`
	// 是否primary
	IsPrimary bool `protobuf:"varint,7,opt,name=is_primary,json=isPrimary,proto3" json:"is_primary,omitempty"`
	// extra
	Extra *RecurringPaymentMethodModel_Extra `protobuf:"bytes,8,opt,name=extra,proto3" json:"extra,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *RecurringPaymentMethodView) Reset() {
	*x = RecurringPaymentMethodView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecurringPaymentMethodView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecurringPaymentMethodView) ProtoMessage() {}

func (x *RecurringPaymentMethodView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecurringPaymentMethodView.ProtoReflect.Descriptor instead.
func (*RecurringPaymentMethodView) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{7}
}

func (x *RecurringPaymentMethodView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RecurringPaymentMethodView) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *RecurringPaymentMethodView) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *RecurringPaymentMethodView) GetChannelCustomerId() string {
	if x != nil {
		return x.ChannelCustomerId
	}
	return ""
}

func (x *RecurringPaymentMethodView) GetChannelPaymentMethod() *RecurringPaymentMethodModel_ChannelPaymentMethod {
	if x != nil {
		return x.ChannelPaymentMethod
	}
	return nil
}

func (x *RecurringPaymentMethodView) GetMethodType() RecurringPaymentMethodModel_MethodType {
	if x != nil {
		return x.MethodType
	}
	return RecurringPaymentMethodModel_METHOD_TYPE_UNSPECIFIED
}

func (x *RecurringPaymentMethodView) GetIsPrimary() bool {
	if x != nil {
		return x.IsPrimary
	}
	return false
}

func (x *RecurringPaymentMethodView) GetExtra() *RecurringPaymentMethodModel_Extra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *RecurringPaymentMethodView) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// transaction model
type PaymentTransactionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// payer 支付方
	Payer *User `protobuf:"bytes,2,opt,name=payer,proto3" json:"payer,omitempty"`
	// payee 收款方
	Payee *User `protobuf:"bytes,3,opt,name=payee,proto3" json:"payee,omitempty"`
	// 调用支付的上层业务系统类型
	ExternalType ExternalType `protobuf:"varint,4,opt,name=external_type,json=externalType,proto3,enum=moego.models.payment.v2.ExternalType" json:"external_type,omitempty"`
	// 调用支付的上层业务系统内单据 ID，用于关联支付单据，与 id 一一对应
	// 支付系统会将此字段作为幂等 key，上层业务系统需要保证此字段的唯一性
	ExternalId string `protobuf:"bytes,5,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 支付渠道
	ChannelType ChannelType `protobuf:"varint,6,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 渠道支付单据 ID，与 id 一一对应
	ChannelTransactionId string `protobuf:"bytes,7,opt,name=channel_transaction_id,json=channelTransactionId,proto3" json:"channel_transaction_id,omitempty"`
	// 支付金额，创建支付单据时传入的金额
	Amount *money.Money `protobuf:"bytes,8,opt,name=amount,proto3" json:"amount,omitempty"`
	// 支付时关联的 tips 金额
	TipsAmount *money.Money `protobuf:"bytes,9,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// 手续费
	ProcessingFee *money.Money `protobuf:"bytes,10,opt,name=processing_fee,json=processingFee,proto3" json:"processing_fee,omitempty"`
	// convenience fee
	ConvenienceFee *money.Money `protobuf:"bytes,11,opt,name=convenience_fee,json=convenienceFee,proto3" json:"convenience_fee,omitempty"`
	// 支付状态
	Status PaymentTransactionModel_TransactionStatus `protobuf:"varint,13,opt,name=status,proto3,enum=moego.models.payment.v2.PaymentTransactionModel_TransactionStatus" json:"status,omitempty"`
	// 交易创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 交易更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,16,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// method type
	MethodType PaymentMethod_MethodType `protobuf:"varint,17,opt,name=method_type,json=methodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"method_type,omitempty"`
	// payment method
	MethodDetail *PaymentMethod_Detail `protobuf:"bytes,18,opt,name=method_detail,json=methodDetail,proto3" json:"method_detail,omitempty"`
	// method id
	MethodId int64 `protobuf:"varint,19,opt,name=method_id,json=methodId,proto3" json:"method_id,omitempty"`
}

func (x *PaymentTransactionModel) Reset() {
	*x = PaymentTransactionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentTransactionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentTransactionModel) ProtoMessage() {}

func (x *PaymentTransactionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentTransactionModel.ProtoReflect.Descriptor instead.
func (*PaymentTransactionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{8}
}

func (x *PaymentTransactionModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PaymentTransactionModel) GetPayer() *User {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *PaymentTransactionModel) GetPayee() *User {
	if x != nil {
		return x.Payee
	}
	return nil
}

func (x *PaymentTransactionModel) GetExternalType() ExternalType {
	if x != nil {
		return x.ExternalType
	}
	return ExternalType_EXTERNAL_TYPE_UNSPECIFIED
}

func (x *PaymentTransactionModel) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *PaymentTransactionModel) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *PaymentTransactionModel) GetChannelTransactionId() string {
	if x != nil {
		return x.ChannelTransactionId
	}
	return ""
}

func (x *PaymentTransactionModel) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PaymentTransactionModel) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

func (x *PaymentTransactionModel) GetProcessingFee() *money.Money {
	if x != nil {
		return x.ProcessingFee
	}
	return nil
}

func (x *PaymentTransactionModel) GetConvenienceFee() *money.Money {
	if x != nil {
		return x.ConvenienceFee
	}
	return nil
}

func (x *PaymentTransactionModel) GetStatus() PaymentTransactionModel_TransactionStatus {
	if x != nil {
		return x.Status
	}
	return PaymentTransactionModel_TRANSACTION_STATUS_UNSPECIFIED
}

func (x *PaymentTransactionModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *PaymentTransactionModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *PaymentTransactionModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *PaymentTransactionModel) GetMethodType() PaymentMethod_MethodType {
	if x != nil {
		return x.MethodType
	}
	return PaymentMethod_METHOD_TYPE_UNSPECIFIED
}

func (x *PaymentTransactionModel) GetMethodDetail() *PaymentMethod_Detail {
	if x != nil {
		return x.MethodDetail
	}
	return nil
}

func (x *PaymentTransactionModel) GetMethodId() int64 {
	if x != nil {
		return x.MethodId
	}
	return 0
}

// transaction view
type PaymentTransactionView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// payer 支付方
	Payer *User `protobuf:"bytes,2,opt,name=payer,proto3" json:"payer,omitempty"`
	// payee 收款方
	Payee *User `protobuf:"bytes,3,opt,name=payee,proto3" json:"payee,omitempty"`
	// 支付时的备注，一般是client name
	PaidBy string `protobuf:"bytes,4,opt,name=paid_by,json=paidBy,proto3" json:"paid_by,omitempty"`
	// 调用支付的上层业务系统类型
	ExternalType ExternalType `protobuf:"varint,5,opt,name=external_type,json=externalType,proto3,enum=moego.models.payment.v2.ExternalType" json:"external_type,omitempty"`
	// 调用支付的上层业务系统内单据 ID，用于关联支付单据，与 id 一一对应
	// 支付系统会将此字段作为幂等 key，上层业务系统需要保证此字段的唯一性
	ExternalId string `protobuf:"bytes,6,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 支付渠道
	ChannelType ChannelType `protobuf:"varint,7,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 渠道支付单据 ID，与 id 一一对应
	ChannelTransactionId string `protobuf:"bytes,8,opt,name=channel_transaction_id,json=channelTransactionId,proto3" json:"channel_transaction_id,omitempty"`
	// 支付金额，创建支付单据时传入的金额
	Amount *money.Money `protobuf:"bytes,9,opt,name=amount,proto3" json:"amount,omitempty"`
	// 支付时关联的 tips 金额
	TipsAmount *money.Money `protobuf:"bytes,10,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// 手续费
	ProcessingFee *money.Money `protobuf:"bytes,11,opt,name=processing_fee,json=processingFee,proto3" json:"processing_fee,omitempty"`
	// convenience fee
	ConvenienceFee *money.Money `protobuf:"bytes,12,opt,name=convenience_fee,json=convenienceFee,proto3" json:"convenience_fee,omitempty"`
	// 支付状态
	Status PaymentTransactionModel_TransactionStatus `protobuf:"varint,13,opt,name=status,proto3,enum=moego.models.payment.v2.PaymentTransactionModel_TransactionStatus" json:"status,omitempty"`
	// 交易创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 交易更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 展示的支付方式
	TransactionMethod string `protobuf:"bytes,16,opt,name=transaction_method,json=transactionMethod,proto3" json:"transaction_method,omitempty"`
	// module
	Module string `protobuf:"bytes,17,opt,name=module,proto3" json:"module,omitempty"`
	// module id , e.g. grooming id
	ModuleId string `protobuf:"bytes,18,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	// transaction 描述
	Description string `protobuf:"bytes,19,opt,name=description,proto3" json:"description,omitempty"`
	// transaction 关联的 payment 列表
	PaymentViews []*PaymentView `protobuf:"bytes,20,rep,name=payment_views,json=paymentViews,proto3" json:"payment_views,omitempty"`
}

func (x *PaymentTransactionView) Reset() {
	*x = PaymentTransactionView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentTransactionView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentTransactionView) ProtoMessage() {}

func (x *PaymentTransactionView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentTransactionView.ProtoReflect.Descriptor instead.
func (*PaymentTransactionView) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{9}
}

func (x *PaymentTransactionView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PaymentTransactionView) GetPayer() *User {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *PaymentTransactionView) GetPayee() *User {
	if x != nil {
		return x.Payee
	}
	return nil
}

func (x *PaymentTransactionView) GetPaidBy() string {
	if x != nil {
		return x.PaidBy
	}
	return ""
}

func (x *PaymentTransactionView) GetExternalType() ExternalType {
	if x != nil {
		return x.ExternalType
	}
	return ExternalType_EXTERNAL_TYPE_UNSPECIFIED
}

func (x *PaymentTransactionView) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *PaymentTransactionView) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *PaymentTransactionView) GetChannelTransactionId() string {
	if x != nil {
		return x.ChannelTransactionId
	}
	return ""
}

func (x *PaymentTransactionView) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PaymentTransactionView) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

func (x *PaymentTransactionView) GetProcessingFee() *money.Money {
	if x != nil {
		return x.ProcessingFee
	}
	return nil
}

func (x *PaymentTransactionView) GetConvenienceFee() *money.Money {
	if x != nil {
		return x.ConvenienceFee
	}
	return nil
}

func (x *PaymentTransactionView) GetStatus() PaymentTransactionModel_TransactionStatus {
	if x != nil {
		return x.Status
	}
	return PaymentTransactionModel_TRANSACTION_STATUS_UNSPECIFIED
}

func (x *PaymentTransactionView) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *PaymentTransactionView) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *PaymentTransactionView) GetTransactionMethod() string {
	if x != nil {
		return x.TransactionMethod
	}
	return ""
}

func (x *PaymentTransactionView) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *PaymentTransactionView) GetModuleId() string {
	if x != nil {
		return x.ModuleId
	}
	return ""
}

func (x *PaymentTransactionView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PaymentTransactionView) GetPaymentViews() []*PaymentView {
	if x != nil {
		return x.PaymentViews
	}
	return nil
}

// Terminal
type Terminal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// terminal id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// reader type
	TerminalType Terminal_TerminalType `protobuf:"varint,2,opt,name=terminal_type,json=terminalType,proto3,enum=moego.models.payment.v2.Terminal_TerminalType" json:"terminal_type,omitempty"`
	// channel type
	ChannelType ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 渠道reader id
	ChannelTerminalId string `protobuf:"bytes,4,opt,name=channel_terminal_id,json=channelTerminalId,proto3" json:"channel_terminal_id,omitempty"`
	// state
	State Terminal_State `protobuf:"varint,5,opt,name=state,proto3,enum=moego.models.payment.v2.Terminal_State" json:"state,omitempty"`
	// transaction id
	LastTransactionId int64 `protobuf:"varint,6,opt,name=last_transaction_id,json=lastTransactionId,proto3" json:"last_transaction_id,omitempty"`
}

func (x *Terminal) Reset() {
	*x = Terminal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Terminal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Terminal) ProtoMessage() {}

func (x *Terminal) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Terminal.ProtoReflect.Descriptor instead.
func (*Terminal) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{10}
}

func (x *Terminal) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Terminal) GetTerminalType() Terminal_TerminalType {
	if x != nil {
		return x.TerminalType
	}
	return Terminal_TERMINAL_TYPE_UNSPECIFIED
}

func (x *Terminal) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *Terminal) GetChannelTerminalId() string {
	if x != nil {
		return x.ChannelTerminalId
	}
	return ""
}

func (x *Terminal) GetState() Terminal_State {
	if x != nil {
		return x.State
	}
	return Terminal_STATE_UNSPECIFIED
}

func (x *Terminal) GetLastTransactionId() int64 {
	if x != nil {
		return x.LastTransactionId
	}
	return 0
}

// terminal view
type TerminalView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// terminal id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// terminal type
	TerminalType Terminal_TerminalType `protobuf:"varint,2,opt,name=terminal_type,json=terminalType,proto3,enum=moego.models.payment.v2.Terminal_TerminalType" json:"terminal_type,omitempty"`
	// channel type
	ChannelType ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 渠道terminal id
	ChannelTerminalId string `protobuf:"bytes,4,opt,name=channel_terminal_id,json=channelTerminalId,proto3" json:"channel_terminal_id,omitempty"`
	// state
	State Terminal_State `protobuf:"varint,5,opt,name=state,proto3,enum=moego.models.payment.v2.Terminal_State" json:"state,omitempty"`
	// transaction id
	LastTransactionId int64 `protobuf:"varint,6,opt,name=last_transaction_id,json=lastTransactionId,proto3" json:"last_transaction_id,omitempty"`
}

func (x *TerminalView) Reset() {
	*x = TerminalView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TerminalView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalView) ProtoMessage() {}

func (x *TerminalView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalView.ProtoReflect.Descriptor instead.
func (*TerminalView) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{11}
}

func (x *TerminalView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TerminalView) GetTerminalType() Terminal_TerminalType {
	if x != nil {
		return x.TerminalType
	}
	return Terminal_TERMINAL_TYPE_UNSPECIFIED
}

func (x *TerminalView) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *TerminalView) GetChannelTerminalId() string {
	if x != nil {
		return x.ChannelTerminalId
	}
	return ""
}

func (x *TerminalView) GetState() Terminal_State {
	if x != nil {
		return x.State
	}
	return Terminal_STATE_UNSPECIFIED
}

func (x *TerminalView) GetLastTransactionId() int64 {
	if x != nil {
		return x.LastTransactionId
	}
	return 0
}

// 退款信息
type PaymentModel_Refund struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 退款状态
	RefundStatus PaymentModel_Refund_Status `protobuf:"varint,1,opt,name=refund_status,json=refundStatus,proto3,enum=moego.models.payment.v2.PaymentModel_Refund_Status" json:"refund_status,omitempty"`
	// 退款总金额
	RefundAmount *money.Money `protobuf:"bytes,2,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount,omitempty"`
	// 退款次数
	RefundCount int32 `protobuf:"varint,3,opt,name=refund_count,json=refundCount,proto3" json:"refund_count,omitempty"`
}

func (x *PaymentModel_Refund) Reset() {
	*x = PaymentModel_Refund{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentModel_Refund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentModel_Refund) ProtoMessage() {}

func (x *PaymentModel_Refund) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentModel_Refund.ProtoReflect.Descriptor instead.
func (*PaymentModel_Refund) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{1, 0}
}

func (x *PaymentModel_Refund) GetRefundStatus() PaymentModel_Refund_Status {
	if x != nil {
		return x.RefundStatus
	}
	return PaymentModel_Refund_STATUS_UNSPECIFIED
}

func (x *PaymentModel_Refund) GetRefundAmount() *money.Money {
	if x != nil {
		return x.RefundAmount
	}
	return nil
}

func (x *PaymentModel_Refund) GetRefundCount() int32 {
	if x != nil {
		return x.RefundCount
	}
	return 0
}

// 详细信息
type PaymentMethod_Detail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 详细信息
	//
	// Types that are assignable to Detail:
	//
	//	*PaymentMethod_Detail_Card_
	//	*PaymentMethod_Detail_RecurringPaymentMethod_
	//	*PaymentMethod_Detail_BookEntry_
	//	*PaymentMethod_Detail_Terminal_
	Detail isPaymentMethod_Detail_Detail `protobuf_oneof:"detail"`
}

func (x *PaymentMethod_Detail) Reset() {
	*x = PaymentMethod_Detail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethod_Detail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod_Detail) ProtoMessage() {}

func (x *PaymentMethod_Detail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod_Detail.ProtoReflect.Descriptor instead.
func (*PaymentMethod_Detail) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3, 0}
}

func (m *PaymentMethod_Detail) GetDetail() isPaymentMethod_Detail_Detail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (x *PaymentMethod_Detail) GetCard() *PaymentMethod_Detail_Card {
	if x, ok := x.GetDetail().(*PaymentMethod_Detail_Card_); ok {
		return x.Card
	}
	return nil
}

func (x *PaymentMethod_Detail) GetRecurringPaymentMethod() *PaymentMethod_Detail_RecurringPaymentMethod {
	if x, ok := x.GetDetail().(*PaymentMethod_Detail_RecurringPaymentMethod_); ok {
		return x.RecurringPaymentMethod
	}
	return nil
}

func (x *PaymentMethod_Detail) GetBookEntry() *PaymentMethod_Detail_BookEntry {
	if x, ok := x.GetDetail().(*PaymentMethod_Detail_BookEntry_); ok {
		return x.BookEntry
	}
	return nil
}

func (x *PaymentMethod_Detail) GetTerminal() *PaymentMethod_Detail_Terminal {
	if x, ok := x.GetDetail().(*PaymentMethod_Detail_Terminal_); ok {
		return x.Terminal
	}
	return nil
}

type isPaymentMethod_Detail_Detail interface {
	isPaymentMethod_Detail_Detail()
}

type PaymentMethod_Detail_Card_ struct {
	// Card
	Card *PaymentMethod_Detail_Card `protobuf:"bytes,1,opt,name=card,proto3,oneof"`
}

type PaymentMethod_Detail_RecurringPaymentMethod_ struct {
	// RecurringPaymentMethod
	RecurringPaymentMethod *PaymentMethod_Detail_RecurringPaymentMethod `protobuf:"bytes,2,opt,name=recurring_payment_method,json=recurringPaymentMethod,proto3,oneof"`
}

type PaymentMethod_Detail_BookEntry_ struct {
	// Book entry
	BookEntry *PaymentMethod_Detail_BookEntry `protobuf:"bytes,3,opt,name=book_entry,json=bookEntry,proto3,oneof"`
}

type PaymentMethod_Detail_Terminal_ struct {
	// terminal
	Terminal *PaymentMethod_Detail_Terminal `protobuf:"bytes,4,opt,name=terminal,proto3,oneof"`
}

func (*PaymentMethod_Detail_Card_) isPaymentMethod_Detail_Detail() {}

func (*PaymentMethod_Detail_RecurringPaymentMethod_) isPaymentMethod_Detail_Detail() {}

func (*PaymentMethod_Detail_BookEntry_) isPaymentMethod_Detail_Detail() {}

func (*PaymentMethod_Detail_Terminal_) isPaymentMethod_Detail_Detail() {}

// Adyen 的支付信息前端不需要感知，直接从 OnSubmit 事件的 data 中获取，原样透传即可
type PaymentMethod_Detail_AdyenDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// OnSubmit 事件的 data
	Detail string `protobuf:"bytes,1,opt,name=detail,proto3" json:"detail,omitempty"`
}

func (x *PaymentMethod_Detail_AdyenDetail) Reset() {
	*x = PaymentMethod_Detail_AdyenDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethod_Detail_AdyenDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod_Detail_AdyenDetail) ProtoMessage() {}

func (x *PaymentMethod_Detail_AdyenDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod_Detail_AdyenDetail.ProtoReflect.Descriptor instead.
func (*PaymentMethod_Detail_AdyenDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3, 0, 0}
}

func (x *PaymentMethod_Detail_AdyenDetail) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

// Card
type PaymentMethod_Detail_Card struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 详细信息
	//
	// Types that are assignable to Detail:
	//
	//	*PaymentMethod_Detail_Card_Adyen
	Detail isPaymentMethod_Detail_Card_Detail `protobuf_oneof:"detail"`
	// 卡支付还会有签名
	SignatureUrl *string `protobuf:"bytes,2,opt,name=signature_url,json=signatureUrl,proto3,oneof" json:"signature_url,omitempty"`
	// 是否支付时存卡，默认不存
	SaveCard bool `protobuf:"varint,3,opt,name=save_card,json=saveCard,proto3" json:"save_card,omitempty"`
	// funding source，不需要前端传
	FundingSource *FundingSource `protobuf:"varint,4,opt,name=funding_source,json=fundingSource,proto3,enum=moego.models.payment.v2.FundingSource,oneof" json:"funding_source,omitempty"`
	// extra
	Extra *PaymentMethod_Detail_Card_Extra `protobuf:"bytes,5,opt,name=extra,proto3,oneof" json:"extra,omitempty"`
}

func (x *PaymentMethod_Detail_Card) Reset() {
	*x = PaymentMethod_Detail_Card{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethod_Detail_Card) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod_Detail_Card) ProtoMessage() {}

func (x *PaymentMethod_Detail_Card) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod_Detail_Card.ProtoReflect.Descriptor instead.
func (*PaymentMethod_Detail_Card) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3, 0, 1}
}

func (m *PaymentMethod_Detail_Card) GetDetail() isPaymentMethod_Detail_Card_Detail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (x *PaymentMethod_Detail_Card) GetAdyen() *PaymentMethod_Detail_AdyenDetail {
	if x, ok := x.GetDetail().(*PaymentMethod_Detail_Card_Adyen); ok {
		return x.Adyen
	}
	return nil
}

func (x *PaymentMethod_Detail_Card) GetSignatureUrl() string {
	if x != nil && x.SignatureUrl != nil {
		return *x.SignatureUrl
	}
	return ""
}

func (x *PaymentMethod_Detail_Card) GetSaveCard() bool {
	if x != nil {
		return x.SaveCard
	}
	return false
}

func (x *PaymentMethod_Detail_Card) GetFundingSource() FundingSource {
	if x != nil && x.FundingSource != nil {
		return *x.FundingSource
	}
	return FundingSource_FUNDING_SOURCE_UNSPECIFIED
}

func (x *PaymentMethod_Detail_Card) GetExtra() *PaymentMethod_Detail_Card_Extra {
	if x != nil {
		return x.Extra
	}
	return nil
}

type isPaymentMethod_Detail_Card_Detail interface {
	isPaymentMethod_Detail_Card_Detail()
}

type PaymentMethod_Detail_Card_Adyen struct {
	// AdyenDetail
	Adyen *PaymentMethod_Detail_AdyenDetail `protobuf:"bytes,1,opt,name=adyen,proto3,oneof"`
}

func (*PaymentMethod_Detail_Card_Adyen) isPaymentMethod_Detail_Card_Detail() {}

// RecurringPaymentMethod 已存储的支付方式
type PaymentMethod_Detail_RecurringPaymentMethod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// recurring payment method id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 卡支付还会有签名, 当recurring的payment method 是COF的时候需要
	SignatureUrl *string `protobuf:"bytes,2,opt,name=signature_url,json=signatureUrl,proto3,oneof" json:"signature_url,omitempty"`
	// 前端不需要传，后端自行根据 id 填充
	RecurringPaymentMethod *RecurringPaymentMethodModel `protobuf:"bytes,3,opt,name=recurring_payment_method,json=recurringPaymentMethod,proto3,oneof" json:"recurring_payment_method,omitempty"`
}

func (x *PaymentMethod_Detail_RecurringPaymentMethod) Reset() {
	*x = PaymentMethod_Detail_RecurringPaymentMethod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethod_Detail_RecurringPaymentMethod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod_Detail_RecurringPaymentMethod) ProtoMessage() {}

func (x *PaymentMethod_Detail_RecurringPaymentMethod) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod_Detail_RecurringPaymentMethod.ProtoReflect.Descriptor instead.
func (*PaymentMethod_Detail_RecurringPaymentMethod) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3, 0, 2}
}

func (x *PaymentMethod_Detail_RecurringPaymentMethod) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PaymentMethod_Detail_RecurringPaymentMethod) GetSignatureUrl() string {
	if x != nil && x.SignatureUrl != nil {
		return *x.SignatureUrl
	}
	return ""
}

func (x *PaymentMethod_Detail_RecurringPaymentMethod) GetRecurringPaymentMethod() *RecurringPaymentMethodModel {
	if x != nil {
		return x.RecurringPaymentMethod
	}
	return nil
}

// BookEntry 记账式支付
type PaymentMethod_Detail_BookEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 详细信息
	//
	// Types that are assignable to Detail:
	//
	//	*PaymentMethod_Detail_BookEntry_Check_
	//	*PaymentMethod_Detail_BookEntry_Other_
	Detail isPaymentMethod_Detail_BookEntry_Detail `protobuf_oneof:"detail"`
}

func (x *PaymentMethod_Detail_BookEntry) Reset() {
	*x = PaymentMethod_Detail_BookEntry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethod_Detail_BookEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod_Detail_BookEntry) ProtoMessage() {}

func (x *PaymentMethod_Detail_BookEntry) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod_Detail_BookEntry.ProtoReflect.Descriptor instead.
func (*PaymentMethod_Detail_BookEntry) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3, 0, 3}
}

func (m *PaymentMethod_Detail_BookEntry) GetDetail() isPaymentMethod_Detail_BookEntry_Detail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (x *PaymentMethod_Detail_BookEntry) GetCheck() *PaymentMethod_Detail_BookEntry_Check {
	if x, ok := x.GetDetail().(*PaymentMethod_Detail_BookEntry_Check_); ok {
		return x.Check
	}
	return nil
}

func (x *PaymentMethod_Detail_BookEntry) GetOther() *PaymentMethod_Detail_BookEntry_Other {
	if x, ok := x.GetDetail().(*PaymentMethod_Detail_BookEntry_Other_); ok {
		return x.Other
	}
	return nil
}

type isPaymentMethod_Detail_BookEntry_Detail interface {
	isPaymentMethod_Detail_BookEntry_Detail()
}

type PaymentMethod_Detail_BookEntry_Check_ struct {
	// Check
	Check *PaymentMethod_Detail_BookEntry_Check `protobuf:"bytes,1,opt,name=check,proto3,oneof"`
}

type PaymentMethod_Detail_BookEntry_Other_ struct {
	// Other
	Other *PaymentMethod_Detail_BookEntry_Other `protobuf:"bytes,2,opt,name=other,proto3,oneof"`
}

func (*PaymentMethod_Detail_BookEntry_Check_) isPaymentMethod_Detail_BookEntry_Detail() {}

func (*PaymentMethod_Detail_BookEntry_Other_) isPaymentMethod_Detail_BookEntry_Detail() {}

// Terminal 终端支付
type PaymentMethod_Detail_Terminal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// terminal id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// tips base amount
	TipsBaseAmount *money.Money `protobuf:"bytes,2,opt,name=tips_base_amount,json=tipsBaseAmount,proto3" json:"tips_base_amount,omitempty"`
	// terminal，前端不需要传，后端自行根据 id 填充
	Terminal *Terminal `protobuf:"bytes,3,opt,name=terminal,proto3,oneof" json:"terminal,omitempty"`
}

func (x *PaymentMethod_Detail_Terminal) Reset() {
	*x = PaymentMethod_Detail_Terminal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethod_Detail_Terminal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod_Detail_Terminal) ProtoMessage() {}

func (x *PaymentMethod_Detail_Terminal) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod_Detail_Terminal.ProtoReflect.Descriptor instead.
func (*PaymentMethod_Detail_Terminal) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3, 0, 4}
}

func (x *PaymentMethod_Detail_Terminal) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PaymentMethod_Detail_Terminal) GetTipsBaseAmount() *money.Money {
	if x != nil {
		return x.TipsBaseAmount
	}
	return nil
}

func (x *PaymentMethod_Detail_Terminal) GetTerminal() *Terminal {
	if x != nil {
		return x.Terminal
	}
	return nil
}

// extra
type PaymentMethod_Detail_Card_Extra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 卡号后四位
	LastFourDigits *string `protobuf:"bytes,1,opt,name=last_four_digits,json=lastFourDigits,proto3,oneof" json:"last_four_digits,omitempty"`
	// 卡有效期月份
	ExpiryMonth *string `protobuf:"bytes,2,opt,name=expiry_month,json=expiryMonth,proto3,oneof" json:"expiry_month,omitempty"`
	// 卡有效期年份
	ExpiryYear *string `protobuf:"bytes,3,opt,name=expiry_year,json=expiryYear,proto3,oneof" json:"expiry_year,omitempty"`
	// 卡品牌, 如 Visa, MasterCard
	Brand *string `protobuf:"bytes,4,opt,name=brand,proto3,oneof" json:"brand,omitempty"`
	// 卡的别名，用于显示
	Alias *string `protobuf:"bytes,5,opt,name=alias,proto3,oneof" json:"alias,omitempty"`
	// 是否已经 authorized
	IsAuthorized *bool `protobuf:"varint,6,opt,name=is_authorized,json=isAuthorized,proto3,oneof" json:"is_authorized,omitempty"`
	// 持卡人 first name，是存卡时的快照，用于显示
	FirstName *string `protobuf:"bytes,7,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// 持卡人 last name, 是存卡时的快照，用于显示
	LastName *string `protobuf:"bytes,8,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// phone number, 是存卡时的快照，用于显示
	PhoneNumber *string `protobuf:"bytes,9,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
	// email, 是存卡时的快照，用于显示
	Email *string `protobuf:"bytes,10,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// funding source, credit card 或者 debit card
	FundingSource *FundingSource `protobuf:"varint,11,opt,name=funding_source,json=fundingSource,proto3,enum=moego.models.payment.v2.FundingSource,oneof" json:"funding_source,omitempty"`
}

func (x *PaymentMethod_Detail_Card_Extra) Reset() {
	*x = PaymentMethod_Detail_Card_Extra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethod_Detail_Card_Extra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod_Detail_Card_Extra) ProtoMessage() {}

func (x *PaymentMethod_Detail_Card_Extra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod_Detail_Card_Extra.ProtoReflect.Descriptor instead.
func (*PaymentMethod_Detail_Card_Extra) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3, 0, 1, 0}
}

func (x *PaymentMethod_Detail_Card_Extra) GetLastFourDigits() string {
	if x != nil && x.LastFourDigits != nil {
		return *x.LastFourDigits
	}
	return ""
}

func (x *PaymentMethod_Detail_Card_Extra) GetExpiryMonth() string {
	if x != nil && x.ExpiryMonth != nil {
		return *x.ExpiryMonth
	}
	return ""
}

func (x *PaymentMethod_Detail_Card_Extra) GetExpiryYear() string {
	if x != nil && x.ExpiryYear != nil {
		return *x.ExpiryYear
	}
	return ""
}

func (x *PaymentMethod_Detail_Card_Extra) GetBrand() string {
	if x != nil && x.Brand != nil {
		return *x.Brand
	}
	return ""
}

func (x *PaymentMethod_Detail_Card_Extra) GetAlias() string {
	if x != nil && x.Alias != nil {
		return *x.Alias
	}
	return ""
}

func (x *PaymentMethod_Detail_Card_Extra) GetIsAuthorized() bool {
	if x != nil && x.IsAuthorized != nil {
		return *x.IsAuthorized
	}
	return false
}

func (x *PaymentMethod_Detail_Card_Extra) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *PaymentMethod_Detail_Card_Extra) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *PaymentMethod_Detail_Card_Extra) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *PaymentMethod_Detail_Card_Extra) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *PaymentMethod_Detail_Card_Extra) GetFundingSource() FundingSource {
	if x != nil && x.FundingSource != nil {
		return *x.FundingSource
	}
	return FundingSource_FUNDING_SOURCE_UNSPECIFIED
}

// Check
type PaymentMethod_Detail_BookEntry_Check struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支票号
	CheckNumber string `protobuf:"bytes,1,opt,name=check_number,json=checkNumber,proto3" json:"check_number,omitempty"`
}

func (x *PaymentMethod_Detail_BookEntry_Check) Reset() {
	*x = PaymentMethod_Detail_BookEntry_Check{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethod_Detail_BookEntry_Check) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod_Detail_BookEntry_Check) ProtoMessage() {}

func (x *PaymentMethod_Detail_BookEntry_Check) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod_Detail_BookEntry_Check.ProtoReflect.Descriptor instead.
func (*PaymentMethod_Detail_BookEntry_Check) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3, 0, 3, 0}
}

func (x *PaymentMethod_Detail_BookEntry_Check) GetCheckNumber() string {
	if x != nil {
		return x.CheckNumber
	}
	return ""
}

// 其他自定义的支付方式
type PaymentMethod_Detail_BookEntry_Other struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付方式 id，这个数据当前存储在 moe_business_payment_method，后续考虑迁移
	MethodId int64 `protobuf:"varint,1,opt,name=method_id,json=methodId,proto3" json:"method_id,omitempty"`
}

func (x *PaymentMethod_Detail_BookEntry_Other) Reset() {
	*x = PaymentMethod_Detail_BookEntry_Other{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethod_Detail_BookEntry_Other) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod_Detail_BookEntry_Other) ProtoMessage() {}

func (x *PaymentMethod_Detail_BookEntry_Other) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod_Detail_BookEntry_Other.ProtoReflect.Descriptor instead.
func (*PaymentMethod_Detail_BookEntry_Other) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{3, 0, 3, 1}
}

func (x *PaymentMethod_Detail_BookEntry_Other) GetMethodId() int64 {
	if x != nil {
		return x.MethodId
	}
	return 0
}

// channel payment method
type RecurringPaymentMethodModel_ChannelPaymentMethod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道保存的payment method
	//
	// Types that are assignable to ChannelPaymentMethod:
	//
	//	*RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCof
	//	*RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCof
	ChannelPaymentMethod isRecurringPaymentMethodModel_ChannelPaymentMethod_ChannelPaymentMethod `protobuf_oneof:"channel_payment_method"`
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod) Reset() {
	*x = RecurringPaymentMethodModel_ChannelPaymentMethod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecurringPaymentMethodModel_ChannelPaymentMethod) ProtoMessage() {}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecurringPaymentMethodModel_ChannelPaymentMethod.ProtoReflect.Descriptor instead.
func (*RecurringPaymentMethodModel_ChannelPaymentMethod) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{6, 0}
}

func (m *RecurringPaymentMethodModel_ChannelPaymentMethod) GetChannelPaymentMethod() isRecurringPaymentMethodModel_ChannelPaymentMethod_ChannelPaymentMethod {
	if m != nil {
		return m.ChannelPaymentMethod
	}
	return nil
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod) GetStripeCof() *RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF {
	if x, ok := x.GetChannelPaymentMethod().(*RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCof); ok {
		return x.StripeCof
	}
	return nil
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod) GetAdyenCof() *RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF {
	if x, ok := x.GetChannelPaymentMethod().(*RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCof); ok {
		return x.AdyenCof
	}
	return nil
}

type isRecurringPaymentMethodModel_ChannelPaymentMethod_ChannelPaymentMethod interface {
	isRecurringPaymentMethodModel_ChannelPaymentMethod_ChannelPaymentMethod()
}

type RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCof struct {
	// StripeCOF
	StripeCof *RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF `protobuf:"bytes,1,opt,name=stripe_cof,json=stripeCof,proto3,oneof"`
}

type RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCof struct {
	// AdyenCOF
	AdyenCof *RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF `protobuf:"bytes,2,opt,name=adyen_cof,json=adyenCof,proto3,oneof"`
}

func (*RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCof) isRecurringPaymentMethodModel_ChannelPaymentMethod_ChannelPaymentMethod() {
}

func (*RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCof) isRecurringPaymentMethodModel_ChannelPaymentMethod_ChannelPaymentMethod() {
}

// extra
type RecurringPaymentMethodModel_Extra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// extra
	//
	// Types that are assignable to Extra:
	//
	//	*RecurringPaymentMethodModel_Extra_Card
	Extra isRecurringPaymentMethodModel_Extra_Extra `protobuf_oneof:"extra"`
}

func (x *RecurringPaymentMethodModel_Extra) Reset() {
	*x = RecurringPaymentMethodModel_Extra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecurringPaymentMethodModel_Extra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecurringPaymentMethodModel_Extra) ProtoMessage() {}

func (x *RecurringPaymentMethodModel_Extra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecurringPaymentMethodModel_Extra.ProtoReflect.Descriptor instead.
func (*RecurringPaymentMethodModel_Extra) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{6, 1}
}

func (m *RecurringPaymentMethodModel_Extra) GetExtra() isRecurringPaymentMethodModel_Extra_Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (x *RecurringPaymentMethodModel_Extra) GetCard() *PaymentMethod_Detail_Card_Extra {
	if x, ok := x.GetExtra().(*RecurringPaymentMethodModel_Extra_Card); ok {
		return x.Card
	}
	return nil
}

type isRecurringPaymentMethodModel_Extra_Extra interface {
	isRecurringPaymentMethodModel_Extra_Extra()
}

type RecurringPaymentMethodModel_Extra_Card struct {
	// CardExtra
	Card *PaymentMethod_Detail_Card_Extra `protobuf:"bytes,1,opt,name=card,proto3,oneof"`
}

func (*RecurringPaymentMethodModel_Extra_Card) isRecurringPaymentMethodModel_Extra_Extra() {}

// stripe cof
type RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 保存的 method id , pm_xxx 格式
	RecurringMethodId string `protobuf:"bytes,1,opt,name=recurring_method_id,json=recurringMethodId,proto3" json:"recurring_method_id,omitempty"`
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF) Reset() {
	*x = RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF) ProtoMessage() {}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF.ProtoReflect.Descriptor instead.
func (*RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{6, 0, 0}
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF) GetRecurringMethodId() string {
	if x != nil {
		return x.RecurringMethodId
	}
	return ""
}

// adyen cof
type RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// adyen cof id
	RecurringDetailReference string `protobuf:"bytes,1,opt,name=recurring_detail_reference,json=recurringDetailReference,proto3" json:"recurring_detail_reference,omitempty"`
	// encrypted security code
	EncryptedSecurityCode string `protobuf:"bytes,2,opt,name=encrypted_security_code,json=encryptedSecurityCode,proto3" json:"encrypted_security_code,omitempty"`
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF) Reset() {
	*x = RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF) ProtoMessage() {}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payment_models_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF.ProtoReflect.Descriptor instead.
func (*RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_models_proto_rawDescGZIP(), []int{6, 0, 1}
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF) GetRecurringDetailReference() string {
	if x != nil {
		return x.RecurringDetailReference
	}
	return ""
}

func (x *RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF) GetEncryptedSecurityCode() string {
	if x != nil {
		return x.EncryptedSecurityCode
	}
	return ""
}

var File_moego_models_payment_v2_payment_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v2_payment_models_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7d, 0x0a, 0x04, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x44, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xea, 0x0b, 0x0a, 0x0c, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61,
	0x79, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x12,
	0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70,
	0x61, 0x79, 0x65, 0x65, 0x12, 0x4a, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49,
	0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74,
	0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x46, 0x65, 0x65, 0x12, 0x3b, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65,
	0x65, 0x12, 0x3b, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x06, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x06, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x12, 0x54, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x1a,
	0x85, 0x02, 0x0a, 0x06, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x58, 0x0a, 0x0d, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x45, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x52, 0x45,
	0x46, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x52,
	0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x22, 0x93, 0x01, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54,
	0x45, 0x44, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x54, 0x45,
	0x44, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44,
	0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10,
	0x05, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x12, 0x0e, 0x0a,
	0x0a, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x07, 0x22, 0x47, 0x0a,
	0x0b, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x54,
	0x41, 0x4e, 0x44, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x52, 0x45, 0x5f,
	0x41, 0x55, 0x54, 0x48, 0x10, 0x02, 0x22, 0xe7, 0x0b, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x05, 0x70,
	0x61, 0x79, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65,
	0x12, 0x4a, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x47, 0x0a,
	0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x33, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65,
	0x12, 0x3b, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x66, 0x65, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x12, 0x3b, 0x0a,
	0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x0c, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x52, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x44, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52,
	0x06, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x46, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x56, 0x69, 0x65, 0x77, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x11, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x69, 0x64, 0x42, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x22, 0xb4, 0x14, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x52, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39,
	0x0a, 0x19, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x16, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x06, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x1a, 0xe3, 0x10, 0x0a, 0x06, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x48, 0x0a, 0x04, 0x63,
	0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x48, 0x00, 0x52,
	0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x80, 0x01, 0x0a, 0x18, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x48, 0x00,
	0x52, 0x16, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x58, 0x0a, 0x0a, 0x62, 0x6f, 0x6f, 0x6b,
	0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x42, 0x6f, 0x6f, 0x6b,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x48, 0x00, 0x52, 0x09, 0x62, 0x6f, 0x6f, 0x6b, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x54, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x08,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x1a, 0x25, 0x0a, 0x0b, 0x41, 0x64, 0x79, 0x65,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x1a,
	0xed, 0x07, 0x0a, 0x04, 0x43, 0x61, 0x72, 0x64, 0x12, 0x51, 0x0a, 0x05, 0x61, 0x64, 0x79, 0x65,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x48, 0x00, 0x52, 0x05, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x0d, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x01, 0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x55,
	0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x73, 0x61, 0x76, 0x65, 0x43, 0x61,
	0x72, 0x64, 0x12, 0x52, 0x0a, 0x0e, 0x66, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x48, 0x02, 0x52, 0x0d, 0x66, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x53, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x48,
	0x03, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x88, 0x01, 0x01, 0x1a, 0xe8, 0x04, 0x0a, 0x05,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x2d, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x6f,
	0x75, 0x72, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x44, 0x69, 0x67, 0x69, 0x74,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x02, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x59, 0x65, 0x61, 0x72, 0x88,
	0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x03, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a,
	0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x05,
	0x61, 0x6c, 0x69, 0x61, 0x73, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x61,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x05, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x08, 0x6c, 0x61, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x19, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x52, 0x0a, 0x0e, 0x66,
	0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x75,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x0a, 0x52, 0x0d, 0x66,
	0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42,
	0x13, 0x0a, 0x11, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x72, 0x5f, 0x64, 0x69,
	0x67, 0x69, 0x74, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79,
	0x5f, 0x79, 0x65, 0x61, 0x72, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x42,
	0x08, 0x0a, 0x06, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x69, 0x73,
	0x5f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x1a,
	0xf6, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x0d, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x55, 0x72,
	0x6c, 0x88, 0x01, 0x01, 0x12, 0x73, 0x0a, 0x18, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x01, 0x52, 0x16,
	0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x1b, 0x0a, 0x19, 0x5f,
	0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x1a, 0x95, 0x02, 0x0a, 0x09, 0x42, 0x6f, 0x6f,
	0x6b, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x55, 0x0a, 0x05, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x05, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x55, 0x0a,
	0x05, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x42, 0x6f, 0x6f, 0x6b,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x2e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x48, 0x00, 0x52, 0x05, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x1a, 0x2a, 0x0a, 0x05, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x1a, 0x24, 0x0a, 0x05, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x42, 0x08, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x1a, 0xa9, 0x01, 0x0a, 0x08, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3c, 0x0a,
	0x10, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x74, 0x69, 0x70,
	0x73, 0x42, 0x61, 0x73, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x08, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x48, 0x00, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x42, 0x08, 0x0a, 0x06,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x6f, 0x0a, 0x0a, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x52,
	0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x4f, 0x4f,
	0x4b, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x45, 0x52,
	0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x10, 0x04, 0x22, 0xfd, 0x04, 0x0a, 0x0b, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x05,
	0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65,
	0x65, 0x12, 0x4a, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x47,
	0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x49,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x22, 0x64, 0x0a, 0x0c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a,
	0x09, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x22, 0xd1, 0x04, 0x0a, 0x0a, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x12, 0x4a, 0x0a,
	0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x45,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xe9, 0x09, 0x0a, 0x1b,
	0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x7f, 0x0a, 0x16, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x52, 0x14, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x60, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x55, 0x0a, 0x05, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x48, 0x00, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x88, 0x01,
	0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xd9, 0x03, 0x0a, 0x14,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x74, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x63,
	0x6f, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x4f, 0x46, 0x48, 0x00, 0x52,
	0x09, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x6f, 0x66, 0x12, 0x71, 0x0a, 0x09, 0x61, 0x64,
	0x79, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x52, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x43, 0x4f,
	0x46, 0x48, 0x00, 0x52, 0x08, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x43, 0x6f, 0x66, 0x1a, 0x3b, 0x0a,
	0x09, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x4f, 0x46, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x1a, 0x80, 0x01, 0x0a, 0x08, 0x41,
	0x64, 0x79, 0x65, 0x6e, 0x43, 0x4f, 0x46, 0x12, 0x3c, 0x0a, 0x1a, 0x72, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65,
	0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x18, 0x0a,
	0x16, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x1a, 0x60, 0x0a, 0x05, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x12, 0x4e, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x48, 0x00, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64,
	0x42, 0x07, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x32, 0x0a, 0x0a, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x45, 0x54, 0x48, 0x4f,
	0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x43, 0x4f, 0x46, 0x10, 0x01, 0x42, 0x08, 0x0a,
	0x06, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0xe7, 0x04, 0x0a, 0x1a, 0x52, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x31, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x7f, 0x0a, 0x16, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x14, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x12, 0x60, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x12, 0x50, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0xb0, 0x09, 0x0a, 0x17, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x33, 0x0a,
	0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79,
	0x65, 0x72, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x12, 0x4a, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a,
	0x16, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x33, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x12,
	0x3b, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66,
	0x65, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x12, 0x5a, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x52, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0c, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x22, 0x9b, 0x01, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a,
	0x08, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x53,
	0x55, 0x42, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e,
	0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a,
	0x45, 0x44, 0x10, 0x07, 0x22, 0x97, 0x08, 0x0a, 0x16, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70,
	0x61, 0x79, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x69,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x69, 0x64,
	0x42, 0x79, 0x12, 0x4a, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x69,
	0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x39, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x12, 0x3b, 0x0a, 0x0f, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x12, 0x5a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2d, 0x0a,
	0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76,
	0x69, 0x65, 0x77, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x73, 0x22, 0xf9,
	0x03, 0x0a, 0x08, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x53, 0x0a, 0x0d, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x6c, 0x0a, 0x0c, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x45, 0x52, 0x4d,
	0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x4d, 0x41, 0x52, 0x54,
	0x5f, 0x52, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x42, 0x4c, 0x55,
	0x45, 0x54, 0x4f, 0x4f, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x02, 0x12,
	0x15, 0x0a, 0x11, 0x54, 0x41, 0x50, 0x5f, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x52, 0x45,
	0x41, 0x44, 0x45, 0x52, 0x10, 0x03, 0x22, 0x32, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x15, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x44, 0x4c, 0x45, 0x10, 0x01,
	0x12, 0x08, 0x0a, 0x04, 0x42, 0x55, 0x53, 0x59, 0x10, 0x02, 0x22, 0xdb, 0x02, 0x0a, 0x0c, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x53, 0x0a, 0x0d, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x56, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v2_payment_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v2_payment_models_proto_rawDescData = file_moego_models_payment_v2_payment_models_proto_rawDesc
)

func file_moego_models_payment_v2_payment_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v2_payment_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v2_payment_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v2_payment_models_proto_rawDescData)
	})
	return file_moego_models_payment_v2_payment_models_proto_rawDescData
}

var file_moego_models_payment_v2_payment_models_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_moego_models_payment_v2_payment_models_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_moego_models_payment_v2_payment_models_proto_goTypes = []interface{}{
	(PaymentModel_PaymentStatus)(0),                                    // 0: moego.models.payment.v2.PaymentModel.PaymentStatus
	(PaymentModel_PaymentType)(0),                                      // 1: moego.models.payment.v2.PaymentModel.PaymentType
	(PaymentModel_Refund_Status)(0),                                    // 2: moego.models.payment.v2.PaymentModel.Refund.Status
	(PaymentMethod_MethodType)(0),                                      // 3: moego.models.payment.v2.PaymentMethod.MethodType
	(RefundModel_RefundStatus)(0),                                      // 4: moego.models.payment.v2.RefundModel.RefundStatus
	(RecurringPaymentMethodModel_MethodType)(0),                        // 5: moego.models.payment.v2.RecurringPaymentMethodModel.MethodType
	(PaymentTransactionModel_TransactionStatus)(0),                     // 6: moego.models.payment.v2.PaymentTransactionModel.TransactionStatus
	(Terminal_TerminalType)(0),                                         // 7: moego.models.payment.v2.Terminal.TerminalType
	(Terminal_State)(0),                                                // 8: moego.models.payment.v2.Terminal.State
	(*User)(nil),                                                       // 9: moego.models.payment.v2.User
	(*PaymentModel)(nil),                                               // 10: moego.models.payment.v2.PaymentModel
	(*PaymentView)(nil),                                                // 11: moego.models.payment.v2.PaymentView
	(*PaymentMethod)(nil),                                              // 12: moego.models.payment.v2.PaymentMethod
	(*RefundModel)(nil),                                                // 13: moego.models.payment.v2.RefundModel
	(*RefundView)(nil),                                                 // 14: moego.models.payment.v2.RefundView
	(*RecurringPaymentMethodModel)(nil),                                // 15: moego.models.payment.v2.RecurringPaymentMethodModel
	(*RecurringPaymentMethodView)(nil),                                 // 16: moego.models.payment.v2.RecurringPaymentMethodView
	(*PaymentTransactionModel)(nil),                                    // 17: moego.models.payment.v2.PaymentTransactionModel
	(*PaymentTransactionView)(nil),                                     // 18: moego.models.payment.v2.PaymentTransactionView
	(*Terminal)(nil),                                                   // 19: moego.models.payment.v2.Terminal
	(*TerminalView)(nil),                                               // 20: moego.models.payment.v2.TerminalView
	(*PaymentModel_Refund)(nil),                                        // 21: moego.models.payment.v2.PaymentModel.Refund
	(*PaymentMethod_Detail)(nil),                                       // 22: moego.models.payment.v2.PaymentMethod.Detail
	(*PaymentMethod_Detail_AdyenDetail)(nil),                           // 23: moego.models.payment.v2.PaymentMethod.Detail.AdyenDetail
	(*PaymentMethod_Detail_Card)(nil),                                  // 24: moego.models.payment.v2.PaymentMethod.Detail.Card
	(*PaymentMethod_Detail_RecurringPaymentMethod)(nil),                // 25: moego.models.payment.v2.PaymentMethod.Detail.RecurringPaymentMethod
	(*PaymentMethod_Detail_BookEntry)(nil),                             // 26: moego.models.payment.v2.PaymentMethod.Detail.BookEntry
	(*PaymentMethod_Detail_Terminal)(nil),                              // 27: moego.models.payment.v2.PaymentMethod.Detail.Terminal
	(*PaymentMethod_Detail_Card_Extra)(nil),                            // 28: moego.models.payment.v2.PaymentMethod.Detail.Card.Extra
	(*PaymentMethod_Detail_BookEntry_Check)(nil),                       // 29: moego.models.payment.v2.PaymentMethod.Detail.BookEntry.Check
	(*PaymentMethod_Detail_BookEntry_Other)(nil),                       // 30: moego.models.payment.v2.PaymentMethod.Detail.BookEntry.Other
	(*RecurringPaymentMethodModel_ChannelPaymentMethod)(nil),           // 31: moego.models.payment.v2.RecurringPaymentMethodModel.ChannelPaymentMethod
	(*RecurringPaymentMethodModel_Extra)(nil),                          // 32: moego.models.payment.v2.RecurringPaymentMethodModel.Extra
	(*RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF)(nil), // 33: moego.models.payment.v2.RecurringPaymentMethodModel.ChannelPaymentMethod.StripeCOF
	(*RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF)(nil),  // 34: moego.models.payment.v2.RecurringPaymentMethodModel.ChannelPaymentMethod.AdyenCOF
	(EntityType)(0),                                                    // 35: moego.models.payment.v2.EntityType
	(ExternalType)(0),                                                  // 36: moego.models.payment.v2.ExternalType
	(ChannelType)(0),                                                   // 37: moego.models.payment.v2.ChannelType
	(*money.Money)(nil),                                                // 38: google.type.Money
	(*timestamppb.Timestamp)(nil),                                      // 39: google.protobuf.Timestamp
	(FundingSource)(0),                                                 // 40: moego.models.payment.v2.FundingSource
}
var file_moego_models_payment_v2_payment_models_proto_depIdxs = []int32{
	35,  // 0: moego.models.payment.v2.User.entity_type:type_name -> moego.models.payment.v2.EntityType
	9,   // 1: moego.models.payment.v2.PaymentModel.payer:type_name -> moego.models.payment.v2.User
	9,   // 2: moego.models.payment.v2.PaymentModel.payee:type_name -> moego.models.payment.v2.User
	36,  // 3: moego.models.payment.v2.PaymentModel.external_type:type_name -> moego.models.payment.v2.ExternalType
	37,  // 4: moego.models.payment.v2.PaymentModel.channel_type:type_name -> moego.models.payment.v2.ChannelType
	38,  // 5: moego.models.payment.v2.PaymentModel.amount:type_name -> google.type.Money
	38,  // 6: moego.models.payment.v2.PaymentModel.tips_amount:type_name -> google.type.Money
	38,  // 7: moego.models.payment.v2.PaymentModel.processing_fee:type_name -> google.type.Money
	38,  // 8: moego.models.payment.v2.PaymentModel.convenience_fee:type_name -> google.type.Money
	38,  // 9: moego.models.payment.v2.PaymentModel.discount_amount:type_name -> google.type.Money
	0,   // 10: moego.models.payment.v2.PaymentModel.status:type_name -> moego.models.payment.v2.PaymentModel.PaymentStatus
	21,  // 11: moego.models.payment.v2.PaymentModel.refund:type_name -> moego.models.payment.v2.PaymentModel.Refund
	1,   // 12: moego.models.payment.v2.PaymentModel.payment_type:type_name -> moego.models.payment.v2.PaymentModel.PaymentType
	3,   // 13: moego.models.payment.v2.PaymentModel.method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	9,   // 14: moego.models.payment.v2.PaymentView.payer:type_name -> moego.models.payment.v2.User
	9,   // 15: moego.models.payment.v2.PaymentView.payee:type_name -> moego.models.payment.v2.User
	36,  // 16: moego.models.payment.v2.PaymentView.external_type:type_name -> moego.models.payment.v2.ExternalType
	37,  // 17: moego.models.payment.v2.PaymentView.channel_type:type_name -> moego.models.payment.v2.ChannelType
	38,  // 18: moego.models.payment.v2.PaymentView.amount:type_name -> google.type.Money
	38,  // 19: moego.models.payment.v2.PaymentView.tips_amount:type_name -> google.type.Money
	38,  // 20: moego.models.payment.v2.PaymentView.processing_fee:type_name -> google.type.Money
	38,  // 21: moego.models.payment.v2.PaymentView.convenience_fee:type_name -> google.type.Money
	38,  // 22: moego.models.payment.v2.PaymentView.discount_amount:type_name -> google.type.Money
	1,   // 23: moego.models.payment.v2.PaymentView.payment_type:type_name -> moego.models.payment.v2.PaymentModel.PaymentType
	3,   // 24: moego.models.payment.v2.PaymentView.method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	0,   // 25: moego.models.payment.v2.PaymentView.status:type_name -> moego.models.payment.v2.PaymentModel.PaymentStatus
	21,  // 26: moego.models.payment.v2.PaymentView.refund:type_name -> moego.models.payment.v2.PaymentModel.Refund
	14,  // 27: moego.models.payment.v2.PaymentView.refund_views:type_name -> moego.models.payment.v2.RefundView
	38,  // 28: moego.models.payment.v2.PaymentView.refundable_amount:type_name -> google.type.Money
	39,  // 29: moego.models.payment.v2.PaymentView.create_time:type_name -> google.protobuf.Timestamp
	39,  // 30: moego.models.payment.v2.PaymentView.update_time:type_name -> google.protobuf.Timestamp
	3,   // 31: moego.models.payment.v2.PaymentMethod.method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	37,  // 32: moego.models.payment.v2.PaymentMethod.channel_type:type_name -> moego.models.payment.v2.ChannelType
	22,  // 33: moego.models.payment.v2.PaymentMethod.detail:type_name -> moego.models.payment.v2.PaymentMethod.Detail
	9,   // 34: moego.models.payment.v2.RefundModel.payer:type_name -> moego.models.payment.v2.User
	9,   // 35: moego.models.payment.v2.RefundModel.payee:type_name -> moego.models.payment.v2.User
	36,  // 36: moego.models.payment.v2.RefundModel.external_type:type_name -> moego.models.payment.v2.ExternalType
	37,  // 37: moego.models.payment.v2.RefundModel.channel_type:type_name -> moego.models.payment.v2.ChannelType
	38,  // 38: moego.models.payment.v2.RefundModel.amount:type_name -> google.type.Money
	4,   // 39: moego.models.payment.v2.RefundModel.status:type_name -> moego.models.payment.v2.RefundModel.RefundStatus
	9,   // 40: moego.models.payment.v2.RefundView.payer:type_name -> moego.models.payment.v2.User
	9,   // 41: moego.models.payment.v2.RefundView.payee:type_name -> moego.models.payment.v2.User
	36,  // 42: moego.models.payment.v2.RefundView.external_type:type_name -> moego.models.payment.v2.ExternalType
	37,  // 43: moego.models.payment.v2.RefundView.channel_type:type_name -> moego.models.payment.v2.ChannelType
	38,  // 44: moego.models.payment.v2.RefundView.amount:type_name -> google.type.Money
	4,   // 45: moego.models.payment.v2.RefundView.status:type_name -> moego.models.payment.v2.RefundModel.RefundStatus
	37,  // 46: moego.models.payment.v2.RecurringPaymentMethodModel.channel_type:type_name -> moego.models.payment.v2.ChannelType
	9,   // 47: moego.models.payment.v2.RecurringPaymentMethodModel.user:type_name -> moego.models.payment.v2.User
	31,  // 48: moego.models.payment.v2.RecurringPaymentMethodModel.channel_payment_method:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.ChannelPaymentMethod
	5,   // 49: moego.models.payment.v2.RecurringPaymentMethodModel.method_type:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.MethodType
	32,  // 50: moego.models.payment.v2.RecurringPaymentMethodModel.extra:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.Extra
	39,  // 51: moego.models.payment.v2.RecurringPaymentMethodModel.created_at:type_name -> google.protobuf.Timestamp
	37,  // 52: moego.models.payment.v2.RecurringPaymentMethodView.channel_type:type_name -> moego.models.payment.v2.ChannelType
	9,   // 53: moego.models.payment.v2.RecurringPaymentMethodView.user:type_name -> moego.models.payment.v2.User
	31,  // 54: moego.models.payment.v2.RecurringPaymentMethodView.channel_payment_method:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.ChannelPaymentMethod
	5,   // 55: moego.models.payment.v2.RecurringPaymentMethodView.method_type:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.MethodType
	32,  // 56: moego.models.payment.v2.RecurringPaymentMethodView.extra:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.Extra
	39,  // 57: moego.models.payment.v2.RecurringPaymentMethodView.created_at:type_name -> google.protobuf.Timestamp
	9,   // 58: moego.models.payment.v2.PaymentTransactionModel.payer:type_name -> moego.models.payment.v2.User
	9,   // 59: moego.models.payment.v2.PaymentTransactionModel.payee:type_name -> moego.models.payment.v2.User
	36,  // 60: moego.models.payment.v2.PaymentTransactionModel.external_type:type_name -> moego.models.payment.v2.ExternalType
	37,  // 61: moego.models.payment.v2.PaymentTransactionModel.channel_type:type_name -> moego.models.payment.v2.ChannelType
	38,  // 62: moego.models.payment.v2.PaymentTransactionModel.amount:type_name -> google.type.Money
	38,  // 63: moego.models.payment.v2.PaymentTransactionModel.tips_amount:type_name -> google.type.Money
	38,  // 64: moego.models.payment.v2.PaymentTransactionModel.processing_fee:type_name -> google.type.Money
	38,  // 65: moego.models.payment.v2.PaymentTransactionModel.convenience_fee:type_name -> google.type.Money
	6,   // 66: moego.models.payment.v2.PaymentTransactionModel.status:type_name -> moego.models.payment.v2.PaymentTransactionModel.TransactionStatus
	39,  // 67: moego.models.payment.v2.PaymentTransactionModel.create_time:type_name -> google.protobuf.Timestamp
	39,  // 68: moego.models.payment.v2.PaymentTransactionModel.update_time:type_name -> google.protobuf.Timestamp
	3,   // 69: moego.models.payment.v2.PaymentTransactionModel.method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	22,  // 70: moego.models.payment.v2.PaymentTransactionModel.method_detail:type_name -> moego.models.payment.v2.PaymentMethod.Detail
	9,   // 71: moego.models.payment.v2.PaymentTransactionView.payer:type_name -> moego.models.payment.v2.User
	9,   // 72: moego.models.payment.v2.PaymentTransactionView.payee:type_name -> moego.models.payment.v2.User
	36,  // 73: moego.models.payment.v2.PaymentTransactionView.external_type:type_name -> moego.models.payment.v2.ExternalType
	37,  // 74: moego.models.payment.v2.PaymentTransactionView.channel_type:type_name -> moego.models.payment.v2.ChannelType
	38,  // 75: moego.models.payment.v2.PaymentTransactionView.amount:type_name -> google.type.Money
	38,  // 76: moego.models.payment.v2.PaymentTransactionView.tips_amount:type_name -> google.type.Money
	38,  // 77: moego.models.payment.v2.PaymentTransactionView.processing_fee:type_name -> google.type.Money
	38,  // 78: moego.models.payment.v2.PaymentTransactionView.convenience_fee:type_name -> google.type.Money
	6,   // 79: moego.models.payment.v2.PaymentTransactionView.status:type_name -> moego.models.payment.v2.PaymentTransactionModel.TransactionStatus
	39,  // 80: moego.models.payment.v2.PaymentTransactionView.create_time:type_name -> google.protobuf.Timestamp
	39,  // 81: moego.models.payment.v2.PaymentTransactionView.update_time:type_name -> google.protobuf.Timestamp
	11,  // 82: moego.models.payment.v2.PaymentTransactionView.payment_views:type_name -> moego.models.payment.v2.PaymentView
	7,   // 83: moego.models.payment.v2.Terminal.terminal_type:type_name -> moego.models.payment.v2.Terminal.TerminalType
	37,  // 84: moego.models.payment.v2.Terminal.channel_type:type_name -> moego.models.payment.v2.ChannelType
	8,   // 85: moego.models.payment.v2.Terminal.state:type_name -> moego.models.payment.v2.Terminal.State
	7,   // 86: moego.models.payment.v2.TerminalView.terminal_type:type_name -> moego.models.payment.v2.Terminal.TerminalType
	37,  // 87: moego.models.payment.v2.TerminalView.channel_type:type_name -> moego.models.payment.v2.ChannelType
	8,   // 88: moego.models.payment.v2.TerminalView.state:type_name -> moego.models.payment.v2.Terminal.State
	2,   // 89: moego.models.payment.v2.PaymentModel.Refund.refund_status:type_name -> moego.models.payment.v2.PaymentModel.Refund.Status
	38,  // 90: moego.models.payment.v2.PaymentModel.Refund.refund_amount:type_name -> google.type.Money
	24,  // 91: moego.models.payment.v2.PaymentMethod.Detail.card:type_name -> moego.models.payment.v2.PaymentMethod.Detail.Card
	25,  // 92: moego.models.payment.v2.PaymentMethod.Detail.recurring_payment_method:type_name -> moego.models.payment.v2.PaymentMethod.Detail.RecurringPaymentMethod
	26,  // 93: moego.models.payment.v2.PaymentMethod.Detail.book_entry:type_name -> moego.models.payment.v2.PaymentMethod.Detail.BookEntry
	27,  // 94: moego.models.payment.v2.PaymentMethod.Detail.terminal:type_name -> moego.models.payment.v2.PaymentMethod.Detail.Terminal
	23,  // 95: moego.models.payment.v2.PaymentMethod.Detail.Card.adyen:type_name -> moego.models.payment.v2.PaymentMethod.Detail.AdyenDetail
	40,  // 96: moego.models.payment.v2.PaymentMethod.Detail.Card.funding_source:type_name -> moego.models.payment.v2.FundingSource
	28,  // 97: moego.models.payment.v2.PaymentMethod.Detail.Card.extra:type_name -> moego.models.payment.v2.PaymentMethod.Detail.Card.Extra
	15,  // 98: moego.models.payment.v2.PaymentMethod.Detail.RecurringPaymentMethod.recurring_payment_method:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel
	29,  // 99: moego.models.payment.v2.PaymentMethod.Detail.BookEntry.check:type_name -> moego.models.payment.v2.PaymentMethod.Detail.BookEntry.Check
	30,  // 100: moego.models.payment.v2.PaymentMethod.Detail.BookEntry.other:type_name -> moego.models.payment.v2.PaymentMethod.Detail.BookEntry.Other
	38,  // 101: moego.models.payment.v2.PaymentMethod.Detail.Terminal.tips_base_amount:type_name -> google.type.Money
	19,  // 102: moego.models.payment.v2.PaymentMethod.Detail.Terminal.terminal:type_name -> moego.models.payment.v2.Terminal
	40,  // 103: moego.models.payment.v2.PaymentMethod.Detail.Card.Extra.funding_source:type_name -> moego.models.payment.v2.FundingSource
	33,  // 104: moego.models.payment.v2.RecurringPaymentMethodModel.ChannelPaymentMethod.stripe_cof:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.ChannelPaymentMethod.StripeCOF
	34,  // 105: moego.models.payment.v2.RecurringPaymentMethodModel.ChannelPaymentMethod.adyen_cof:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.ChannelPaymentMethod.AdyenCOF
	28,  // 106: moego.models.payment.v2.RecurringPaymentMethodModel.Extra.card:type_name -> moego.models.payment.v2.PaymentMethod.Detail.Card.Extra
	107, // [107:107] is the sub-list for method output_type
	107, // [107:107] is the sub-list for method input_type
	107, // [107:107] is the sub-list for extension type_name
	107, // [107:107] is the sub-list for extension extendee
	0,   // [0:107] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v2_payment_models_proto_init() }
func file_moego_models_payment_v2_payment_models_proto_init() {
	if File_moego_models_payment_v2_payment_models_proto != nil {
		return
	}
	file_moego_models_payment_v2_common_enums_proto_init()
	file_moego_models_payment_v2_payment_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v2_payment_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecurringPaymentMethodModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecurringPaymentMethodView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentTransactionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentTransactionView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Terminal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TerminalView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentModel_Refund); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethod_Detail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethod_Detail_AdyenDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethod_Detail_Card); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethod_Detail_RecurringPaymentMethod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethod_Detail_BookEntry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethod_Detail_Terminal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethod_Detail_Card_Extra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethod_Detail_BookEntry_Check); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethod_Detail_BookEntry_Other); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecurringPaymentMethodModel_ChannelPaymentMethod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecurringPaymentMethodModel_Extra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCOF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payment_models_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCOF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_payment_v2_payment_models_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_models_payment_v2_payment_models_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*PaymentMethod_Detail_Card_)(nil),
		(*PaymentMethod_Detail_RecurringPaymentMethod_)(nil),
		(*PaymentMethod_Detail_BookEntry_)(nil),
		(*PaymentMethod_Detail_Terminal_)(nil),
	}
	file_moego_models_payment_v2_payment_models_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*PaymentMethod_Detail_Card_Adyen)(nil),
	}
	file_moego_models_payment_v2_payment_models_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_models_payment_v2_payment_models_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*PaymentMethod_Detail_BookEntry_Check_)(nil),
		(*PaymentMethod_Detail_BookEntry_Other_)(nil),
	}
	file_moego_models_payment_v2_payment_models_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_moego_models_payment_v2_payment_models_proto_msgTypes[19].OneofWrappers = []interface{}{}
	file_moego_models_payment_v2_payment_models_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*RecurringPaymentMethodModel_ChannelPaymentMethod_StripeCof)(nil),
		(*RecurringPaymentMethodModel_ChannelPaymentMethod_AdyenCof)(nil),
	}
	file_moego_models_payment_v2_payment_models_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*RecurringPaymentMethodModel_Extra_Card)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v2_payment_models_proto_rawDesc,
			NumEnums:      9,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v2_payment_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v2_payment_models_proto_depIdxs,
		EnumInfos:         file_moego_models_payment_v2_payment_models_proto_enumTypes,
		MessageInfos:      file_moego_models_payment_v2_payment_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v2_payment_models_proto = out.File
	file_moego_models_payment_v2_payment_models_proto_rawDesc = nil
	file_moego_models_payment_v2_payment_models_proto_goTypes = nil
	file_moego_models_payment_v2_payment_models_proto_depIdxs = nil
}
