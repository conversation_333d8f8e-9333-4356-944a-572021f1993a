// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/usps/v1/usps_models.proto

package uspspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AddressInfo
type AddressInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The two-character state code.
	State string `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	// This is the city name of the address.
	City string `protobuf:"bytes,2,opt,name=city,proto3" json:"city,omitempty"`
	// This is the abbreviation of the city name for the address.
	CityAbbreviation *string `protobuf:"bytes,3,opt,name=city_abbreviation,json=cityAbbreviation,proto3,oneof" json:"city_abbreviation,omitempty"`
	// The number of a building along with the name of the road or street on which it is located.
	StreetAddress *string `protobuf:"bytes,4,opt,name=street_address,json=streetAddress,proto3,oneof" json:"street_address,omitempty"`
	// This is the abbreviation of the primary street address line for the address.
	StreetAddressAbbreviation *string `protobuf:"bytes,5,opt,name=street_address_abbreviation,json=streetAddressAbbreviation,proto3,oneof" json:"street_address_abbreviation,omitempty"`
	// The secondary unit designator, such as apartment(APT) or suite(STE) number, defining the exact location of the address within a building.
	// For more information please see Postal Explorer.
	SecondaryAddress *string `protobuf:"bytes,6,opt,name=secondary_address,json=secondaryAddress,proto3,oneof" json:"secondary_address,omitempty"`
	// This is the 5-digit ZIP code.
	Zipcode *string `protobuf:"bytes,7,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// This is the 4-digit component of the ZIP+4 code.
	// Using the correct ZIP+4 reduces the number of times your mail is handled and can decrease the chance of a misdelivery or error.
	ZipPlus4 *string `protobuf:"bytes,8,opt,name=zip_plus4,json=zipPlus4,proto3,oneof" json:"zip_plus4,omitempty"`
	// An area, sector, or residential development within a geographic area (typically used for addresses in Puerto Rico).
	Urbanization *string `protobuf:"bytes,9,opt,name=urbanization,proto3,oneof" json:"urbanization,omitempty"`
}

func (x *AddressInfo) Reset() {
	*x = AddressInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_usps_v1_usps_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressInfo) ProtoMessage() {}

func (x *AddressInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_usps_v1_usps_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressInfo.ProtoReflect.Descriptor instead.
func (*AddressInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_usps_v1_usps_models_proto_rawDescGZIP(), []int{0}
}

func (x *AddressInfo) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *AddressInfo) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *AddressInfo) GetCityAbbreviation() string {
	if x != nil && x.CityAbbreviation != nil {
		return *x.CityAbbreviation
	}
	return ""
}

func (x *AddressInfo) GetStreetAddress() string {
	if x != nil && x.StreetAddress != nil {
		return *x.StreetAddress
	}
	return ""
}

func (x *AddressInfo) GetStreetAddressAbbreviation() string {
	if x != nil && x.StreetAddressAbbreviation != nil {
		return *x.StreetAddressAbbreviation
	}
	return ""
}

func (x *AddressInfo) GetSecondaryAddress() string {
	if x != nil && x.SecondaryAddress != nil {
		return *x.SecondaryAddress
	}
	return ""
}

func (x *AddressInfo) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *AddressInfo) GetZipPlus4() string {
	if x != nil && x.ZipPlus4 != nil {
		return *x.ZipPlus4
	}
	return ""
}

func (x *AddressInfo) GetUrbanization() string {
	if x != nil && x.Urbanization != nil {
		return *x.Urbanization
	}
	return ""
}

// AddressAdditionalInfo
type AddressAdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A specific set of digits between 00 and 99 is assigned to every address that is combined with the ZIP + 4® Code to provide a unique identifier for every delivery address.
	// A street address does not necessarily represent a single delivery point because a street address such as one for an apartment building may have several delivery points.
	DeliveryPoint *string `protobuf:"bytes,1,opt,name=delivery_point,json=deliveryPoint,proto3,oneof" json:"delivery_point,omitempty"`
	// Central Delivery is for all business office buildings and/or industrial/professional parks.
	// This may include call windows, horizontal locked mail receptacles, and cluster box units.
	CentralDeliveryPoint *bool `protobuf:"varint,2,opt,name=central_delivery_point,json=centralDeliveryPoint,proto3,oneof" json:"central_delivery_point,omitempty"` // e.g. "Y", "N"
	// This is the carrier route code (values unspecified).
	CarrierRoute *string `protobuf:"bytes,3,opt,name=carrier_route,json=carrierRoute,proto3,oneof" json:"carrier_route,omitempty"`
	// Enum: "Y" "D" "S" "N"
	// The DPV Confirmation Indicator is the primary method used by the USPS® to determine whether an address is considered deliverable or undeliverable.
	//   - Y 'Address was DPV confirmed for both primary and (if present) secondary numbers.'
	//   - D 'Address was DPV confirmed for the primary number only, and the secondary number information was missing.'
	//   - S 'Address was DPV confirmed for the primary number only, and the secondary number information was present but not confirmed.'
	//   - N 'Both primary and (if present) secondary number information failed to DPV confirm.'
	DpvConfirmation *string `protobuf:"bytes,4,opt,name=dpv_confirmation,json=dpvConfirmation,proto3,oneof" json:"dpv_confirmation,omitempty"`
	// Indicates if the location is a Commercial Mail Receiving Agency (CMRA).
	// see: https://faq.usps.com/s/article/Commercial-Mail-Receiving-Agency-CMRA
	DpvCmra *bool `protobuf:"varint,5,opt,name=dpv_cmra,json=dpvCmra,proto3,oneof" json:"dpv_cmra,omitempty"`
	// Indicates whether this is a business address.
	Business *bool `protobuf:"varint,6,opt,name=business,proto3,oneof" json:"business,omitempty"`
	// Indicates whether the location designated by the address is occupied.
	Vacant *bool `protobuf:"varint,7,opt,name=vacant,proto3,oneof" json:"vacant,omitempty"`
}

func (x *AddressAdditionalInfo) Reset() {
	*x = AddressAdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_usps_v1_usps_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressAdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressAdditionalInfo) ProtoMessage() {}

func (x *AddressAdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_usps_v1_usps_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressAdditionalInfo.ProtoReflect.Descriptor instead.
func (*AddressAdditionalInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_usps_v1_usps_models_proto_rawDescGZIP(), []int{1}
}

func (x *AddressAdditionalInfo) GetDeliveryPoint() string {
	if x != nil && x.DeliveryPoint != nil {
		return *x.DeliveryPoint
	}
	return ""
}

func (x *AddressAdditionalInfo) GetCentralDeliveryPoint() bool {
	if x != nil && x.CentralDeliveryPoint != nil {
		return *x.CentralDeliveryPoint
	}
	return false
}

func (x *AddressAdditionalInfo) GetCarrierRoute() string {
	if x != nil && x.CarrierRoute != nil {
		return *x.CarrierRoute
	}
	return ""
}

func (x *AddressAdditionalInfo) GetDpvConfirmation() string {
	if x != nil && x.DpvConfirmation != nil {
		return *x.DpvConfirmation
	}
	return ""
}

func (x *AddressAdditionalInfo) GetDpvCmra() bool {
	if x != nil && x.DpvCmra != nil {
		return *x.DpvCmra
	}
	return false
}

func (x *AddressAdditionalInfo) GetBusiness() bool {
	if x != nil && x.Business != nil {
		return *x.Business
	}
	return false
}

func (x *AddressAdditionalInfo) GetVacant() bool {
	if x != nil && x.Vacant != nil {
		return *x.Vacant
	}
	return false
}

// AddressCorrection
// Codes that indicate how to improve the address input to get a better match.
// Code 32 will indicate "Default address: The address you entered was found but more information is needed (such as an apartment, suite, or box number."
// The recommended change would be to add additional information, such as an apartment, suite, or box number, to match to a specific address.
// Code 22 will indicate "Multiple addresses were found for the information you entered, and no default exists."
// The address could not be resolved as entered and more information would be needed to identify the address.
type AddressCorrection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The code corresponding to the address correction.
	Code *string `protobuf:"bytes,1,opt,name=code,proto3,oneof" json:"code,omitempty"`
	// This is the description of the address correction.
	Text *string `protobuf:"bytes,2,opt,name=text,proto3,oneof" json:"text,omitempty"`
}

func (x *AddressCorrection) Reset() {
	*x = AddressCorrection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_usps_v1_usps_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressCorrection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressCorrection) ProtoMessage() {}

func (x *AddressCorrection) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_usps_v1_usps_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressCorrection.ProtoReflect.Descriptor instead.
func (*AddressCorrection) Descriptor() ([]byte, []int) {
	return file_moego_models_usps_v1_usps_models_proto_rawDescGZIP(), []int{2}
}

func (x *AddressCorrection) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *AddressCorrection) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

// AddressMatch
// Codes that indicate if an address is an exact match.
// Code 31 will be returned "Single Response - exact match" indicating that the address was correctly matched to a ZIP+4 record.
type AddressMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code
	Code *string `protobuf:"bytes,1,opt,name=code,proto3,oneof" json:"code,omitempty"`
	// text
	Text *string `protobuf:"bytes,2,opt,name=text,proto3,oneof" json:"text,omitempty"`
}

func (x *AddressMatch) Reset() {
	*x = AddressMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_usps_v1_usps_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressMatch) ProtoMessage() {}

func (x *AddressMatch) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_usps_v1_usps_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressMatch.ProtoReflect.Descriptor instead.
func (*AddressMatch) Descriptor() ([]byte, []int) {
	return file_moego_models_usps_v1_usps_models_proto_rawDescGZIP(), []int{3}
}

func (x *AddressMatch) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *AddressMatch) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

var File_moego_models_usps_v1_usps_models_proto protoreflect.FileDescriptor

var file_moego_models_usps_v1_usps_models_proto_rawDesc = []byte{
	0x0a, 0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x75,
	0x73, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x70, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x75, 0x73, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x22, 0x80,
	0x04, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x30, 0x0a, 0x11, 0x63, 0x69, 0x74, 0x79,
	0x5f, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x10, 0x63, 0x69, 0x74, 0x79, 0x41, 0x62, 0x62, 0x72, 0x65,
	0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x73, 0x74,
	0x72, 0x65, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x01, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a, 0x1b, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x19, 0x73,
	0x74, 0x72, 0x65, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x41, 0x62, 0x62, 0x72,
	0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x10, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x61, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a,
	0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04,
	0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09,
	0x7a, 0x69, 0x70, 0x5f, 0x70, 0x6c, 0x75, 0x73, 0x34, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x05, 0x52, 0x08, 0x7a, 0x69, 0x70, 0x50, 0x6c, 0x75, 0x73, 0x34, 0x88, 0x01, 0x01, 0x12, 0x27,
	0x0a, 0x0c, 0x75, 0x72, 0x62, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x0c, 0x75, 0x72, 0x62, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x63, 0x69, 0x74, 0x79,
	0x5f, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0x0a,
	0x0f, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x42, 0x1e, 0x0a, 0x1c, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f,
	0x64, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x7a, 0x69, 0x70, 0x5f, 0x70, 0x6c, 0x75, 0x73, 0x34,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x75, 0x72, 0x62, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xb0, 0x03, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x0e, 0x64,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x16, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x61, 0x6c, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x14, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x61, 0x6c, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0c, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x10,
	0x64, 0x70, 0x76, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0f, 0x64, 0x70, 0x76, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08,
	0x64, 0x70, 0x76, 0x5f, 0x63, 0x6d, 0x72, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04,
	0x52, 0x07, 0x64, 0x70, 0x76, 0x43, 0x6d, 0x72, 0x61, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05,
	0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a,
	0x06, 0x76, 0x61, 0x63, 0x61, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52,
	0x06, 0x76, 0x61, 0x63, 0x61, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x64,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x42, 0x19, 0x0a,
	0x17, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x64,
	0x70, 0x76, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x70, 0x76, 0x5f, 0x63, 0x6d, 0x72, 0x61, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x76, 0x61,
	0x63, 0x61, 0x6e, 0x74, 0x22, 0x57, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43,
	0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x01, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x22, 0x52, 0x0a,
	0x0c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x17, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x88, 0x01, 0x01, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x42, 0x72, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x75, 0x73, 0x70, 0x73, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x50, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x75, 0x73, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x75,
	0x73, 0x70, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_usps_v1_usps_models_proto_rawDescOnce sync.Once
	file_moego_models_usps_v1_usps_models_proto_rawDescData = file_moego_models_usps_v1_usps_models_proto_rawDesc
)

func file_moego_models_usps_v1_usps_models_proto_rawDescGZIP() []byte {
	file_moego_models_usps_v1_usps_models_proto_rawDescOnce.Do(func() {
		file_moego_models_usps_v1_usps_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_usps_v1_usps_models_proto_rawDescData)
	})
	return file_moego_models_usps_v1_usps_models_proto_rawDescData
}

var file_moego_models_usps_v1_usps_models_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_usps_v1_usps_models_proto_goTypes = []interface{}{
	(*AddressInfo)(nil),           // 0: moego.models.usps.v1.AddressInfo
	(*AddressAdditionalInfo)(nil), // 1: moego.models.usps.v1.AddressAdditionalInfo
	(*AddressCorrection)(nil),     // 2: moego.models.usps.v1.AddressCorrection
	(*AddressMatch)(nil),          // 3: moego.models.usps.v1.AddressMatch
}
var file_moego_models_usps_v1_usps_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_usps_v1_usps_models_proto_init() }
func file_moego_models_usps_v1_usps_models_proto_init() {
	if File_moego_models_usps_v1_usps_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_usps_v1_usps_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_usps_v1_usps_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressAdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_usps_v1_usps_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressCorrection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_usps_v1_usps_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_usps_v1_usps_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_usps_v1_usps_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_usps_v1_usps_models_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_usps_v1_usps_models_proto_msgTypes[3].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_usps_v1_usps_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_usps_v1_usps_models_proto_goTypes,
		DependencyIndexes: file_moego_models_usps_v1_usps_models_proto_depIdxs,
		MessageInfos:      file_moego_models_usps_v1_usps_models_proto_msgTypes,
	}.Build()
	File_moego_models_usps_v1_usps_models_proto = out.File
	file_moego_models_usps_v1_usps_models_proto_rawDesc = nil
	file_moego_models_usps_v1_usps_models_proto_goTypes = nil
	file_moego_models_usps_v1_usps_models_proto_depIdxs = nil
}
