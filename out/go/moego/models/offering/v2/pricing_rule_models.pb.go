// @since 2025-02-26 16:46:24
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v2/pricing_rule_models.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PricingRule model
type PricingRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// rule type
	Type RuleType `protobuf:"varint,3,opt,name=type,proto3,enum=moego.models.offering.v2.RuleType" json:"type,omitempty"`
	// rule name
	RuleName string `protobuf:"bytes,4,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	// active, true means active, false means inactive
	IsActive bool `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// all boarding applicable
	AllBoardingApplicable bool `protobuf:"varint,6,opt,name=all_boarding_applicable,json=allBoardingApplicable,proto3" json:"all_boarding_applicable,omitempty"`
	// selected service ids, only effective when all_service is false
	SelectedBoardingServices []int64 `protobuf:"varint,7,rep,packed,name=selected_boarding_services,json=selectedBoardingServices,proto3" json:"selected_boarding_services,omitempty"`
	// all daycare applicable
	AllDaycareApplicable bool `protobuf:"varint,8,opt,name=all_daycare_applicable,json=allDaycareApplicable,proto3" json:"all_daycare_applicable,omitempty"`
	// selected service ids, only effective when all_service is false
	SelectedDaycareServices []int64 `protobuf:"varint,9,rep,packed,name=selected_daycare_services,json=selectedDaycareServices,proto3" json:"selected_daycare_services,omitempty"`
	// rule apply type, apply to each one/apply to additional
	RuleApplyType RuleApplyType `protobuf:"varint,10,opt,name=rule_apply_type,json=ruleApplyType,proto3,enum=moego.models.offering.v2.RuleApplyType" json:"rule_apply_type,omitempty"`
	// same lodging unit, only effective when rule_item_type is multiple pet
	NeedInSameLodging bool `protobuf:"varint,11,opt,name=need_in_same_lodging,json=needInSameLodging,proto3" json:"need_in_same_lodging,omitempty"`
	// rule configuration
	RuleConfiguration *PricingRuleConfiguration `protobuf:"bytes,12,opt,name=rule_configuration,json=ruleConfiguration,proto3" json:"rule_configuration,omitempty"`
	// updated staff id
	UpdatedBy int64 `protobuf:"varint,13,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// the delete time, non-null means is deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
	// is charge per lodging, only effective when rule_item_type is peak date
	IsChargePerLodging bool `protobuf:"varint,17,opt,name=is_charge_per_lodging,json=isChargePerLodging,proto3" json:"is_charge_per_lodging,omitempty"`
	// source
	Source Source `protobuf:"varint,18,opt,name=source,proto3,enum=moego.models.offering.v2.Source" json:"source,omitempty"`
}

func (x *PricingRule) Reset() {
	*x = PricingRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricingRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingRule) ProtoMessage() {}

func (x *PricingRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingRule.ProtoReflect.Descriptor instead.
func (*PricingRule) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_models_proto_rawDescGZIP(), []int{0}
}

func (x *PricingRule) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PricingRule) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *PricingRule) GetType() RuleType {
	if x != nil {
		return x.Type
	}
	return RuleType_RULE_TYPE_UNSPECIFIED
}

func (x *PricingRule) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *PricingRule) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *PricingRule) GetAllBoardingApplicable() bool {
	if x != nil {
		return x.AllBoardingApplicable
	}
	return false
}

func (x *PricingRule) GetSelectedBoardingServices() []int64 {
	if x != nil {
		return x.SelectedBoardingServices
	}
	return nil
}

func (x *PricingRule) GetAllDaycareApplicable() bool {
	if x != nil {
		return x.AllDaycareApplicable
	}
	return false
}

func (x *PricingRule) GetSelectedDaycareServices() []int64 {
	if x != nil {
		return x.SelectedDaycareServices
	}
	return nil
}

func (x *PricingRule) GetRuleApplyType() RuleApplyType {
	if x != nil {
		return x.RuleApplyType
	}
	return RuleApplyType_RULE_APPLY_TYPE_UNSPECIFIED
}

func (x *PricingRule) GetNeedInSameLodging() bool {
	if x != nil {
		return x.NeedInSameLodging
	}
	return false
}

func (x *PricingRule) GetRuleConfiguration() *PricingRuleConfiguration {
	if x != nil {
		return x.RuleConfiguration
	}
	return nil
}

func (x *PricingRule) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *PricingRule) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PricingRule) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *PricingRule) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *PricingRule) GetIsChargePerLodging() bool {
	if x != nil {
		return x.IsChargePerLodging
	}
	return false
}

func (x *PricingRule) GetSource() Source {
	if x != nil {
		return x.Source
	}
	return Source_SOURCE_UNSPECIFIED
}

var File_moego_models_offering_v2_pricing_rule_models_proto protoreflect.FileDescriptor

var file_moego_models_offering_v2_pricing_rule_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcc, 0x07, 0x0a, 0x0b, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x61, 0x6c, 0x6c, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x3c, 0x0a,
	0x1a, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x18, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x61,
	0x6c, 0x6c, 0x5f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c,
	0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x3a, 0x0a, 0x19, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x17, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x4f, 0x0a,
	0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0d, 0x72, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f,
	0x0a, 0x14, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x61, 0x6d, 0x65, 0x5f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x6e, 0x65,
	0x65, 0x64, 0x49, 0x6e, 0x53, 0x61, 0x6d, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x12,
	0x61, 0x0a, 0x12, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x11, 0x72, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x50, 0x65, 0x72, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v2_pricing_rule_models_proto_rawDescOnce sync.Once
	file_moego_models_offering_v2_pricing_rule_models_proto_rawDescData = file_moego_models_offering_v2_pricing_rule_models_proto_rawDesc
)

func file_moego_models_offering_v2_pricing_rule_models_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v2_pricing_rule_models_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v2_pricing_rule_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v2_pricing_rule_models_proto_rawDescData)
	})
	return file_moego_models_offering_v2_pricing_rule_models_proto_rawDescData
}

var file_moego_models_offering_v2_pricing_rule_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_offering_v2_pricing_rule_models_proto_goTypes = []interface{}{
	(*PricingRule)(nil),              // 0: moego.models.offering.v2.PricingRule
	(RuleType)(0),                    // 1: moego.models.offering.v2.RuleType
	(RuleApplyType)(0),               // 2: moego.models.offering.v2.RuleApplyType
	(*PricingRuleConfiguration)(nil), // 3: moego.models.offering.v2.PricingRuleConfiguration
	(*timestamppb.Timestamp)(nil),    // 4: google.protobuf.Timestamp
	(Source)(0),                      // 5: moego.models.offering.v2.Source
}
var file_moego_models_offering_v2_pricing_rule_models_proto_depIdxs = []int32{
	1, // 0: moego.models.offering.v2.PricingRule.type:type_name -> moego.models.offering.v2.RuleType
	2, // 1: moego.models.offering.v2.PricingRule.rule_apply_type:type_name -> moego.models.offering.v2.RuleApplyType
	3, // 2: moego.models.offering.v2.PricingRule.rule_configuration:type_name -> moego.models.offering.v2.PricingRuleConfiguration
	4, // 3: moego.models.offering.v2.PricingRule.created_at:type_name -> google.protobuf.Timestamp
	4, // 4: moego.models.offering.v2.PricingRule.updated_at:type_name -> google.protobuf.Timestamp
	4, // 5: moego.models.offering.v2.PricingRule.deleted_at:type_name -> google.protobuf.Timestamp
	5, // 6: moego.models.offering.v2.PricingRule.source:type_name -> moego.models.offering.v2.Source
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v2_pricing_rule_models_proto_init() }
func file_moego_models_offering_v2_pricing_rule_models_proto_init() {
	if File_moego_models_offering_v2_pricing_rule_models_proto != nil {
		return
	}
	file_moego_models_offering_v2_pricing_rule_defs_proto_init()
	file_moego_models_offering_v2_pricing_rule_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v2_pricing_rule_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricingRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_offering_v2_pricing_rule_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v2_pricing_rule_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v2_pricing_rule_models_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v2_pricing_rule_models_proto_depIdxs,
		MessageInfos:      file_moego_models_offering_v2_pricing_rule_models_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v2_pricing_rule_models_proto = out.File
	file_moego_models_offering_v2_pricing_rule_models_proto_rawDesc = nil
	file_moego_models_offering_v2_pricing_rule_models_proto_goTypes = nil
	file_moego_models_offering_v2_pricing_rule_models_proto_depIdxs = nil
}
