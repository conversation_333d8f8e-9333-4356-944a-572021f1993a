// @since 2025-02-26 16:46:24
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v2/pricing_rule_defs.proto

package offeringpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pricing rule upsert definition
type PricingRuleUpsertDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// rule type
	Type RuleType `protobuf:"varint,3,opt,name=type,proto3,enum=moego.models.offering.v2.RuleType" json:"type,omitempty"`
	// rule name
	RuleName string `protobuf:"bytes,4,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	// active, true means active, false means inactive
	IsActive bool `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// all boarding applicable
	AllBoardingApplicable bool `protobuf:"varint,6,opt,name=all_boarding_applicable,json=allBoardingApplicable,proto3" json:"all_boarding_applicable,omitempty"`
	// selected service ids, only effective when all_service is false
	SelectedBoardingServices []int64 `protobuf:"varint,7,rep,packed,name=selected_boarding_services,json=selectedBoardingServices,proto3" json:"selected_boarding_services,omitempty"`
	// all daycare applicable
	AllDaycareApplicable bool `protobuf:"varint,8,opt,name=all_daycare_applicable,json=allDaycareApplicable,proto3" json:"all_daycare_applicable,omitempty"`
	// selected service ids, only effective when all_service is false
	SelectedDaycareServices []int64 `protobuf:"varint,9,rep,packed,name=selected_daycare_services,json=selectedDaycareServices,proto3" json:"selected_daycare_services,omitempty"`
	// rule configuration
	RuleConfiguration *PricingRuleConfiguration `protobuf:"bytes,10,opt,name=rule_configuration,json=ruleConfiguration,proto3" json:"rule_configuration,omitempty"`
	// rule apply type, apply to each one/apply to additional
	RuleApplyType RuleApplyType `protobuf:"varint,11,opt,name=rule_apply_type,json=ruleApplyType,proto3,enum=moego.models.offering.v2.RuleApplyType" json:"rule_apply_type,omitempty"`
	// same lodging unit, only effective when rule_item_type is multiple pet
	NeedInSameLodging bool `protobuf:"varint,12,opt,name=need_in_same_lodging,json=needInSameLodging,proto3" json:"need_in_same_lodging,omitempty"`
	// updated staff id
	UpdatedBy *int64 `protobuf:"varint,13,opt,name=updated_by,json=updatedBy,proto3,oneof" json:"updated_by,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3,oneof" json:"created_at,omitempty"`
	// the update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3,oneof" json:"updated_at,omitempty"`
	// the delete time, non-null means is deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
	// is charge per lodging, only effective when rule_item_type is peak date
	IsChargePerLodging *bool `protobuf:"varint,17,opt,name=is_charge_per_lodging,json=isChargePerLodging,proto3,oneof" json:"is_charge_per_lodging,omitempty"`
	// source
	Source *Source `protobuf:"varint,18,opt,name=source,proto3,enum=moego.models.offering.v2.Source,oneof" json:"source,omitempty"`
}

func (x *PricingRuleUpsertDef) Reset() {
	*x = PricingRuleUpsertDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricingRuleUpsertDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingRuleUpsertDef) ProtoMessage() {}

func (x *PricingRuleUpsertDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingRuleUpsertDef.ProtoReflect.Descriptor instead.
func (*PricingRuleUpsertDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{0}
}

func (x *PricingRuleUpsertDef) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *PricingRuleUpsertDef) GetType() RuleType {
	if x != nil {
		return x.Type
	}
	return RuleType_RULE_TYPE_UNSPECIFIED
}

func (x *PricingRuleUpsertDef) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *PricingRuleUpsertDef) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *PricingRuleUpsertDef) GetAllBoardingApplicable() bool {
	if x != nil {
		return x.AllBoardingApplicable
	}
	return false
}

func (x *PricingRuleUpsertDef) GetSelectedBoardingServices() []int64 {
	if x != nil {
		return x.SelectedBoardingServices
	}
	return nil
}

func (x *PricingRuleUpsertDef) GetAllDaycareApplicable() bool {
	if x != nil {
		return x.AllDaycareApplicable
	}
	return false
}

func (x *PricingRuleUpsertDef) GetSelectedDaycareServices() []int64 {
	if x != nil {
		return x.SelectedDaycareServices
	}
	return nil
}

func (x *PricingRuleUpsertDef) GetRuleConfiguration() *PricingRuleConfiguration {
	if x != nil {
		return x.RuleConfiguration
	}
	return nil
}

func (x *PricingRuleUpsertDef) GetRuleApplyType() RuleApplyType {
	if x != nil {
		return x.RuleApplyType
	}
	return RuleApplyType_RULE_APPLY_TYPE_UNSPECIFIED
}

func (x *PricingRuleUpsertDef) GetNeedInSameLodging() bool {
	if x != nil {
		return x.NeedInSameLodging
	}
	return false
}

func (x *PricingRuleUpsertDef) GetUpdatedBy() int64 {
	if x != nil && x.UpdatedBy != nil {
		return *x.UpdatedBy
	}
	return 0
}

func (x *PricingRuleUpsertDef) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PricingRuleUpsertDef) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *PricingRuleUpsertDef) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *PricingRuleUpsertDef) GetIsChargePerLodging() bool {
	if x != nil && x.IsChargePerLodging != nil {
		return *x.IsChargePerLodging
	}
	return false
}

func (x *PricingRuleUpsertDef) GetSource() Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return Source_SOURCE_UNSPECIFIED
}

// GenericValue represents a polymorphic value for condition evaluation
type GenericValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// value type
	//
	// Types that are assignable to Value:
	//
	//	*GenericValue_NumberValue
	//	*GenericValue_DateRange
	//	*GenericValue_RepeatDates
	Value isGenericValue_Value `protobuf_oneof:"value"`
}

func (x *GenericValue) Reset() {
	*x = GenericValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenericValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenericValue) ProtoMessage() {}

func (x *GenericValue) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenericValue.ProtoReflect.Descriptor instead.
func (*GenericValue) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{1}
}

func (m *GenericValue) GetValue() isGenericValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *GenericValue) GetNumberValue() float64 {
	if x, ok := x.GetValue().(*GenericValue_NumberValue); ok {
		return x.NumberValue
	}
	return 0
}

func (x *GenericValue) GetDateRange() *v2.StringDateRange {
	if x, ok := x.GetValue().(*GenericValue_DateRange); ok {
		return x.DateRange
	}
	return nil
}

func (x *GenericValue) GetRepeatDates() *RepeatDates {
	if x, ok := x.GetValue().(*GenericValue_RepeatDates); ok {
		return x.RepeatDates
	}
	return nil
}

type isGenericValue_Value interface {
	isGenericValue_Value()
}

type GenericValue_NumberValue struct {
	// number value
	NumberValue float64 `protobuf:"fixed64,1,opt,name=number_value,json=numberValue,proto3,oneof"`
}

type GenericValue_DateRange struct {
	// date range
	DateRange *v2.StringDateRange `protobuf:"bytes,4,opt,name=date_range,json=dateRange,proto3,oneof"`
}

type GenericValue_RepeatDates struct {
	// repeat dates
	RepeatDates *RepeatDates `protobuf:"bytes,5,opt,name=repeat_dates,json=repeatDates,proto3,oneof"`
}

func (*GenericValue_NumberValue) isGenericValue_Value() {}

func (*GenericValue_DateRange) isGenericValue_Value() {}

func (*GenericValue_RepeatDates) isGenericValue_Value() {}

// Repeat dates represents a repeated date range
type RepeatDates struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date range
	DateRange *v2.StringDateRange `protobuf:"bytes,1,opt,name=date_range,json=dateRange,proto3" json:"date_range,omitempty"`
	// the interval
	//
	// Types that are assignable to Interval:
	//
	//	*RepeatDates_Week_
	Interval isRepeatDates_Interval `protobuf_oneof:"interval"`
}

func (x *RepeatDates) Reset() {
	*x = RepeatDates{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatDates) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatDates) ProtoMessage() {}

func (x *RepeatDates) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatDates.ProtoReflect.Descriptor instead.
func (*RepeatDates) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{2}
}

func (x *RepeatDates) GetDateRange() *v2.StringDateRange {
	if x != nil {
		return x.DateRange
	}
	return nil
}

func (m *RepeatDates) GetInterval() isRepeatDates_Interval {
	if m != nil {
		return m.Interval
	}
	return nil
}

func (x *RepeatDates) GetWeek() *RepeatDates_Week {
	if x, ok := x.GetInterval().(*RepeatDates_Week_); ok {
		return x.Week
	}
	return nil
}

type isRepeatDates_Interval interface {
	isRepeatDates_Interval()
}

type RepeatDates_Week_ struct {
	// week
	Week *RepeatDates_Week `protobuf:"bytes,2,opt,name=week,proto3,oneof"`
}

func (*RepeatDates_Week_) isRepeatDates_Interval() {}

// Condition represents a single condition to be evaluated
type Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// condition type
	Type ConditionType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.offering.v2.ConditionType" json:"type,omitempty"`
	// operator
	Operator v2.Operator `protobuf:"varint,2,opt,name=operator,proto3,enum=moego.utils.v2.Operator" json:"operator,omitempty"`
	// value
	Value *GenericValue `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Condition) Reset() {
	*x = Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Condition) ProtoMessage() {}

func (x *Condition) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Condition.ProtoReflect.Descriptor instead.
func (*Condition) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{3}
}

func (x *Condition) GetType() ConditionType {
	if x != nil {
		return x.Type
	}
	return ConditionType_CONDITION_TYPE_UNSPECIFIED
}

func (x *Condition) GetOperator() v2.Operator {
	if x != nil {
		return x.Operator
	}
	return v2.Operator(0)
}

func (x *Condition) GetValue() *GenericValue {
	if x != nil {
		return x.Value
	}
	return nil
}

// Effect represents how a price should be modified
type Effect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// effect type
	Type EffectType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.offering.v2.EffectType" json:"type,omitempty"`
	// value
	Value float64 `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Effect) Reset() {
	*x = Effect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Effect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Effect) ProtoMessage() {}

func (x *Effect) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Effect.ProtoReflect.Descriptor instead.
func (*Effect) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{4}
}

func (x *Effect) GetType() EffectType {
	if x != nil {
		return x.Type
	}
	return EffectType_EFFECT_TYPE_UNSPECIFIED
}

func (x *Effect) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

// ConditionGroup represents a group of conditions with a specific effect
type ConditionGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conditions
	Conditions []*Condition `protobuf:"bytes,1,rep,name=conditions,proto3" json:"conditions,omitempty"`
	// effect
	Effect *Effect `protobuf:"bytes,2,opt,name=effect,proto3" json:"effect,omitempty"`
}

func (x *ConditionGroup) Reset() {
	*x = ConditionGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConditionGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionGroup) ProtoMessage() {}

func (x *ConditionGroup) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionGroup.ProtoReflect.Descriptor instead.
func (*ConditionGroup) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{5}
}

func (x *ConditionGroup) GetConditions() []*Condition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *ConditionGroup) GetEffect() *Effect {
	if x != nil {
		return x.Effect
	}
	return nil
}

// pricing rule configuration
type PricingRuleConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// condition groups
	ConditionGroups []*ConditionGroup `protobuf:"bytes,1,rep,name=condition_groups,json=conditionGroups,proto3" json:"condition_groups,omitempty"`
}

func (x *PricingRuleConfiguration) Reset() {
	*x = PricingRuleConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricingRuleConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingRuleConfiguration) ProtoMessage() {}

func (x *PricingRuleConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingRuleConfiguration.ProtoReflect.Descriptor instead.
func (*PricingRuleConfiguration) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{6}
}

func (x *PricingRuleConfiguration) GetConditionGroups() []*ConditionGroup {
	if x != nil {
		return x.ConditionGroups
	}
	return nil
}

// list pricing rule filter
type ListPricingRuleFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule type
	RuleTypes []RuleType `protobuf:"varint,1,rep,packed,name=rule_types,json=ruleTypes,proto3,enum=moego.models.offering.v2.RuleType" json:"rule_types,omitempty"`
	// care type: grooming/boarding/daycare
	CareTypes []v1.ServiceItemType `protobuf:"varint,2,rep,packed,name=care_types,json=careTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"care_types,omitempty"`
	// active
	IsActive *bool `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// pricing rule ids
	Ids []int64 `protobuf:"varint,5,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// exclude pricing rule ids
	ExcludeIds []int64 `protobuf:"varint,6,rep,packed,name=exclude_ids,json=excludeIds,proto3" json:"exclude_ids,omitempty"`
}

func (x *ListPricingRuleFilter) Reset() {
	*x = ListPricingRuleFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRuleFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRuleFilter) ProtoMessage() {}

func (x *ListPricingRuleFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRuleFilter.ProtoReflect.Descriptor instead.
func (*ListPricingRuleFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{7}
}

func (x *ListPricingRuleFilter) GetRuleTypes() []RuleType {
	if x != nil {
		return x.RuleTypes
	}
	return nil
}

func (x *ListPricingRuleFilter) GetCareTypes() []v1.ServiceItemType {
	if x != nil {
		return x.CareTypes
	}
	return nil
}

func (x *ListPricingRuleFilter) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *ListPricingRuleFilter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListPricingRuleFilter) GetExcludeIds() []int64 {
	if x != nil {
		return x.ExcludeIds
	}
	return nil
}

// pet detail calculate definition, used for pricing rule calculation
type PreviewPetDetailCalculateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id, not evaluation id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service price
	ServicePrice float64 `protobuf:"fixed64,3,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// stay length
	StayLength int32 `protobuf:"varint,5,opt,name=stay_length,json=stayLength,proto3" json:"stay_length,omitempty"`
}

func (x *PreviewPetDetailCalculateDef) Reset() {
	*x = PreviewPetDetailCalculateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewPetDetailCalculateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewPetDetailCalculateDef) ProtoMessage() {}

func (x *PreviewPetDetailCalculateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewPetDetailCalculateDef.ProtoReflect.Descriptor instead.
func (*PreviewPetDetailCalculateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{8}
}

func (x *PreviewPetDetailCalculateDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PreviewPetDetailCalculateDef) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PreviewPetDetailCalculateDef) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *PreviewPetDetailCalculateDef) GetStayLength() int32 {
	if x != nil {
		return x.StayLength
	}
	return 0
}

// pet detail calculate definition, used for pricing rule calculation
type PreviewPetDetailCalculateResultDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id, not evaluation id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service price
	AdjustedPrice float64 `protobuf:"fixed64,3,opt,name=adjusted_price,json=adjustedPrice,proto3" json:"adjusted_price,omitempty"`
}

func (x *PreviewPetDetailCalculateResultDef) Reset() {
	*x = PreviewPetDetailCalculateResultDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewPetDetailCalculateResultDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewPetDetailCalculateResultDef) ProtoMessage() {}

func (x *PreviewPetDetailCalculateResultDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewPetDetailCalculateResultDef.ProtoReflect.Descriptor instead.
func (*PreviewPetDetailCalculateResultDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{9}
}

func (x *PreviewPetDetailCalculateResultDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PreviewPetDetailCalculateResultDef) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PreviewPetDetailCalculateResultDef) GetAdjustedPrice() float64 {
	if x != nil {
		return x.AdjustedPrice
	}
	return 0
}

// pet detail calculate definition, used for pricing rule calculation
type PetDetailCalculateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id, not evaluation id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service price
	ServicePrice float64 `protobuf:"fixed64,3,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// lodging unit id
	LodgingUnitId *int64 `protobuf:"varint,4,opt,name=lodging_unit_id,json=lodgingUnitId,proto3,oneof" json:"lodging_unit_id,omitempty"`
	// service date
	ServiceDate *string `protobuf:"bytes,5,opt,name=service_date,json=serviceDate,proto3,oneof" json:"service_date,omitempty"`
	// scope type price, apply to other services
	ScopeTypePrice *v1.ServiceScopeType `protobuf:"varint,11,opt,name=scope_type_price,json=scopeTypePrice,proto3,enum=moego.models.offering.v1.ServiceScopeType,oneof" json:"scope_type_price,omitempty"`
	// is split lodging
	IsSplitLodging bool `protobuf:"varint,12,opt,name=is_split_lodging,json=isSplitLodging,proto3" json:"is_split_lodging,omitempty"`
}

func (x *PetDetailCalculateDef) Reset() {
	*x = PetDetailCalculateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetDetailCalculateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDetailCalculateDef) ProtoMessage() {}

func (x *PetDetailCalculateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDetailCalculateDef.ProtoReflect.Descriptor instead.
func (*PetDetailCalculateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{10}
}

func (x *PetDetailCalculateDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetDetailCalculateDef) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetDetailCalculateDef) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *PetDetailCalculateDef) GetLodgingUnitId() int64 {
	if x != nil && x.LodgingUnitId != nil {
		return *x.LodgingUnitId
	}
	return 0
}

func (x *PetDetailCalculateDef) GetServiceDate() string {
	if x != nil && x.ServiceDate != nil {
		return *x.ServiceDate
	}
	return ""
}

func (x *PetDetailCalculateDef) GetScopeTypePrice() v1.ServiceScopeType {
	if x != nil && x.ScopeTypePrice != nil {
		return *x.ScopeTypePrice
	}
	return v1.ServiceScopeType(0)
}

func (x *PetDetailCalculateDef) GetIsSplitLodging() bool {
	if x != nil {
		return x.IsSplitLodging
	}
	return false
}

// pet detail calculate result definition, used for pricing rule calculation
type PetDetailCalculateResultDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service price after calculation
	AdjustedPrice float64 `protobuf:"fixed64,4,opt,name=adjusted_price,json=adjustedPrice,proto3" json:"adjusted_price,omitempty"`
	// service date
	ServiceDate *string `protobuf:"bytes,6,opt,name=service_date,json=serviceDate,proto3,oneof" json:"service_date,omitempty"`
	// applied rule ids, empty means for preview
	AppliedRuleIds []int64 `protobuf:"varint,7,rep,packed,name=applied_rule_ids,json=appliedRuleIds,proto3" json:"applied_rule_ids,omitempty"`
}

func (x *PetDetailCalculateResultDef) Reset() {
	*x = PetDetailCalculateResultDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetDetailCalculateResultDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDetailCalculateResultDef) ProtoMessage() {}

func (x *PetDetailCalculateResultDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDetailCalculateResultDef.ProtoReflect.Descriptor instead.
func (*PetDetailCalculateResultDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{11}
}

func (x *PetDetailCalculateResultDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetDetailCalculateResultDef) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetDetailCalculateResultDef) GetAdjustedPrice() float64 {
	if x != nil {
		return x.AdjustedPrice
	}
	return 0
}

func (x *PetDetailCalculateResultDef) GetServiceDate() string {
	if x != nil && x.ServiceDate != nil {
		return *x.ServiceDate
	}
	return ""
}

func (x *PetDetailCalculateResultDef) GetAppliedRuleIds() []int64 {
	if x != nil {
		return x.AppliedRuleIds
	}
	return nil
}

// discount setting definition
type DiscountSettingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// apply best only
	ApplyBestOnly bool `protobuf:"varint,2,opt,name=apply_best_only,json=applyBestOnly,proto3" json:"apply_best_only,omitempty"`
	// apply sequence
	ApplySequence []RuleType `protobuf:"varint,3,rep,packed,name=apply_sequence,json=applySequence,proto3,enum=moego.models.offering.v2.RuleType" json:"apply_sequence,omitempty"`
}

func (x *DiscountSettingDef) Reset() {
	*x = DiscountSettingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscountSettingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscountSettingDef) ProtoMessage() {}

func (x *DiscountSettingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscountSettingDef.ProtoReflect.Descriptor instead.
func (*DiscountSettingDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{12}
}

func (x *DiscountSettingDef) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *DiscountSettingDef) GetApplyBestOnly() bool {
	if x != nil {
		return x.ApplyBestOnly
	}
	return false
}

func (x *DiscountSettingDef) GetApplySequence() []RuleType {
	if x != nil {
		return x.ApplySequence
	}
	return nil
}

// week interval
type RepeatDates_Week struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// interval num
	IntervalNum int32 `protobuf:"varint,3,opt,name=interval_num,json=intervalNum,proto3" json:"interval_num,omitempty"`
	// repeat on weekdays
	DayOfWeeks []dayofweek.DayOfWeek `protobuf:"varint,1,rep,packed,name=day_of_weeks,json=dayOfWeeks,proto3,enum=google.type.DayOfWeek" json:"day_of_weeks,omitempty"`
}

func (x *RepeatDates_Week) Reset() {
	*x = RepeatDates_Week{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatDates_Week) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatDates_Week) ProtoMessage() {}

func (x *RepeatDates_Week) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatDates_Week.ProtoReflect.Descriptor instead.
func (*RepeatDates_Week) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP(), []int{2, 0}
}

func (x *RepeatDates_Week) GetIntervalNum() int32 {
	if x != nil {
		return x.IntervalNum
	}
	return 0
}

func (x *RepeatDates_Week) GetDayOfWeeks() []dayofweek.DayOfWeek {
	if x != nil {
		return x.DayOfWeeks
	}
	return nil
}

var File_moego_models_offering_v2_pricing_rule_defs_proto protoreflect.FileDescriptor

var file_moego_models_offering_v2_pricing_rule_defs_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x79, 0x6f, 0x66,
	0x77, 0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x09, 0x0a,
	0x14, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x44, 0x65, 0x66, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x27,
	0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x08, 0x72,
	0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x61, 0x6c, 0x6c, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x51, 0x0a, 0x1a,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x10, 0xf4, 0x03, 0x18, 0x01, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x28, 0x01, 0x52, 0x18, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x42,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12,
	0x34, 0x0a, 0x16, 0x61, 0x6c, 0x6c, 0x5f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x14, 0x61, 0x6c, 0x6c, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x19, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d,
	0x10, 0xf4, 0x03, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x28, 0x01, 0x52, 0x17, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x6b, 0x0a, 0x12, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x11, 0x72, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2f, 0x0a, 0x14, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x61, 0x6d, 0x65,
	0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x6e, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x53, 0x61, 0x6d, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x12, 0x22, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x02, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x04, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x12, 0x69, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x50, 0x65, 0x72, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x49, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x06, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x18, 0x0a, 0x16, 0x5f,
	0x69, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0xda, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x33, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x21, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x00, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x09, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x80, 0x02,
	0x0a, 0x0b, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x3e, 0x0a,
	0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x40, 0x0a,
	0x04, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x73, 0x2e, 0x57, 0x65, 0x65, 0x6b, 0x48, 0x00, 0x52, 0x04, 0x77, 0x65, 0x65, 0x6b, 0x1a,
	0x63, 0x0a, 0x04, 0x57, 0x65, 0x65, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x38, 0x0a, 0x0c, 0x64, 0x61,
	0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x0a, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x57,
	0x65, 0x65, 0x6b, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x22, 0xbc, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x69, 0x63, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x68, 0x0a, 0x06, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x12, 0x38, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x9b, 0x01, 0x0a, 0x0e, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x4f, 0x0a, 0x0a,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10,
	0x64, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x38, 0x0a,
	0x06, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x52,
	0x06, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x22, 0x7b, 0x0a, 0x18, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x5f, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08,
	0x01, 0x10, 0x64, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x22, 0xcf, 0x02, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x54,
	0x0a, 0x0a, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10, 0x0a,
	0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x5b, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10, 0x0a, 0x22, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x09, 0x63, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0xe8, 0x07, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x0b, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0f, 0xfa, 0x42, 0x0c,
	0x92, 0x01, 0x09, 0x10, 0xe8, 0x07, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x49, 0x64, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0xc5, 0x01, 0x0a, 0x1c, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x33, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x79, 0x5f, 0x6c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x79, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x22, 0xa3,
	0x01, 0x0a, 0x22, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05,
	0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x35, 0x0a,
	0x0e, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0d, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x65, 0x64, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x22, 0xd9, 0x03, 0x0a, 0x15, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1e,
	0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x26,
	0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa,
	0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x0f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52,
	0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x42, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13,
	0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x24, 0x48, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a, 0x10, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x0e, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x10,
	0x69, 0x73, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x22, 0xa9, 0x02, 0x0a, 0x1b, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61,
	0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x66,
	0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x0e, 0x61, 0x64, 0x6a, 0x75,
	0x73, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x52, 0x0d, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x42, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c,
	0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x24, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa,
	0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xb6, 0x01, 0x0a,
	0x12, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x66, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6c,
	0x79, 0x5f, 0x62, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x42, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x6c, 0x79,
	0x12, 0x5c, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x11, 0xfa, 0x42,
	0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x05,
	0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescOnce sync.Once
	file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescData = file_moego_models_offering_v2_pricing_rule_defs_proto_rawDesc
)

func file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescData)
	})
	return file_moego_models_offering_v2_pricing_rule_defs_proto_rawDescData
}

var file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_models_offering_v2_pricing_rule_defs_proto_goTypes = []interface{}{
	(*PricingRuleUpsertDef)(nil),               // 0: moego.models.offering.v2.PricingRuleUpsertDef
	(*GenericValue)(nil),                       // 1: moego.models.offering.v2.GenericValue
	(*RepeatDates)(nil),                        // 2: moego.models.offering.v2.RepeatDates
	(*Condition)(nil),                          // 3: moego.models.offering.v2.Condition
	(*Effect)(nil),                             // 4: moego.models.offering.v2.Effect
	(*ConditionGroup)(nil),                     // 5: moego.models.offering.v2.ConditionGroup
	(*PricingRuleConfiguration)(nil),           // 6: moego.models.offering.v2.PricingRuleConfiguration
	(*ListPricingRuleFilter)(nil),              // 7: moego.models.offering.v2.ListPricingRuleFilter
	(*PreviewPetDetailCalculateDef)(nil),       // 8: moego.models.offering.v2.PreviewPetDetailCalculateDef
	(*PreviewPetDetailCalculateResultDef)(nil), // 9: moego.models.offering.v2.PreviewPetDetailCalculateResultDef
	(*PetDetailCalculateDef)(nil),              // 10: moego.models.offering.v2.PetDetailCalculateDef
	(*PetDetailCalculateResultDef)(nil),        // 11: moego.models.offering.v2.PetDetailCalculateResultDef
	(*DiscountSettingDef)(nil),                 // 12: moego.models.offering.v2.DiscountSettingDef
	(*RepeatDates_Week)(nil),                   // 13: moego.models.offering.v2.RepeatDates.Week
	(RuleType)(0),                              // 14: moego.models.offering.v2.RuleType
	(RuleApplyType)(0),                         // 15: moego.models.offering.v2.RuleApplyType
	(*timestamppb.Timestamp)(nil),              // 16: google.protobuf.Timestamp
	(Source)(0),                                // 17: moego.models.offering.v2.Source
	(*v2.StringDateRange)(nil),                 // 18: moego.utils.v2.StringDateRange
	(ConditionType)(0),                         // 19: moego.models.offering.v2.ConditionType
	(v2.Operator)(0),                           // 20: moego.utils.v2.Operator
	(EffectType)(0),                            // 21: moego.models.offering.v2.EffectType
	(v1.ServiceItemType)(0),                    // 22: moego.models.offering.v1.ServiceItemType
	(v1.ServiceScopeType)(0),                   // 23: moego.models.offering.v1.ServiceScopeType
	(dayofweek.DayOfWeek)(0),                   // 24: google.type.DayOfWeek
}
var file_moego_models_offering_v2_pricing_rule_defs_proto_depIdxs = []int32{
	14, // 0: moego.models.offering.v2.PricingRuleUpsertDef.type:type_name -> moego.models.offering.v2.RuleType
	6,  // 1: moego.models.offering.v2.PricingRuleUpsertDef.rule_configuration:type_name -> moego.models.offering.v2.PricingRuleConfiguration
	15, // 2: moego.models.offering.v2.PricingRuleUpsertDef.rule_apply_type:type_name -> moego.models.offering.v2.RuleApplyType
	16, // 3: moego.models.offering.v2.PricingRuleUpsertDef.created_at:type_name -> google.protobuf.Timestamp
	16, // 4: moego.models.offering.v2.PricingRuleUpsertDef.updated_at:type_name -> google.protobuf.Timestamp
	16, // 5: moego.models.offering.v2.PricingRuleUpsertDef.deleted_at:type_name -> google.protobuf.Timestamp
	17, // 6: moego.models.offering.v2.PricingRuleUpsertDef.source:type_name -> moego.models.offering.v2.Source
	18, // 7: moego.models.offering.v2.GenericValue.date_range:type_name -> moego.utils.v2.StringDateRange
	2,  // 8: moego.models.offering.v2.GenericValue.repeat_dates:type_name -> moego.models.offering.v2.RepeatDates
	18, // 9: moego.models.offering.v2.RepeatDates.date_range:type_name -> moego.utils.v2.StringDateRange
	13, // 10: moego.models.offering.v2.RepeatDates.week:type_name -> moego.models.offering.v2.RepeatDates.Week
	19, // 11: moego.models.offering.v2.Condition.type:type_name -> moego.models.offering.v2.ConditionType
	20, // 12: moego.models.offering.v2.Condition.operator:type_name -> moego.utils.v2.Operator
	1,  // 13: moego.models.offering.v2.Condition.value:type_name -> moego.models.offering.v2.GenericValue
	21, // 14: moego.models.offering.v2.Effect.type:type_name -> moego.models.offering.v2.EffectType
	3,  // 15: moego.models.offering.v2.ConditionGroup.conditions:type_name -> moego.models.offering.v2.Condition
	4,  // 16: moego.models.offering.v2.ConditionGroup.effect:type_name -> moego.models.offering.v2.Effect
	5,  // 17: moego.models.offering.v2.PricingRuleConfiguration.condition_groups:type_name -> moego.models.offering.v2.ConditionGroup
	14, // 18: moego.models.offering.v2.ListPricingRuleFilter.rule_types:type_name -> moego.models.offering.v2.RuleType
	22, // 19: moego.models.offering.v2.ListPricingRuleFilter.care_types:type_name -> moego.models.offering.v1.ServiceItemType
	23, // 20: moego.models.offering.v2.PetDetailCalculateDef.scope_type_price:type_name -> moego.models.offering.v1.ServiceScopeType
	14, // 21: moego.models.offering.v2.DiscountSettingDef.apply_sequence:type_name -> moego.models.offering.v2.RuleType
	24, // 22: moego.models.offering.v2.RepeatDates.Week.day_of_weeks:type_name -> google.type.DayOfWeek
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v2_pricing_rule_defs_proto_init() }
func file_moego_models_offering_v2_pricing_rule_defs_proto_init() {
	if File_moego_models_offering_v2_pricing_rule_defs_proto != nil {
		return
	}
	file_moego_models_offering_v2_pricing_rule_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricingRuleUpsertDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenericValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatDates); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Effect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConditionGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricingRuleConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRuleFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewPetDetailCalculateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewPetDetailCalculateResultDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetDetailCalculateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetDetailCalculateResultDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscountSettingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatDates_Week); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*GenericValue_NumberValue)(nil),
		(*GenericValue_DateRange)(nil),
		(*GenericValue_RepeatDates)(nil),
	}
	file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*RepeatDates_Week_)(nil),
	}
	file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes[12].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v2_pricing_rule_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v2_pricing_rule_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v2_pricing_rule_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_offering_v2_pricing_rule_defs_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v2_pricing_rule_defs_proto = out.File
	file_moego_models_offering_v2_pricing_rule_defs_proto_rawDesc = nil
	file_moego_models_offering_v2_pricing_rule_defs_proto_goTypes = nil
	file_moego_models_offering_v2_pricing_rule_defs_proto_depIdxs = nil
}
