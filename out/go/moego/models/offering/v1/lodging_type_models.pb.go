// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/lodging_type_models.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// source
type LodgingTypeModel_Source int32

const (
	// source is not set
	LodgingTypeModel_SOURCE_UNSPECIFIED LodgingTypeModel_Source = 0
	// source is from MoeGo platform (e.x. b web/app)
	LodgingTypeModel_MOEGO_PLATFORM LodgingTypeModel_Source = 1
	// source is from Enterprise Hub
	LodgingTypeModel_ENTERPRISE_HUB LodgingTypeModel_Source = 2
)

// Enum value maps for LodgingTypeModel_Source.
var (
	LodgingTypeModel_Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "MOEGO_PLATFORM",
		2: "ENTERPRISE_HUB",
	}
	LodgingTypeModel_Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"MOEGO_PLATFORM":     1,
		"ENTERPRISE_HUB":     2,
	}
)

func (x LodgingTypeModel_Source) Enum() *LodgingTypeModel_Source {
	p := new(LodgingTypeModel_Source)
	*p = x
	return p
}

func (x LodgingTypeModel_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LodgingTypeModel_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_lodging_type_models_proto_enumTypes[0].Descriptor()
}

func (LodgingTypeModel_Source) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_lodging_type_models_proto_enumTypes[0]
}

func (x LodgingTypeModel_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LodgingTypeModel_Source.Descriptor instead.
func (LodgingTypeModel_Source) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_lodging_type_models_proto_rawDescGZIP(), []int{1, 0}
}

// lodging type list model
type LodgingTypeView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging type
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name of the lodging type
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description of the lodging type
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// images of this lodging type
	PhotoList []string `protobuf:"bytes,4,rep,name=photo_list,json=photoList,proto3" json:"photo_list,omitempty"`
	// max pet number of this lodging type
	MaxPetNum int32 `protobuf:"varint,5,opt,name=max_pet_num,json=maxPetNum,proto3" json:"max_pet_num,omitempty"`
	// max pet total weight of this lodging type
	//
	// Deprecated: Do not use.
	MaxPetTotalWeight int32 `protobuf:"varint,6,opt,name=max_pet_total_weight,json=maxPetTotalWeight,proto3" json:"max_pet_total_weight,omitempty"`
	// available for all pet size
	//
	// Deprecated: Do not use.
	AllPetSizes bool `protobuf:"varint,7,opt,name=all_pet_sizes,json=allPetSizes,proto3" json:"all_pet_sizes,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	// moe_pet_size.id list
	PetSizeIds []int64 `protobuf:"varint,8,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// lodging unit type in this lodging type
	LodgingUnitType LodgingUnitType `protobuf:"varint,9,opt,name=lodging_unit_type,json=lodgingUnitType,proto3,enum=moego.models.offering.v1.LodgingUnitType" json:"lodging_unit_type,omitempty"`
	// whether the lodging type is available for all pet size
	PetSizeFilter bool `protobuf:"varint,10,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// Sort for the lodging type
	Sort int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	// source
	Source LodgingTypeModel_Source `protobuf:"varint,12,opt,name=source,proto3,enum=moego.models.offering.v1.LodgingTypeModel_Source" json:"source,omitempty"`
}

func (x *LodgingTypeView) Reset() {
	*x = LodgingTypeView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_lodging_type_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingTypeView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingTypeView) ProtoMessage() {}

func (x *LodgingTypeView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_lodging_type_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingTypeView.ProtoReflect.Descriptor instead.
func (*LodgingTypeView) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_lodging_type_models_proto_rawDescGZIP(), []int{0}
}

func (x *LodgingTypeView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingTypeView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingTypeView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LodgingTypeView) GetPhotoList() []string {
	if x != nil {
		return x.PhotoList
	}
	return nil
}

func (x *LodgingTypeView) GetMaxPetNum() int32 {
	if x != nil {
		return x.MaxPetNum
	}
	return 0
}

// Deprecated: Do not use.
func (x *LodgingTypeView) GetMaxPetTotalWeight() int32 {
	if x != nil {
		return x.MaxPetTotalWeight
	}
	return 0
}

// Deprecated: Do not use.
func (x *LodgingTypeView) GetAllPetSizes() bool {
	if x != nil {
		return x.AllPetSizes
	}
	return false
}

func (x *LodgingTypeView) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *LodgingTypeView) GetLodgingUnitType() LodgingUnitType {
	if x != nil {
		return x.LodgingUnitType
	}
	return LodgingUnitType_LODGING_UNIT_TYPE_UNSPECIFIED
}

func (x *LodgingTypeView) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *LodgingTypeView) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *LodgingTypeView) GetSource() LodgingTypeModel_Source {
	if x != nil {
		return x.Source
	}
	return LodgingTypeModel_SOURCE_UNSPECIFIED
}

// lodging type list model
type LodgingTypeModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging type
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name of the lodging type
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description of the lodging type
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// images of this lodging type
	PhotoList []string `protobuf:"bytes,4,rep,name=photo_list,json=photoList,proto3" json:"photo_list,omitempty"`
	// max pet number of this lodging type
	MaxPetNum int32 `protobuf:"varint,5,opt,name=max_pet_num,json=maxPetNum,proto3" json:"max_pet_num,omitempty"`
	// max pet total weight of this lodging type
	//
	// Deprecated: Do not use.
	MaxPetTotalWeight int32 `protobuf:"varint,6,opt,name=max_pet_total_weight,json=maxPetTotalWeight,proto3" json:"max_pet_total_weight,omitempty"`
	// available for all pet size
	//
	// Deprecated: Do not use.
	AllPetSizes bool `protobuf:"varint,7,opt,name=all_pet_sizes,json=allPetSizes,proto3" json:"all_pet_sizes,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	// moe_pet_size.id list
	PetSizeIds []int64 `protobuf:"varint,8,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// lodging unit type in this lodging type
	LodgingUnitType LodgingUnitType `protobuf:"varint,9,opt,name=lodging_unit_type,json=lodgingUnitType,proto3,enum=moego.models.offering.v1.LodgingUnitType" json:"lodging_unit_type,omitempty"`
	// whether the lodging type is available for all pet size
	PetSizeFilter bool `protobuf:"varint,10,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// Sort for the lodging type
	Sort int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	// source
	Source LodgingTypeModel_Source `protobuf:"varint,12,opt,name=source,proto3,enum=moego.models.offering.v1.LodgingTypeModel_Source" json:"source,omitempty"`
}

func (x *LodgingTypeModel) Reset() {
	*x = LodgingTypeModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_lodging_type_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingTypeModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingTypeModel) ProtoMessage() {}

func (x *LodgingTypeModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_lodging_type_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingTypeModel.ProtoReflect.Descriptor instead.
func (*LodgingTypeModel) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_lodging_type_models_proto_rawDescGZIP(), []int{1}
}

func (x *LodgingTypeModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingTypeModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingTypeModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LodgingTypeModel) GetPhotoList() []string {
	if x != nil {
		return x.PhotoList
	}
	return nil
}

func (x *LodgingTypeModel) GetMaxPetNum() int32 {
	if x != nil {
		return x.MaxPetNum
	}
	return 0
}

// Deprecated: Do not use.
func (x *LodgingTypeModel) GetMaxPetTotalWeight() int32 {
	if x != nil {
		return x.MaxPetTotalWeight
	}
	return 0
}

// Deprecated: Do not use.
func (x *LodgingTypeModel) GetAllPetSizes() bool {
	if x != nil {
		return x.AllPetSizes
	}
	return false
}

func (x *LodgingTypeModel) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *LodgingTypeModel) GetLodgingUnitType() LodgingUnitType {
	if x != nil {
		return x.LodgingUnitType
	}
	return LodgingUnitType_LODGING_UNIT_TYPE_UNSPECIFIED
}

func (x *LodgingTypeModel) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *LodgingTypeModel) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *LodgingTypeModel) GetSource() LodgingTypeModel_Source {
	if x != nil {
		return x.Source
	}
	return LodgingTypeModel_SOURCE_UNSPECIFIED
}

var File_moego_models_offering_v1_lodging_type_models_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_lodging_type_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x2b,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf3, 0x03, 0x0a, 0x0f,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x50, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x12, 0x33, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x26, 0x0a, 0x0d, 0x61, 0x6c, 0x6c,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x73, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x49, 0x64, 0x73, 0x12, 0x55, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65,
	0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x49, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x22, 0xbe, 0x04, 0x0a, 0x10, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x6d,
	0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x33, 0x0a, 0x14, 0x6d,
	0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x6d,
	0x61, 0x78, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x26, 0x0a, 0x0d, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x61, 0x6c, 0x6c,
	0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a,
	0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x73, 0x12, 0x55, 0x0a, 0x11, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x53,
	0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x49, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x48, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x4f,
	0x45, 0x47, 0x4f, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x48, 0x55, 0x42,
	0x10, 0x02, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_lodging_type_models_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_lodging_type_models_proto_rawDescData = file_moego_models_offering_v1_lodging_type_models_proto_rawDesc
)

func file_moego_models_offering_v1_lodging_type_models_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_lodging_type_models_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_lodging_type_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_lodging_type_models_proto_rawDescData)
	})
	return file_moego_models_offering_v1_lodging_type_models_proto_rawDescData
}

var file_moego_models_offering_v1_lodging_type_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_offering_v1_lodging_type_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_offering_v1_lodging_type_models_proto_goTypes = []interface{}{
	(LodgingTypeModel_Source)(0), // 0: moego.models.offering.v1.LodgingTypeModel.Source
	(*LodgingTypeView)(nil),      // 1: moego.models.offering.v1.LodgingTypeView
	(*LodgingTypeModel)(nil),     // 2: moego.models.offering.v1.LodgingTypeModel
	(LodgingUnitType)(0),         // 3: moego.models.offering.v1.LodgingUnitType
}
var file_moego_models_offering_v1_lodging_type_models_proto_depIdxs = []int32{
	3, // 0: moego.models.offering.v1.LodgingTypeView.lodging_unit_type:type_name -> moego.models.offering.v1.LodgingUnitType
	0, // 1: moego.models.offering.v1.LodgingTypeView.source:type_name -> moego.models.offering.v1.LodgingTypeModel.Source
	3, // 2: moego.models.offering.v1.LodgingTypeModel.lodging_unit_type:type_name -> moego.models.offering.v1.LodgingUnitType
	0, // 3: moego.models.offering.v1.LodgingTypeModel.source:type_name -> moego.models.offering.v1.LodgingTypeModel.Source
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_lodging_type_models_proto_init() }
func file_moego_models_offering_v1_lodging_type_models_proto_init() {
	if File_moego_models_offering_v1_lodging_type_models_proto != nil {
		return
	}
	file_moego_models_offering_v1_lodging_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v1_lodging_type_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingTypeView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_lodging_type_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingTypeModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_lodging_type_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_lodging_type_models_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_lodging_type_models_proto_depIdxs,
		EnumInfos:         file_moego_models_offering_v1_lodging_type_models_proto_enumTypes,
		MessageInfos:      file_moego_models_offering_v1_lodging_type_models_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v1_lodging_type_models_proto = out.File
	file_moego_models_offering_v1_lodging_type_models_proto_rawDesc = nil
	file_moego_models_offering_v1_lodging_type_models_proto_goTypes = nil
	file_moego_models_offering_v1_lodging_type_models_proto_depIdxs = nil
}
