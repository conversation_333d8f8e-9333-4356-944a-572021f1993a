// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/service_charge_model.proto

package orderpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Auto apply status
type ServiceCharge_AutoApplyStatus int32

const (
	// Unspecified
	ServiceCharge_AUTO_APPLY_STATUS_UNSPECIFIED ServiceCharge_AutoApplyStatus = 0
	// No auto apply
	ServiceCharge_AUTO_APPLY_DISABLED ServiceCharge_AutoApplyStatus = 1
	// Auto apply for all
	ServiceCharge_AUTO_APPLY_ENABLED ServiceCharge_AutoApplyStatus = 2
	// Auto apply with condition
	ServiceCharge_AUTO_APPLY_ENABLED_WITH_CONDITION ServiceCharge_AutoApplyStatus = 3
)

// Enum value maps for ServiceCharge_AutoApplyStatus.
var (
	ServiceCharge_AutoApplyStatus_name = map[int32]string{
		0: "AUTO_APPLY_STATUS_UNSPECIFIED",
		1: "AUTO_APPLY_DISABLED",
		2: "AUTO_APPLY_ENABLED",
		3: "AUTO_APPLY_ENABLED_WITH_CONDITION",
	}
	ServiceCharge_AutoApplyStatus_value = map[string]int32{
		"AUTO_APPLY_STATUS_UNSPECIFIED":     0,
		"AUTO_APPLY_DISABLED":               1,
		"AUTO_APPLY_ENABLED":                2,
		"AUTO_APPLY_ENABLED_WITH_CONDITION": 3,
	}
)

func (x ServiceCharge_AutoApplyStatus) Enum() *ServiceCharge_AutoApplyStatus {
	p := new(ServiceCharge_AutoApplyStatus)
	*p = x
	return p
}

func (x ServiceCharge_AutoApplyStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceCharge_AutoApplyStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_service_charge_model_proto_enumTypes[0].Descriptor()
}

func (ServiceCharge_AutoApplyStatus) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_service_charge_model_proto_enumTypes[0]
}

func (x ServiceCharge_AutoApplyStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceCharge_AutoApplyStatus.Descriptor instead.
func (ServiceCharge_AutoApplyStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{0, 0}
}

// Auto apply condition
type ServiceCharge_AutoApplyCondition int32

const (
	// Unspecified
	ServiceCharge_AUTO_APPLY_CONDITION_UNSPECIFIED ServiceCharge_AutoApplyCondition = 0
	// Boarding/Daycare late pickup
	ServiceCharge_BD_LATE_PICKUP ServiceCharge_AutoApplyCondition = 1
	// Boarding/Daycare early drop off
	ServiceCharge_BD_EARLY_DROP_OFF ServiceCharge_AutoApplyCondition = 2
)

// Enum value maps for ServiceCharge_AutoApplyCondition.
var (
	ServiceCharge_AutoApplyCondition_name = map[int32]string{
		0: "AUTO_APPLY_CONDITION_UNSPECIFIED",
		1: "BD_LATE_PICKUP",
		2: "BD_EARLY_DROP_OFF",
	}
	ServiceCharge_AutoApplyCondition_value = map[string]int32{
		"AUTO_APPLY_CONDITION_UNSPECIFIED": 0,
		"BD_LATE_PICKUP":                   1,
		"BD_EARLY_DROP_OFF":                2,
	}
)

func (x ServiceCharge_AutoApplyCondition) Enum() *ServiceCharge_AutoApplyCondition {
	p := new(ServiceCharge_AutoApplyCondition)
	*p = x
	return p
}

func (x ServiceCharge_AutoApplyCondition) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceCharge_AutoApplyCondition) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_service_charge_model_proto_enumTypes[1].Descriptor()
}

func (ServiceCharge_AutoApplyCondition) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_service_charge_model_proto_enumTypes[1]
}

func (x ServiceCharge_AutoApplyCondition) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceCharge_AutoApplyCondition.Descriptor instead.
func (ServiceCharge_AutoApplyCondition) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{0, 1}
}

// Auto apply time type
type ServiceCharge_AutoApplyTimeType int32

const (
	// Unspecified
	ServiceCharge_AUTO_APPLY_TIME_TYPE_UNSPECIFIED ServiceCharge_AutoApplyTimeType = 0
	// business hours
	ServiceCharge_BUSINESS_HOUR ServiceCharge_AutoApplyTimeType = 1
	// certain time
	ServiceCharge_CERTAIN_TIME ServiceCharge_AutoApplyTimeType = 2
)

// Enum value maps for ServiceCharge_AutoApplyTimeType.
var (
	ServiceCharge_AutoApplyTimeType_name = map[int32]string{
		0: "AUTO_APPLY_TIME_TYPE_UNSPECIFIED",
		1: "BUSINESS_HOUR",
		2: "CERTAIN_TIME",
	}
	ServiceCharge_AutoApplyTimeType_value = map[string]int32{
		"AUTO_APPLY_TIME_TYPE_UNSPECIFIED": 0,
		"BUSINESS_HOUR":                    1,
		"CERTAIN_TIME":                     2,
	}
)

func (x ServiceCharge_AutoApplyTimeType) Enum() *ServiceCharge_AutoApplyTimeType {
	p := new(ServiceCharge_AutoApplyTimeType)
	*p = x
	return p
}

func (x ServiceCharge_AutoApplyTimeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceCharge_AutoApplyTimeType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_service_charge_model_proto_enumTypes[2].Descriptor()
}

func (ServiceCharge_AutoApplyTimeType) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_service_charge_model_proto_enumTypes[2]
}

func (x ServiceCharge_AutoApplyTimeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceCharge_AutoApplyTimeType.Descriptor instead.
func (ServiceCharge_AutoApplyTimeType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{0, 2}
}

// apply type
type ServiceCharge_ApplyType int32

const (
	// Unspecified
	ServiceCharge_APPLY_TYPE_UNSPECIFIED ServiceCharge_ApplyType = 0
	// Per Appointment
	ServiceCharge_PER_APPOINTMENT ServiceCharge_ApplyType = 1
	// Per Pet
	ServiceCharge_PER_PET ServiceCharge_ApplyType = 2
	// Per Pricing Unit
	ServiceCharge_PER_PRICING_UNIT ServiceCharge_ApplyType = 3
)

// Enum value maps for ServiceCharge_ApplyType.
var (
	ServiceCharge_ApplyType_name = map[int32]string{
		0: "APPLY_TYPE_UNSPECIFIED",
		1: "PER_APPOINTMENT",
		2: "PER_PET",
		3: "PER_PRICING_UNIT",
	}
	ServiceCharge_ApplyType_value = map[string]int32{
		"APPLY_TYPE_UNSPECIFIED": 0,
		"PER_APPOINTMENT":        1,
		"PER_PET":                2,
		"PER_PRICING_UNIT":       3,
	}
)

func (x ServiceCharge_ApplyType) Enum() *ServiceCharge_ApplyType {
	p := new(ServiceCharge_ApplyType)
	*p = x
	return p
}

func (x ServiceCharge_ApplyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceCharge_ApplyType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_service_charge_model_proto_enumTypes[3].Descriptor()
}

func (ServiceCharge_ApplyType) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_service_charge_model_proto_enumTypes[3]
}

func (x ServiceCharge_ApplyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceCharge_ApplyType.Descriptor instead.
func (ServiceCharge_ApplyType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{0, 3}
}

// Exceed 24-hours period rule. Only applicable when surcharge_type == CHARGE_24_HOUR
// Type of time-based charge calculation (e.g., exceed certain hours)
type ServiceCharge_TimeBasedPricingType int32

const (
	// Unspecified
	ServiceCharge_TIME_BASED_PRICING_TYPE_UNSPECIFIED ServiceCharge_TimeBasedPricingType = 0
	// A flat rate applied regardless of extra hours or pet count.
	ServiceCharge_FLAT_RATE ServiceCharge_TimeBasedPricingType = 1
	// Tiered pricing based on how many hours the service exceeds.
	ServiceCharge_HOURLY_EXCEED_TIERED_RATE ServiceCharge_TimeBasedPricingType = 2
)

// Enum value maps for ServiceCharge_TimeBasedPricingType.
var (
	ServiceCharge_TimeBasedPricingType_name = map[int32]string{
		0: "TIME_BASED_PRICING_TYPE_UNSPECIFIED",
		1: "FLAT_RATE",
		2: "HOURLY_EXCEED_TIERED_RATE",
	}
	ServiceCharge_TimeBasedPricingType_value = map[string]int32{
		"TIME_BASED_PRICING_TYPE_UNSPECIFIED": 0,
		"FLAT_RATE":                           1,
		"HOURLY_EXCEED_TIERED_RATE":           2,
	}
)

func (x ServiceCharge_TimeBasedPricingType) Enum() *ServiceCharge_TimeBasedPricingType {
	p := new(ServiceCharge_TimeBasedPricingType)
	*p = x
	return p
}

func (x ServiceCharge_TimeBasedPricingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceCharge_TimeBasedPricingType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_service_charge_model_proto_enumTypes[4].Descriptor()
}

func (ServiceCharge_TimeBasedPricingType) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_service_charge_model_proto_enumTypes[4]
}

func (x ServiceCharge_TimeBasedPricingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceCharge_TimeBasedPricingType.Descriptor instead.
func (ServiceCharge_TimeBasedPricingType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{0, 4}
}

// How to apply the charge when multiple pets are involved (only applicable if time-based tiered rate is used)
type ServiceCharge_MultiplePetsChargeType int32

const (
	// Unspecified
	ServiceCharge_MULTIPLE_PETS_CHARGE_TYPE_UNSPECIFIED ServiceCharge_MultiplePetsChargeType = 0
	// Each pet is charged the same amount.
	ServiceCharge_SAME_CHARGE_PER_PET ServiceCharge_MultiplePetsChargeType = 1
	// First pet full price, others different.
	ServiceCharge_DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS ServiceCharge_MultiplePetsChargeType = 2
)

// Enum value maps for ServiceCharge_MultiplePetsChargeType.
var (
	ServiceCharge_MultiplePetsChargeType_name = map[int32]string{
		0: "MULTIPLE_PETS_CHARGE_TYPE_UNSPECIFIED",
		1: "SAME_CHARGE_PER_PET",
		2: "DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS",
	}
	ServiceCharge_MultiplePetsChargeType_value = map[string]int32{
		"MULTIPLE_PETS_CHARGE_TYPE_UNSPECIFIED": 0,
		"SAME_CHARGE_PER_PET":                   1,
		"DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS":  2,
	}
)

func (x ServiceCharge_MultiplePetsChargeType) Enum() *ServiceCharge_MultiplePetsChargeType {
	p := new(ServiceCharge_MultiplePetsChargeType)
	*p = x
	return p
}

func (x ServiceCharge_MultiplePetsChargeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceCharge_MultiplePetsChargeType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_service_charge_model_proto_enumTypes[5].Descriptor()
}

func (ServiceCharge_MultiplePetsChargeType) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_service_charge_model_proto_enumTypes[5]
}

func (x ServiceCharge_MultiplePetsChargeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceCharge_MultiplePetsChargeType.Descriptor instead.
func (ServiceCharge_MultiplePetsChargeType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{0, 5}
}

// source
type ServiceCharge_Source int32

const (
	// source is not set
	ServiceCharge_SOURCE_UNSPECIFIED ServiceCharge_Source = 0
	// source is from MoeGo platform (e.x. b web/app)
	ServiceCharge_MOEGO_PLATFORM ServiceCharge_Source = 1
	// source is from Enterprise Hub
	ServiceCharge_ENTERPRISE_HUB ServiceCharge_Source = 2
)

// Enum value maps for ServiceCharge_Source.
var (
	ServiceCharge_Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "MOEGO_PLATFORM",
		2: "ENTERPRISE_HUB",
	}
	ServiceCharge_Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"MOEGO_PLATFORM":     1,
		"ENTERPRISE_HUB":     2,
	}
)

func (x ServiceCharge_Source) Enum() *ServiceCharge_Source {
	p := new(ServiceCharge_Source)
	*p = x
	return p
}

func (x ServiceCharge_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceCharge_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_service_charge_model_proto_enumTypes[6].Descriptor()
}

func (ServiceCharge_Source) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_service_charge_model_proto_enumTypes[6]
}

func (x ServiceCharge_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceCharge_Source.Descriptor instead.
func (ServiceCharge_Source) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{0, 6}
}

// service charge charge
type ServiceCharge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// split method
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
	// customize config
	TaxId int32 `protobuf:"varint,6,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// sort
	Sort int32 `protobuf:"varint,7,opt,name=sort,proto3" json:"sort,omitempty"`
	// auto apply status
	AutoApplyStatus ServiceCharge_AutoApplyStatus `protobuf:"varint,8,opt,name=auto_apply_status,json=autoApplyStatus,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyStatus" json:"auto_apply_status,omitempty"`
	// auto apply condition
	AutoApplyCondition ServiceCharge_AutoApplyCondition `protobuf:"varint,9,opt,name=auto_apply_condition,json=autoApplyCondition,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyCondition" json:"auto_apply_condition,omitempty"`
	// auto apply time, unit: minute
	// Only available when auto_apply_status is AUTO_APPLY_ENABLED_WITH_CONDITION
	AutoApplyTime int32 `protobuf:"varint,10,opt,name=auto_apply_time,json=autoApplyTime,proto3" json:"auto_apply_time,omitempty"`
	// is mandatory, preserved 8-29 for future use
	// deprecated by freeman since /2024/9/24, use auto_apply_status instead
	//
	// Deprecated: Do not use.
	IsMandatory bool `protobuf:"varint,30,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,31,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,32,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// created by, staff id
	CreatedBy int64 `protobuf:"varint,33,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// updated by, staff id
	UpdatedBy int64 `protobuf:"varint,34,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,35,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,36,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// service charge location override data
	LocationOverrideList []*ServiceChargeLocationOverride `protobuf:"bytes,37,rep,name=location_override_list,json=locationOverrideList,proto3" json:"location_override_list,omitempty"`
	// is all location
	IsAllLocation bool `protobuf:"varint,38,opt,name=is_all_location,json=isAllLocation,proto3" json:"is_all_location,omitempty"`
	// apply type
	ApplyType ServiceCharge_ApplyType `protobuf:"varint,39,opt,name=apply_type,json=applyType,proto3,enum=moego.models.order.v1.ServiceCharge_ApplyType" json:"apply_type,omitempty"`
	// auto support service
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,13,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// auto apply time
	AutoApplyTimeType *ServiceCharge_AutoApplyTimeType `protobuf:"varint,14,opt,name=auto_apply_time_type,json=autoApplyTimeType,proto3,enum=moego.models.order.v1.ServiceCharge_AutoApplyTimeType,oneof" json:"auto_apply_time_type,omitempty"`
	// surcharge type
	SurchargeType *SurchargeType `protobuf:"varint,15,opt,name=surcharge_type,json=surchargeType,proto3,enum=moego.models.order.v1.SurchargeType,oneof" json:"surcharge_type,omitempty"`
	// charge method
	ChargeMethod ChargeMethod `protobuf:"varint,16,opt,name=charge_method,json=chargeMethod,proto3,enum=moego.models.order.v1.ChargeMethod" json:"charge_method,omitempty"`
	// food source
	FoodSource *ServiceCharge_FoodSource `protobuf:"bytes,17,opt,name=food_source,json=foodSource,proto3,oneof" json:"food_source,omitempty"`
	// Only applicable when surcharge_type == CHARGE_24_HOUR
	TimeBasedPricingType *ServiceCharge_TimeBasedPricingType `protobuf:"varint,18,opt,name=time_based_pricing_type,json=timeBasedPricingType,proto3,enum=moego.models.order.v1.ServiceCharge_TimeBasedPricingType,oneof" json:"time_based_pricing_type,omitempty"`
	// Applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
	MultiplePetsChargeType *ServiceCharge_MultiplePetsChargeType `protobuf:"varint,19,opt,name=multiple_pets_charge_type,json=multiplePetsChargeType,proto3,enum=moego.models.order.v1.ServiceCharge_MultiplePetsChargeType,oneof" json:"multiple_pets_charge_type,omitempty"`
	// List of rules for hourly exceed charges, applicable only if time_based_pricing_type == HOURLY_EXCEED_TIERED_RATE
	HourlyExceedRules []*ServiceChargeExceedHourRule `protobuf:"bytes,20,rep,name=hourly_exceed_rules,json=hourlyExceedRules,proto3" json:"hourly_exceed_rules,omitempty"`
	// whether the service charge is available for all services
	EnableServiceFilter *bool `protobuf:"varint,21,opt,name=enable_service_filter,json=enableServiceFilter,proto3,oneof" json:"enable_service_filter,omitempty"`
	// service filters
	ServiceFilterRules []*ServiceFilter `protobuf:"bytes,22,rep,name=service_filter_rules,json=serviceFilterRules,proto3" json:"service_filter_rules,omitempty"`
	// source
	Source ServiceCharge_Source `protobuf:"varint,23,opt,name=source,proto3,enum=moego.models.order.v1.ServiceCharge_Source" json:"source,omitempty"`
}

func (x *ServiceCharge) Reset() {
	*x = ServiceCharge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCharge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCharge) ProtoMessage() {}

func (x *ServiceCharge) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCharge.ProtoReflect.Descriptor instead.
func (*ServiceCharge) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceCharge) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceCharge) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ServiceCharge) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceCharge) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceCharge) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceCharge) GetTaxId() int32 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *ServiceCharge) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ServiceCharge) GetAutoApplyStatus() ServiceCharge_AutoApplyStatus {
	if x != nil {
		return x.AutoApplyStatus
	}
	return ServiceCharge_AUTO_APPLY_STATUS_UNSPECIFIED
}

func (x *ServiceCharge) GetAutoApplyCondition() ServiceCharge_AutoApplyCondition {
	if x != nil {
		return x.AutoApplyCondition
	}
	return ServiceCharge_AUTO_APPLY_CONDITION_UNSPECIFIED
}

func (x *ServiceCharge) GetAutoApplyTime() int32 {
	if x != nil {
		return x.AutoApplyTime
	}
	return 0
}

// Deprecated: Do not use.
func (x *ServiceCharge) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

func (x *ServiceCharge) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *ServiceCharge) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *ServiceCharge) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *ServiceCharge) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *ServiceCharge) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ServiceCharge) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ServiceCharge) GetLocationOverrideList() []*ServiceChargeLocationOverride {
	if x != nil {
		return x.LocationOverrideList
	}
	return nil
}

func (x *ServiceCharge) GetIsAllLocation() bool {
	if x != nil {
		return x.IsAllLocation
	}
	return false
}

func (x *ServiceCharge) GetApplyType() ServiceCharge_ApplyType {
	if x != nil {
		return x.ApplyType
	}
	return ServiceCharge_APPLY_TYPE_UNSPECIFIED
}

func (x *ServiceCharge) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ServiceCharge) GetAutoApplyTimeType() ServiceCharge_AutoApplyTimeType {
	if x != nil && x.AutoApplyTimeType != nil {
		return *x.AutoApplyTimeType
	}
	return ServiceCharge_AUTO_APPLY_TIME_TYPE_UNSPECIFIED
}

func (x *ServiceCharge) GetSurchargeType() SurchargeType {
	if x != nil && x.SurchargeType != nil {
		return *x.SurchargeType
	}
	return SurchargeType_SURCHARGE_TYPE_UNSPECIFIED
}

func (x *ServiceCharge) GetChargeMethod() ChargeMethod {
	if x != nil {
		return x.ChargeMethod
	}
	return ChargeMethod_CHARGE_METHOD_UNSPECIFIED
}

func (x *ServiceCharge) GetFoodSource() *ServiceCharge_FoodSource {
	if x != nil {
		return x.FoodSource
	}
	return nil
}

func (x *ServiceCharge) GetTimeBasedPricingType() ServiceCharge_TimeBasedPricingType {
	if x != nil && x.TimeBasedPricingType != nil {
		return *x.TimeBasedPricingType
	}
	return ServiceCharge_TIME_BASED_PRICING_TYPE_UNSPECIFIED
}

func (x *ServiceCharge) GetMultiplePetsChargeType() ServiceCharge_MultiplePetsChargeType {
	if x != nil && x.MultiplePetsChargeType != nil {
		return *x.MultiplePetsChargeType
	}
	return ServiceCharge_MULTIPLE_PETS_CHARGE_TYPE_UNSPECIFIED
}

func (x *ServiceCharge) GetHourlyExceedRules() []*ServiceChargeExceedHourRule {
	if x != nil {
		return x.HourlyExceedRules
	}
	return nil
}

func (x *ServiceCharge) GetEnableServiceFilter() bool {
	if x != nil && x.EnableServiceFilter != nil {
		return *x.EnableServiceFilter
	}
	return false
}

func (x *ServiceCharge) GetServiceFilterRules() []*ServiceFilter {
	if x != nil {
		return x.ServiceFilterRules
	}
	return nil
}

func (x *ServiceCharge) GetSource() ServiceCharge_Source {
	if x != nil {
		return x.Source
	}
	return ServiceCharge_SOURCE_UNSPECIFIED
}

// service filter
type ServiceFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// whether the service charge is available for all services
	AvailableForAllServices bool `protobuf:"varint,2,opt,name=available_for_all_services,json=availableForAllServices,proto3" json:"available_for_all_services,omitempty"`
	// available service ids (only if available_for_all_services is false)
	AvailableServiceIdList []int64 `protobuf:"varint,3,rep,packed,name=available_service_id_list,json=availableServiceIdList,proto3" json:"available_service_id_list,omitempty"`
}

func (x *ServiceFilter) Reset() {
	*x = ServiceFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceFilter) ProtoMessage() {}

func (x *ServiceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceFilter.ProtoReflect.Descriptor instead.
func (*ServiceFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceFilter) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *ServiceFilter) GetAvailableForAllServices() bool {
	if x != nil {
		return x.AvailableForAllServices
	}
	return false
}

func (x *ServiceFilter) GetAvailableServiceIdList() []int64 {
	if x != nil {
		return x.AvailableServiceIdList
	}
	return nil
}

// service charge location override
type ServiceChargeLocationOverride struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// price
	Price *float64 `protobuf:"fixed64,2,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// tax id
	TaxId *int32 `protobuf:"varint,3,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
}

func (x *ServiceChargeLocationOverride) Reset() {
	*x = ServiceChargeLocationOverride{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceChargeLocationOverride) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceChargeLocationOverride) ProtoMessage() {}

func (x *ServiceChargeLocationOverride) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceChargeLocationOverride.ProtoReflect.Descriptor instead.
func (*ServiceChargeLocationOverride) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{2}
}

func (x *ServiceChargeLocationOverride) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ServiceChargeLocationOverride) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *ServiceChargeLocationOverride) GetTaxId() int32 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

// service charge in online booking view
type ServiceChargeOnlineBookingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
	// apply quantity
	ApplyQuantity int32 `protobuf:"varint,6,opt,name=apply_quantity,json=applyQuantity,proto3" json:"apply_quantity,omitempty"`
	// total price
	TotalPrice float64 `protobuf:"fixed64,7,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
}

func (x *ServiceChargeOnlineBookingView) Reset() {
	*x = ServiceChargeOnlineBookingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceChargeOnlineBookingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceChargeOnlineBookingView) ProtoMessage() {}

func (x *ServiceChargeOnlineBookingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceChargeOnlineBookingView.ProtoReflect.Descriptor instead.
func (*ServiceChargeOnlineBookingView) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{3}
}

func (x *ServiceChargeOnlineBookingView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceChargeOnlineBookingView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceChargeOnlineBookingView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceChargeOnlineBookingView) GetApplyQuantity() int32 {
	if x != nil {
		return x.ApplyQuantity
	}
	return 0
}

func (x *ServiceChargeOnlineBookingView) GetTotalPrice() float64 {
	if x != nil {
		return x.TotalPrice
	}
	return 0
}

// feeding medication charge view
type FeedingMedicationChargeView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`
	// charge method
	ChargeMethod ChargeMethod `protobuf:"varint,5,opt,name=charge_method,json=chargeMethod,proto3,enum=moego.models.order.v1.ChargeMethod" json:"charge_method,omitempty"`
	// food source ids
	FoodSourceIds []int64 `protobuf:"varint,6,rep,packed,name=food_source_ids,json=foodSourceIds,proto3" json:"food_source_ids,omitempty"`
	// is all food source
	IsAllFoodSource bool `protobuf:"varint,7,opt,name=is_all_food_source,json=isAllFoodSource,proto3" json:"is_all_food_source,omitempty"`
	// associated food source
	AssociatedFoodSource []*FeedingMedicationChargeView_FoodSource `protobuf:"bytes,8,rep,name=associated_food_source,json=associatedFoodSource,proto3" json:"associated_food_source,omitempty"`
}

func (x *FeedingMedicationChargeView) Reset() {
	*x = FeedingMedicationChargeView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedingMedicationChargeView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedingMedicationChargeView) ProtoMessage() {}

func (x *FeedingMedicationChargeView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedingMedicationChargeView.ProtoReflect.Descriptor instead.
func (*FeedingMedicationChargeView) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{4}
}

func (x *FeedingMedicationChargeView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FeedingMedicationChargeView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FeedingMedicationChargeView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FeedingMedicationChargeView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *FeedingMedicationChargeView) GetChargeMethod() ChargeMethod {
	if x != nil {
		return x.ChargeMethod
	}
	return ChargeMethod_CHARGE_METHOD_UNSPECIFIED
}

func (x *FeedingMedicationChargeView) GetFoodSourceIds() []int64 {
	if x != nil {
		return x.FoodSourceIds
	}
	return nil
}

func (x *FeedingMedicationChargeView) GetIsAllFoodSource() bool {
	if x != nil {
		return x.IsAllFoodSource
	}
	return false
}

func (x *FeedingMedicationChargeView) GetAssociatedFoodSource() []*FeedingMedicationChargeView_FoodSource {
	if x != nil {
		return x.AssociatedFoodSource
	}
	return nil
}

// Represents a fee that applies when the service duration exceeds a certain number of hours.
type ServiceChargeExceedHourRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the fee (e.g., "Peak Hour Fee").
	FeeName string `protobuf:"bytes,1,opt,name=fee_name,json=feeName,proto3" json:"fee_name,omitempty"`
	// hour (0–24)
	Hour int32 `protobuf:"varint,2,opt,name=hour,proto3" json:"hour,omitempty"`
	// Base price for one pet.
	BasePrice float64 `protobuf:"fixed64,3,opt,name=base_price,json=basePrice,proto3" json:"base_price,omitempty"`
	// Optional additional price for each extra pet.
	// Only used if multiple_pets_charge_type == DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS.
	AdditionalPetPrice *float64 `protobuf:"fixed64,4,opt,name=additional_pet_price,json=additionalPetPrice,proto3,oneof" json:"additional_pet_price,omitempty"`
}

func (x *ServiceChargeExceedHourRule) Reset() {
	*x = ServiceChargeExceedHourRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceChargeExceedHourRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceChargeExceedHourRule) ProtoMessage() {}

func (x *ServiceChargeExceedHourRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceChargeExceedHourRule.ProtoReflect.Descriptor instead.
func (*ServiceChargeExceedHourRule) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{5}
}

func (x *ServiceChargeExceedHourRule) GetFeeName() string {
	if x != nil {
		return x.FeeName
	}
	return ""
}

func (x *ServiceChargeExceedHourRule) GetHour() int32 {
	if x != nil {
		return x.Hour
	}
	return 0
}

func (x *ServiceChargeExceedHourRule) GetBasePrice() float64 {
	if x != nil {
		return x.BasePrice
	}
	return 0
}

func (x *ServiceChargeExceedHourRule) GetAdditionalPetPrice() float64 {
	if x != nil && x.AdditionalPetPrice != nil {
		return *x.AdditionalPetPrice
	}
	return 0
}

// food source model
type ServiceCharge_FoodSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// food source ids
	FoodSourceIds []int64 `protobuf:"varint,1,rep,packed,name=food_source_ids,json=foodSourceIds,proto3" json:"food_source_ids,omitempty"`
	// is all food source
	IsAllFoodSource bool `protobuf:"varint,2,opt,name=is_all_food_source,json=isAllFoodSource,proto3" json:"is_all_food_source,omitempty"`
}

func (x *ServiceCharge_FoodSource) Reset() {
	*x = ServiceCharge_FoodSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCharge_FoodSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCharge_FoodSource) ProtoMessage() {}

func (x *ServiceCharge_FoodSource) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCharge_FoodSource.ProtoReflect.Descriptor instead.
func (*ServiceCharge_FoodSource) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ServiceCharge_FoodSource) GetFoodSourceIds() []int64 {
	if x != nil {
		return x.FoodSourceIds
	}
	return nil
}

func (x *ServiceCharge_FoodSource) GetIsAllFoodSource() bool {
	if x != nil {
		return x.IsAllFoodSource
	}
	return false
}

// food source model
type FeedingMedicationChargeView_FoodSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// food source id
	FoodSourceId int64 `protobuf:"varint,1,opt,name=food_source_id,json=foodSourceId,proto3" json:"food_source_id,omitempty"`
	// food source name
	FoodSourceName string `protobuf:"bytes,2,opt,name=food_source_name,json=foodSourceName,proto3" json:"food_source_name,omitempty"`
}

func (x *FeedingMedicationChargeView_FoodSource) Reset() {
	*x = FeedingMedicationChargeView_FoodSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedingMedicationChargeView_FoodSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedingMedicationChargeView_FoodSource) ProtoMessage() {}

func (x *FeedingMedicationChargeView_FoodSource) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_service_charge_model_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedingMedicationChargeView_FoodSource.ProtoReflect.Descriptor instead.
func (*FeedingMedicationChargeView_FoodSource) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP(), []int{4, 0}
}

func (x *FeedingMedicationChargeView_FoodSource) GetFoodSourceId() int64 {
	if x != nil {
		return x.FoodSourceId
	}
	return 0
}

func (x *FeedingMedicationChargeView_FoodSource) GetFoodSourceName() string {
	if x != nil {
		return x.FoodSourceName
	}
	return ""
}

var File_moego_models_order_v1_service_charge_model_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_service_charge_model_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfa, 0x16, 0x0a, 0x0d, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73,
	0x6f, 0x72, 0x74, 0x12, 0x60, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c,
	0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x69, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x61, 0x75,
	0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x26, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0b, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x21, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x22, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x6a, 0x0a, 0x16, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x25, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x14, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x69,
	0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x26,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x6c, 0x0a, 0x14, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e,
	0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x48, 0x00, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69,
	0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x0e, 0x73, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x01, 0x52, 0x0d, 0x73, 0x75, 0x72, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0d, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x55, 0x0a, 0x0b, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x2e, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x02, 0x52, 0x0a, 0x66,
	0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x75, 0x0a, 0x17,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x48, 0x03, 0x52, 0x14, 0x74, 0x69, 0x6d, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x7b, 0x0a, 0x19, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f,
	0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x70, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x48, 0x04, 0x52, 0x16, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x50,
	0x65, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x62, 0x0a, 0x13, 0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x5f, 0x65, 0x78, 0x63, 0x65, 0x65,
	0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x11, 0x68, 0x6f, 0x75, 0x72, 0x6c, 0x79, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a,
	0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x1a, 0x61, 0x0a, 0x0a, 0x46, 0x6f,
	0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x6f, 0x6f, 0x64,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0d, 0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x73,
	0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73,
	0x41, 0x6c, 0x6c, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x8c, 0x01,
	0x0a, 0x0f, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x16, 0x0a,
	0x12, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x59, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x5f, 0x57, 0x49, 0x54, 0x48,
	0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x22, 0x65, 0x0a, 0x12,
	0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x59,
	0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x44, 0x5f, 0x4c,
	0x41, 0x54, 0x45, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11,
	0x42, 0x44, 0x5f, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46,
	0x46, 0x10, 0x02, 0x22, 0x5e, 0x0a, 0x11, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x55, 0x54, 0x4f,
	0x5f, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11,
	0x0a, 0x0d, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x48, 0x4f, 0x55, 0x52, 0x10,
	0x01, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x45, 0x52, 0x54, 0x41, 0x49, 0x4e, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x10, 0x02, 0x22, 0x5f, 0x0a, 0x09, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x16, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f,
	0x50, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x10, 0x02, 0x12, 0x14,
	0x0a, 0x10, 0x50, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x4e,
	0x49, 0x54, 0x10, 0x03, 0x22, 0x6d, 0x0a, 0x14, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x64, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x23,
	0x54, 0x49, 0x4d, 0x45, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x49,
	0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x4c, 0x41, 0x54, 0x5f, 0x52, 0x41,
	0x54, 0x45, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x48, 0x4f, 0x55, 0x52, 0x4c, 0x59, 0x5f, 0x45,
	0x58, 0x43, 0x45, 0x45, 0x44, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x52, 0x41, 0x54,
	0x45, 0x10, 0x02, 0x22, 0x86, 0x01, 0x0a, 0x16, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65,
	0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29,
	0x0a, 0x25, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f, 0x50, 0x45, 0x54, 0x53, 0x5f,
	0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x41, 0x4d,
	0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54,
	0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x54, 0x5f,
	0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x44, 0x44, 0x49, 0x54,
	0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x45, 0x54, 0x53, 0x10, 0x02, 0x22, 0x48, 0x0a, 0x06,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12,
	0x0a, 0x0e, 0x4d, 0x4f, 0x45, 0x47, 0x4f, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d,
	0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45,
	0x5f, 0x48, 0x55, 0x42, 0x10, 0x02, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42,
	0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x1c,
	0x0a, 0x1a, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x73,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x18, 0x0a, 0x16,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xde, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3b, 0x0a, 0x1a, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x72,
	0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f,
	0x72, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x19,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x16, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x1d, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f,
	0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x22, 0xa2, 0x01, 0x0a, 0x1e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x71, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0xea, 0x03, 0x0a, 0x1b,
	0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x66, 0x6f, 0x6f, 0x64, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x61,
	0x6c, 0x6c, 0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x46, 0x6f, 0x6f, 0x64, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x73, 0x0a, 0x16, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x14, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64,
	0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x1a, 0x5c, 0x0a, 0x0a, 0x46, 0x6f,
	0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x6f, 0x6f, 0x64,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x10, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xbb, 0x01, 0x0a, 0x1b, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64,
	0x48, 0x6f, 0x75, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x65, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x62, 0x61, 0x73,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x50, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x17, 0x0a,
	0x15, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x75, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v1_service_charge_model_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_service_charge_model_proto_rawDescData = file_moego_models_order_v1_service_charge_model_proto_rawDesc
)

func file_moego_models_order_v1_service_charge_model_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_service_charge_model_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_service_charge_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_service_charge_model_proto_rawDescData)
	})
	return file_moego_models_order_v1_service_charge_model_proto_rawDescData
}

var file_moego_models_order_v1_service_charge_model_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_moego_models_order_v1_service_charge_model_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_models_order_v1_service_charge_model_proto_goTypes = []interface{}{
	(ServiceCharge_AutoApplyStatus)(0),             // 0: moego.models.order.v1.ServiceCharge.AutoApplyStatus
	(ServiceCharge_AutoApplyCondition)(0),          // 1: moego.models.order.v1.ServiceCharge.AutoApplyCondition
	(ServiceCharge_AutoApplyTimeType)(0),           // 2: moego.models.order.v1.ServiceCharge.AutoApplyTimeType
	(ServiceCharge_ApplyType)(0),                   // 3: moego.models.order.v1.ServiceCharge.ApplyType
	(ServiceCharge_TimeBasedPricingType)(0),        // 4: moego.models.order.v1.ServiceCharge.TimeBasedPricingType
	(ServiceCharge_MultiplePetsChargeType)(0),      // 5: moego.models.order.v1.ServiceCharge.MultiplePetsChargeType
	(ServiceCharge_Source)(0),                      // 6: moego.models.order.v1.ServiceCharge.Source
	(*ServiceCharge)(nil),                          // 7: moego.models.order.v1.ServiceCharge
	(*ServiceFilter)(nil),                          // 8: moego.models.order.v1.ServiceFilter
	(*ServiceChargeLocationOverride)(nil),          // 9: moego.models.order.v1.ServiceChargeLocationOverride
	(*ServiceChargeOnlineBookingView)(nil),         // 10: moego.models.order.v1.ServiceChargeOnlineBookingView
	(*FeedingMedicationChargeView)(nil),            // 11: moego.models.order.v1.FeedingMedicationChargeView
	(*ServiceChargeExceedHourRule)(nil),            // 12: moego.models.order.v1.ServiceChargeExceedHourRule
	(*ServiceCharge_FoodSource)(nil),               // 13: moego.models.order.v1.ServiceCharge.FoodSource
	(*FeedingMedicationChargeView_FoodSource)(nil), // 14: moego.models.order.v1.FeedingMedicationChargeView.FoodSource
	(*timestamppb.Timestamp)(nil),                  // 15: google.protobuf.Timestamp
	(v1.ServiceItemType)(0),                        // 16: moego.models.offering.v1.ServiceItemType
	(SurchargeType)(0),                             // 17: moego.models.order.v1.SurchargeType
	(ChargeMethod)(0),                              // 18: moego.models.order.v1.ChargeMethod
}
var file_moego_models_order_v1_service_charge_model_proto_depIdxs = []int32{
	0,  // 0: moego.models.order.v1.ServiceCharge.auto_apply_status:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyStatus
	1,  // 1: moego.models.order.v1.ServiceCharge.auto_apply_condition:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyCondition
	15, // 2: moego.models.order.v1.ServiceCharge.created_at:type_name -> google.protobuf.Timestamp
	15, // 3: moego.models.order.v1.ServiceCharge.updated_at:type_name -> google.protobuf.Timestamp
	9,  // 4: moego.models.order.v1.ServiceCharge.location_override_list:type_name -> moego.models.order.v1.ServiceChargeLocationOverride
	3,  // 5: moego.models.order.v1.ServiceCharge.apply_type:type_name -> moego.models.order.v1.ServiceCharge.ApplyType
	16, // 6: moego.models.order.v1.ServiceCharge.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	2,  // 7: moego.models.order.v1.ServiceCharge.auto_apply_time_type:type_name -> moego.models.order.v1.ServiceCharge.AutoApplyTimeType
	17, // 8: moego.models.order.v1.ServiceCharge.surcharge_type:type_name -> moego.models.order.v1.SurchargeType
	18, // 9: moego.models.order.v1.ServiceCharge.charge_method:type_name -> moego.models.order.v1.ChargeMethod
	13, // 10: moego.models.order.v1.ServiceCharge.food_source:type_name -> moego.models.order.v1.ServiceCharge.FoodSource
	4,  // 11: moego.models.order.v1.ServiceCharge.time_based_pricing_type:type_name -> moego.models.order.v1.ServiceCharge.TimeBasedPricingType
	5,  // 12: moego.models.order.v1.ServiceCharge.multiple_pets_charge_type:type_name -> moego.models.order.v1.ServiceCharge.MultiplePetsChargeType
	12, // 13: moego.models.order.v1.ServiceCharge.hourly_exceed_rules:type_name -> moego.models.order.v1.ServiceChargeExceedHourRule
	8,  // 14: moego.models.order.v1.ServiceCharge.service_filter_rules:type_name -> moego.models.order.v1.ServiceFilter
	6,  // 15: moego.models.order.v1.ServiceCharge.source:type_name -> moego.models.order.v1.ServiceCharge.Source
	16, // 16: moego.models.order.v1.ServiceFilter.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	18, // 17: moego.models.order.v1.FeedingMedicationChargeView.charge_method:type_name -> moego.models.order.v1.ChargeMethod
	14, // 18: moego.models.order.v1.FeedingMedicationChargeView.associated_food_source:type_name -> moego.models.order.v1.FeedingMedicationChargeView.FoodSource
	19, // [19:19] is the sub-list for method output_type
	19, // [19:19] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_service_charge_model_proto_init() }
func file_moego_models_order_v1_service_charge_model_proto_init() {
	if File_moego_models_order_v1_service_charge_model_proto != nil {
		return
	}
	file_moego_models_order_v1_service_charge_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_service_charge_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCharge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_service_charge_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_service_charge_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceChargeLocationOverride); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_service_charge_model_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceChargeOnlineBookingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_service_charge_model_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedingMedicationChargeView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_service_charge_model_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceChargeExceedHourRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_service_charge_model_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCharge_FoodSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_service_charge_model_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedingMedicationChargeView_FoodSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_order_v1_service_charge_model_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_order_v1_service_charge_model_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_order_v1_service_charge_model_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_service_charge_model_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_service_charge_model_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_service_charge_model_proto_depIdxs,
		EnumInfos:         file_moego_models_order_v1_service_charge_model_proto_enumTypes,
		MessageInfos:      file_moego_models_order_v1_service_charge_model_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_service_charge_model_proto = out.File
	file_moego_models_order_v1_service_charge_model_proto_rawDesc = nil
	file_moego_models_order_v1_service_charge_model_proto_goTypes = nil
	file_moego_models_order_v1_service_charge_model_proto_depIdxs = nil
}
