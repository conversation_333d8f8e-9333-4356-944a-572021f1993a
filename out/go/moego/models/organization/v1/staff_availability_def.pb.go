// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/staff_availability_def.proto

package organizationpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// update staff availability def
type StaffAvailabilityDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// is available, slot场景下无对应
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// schedule type
	ScheduleType *ScheduleType `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.organization.v1.ScheduleType,oneof" json:"schedule_type,omitempty"`
	// slot availability day defs
	SlotAvailabilityDayList []*SlotAvailabilityDayDef `protobuf:"bytes,4,rep,name=slot_availability_day_list,json=slotAvailabilityDayList,proto3" json:"slot_availability_day_list,omitempty"`
	// time schedule type
	TimeScheduleType *ScheduleType `protobuf:"varint,6,opt,name=time_schedule_type,json=timeScheduleType,proto3,enum=moego.models.organization.v1.ScheduleType,oneof" json:"time_schedule_type,omitempty"`
	// time availability day defs
	TimeAvailabilityDayList []*TimeAvailabilityDayDef `protobuf:"bytes,5,rep,name=time_availability_day_list,json=timeAvailabilityDayList,proto3" json:"time_availability_day_list,omitempty"`
}

func (x *StaffAvailabilityDef) Reset() {
	*x = StaffAvailabilityDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffAvailabilityDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffAvailabilityDef) ProtoMessage() {}

func (x *StaffAvailabilityDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffAvailabilityDef.ProtoReflect.Descriptor instead.
func (*StaffAvailabilityDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{0}
}

func (x *StaffAvailabilityDef) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffAvailabilityDef) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *StaffAvailabilityDef) GetScheduleType() ScheduleType {
	if x != nil && x.ScheduleType != nil {
		return *x.ScheduleType
	}
	return ScheduleType_SCHEDULE_TYPE_UNSPECIFIED
}

func (x *StaffAvailabilityDef) GetSlotAvailabilityDayList() []*SlotAvailabilityDayDef {
	if x != nil {
		return x.SlotAvailabilityDayList
	}
	return nil
}

func (x *StaffAvailabilityDef) GetTimeScheduleType() ScheduleType {
	if x != nil && x.TimeScheduleType != nil {
		return *x.TimeScheduleType
	}
	return ScheduleType_SCHEDULE_TYPE_UNSPECIFIED
}

func (x *StaffAvailabilityDef) GetTimeAvailabilityDayList() []*TimeAvailabilityDayDef {
	if x != nil {
		return x.TimeAvailabilityDayList
	}
	return nil
}

// slot availability day def
type SlotAvailabilityDayDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// day of week
	DayOfWeek *dayofweek.DayOfWeek `protobuf:"varint,1,opt,name=day_of_week,json=dayOfWeek,proto3,enum=google.type.DayOfWeek,oneof" json:"day_of_week,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// schedule type
	ScheduleType *ScheduleType `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.organization.v1.ScheduleType,oneof" json:"schedule_type,omitempty"`
	// slot daily setting def
	SlotDailySetting *SlotDailySettingDef `protobuf:"bytes,4,opt,name=slot_daily_setting,json=slotDailySetting,proto3,oneof" json:"slot_daily_setting,omitempty"`
	// staff available hour defs
	SlotHourSettingList []*SlotHourSettingDef `protobuf:"bytes,5,rep,name=slot_hour_setting_list,json=slotHourSettingList,proto3" json:"slot_hour_setting_list,omitempty"`
	// override date
	OverrideDate *string `protobuf:"bytes,6,opt,name=override_date,json=overrideDate,proto3,oneof" json:"override_date,omitempty"`
}

func (x *SlotAvailabilityDayDef) Reset() {
	*x = SlotAvailabilityDayDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotAvailabilityDayDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotAvailabilityDayDef) ProtoMessage() {}

func (x *SlotAvailabilityDayDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotAvailabilityDayDef.ProtoReflect.Descriptor instead.
func (*SlotAvailabilityDayDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{1}
}

func (x *SlotAvailabilityDayDef) GetDayOfWeek() dayofweek.DayOfWeek {
	if x != nil && x.DayOfWeek != nil {
		return *x.DayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

func (x *SlotAvailabilityDayDef) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *SlotAvailabilityDayDef) GetScheduleType() ScheduleType {
	if x != nil && x.ScheduleType != nil {
		return *x.ScheduleType
	}
	return ScheduleType_SCHEDULE_TYPE_UNSPECIFIED
}

func (x *SlotAvailabilityDayDef) GetSlotDailySetting() *SlotDailySettingDef {
	if x != nil {
		return x.SlotDailySetting
	}
	return nil
}

func (x *SlotAvailabilityDayDef) GetSlotHourSettingList() []*SlotHourSettingDef {
	if x != nil {
		return x.SlotHourSettingList
	}
	return nil
}

func (x *SlotAvailabilityDayDef) GetOverrideDate() string {
	if x != nil && x.OverrideDate != nil {
		return *x.OverrideDate
	}
	return ""
}

// time availability day def
type TimeAvailabilityDayDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// day of week
	DayOfWeek *dayofweek.DayOfWeek `protobuf:"varint,1,opt,name=day_of_week,json=dayOfWeek,proto3,enum=google.type.DayOfWeek,oneof" json:"day_of_week,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// schedule type
	ScheduleType *ScheduleType `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.organization.v1.ScheduleType,oneof" json:"schedule_type,omitempty"`
	// time daily setting info
	TimeDailySetting *TimeDailySettingDef `protobuf:"bytes,4,opt,name=time_daily_setting,json=timeDailySetting,proto3" json:"time_daily_setting,omitempty"`
	// time available hour defs
	TimeHourSettingList []*TimeHourSettingDef `protobuf:"bytes,5,rep,name=time_hour_setting_list,json=timeHourSettingList,proto3" json:"time_hour_setting_list,omitempty"`
	// override date
	OverrideDate *string `protobuf:"bytes,6,opt,name=override_date,json=overrideDate,proto3,oneof" json:"override_date,omitempty"`
}

func (x *TimeAvailabilityDayDef) Reset() {
	*x = TimeAvailabilityDayDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeAvailabilityDayDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeAvailabilityDayDef) ProtoMessage() {}

func (x *TimeAvailabilityDayDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeAvailabilityDayDef.ProtoReflect.Descriptor instead.
func (*TimeAvailabilityDayDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{2}
}

func (x *TimeAvailabilityDayDef) GetDayOfWeek() dayofweek.DayOfWeek {
	if x != nil && x.DayOfWeek != nil {
		return *x.DayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

func (x *TimeAvailabilityDayDef) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *TimeAvailabilityDayDef) GetScheduleType() ScheduleType {
	if x != nil && x.ScheduleType != nil {
		return *x.ScheduleType
	}
	return ScheduleType_SCHEDULE_TYPE_UNSPECIFIED
}

func (x *TimeAvailabilityDayDef) GetTimeDailySetting() *TimeDailySettingDef {
	if x != nil {
		return x.TimeDailySetting
	}
	return nil
}

func (x *TimeAvailabilityDayDef) GetTimeHourSettingList() []*TimeHourSettingDef {
	if x != nil {
		return x.TimeHourSettingList
	}
	return nil
}

func (x *TimeAvailabilityDayDef) GetOverrideDate() string {
	if x != nil && x.OverrideDate != nil {
		return *x.OverrideDate
	}
	return ""
}

// by slot daily setting def
type SlotDailySettingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start time
	StartTime int32 `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time
	EndTime *int32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// capacity
	Capacity *int32 `protobuf:"varint,3,opt,name=capacity,proto3,oneof" json:"capacity,omitempty"`
	// booking limit
	Limit *BookingLimitationDef `protobuf:"bytes,4,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	// booking limit group
	LimitationGroups []*LimitationGroupDef `protobuf:"bytes,5,rep,name=limitation_groups,json=limitationGroups,proto3" json:"limitation_groups,omitempty"`
}

func (x *SlotDailySettingDef) Reset() {
	*x = SlotDailySettingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotDailySettingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotDailySettingDef) ProtoMessage() {}

func (x *SlotDailySettingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotDailySettingDef.ProtoReflect.Descriptor instead.
func (*SlotDailySettingDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{3}
}

func (x *SlotDailySettingDef) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *SlotDailySettingDef) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *SlotDailySettingDef) GetCapacity() int32 {
	if x != nil && x.Capacity != nil {
		return *x.Capacity
	}
	return 0
}

func (x *SlotDailySettingDef) GetLimit() *BookingLimitationDef {
	if x != nil {
		return x.Limit
	}
	return nil
}

func (x *SlotDailySettingDef) GetLimitationGroups() []*LimitationGroupDef {
	if x != nil {
		return x.LimitationGroups
	}
	return nil
}

// by time daily setting def
type TimeDailySettingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking limit
	Limit *BookingLimitationDef `protobuf:"bytes,1,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	// booking limit group
	LimitationGroups []*LimitationGroupDef `protobuf:"bytes,5,rep,name=limitation_groups,json=limitationGroups,proto3" json:"limitation_groups,omitempty"`
}

func (x *TimeDailySettingDef) Reset() {
	*x = TimeDailySettingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeDailySettingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeDailySettingDef) ProtoMessage() {}

func (x *TimeDailySettingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeDailySettingDef.ProtoReflect.Descriptor instead.
func (*TimeDailySettingDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{4}
}

func (x *TimeDailySettingDef) GetLimit() *BookingLimitationDef {
	if x != nil {
		return x.Limit
	}
	return nil
}

func (x *TimeDailySettingDef) GetLimitationGroups() []*LimitationGroupDef {
	if x != nil {
		return x.LimitationGroups
	}
	return nil
}

// by slot hour setting def
type SlotHourSettingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// slot start time
	StartTime *int32 `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// slot capacity
	Capacity *int32 `protobuf:"varint,2,opt,name=capacity,proto3,oneof" json:"capacity,omitempty"`
	// booking limit
	Limit *BookingLimitationDef `protobuf:"bytes,3,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	// slot end time
	EndTime *int32 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// booking limit group
	LimitationGroups []*LimitationGroupDef `protobuf:"bytes,5,rep,name=limitation_groups,json=limitationGroups,proto3" json:"limitation_groups,omitempty"`
}

func (x *SlotHourSettingDef) Reset() {
	*x = SlotHourSettingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotHourSettingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotHourSettingDef) ProtoMessage() {}

func (x *SlotHourSettingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotHourSettingDef.ProtoReflect.Descriptor instead.
func (*SlotHourSettingDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{5}
}

func (x *SlotHourSettingDef) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *SlotHourSettingDef) GetCapacity() int32 {
	if x != nil && x.Capacity != nil {
		return *x.Capacity
	}
	return 0
}

func (x *SlotHourSettingDef) GetLimit() *BookingLimitationDef {
	if x != nil {
		return x.Limit
	}
	return nil
}

func (x *SlotHourSettingDef) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *SlotHourSettingDef) GetLimitationGroups() []*LimitationGroupDef {
	if x != nil {
		return x.LimitationGroups
	}
	return nil
}

// by time hour setting def
type TimeHourSettingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start time
	StartTime *int32 `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// end time
	EndTime *int32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
}

func (x *TimeHourSettingDef) Reset() {
	*x = TimeHourSettingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeHourSettingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeHourSettingDef) ProtoMessage() {}

func (x *TimeHourSettingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeHourSettingDef.ProtoReflect.Descriptor instead.
func (*TimeHourSettingDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{6}
}

func (x *TimeHourSettingDef) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *TimeHourSettingDef) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

// slot/time limit setting
type BookingLimitationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service limits
	ServiceLimits []*BookingLimitationDef_ServiceLimitation `protobuf:"bytes,1,rep,name=service_limits,json=serviceLimits,proto3" json:"service_limits,omitempty"`
	// pet size limits
	PetSizeLimits []*BookingLimitationDef_PetSizeLimitation `protobuf:"bytes,2,rep,name=pet_size_limits,json=petSizeLimits,proto3" json:"pet_size_limits,omitempty"`
	// pet breed limits
	PetBreedLimits []*BookingLimitationDef_PetBreedLimitation `protobuf:"bytes,3,rep,name=pet_breed_limits,json=petBreedLimits,proto3" json:"pet_breed_limits,omitempty"`
}

func (x *BookingLimitationDef) Reset() {
	*x = BookingLimitationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingLimitationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingLimitationDef) ProtoMessage() {}

func (x *BookingLimitationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingLimitationDef.ProtoReflect.Descriptor instead.
func (*BookingLimitationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{7}
}

func (x *BookingLimitationDef) GetServiceLimits() []*BookingLimitationDef_ServiceLimitation {
	if x != nil {
		return x.ServiceLimits
	}
	return nil
}

func (x *BookingLimitationDef) GetPetSizeLimits() []*BookingLimitationDef_PetSizeLimitation {
	if x != nil {
		return x.PetSizeLimits
	}
	return nil
}

func (x *BookingLimitationDef) GetPetBreedLimits() []*BookingLimitationDef_PetBreedLimitation {
	if x != nil {
		return x.PetBreedLimits
	}
	return nil
}

// limitation group
type LimitationGroupDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service limits
	ServiceLimits []*ServiceLimitationDef `protobuf:"bytes,1,rep,name=service_limits,json=serviceLimits,proto3" json:"service_limits,omitempty"`
	// pet size limits
	PetSizeLimits []*PetSizeLimitationDef `protobuf:"bytes,2,rep,name=pet_size_limits,json=petSizeLimits,proto3" json:"pet_size_limits,omitempty"`
	// pet breed limits
	PetBreedLimits []*PetBreedLimitationDef `protobuf:"bytes,3,rep,name=pet_breed_limits,json=petBreedLimits,proto3" json:"pet_breed_limits,omitempty"`
	// only accept selected
	OnlyAcceptSelected bool `protobuf:"varint,4,opt,name=only_accept_selected,json=onlyAcceptSelected,proto3" json:"only_accept_selected,omitempty"`
}

func (x *LimitationGroupDef) Reset() {
	*x = LimitationGroupDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitationGroupDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitationGroupDef) ProtoMessage() {}

func (x *LimitationGroupDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitationGroupDef.ProtoReflect.Descriptor instead.
func (*LimitationGroupDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{8}
}

func (x *LimitationGroupDef) GetServiceLimits() []*ServiceLimitationDef {
	if x != nil {
		return x.ServiceLimits
	}
	return nil
}

func (x *LimitationGroupDef) GetPetSizeLimits() []*PetSizeLimitationDef {
	if x != nil {
		return x.PetSizeLimits
	}
	return nil
}

func (x *LimitationGroupDef) GetPetBreedLimits() []*PetBreedLimitationDef {
	if x != nil {
		return x.PetBreedLimits
	}
	return nil
}

func (x *LimitationGroupDef) GetOnlyAcceptSelected() bool {
	if x != nil {
		return x.OnlyAcceptSelected
	}
	return false
}

// service limitation
type ServiceLimitationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service_id
	ServiceIds []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// is_all_service
	IsAllService bool `protobuf:"varint,2,opt,name=is_all_service,json=isAllService,proto3" json:"is_all_service,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *ServiceLimitationDef) Reset() {
	*x = ServiceLimitationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceLimitationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceLimitationDef) ProtoMessage() {}

func (x *ServiceLimitationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceLimitationDef.ProtoReflect.Descriptor instead.
func (*ServiceLimitationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{9}
}

func (x *ServiceLimitationDef) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *ServiceLimitationDef) GetIsAllService() bool {
	if x != nil {
		return x.IsAllService
	}
	return false
}

func (x *ServiceLimitationDef) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// pet size limitation
type PetSizeLimitationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet_size_id
	PetSizeIds []int64 `protobuf:"varint,1,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// is_all_size
	IsAllSize bool `protobuf:"varint,2,opt,name=is_all_size,json=isAllSize,proto3" json:"is_all_size,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *PetSizeLimitationDef) Reset() {
	*x = PetSizeLimitationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetSizeLimitationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetSizeLimitationDef) ProtoMessage() {}

func (x *PetSizeLimitationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetSizeLimitationDef.ProtoReflect.Descriptor instead.
func (*PetSizeLimitationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{10}
}

func (x *PetSizeLimitationDef) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *PetSizeLimitationDef) GetIsAllSize() bool {
	if x != nil {
		return x.IsAllSize
	}
	return false
}

func (x *PetSizeLimitationDef) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// pet breed limitation
type PetBreedLimitationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet_type_id
	PetTypeId int64 `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// is_all_breed
	IsAllBreed bool `protobuf:"varint,2,opt,name=is_all_breed,json=isAllBreed,proto3" json:"is_all_breed,omitempty"`
	// breed_ids
	BreedIds []int64 `protobuf:"varint,3,rep,packed,name=breed_ids,json=breedIds,proto3" json:"breed_ids,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,4,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *PetBreedLimitationDef) Reset() {
	*x = PetBreedLimitationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetBreedLimitationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetBreedLimitationDef) ProtoMessage() {}

func (x *PetBreedLimitationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetBreedLimitationDef.ProtoReflect.Descriptor instead.
func (*PetBreedLimitationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{11}
}

func (x *PetBreedLimitationDef) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *PetBreedLimitationDef) GetIsAllBreed() bool {
	if x != nil {
		return x.IsAllBreed
	}
	return false
}

func (x *PetBreedLimitationDef) GetBreedIds() []int64 {
	if x != nil {
		return x.BreedIds
	}
	return nil
}

func (x *PetBreedLimitationDef) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// slot free staff service
type SlotFreeStaffServiceDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// free services
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *SlotFreeStaffServiceDef) Reset() {
	*x = SlotFreeStaffServiceDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotFreeStaffServiceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotFreeStaffServiceDef) ProtoMessage() {}

func (x *SlotFreeStaffServiceDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotFreeStaffServiceDef.ProtoReflect.Descriptor instead.
func (*SlotFreeStaffServiceDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{12}
}

func (x *SlotFreeStaffServiceDef) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *SlotFreeStaffServiceDef) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// service limitation
type BookingLimitationDef_ServiceLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service_id
	ServiceIds []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// is_all_service
	IsAllService bool `protobuf:"varint,2,opt,name=is_all_service,json=isAllService,proto3" json:"is_all_service,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *BookingLimitationDef_ServiceLimitation) Reset() {
	*x = BookingLimitationDef_ServiceLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingLimitationDef_ServiceLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingLimitationDef_ServiceLimitation) ProtoMessage() {}

func (x *BookingLimitationDef_ServiceLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingLimitationDef_ServiceLimitation.ProtoReflect.Descriptor instead.
func (*BookingLimitationDef_ServiceLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{7, 0}
}

func (x *BookingLimitationDef_ServiceLimitation) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *BookingLimitationDef_ServiceLimitation) GetIsAllService() bool {
	if x != nil {
		return x.IsAllService
	}
	return false
}

func (x *BookingLimitationDef_ServiceLimitation) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// pet size limitation
type BookingLimitationDef_PetSizeLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet_size_id
	PetSizeIds []int64 `protobuf:"varint,1,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// is_all_size
	IsAllSize bool `protobuf:"varint,2,opt,name=is_all_size,json=isAllSize,proto3" json:"is_all_size,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *BookingLimitationDef_PetSizeLimitation) Reset() {
	*x = BookingLimitationDef_PetSizeLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingLimitationDef_PetSizeLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingLimitationDef_PetSizeLimitation) ProtoMessage() {}

func (x *BookingLimitationDef_PetSizeLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingLimitationDef_PetSizeLimitation.ProtoReflect.Descriptor instead.
func (*BookingLimitationDef_PetSizeLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{7, 1}
}

func (x *BookingLimitationDef_PetSizeLimitation) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *BookingLimitationDef_PetSizeLimitation) GetIsAllSize() bool {
	if x != nil {
		return x.IsAllSize
	}
	return false
}

func (x *BookingLimitationDef_PetSizeLimitation) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// pet breed limitation
type BookingLimitationDef_PetBreedLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet_type_id
	PetTypeId int64 `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// is_all_breed
	IsAllBreed bool `protobuf:"varint,2,opt,name=is_all_breed,json=isAllBreed,proto3" json:"is_all_breed,omitempty"`
	// breed_ids
	BreedIds []int64 `protobuf:"varint,3,rep,packed,name=breed_ids,json=breedIds,proto3" json:"breed_ids,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,4,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *BookingLimitationDef_PetBreedLimitation) Reset() {
	*x = BookingLimitationDef_PetBreedLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingLimitationDef_PetBreedLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingLimitationDef_PetBreedLimitation) ProtoMessage() {}

func (x *BookingLimitationDef_PetBreedLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingLimitationDef_PetBreedLimitation.ProtoReflect.Descriptor instead.
func (*BookingLimitationDef_PetBreedLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP(), []int{7, 2}
}

func (x *BookingLimitationDef_PetBreedLimitation) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *BookingLimitationDef_PetBreedLimitation) GetIsAllBreed() bool {
	if x != nil {
		return x.IsAllBreed
	}
	return false
}

func (x *BookingLimitationDef_PetBreedLimitation) GetBreedIds() []int64 {
	if x != nil {
		return x.BreedIds
	}
	return nil
}

func (x *BookingLimitationDef_PetBreedLimitation) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

var File_moego_models_organization_v1_staff_availability_def_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_staff_availability_def_proto_rawDesc = []byte{
	0x0a, 0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x79, 0x6f, 0x66, 0x77, 0x65, 0x65, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb5, 0x04,
	0x0a, 0x14, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x54, 0x0a,
	0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x48, 0x00, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x7b, 0x0a, 0x1a, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x17, 0x73, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x5d, 0x0a, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x01, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x7b, 0x0a, 0x1a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x10, 0x64, 0x52, 0x17, 0x74, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x15,
	0x0a, 0x13, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xa6, 0x04, 0x0a, 0x16, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x44, 0x65, 0x66,
	0x12, 0x47, 0x0a, 0x0b, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x64, 0x61, 0x79,
	0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x54, 0x0a, 0x0d,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x01, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x64, 0x0a, 0x12, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c,
	0x6f, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65,
	0x66, 0x48, 0x02, 0x52, 0x10, 0x73, 0x6c, 0x6f, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x6f, 0x0a, 0x16, 0x73, 0x6c, 0x6f, 0x74,
	0x5f, 0x68, 0x6f, 0x75, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x48, 0x6f, 0x75, 0x72,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x10, 0x64, 0x52, 0x13, 0x73, 0x6c, 0x6f, 0x74, 0x48, 0x6f, 0x75, 0x72, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0d, 0x6f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x03, 0x52, 0x0c, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77,
	0x65, 0x65, 0x6b, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x64,
	0x61, 0x69, 0x6c, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x8a,
	0x04, 0x0a, 0x16, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x44, 0x65, 0x66, 0x12, 0x47, 0x0a, 0x0b, 0x64, 0x61, 0x79,
	0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x79,
	0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x88,
	0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x54, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x01, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5f, 0x0a, 0x12, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x6f, 0x0a, 0x16,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x48, 0x6f, 0x75, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x13, 0x74, 0x69, 0x6d, 0x65, 0x48, 0x6f,
	0x75, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a,
	0x0d, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0c, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x61, 0x79, 0x5f,
	0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xf2, 0x02, 0x0a, 0x13,
	0x53, 0x6c, 0x6f, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x66, 0x12, 0x29, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0,
	0x0b, 0x28, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x00, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x08, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x01, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x88, 0x01, 0x01, 0x12, 0x4d, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x67, 0x0a, 0x11, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x66,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x10, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x22, 0xd7, 0x01, 0x0a, 0x13, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x4d, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x67, 0x0a, 0x11, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x10,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x85, 0x03, 0x0a, 0x12, 0x53,
	0x6c, 0x6f, 0x74, 0x48, 0x6f, 0x75, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65,
	0x66, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28,
	0x00, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x28, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x01, 0x52, 0x08,
	0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x4d, 0x0a, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x02,
	0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x67, 0x0a, 0x11, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x10, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x12, 0x54, 0x69, 0x6d, 0x65, 0x48, 0x6f, 0x75, 0x72, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x22, 0xca, 0x06, 0x0a, 0x14, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x75, 0x0a, 0x0e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x10, 0x64, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x12, 0x76, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x2e, 0x50,
	0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x53,
	0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x79, 0x0a, 0x10, 0x70, 0x65, 0x74,
	0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x10, 0x64, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x73, 0x1a, 0x8c, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x10, 0x80, 0x08, 0x18, 0x01, 0x52, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x23,
	0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63,
	0x69, 0x74, 0x79, 0x1a, 0x87, 0x01, 0x0a, 0x11, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x0c, 0x70, 0x65, 0x74,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x10, 0x80, 0x08, 0x18, 0x01, 0x52, 0x0a, 0x70, 0x65,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x00, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x1a, 0xae, 0x01,
	0x0a, 0x12, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0c, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12,
	0x28, 0x0a, 0x09, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x10, 0x80, 0x08, 0x18, 0x01, 0x52,
	0x08, 0x62, 0x72, 0x65, 0x65, 0x64, 0x49, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x08, 0x63, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x1a, 0x02, 0x28, 0x00, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x22, 0xfa,
	0x02, 0x0a, 0x12, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x44, 0x65, 0x66, 0x12, 0x63, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x0d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x64, 0x0a, 0x0f, 0x70, 0x65,
	0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10,
	0x64, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73,
	0x12, 0x67, 0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x6f, 0x6e, 0x6c,
	0x79, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6f, 0x6e, 0x6c, 0x79, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x8f, 0x01, 0x0a, 0x14,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x66, 0x12, 0x2c, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01,
	0x05, 0x10, 0x80, 0x08, 0x18, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x6c,
	0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x00, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x22, 0x8a, 0x01,
	0x0a, 0x14, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x2d, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42,
	0x08, 0x92, 0x01, 0x05, 0x10, 0x80, 0x08, 0x18, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x41, 0x6c,
	0x6c, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00,
	0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x22, 0xb1, 0x01, 0x0a, 0x15, 0x50,
	0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x66, 0x12, 0x27, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0c, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12,
	0x28, 0x0a, 0x09, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x10, 0x80, 0x08, 0x18, 0x01, 0x52,
	0x08, 0x62, 0x72, 0x65, 0x65, 0x64, 0x49, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x08, 0x63, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x1a, 0x02, 0x28, 0x00, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x22, 0x68,
	0x0a, 0x17, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x29, 0x0a,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x42, 0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_staff_availability_def_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_staff_availability_def_proto_rawDescData = file_moego_models_organization_v1_staff_availability_def_proto_rawDesc
)

func file_moego_models_organization_v1_staff_availability_def_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_staff_availability_def_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_staff_availability_def_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_staff_availability_def_proto_rawDescData)
	})
	return file_moego_models_organization_v1_staff_availability_def_proto_rawDescData
}

var file_moego_models_organization_v1_staff_availability_def_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_moego_models_organization_v1_staff_availability_def_proto_goTypes = []interface{}{
	(*StaffAvailabilityDef)(nil),                    // 0: moego.models.organization.v1.StaffAvailabilityDef
	(*SlotAvailabilityDayDef)(nil),                  // 1: moego.models.organization.v1.SlotAvailabilityDayDef
	(*TimeAvailabilityDayDef)(nil),                  // 2: moego.models.organization.v1.TimeAvailabilityDayDef
	(*SlotDailySettingDef)(nil),                     // 3: moego.models.organization.v1.SlotDailySettingDef
	(*TimeDailySettingDef)(nil),                     // 4: moego.models.organization.v1.TimeDailySettingDef
	(*SlotHourSettingDef)(nil),                      // 5: moego.models.organization.v1.SlotHourSettingDef
	(*TimeHourSettingDef)(nil),                      // 6: moego.models.organization.v1.TimeHourSettingDef
	(*BookingLimitationDef)(nil),                    // 7: moego.models.organization.v1.BookingLimitationDef
	(*LimitationGroupDef)(nil),                      // 8: moego.models.organization.v1.LimitationGroupDef
	(*ServiceLimitationDef)(nil),                    // 9: moego.models.organization.v1.ServiceLimitationDef
	(*PetSizeLimitationDef)(nil),                    // 10: moego.models.organization.v1.PetSizeLimitationDef
	(*PetBreedLimitationDef)(nil),                   // 11: moego.models.organization.v1.PetBreedLimitationDef
	(*SlotFreeStaffServiceDef)(nil),                 // 12: moego.models.organization.v1.SlotFreeStaffServiceDef
	(*BookingLimitationDef_ServiceLimitation)(nil),  // 13: moego.models.organization.v1.BookingLimitationDef.ServiceLimitation
	(*BookingLimitationDef_PetSizeLimitation)(nil),  // 14: moego.models.organization.v1.BookingLimitationDef.PetSizeLimitation
	(*BookingLimitationDef_PetBreedLimitation)(nil), // 15: moego.models.organization.v1.BookingLimitationDef.PetBreedLimitation
	(ScheduleType)(0),                               // 16: moego.models.organization.v1.ScheduleType
	(dayofweek.DayOfWeek)(0),                        // 17: google.type.DayOfWeek
}
var file_moego_models_organization_v1_staff_availability_def_proto_depIdxs = []int32{
	16, // 0: moego.models.organization.v1.StaffAvailabilityDef.schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	1,  // 1: moego.models.organization.v1.StaffAvailabilityDef.slot_availability_day_list:type_name -> moego.models.organization.v1.SlotAvailabilityDayDef
	16, // 2: moego.models.organization.v1.StaffAvailabilityDef.time_schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	2,  // 3: moego.models.organization.v1.StaffAvailabilityDef.time_availability_day_list:type_name -> moego.models.organization.v1.TimeAvailabilityDayDef
	17, // 4: moego.models.organization.v1.SlotAvailabilityDayDef.day_of_week:type_name -> google.type.DayOfWeek
	16, // 5: moego.models.organization.v1.SlotAvailabilityDayDef.schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	3,  // 6: moego.models.organization.v1.SlotAvailabilityDayDef.slot_daily_setting:type_name -> moego.models.organization.v1.SlotDailySettingDef
	5,  // 7: moego.models.organization.v1.SlotAvailabilityDayDef.slot_hour_setting_list:type_name -> moego.models.organization.v1.SlotHourSettingDef
	17, // 8: moego.models.organization.v1.TimeAvailabilityDayDef.day_of_week:type_name -> google.type.DayOfWeek
	16, // 9: moego.models.organization.v1.TimeAvailabilityDayDef.schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	4,  // 10: moego.models.organization.v1.TimeAvailabilityDayDef.time_daily_setting:type_name -> moego.models.organization.v1.TimeDailySettingDef
	6,  // 11: moego.models.organization.v1.TimeAvailabilityDayDef.time_hour_setting_list:type_name -> moego.models.organization.v1.TimeHourSettingDef
	7,  // 12: moego.models.organization.v1.SlotDailySettingDef.limit:type_name -> moego.models.organization.v1.BookingLimitationDef
	8,  // 13: moego.models.organization.v1.SlotDailySettingDef.limitation_groups:type_name -> moego.models.organization.v1.LimitationGroupDef
	7,  // 14: moego.models.organization.v1.TimeDailySettingDef.limit:type_name -> moego.models.organization.v1.BookingLimitationDef
	8,  // 15: moego.models.organization.v1.TimeDailySettingDef.limitation_groups:type_name -> moego.models.organization.v1.LimitationGroupDef
	7,  // 16: moego.models.organization.v1.SlotHourSettingDef.limit:type_name -> moego.models.organization.v1.BookingLimitationDef
	8,  // 17: moego.models.organization.v1.SlotHourSettingDef.limitation_groups:type_name -> moego.models.organization.v1.LimitationGroupDef
	13, // 18: moego.models.organization.v1.BookingLimitationDef.service_limits:type_name -> moego.models.organization.v1.BookingLimitationDef.ServiceLimitation
	14, // 19: moego.models.organization.v1.BookingLimitationDef.pet_size_limits:type_name -> moego.models.organization.v1.BookingLimitationDef.PetSizeLimitation
	15, // 20: moego.models.organization.v1.BookingLimitationDef.pet_breed_limits:type_name -> moego.models.organization.v1.BookingLimitationDef.PetBreedLimitation
	9,  // 21: moego.models.organization.v1.LimitationGroupDef.service_limits:type_name -> moego.models.organization.v1.ServiceLimitationDef
	10, // 22: moego.models.organization.v1.LimitationGroupDef.pet_size_limits:type_name -> moego.models.organization.v1.PetSizeLimitationDef
	11, // 23: moego.models.organization.v1.LimitationGroupDef.pet_breed_limits:type_name -> moego.models.organization.v1.PetBreedLimitationDef
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_staff_availability_def_proto_init() }
func file_moego_models_organization_v1_staff_availability_def_proto_init() {
	if File_moego_models_organization_v1_staff_availability_def_proto != nil {
		return
	}
	file_moego_models_organization_v1_staff_availability_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffAvailabilityDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotAvailabilityDayDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeAvailabilityDayDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotDailySettingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeDailySettingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotHourSettingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeHourSettingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingLimitationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitationGroupDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceLimitationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetSizeLimitationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetBreedLimitationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotFreeStaffServiceDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingLimitationDef_ServiceLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingLimitationDef_PetSizeLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingLimitationDef_PetBreedLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_models_organization_v1_staff_availability_def_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_staff_availability_def_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_staff_availability_def_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_staff_availability_def_proto_depIdxs,
		MessageInfos:      file_moego_models_organization_v1_staff_availability_def_proto_msgTypes,
	}.Build()
	File_moego_models_organization_v1_staff_availability_def_proto = out.File
	file_moego_models_organization_v1_staff_availability_def_proto_rawDesc = nil
	file_moego_models_organization_v1_staff_availability_def_proto_goTypes = nil
	file_moego_models_organization_v1_staff_availability_def_proto_depIdxs = nil
}
