secrets:
  - name: 'moego/testing/datasource'
    prefix: 'secret.datasource.'
server:
  filter:
    - recovery
    - debuglog
  service:
    - name: backend.proto.pet.v1.PetService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 600000
  service:
    - callee: moego-search
      target: dns://moego-search:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: mysql.moe_customer
      target: dsn://${secret.datasource.mysql.username}:${secret.datasource.mysql.password}@tcp(${secret.datasource.mysql.url}:${secret.datasource.mysql.port})/moe_customer?charset=utf8mb4&parseTime=true&timeout=30s
      protocol: gorm
      transport: gorm
    - callee: moego-customer
      target: dns://moego-customer:9090
      protocol: grpc
      network: tcp
      transport: grpc
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: mysql.moe_customer
          max_idle: 10
          max_open: 50
          max_lifetime: 180000