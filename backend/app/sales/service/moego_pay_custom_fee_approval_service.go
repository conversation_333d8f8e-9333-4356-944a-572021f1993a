package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type MoegoPayCustomFeeApprovalService struct {
	salespb.UnimplementedMoegoPayCustomFeeApprovalServiceServer
	al *sales.MoegoPayCustomFeeApprovalLogic
}

func NewMoegoPayCustomFeeApprovalService() *MoegoPayCustomFeeApprovalService {
	return &MoegoPayCustomFeeApprovalService{
		al: sales.NewMoegoPayCustomFeeApprovalLogic(),
	}
}

func (s *MoegoPayCustomFeeApprovalService) GetMoegoPayCustomFeeApproval(ctx context.Context,
	req *salespb.GetMoegoPayCustomFeeApprovalRequest) (*salespb.MoegoPayCustomFeeApproval, error) {
	return s.al.GetMoegoPayCustomFeeApproval(ctx, req)
}

func (s *MoegoPayCustomFeeApprovalService) ListMoegoPayCustomFeeApprovals(ctx context.Context,
	req *salespb.ListMoegoPayCustomFeeApprovalsRequest) (*salespb.ListMoegoPayCustomFeeApprovalsResponse, error) {
	return s.al.ListMoegoPayCustomFeeApprovals(ctx, req)
}

func (s *MoegoPayCustomFeeApprovalService) CountMoegoPayCustomFeeApprovals(ctx context.Context,
	req *salespb.CountMoegoPayCustomFeeApprovalsRequest) (*salespb.CountMoegoPayCustomFeeApprovalsResponse, error) {
	count, err := s.al.CountMoegoPayCustomFeeApprovals(ctx, req)
	if err != nil {
		return nil, err
	}

	return &salespb.CountMoegoPayCustomFeeApprovalsResponse{Count: count}, nil
}

func (s *MoegoPayCustomFeeApprovalService) ApproveMoegoPayCustomFeeApproval(ctx context.Context,
	req *salespb.ApproveMoegoPayCustomFeeApprovalRequest) (*salespb.ApproveMoegoPayCustomFeeApprovalResponse, error) {
	approval, err := s.al.ApproveMoegoPayCustomFeeApproval(ctx, req.GetId(), req.GetHandler())
	if err != nil {
		return nil, err
	}

	return &salespb.ApproveMoegoPayCustomFeeApprovalResponse{
		MoegoPayCustomFeeApproval: approval,
	}, nil
}

func (s *MoegoPayCustomFeeApprovalService) RejectMoegoPayCustomFeeApproval(ctx context.Context,
	req *salespb.RejectMoegoPayCustomFeeApprovalRequest) (*salespb.RejectMoegoPayCustomFeeApprovalResponse, error) {
	approval, err := s.al.RejectMoegoPayCustomFeeApproval(ctx, req.GetId(), req.GetHandler())
	if err != nil {
		return nil, err
	}

	return &salespb.RejectMoegoPayCustomFeeApprovalResponse{
		MoegoPayCustomFeeApproval: approval,
	}, nil
}

func (s *MoegoPayCustomFeeApprovalService) CreateMoegoPayCustomFeeApproval(ctx context.Context,
	req *salespb.CreateMoegoPayCustomFeeApprovalRequest) (*salespb.MoegoPayCustomFeeApproval, error) {
	params := &sales.MoegoPayCustomFeeApprovalCreateParams{
		Creator:               req.Creator,
		CompanyID:             req.CompanyId,
		AccountID:             req.AccountId,
		OwnerEmail:            req.OwnerEmail,
		TerminalPercentage:    req.TerminalPercentage,
		TerminalFixed:         req.TerminalFixed,
		NonTerminalPercentage: req.NonTerminalPercentage,
		NonTerminalFixed:      req.NonTerminalFixed,
		MinVolume:             req.MinVolume,
		Spif:                  req.Spif,
		ContractID:            req.ContractId,
		OpportunityID:         req.OpportunityId,
	}

	ap, err := s.al.CreateMoegoPayCustomFeeApproval(ctx, params)
	if err != nil {
		return nil, err
	}

	return ap, nil
}
