package todo

import (
	"testing"
)

func TestTodoAPI(_ *testing.T) {
	// conn, err := grpc.DialContext(context.Background(), "moego-svc-todo:9090",
	//	grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{InsecureSkipVerify: true})))
	// require.Nil(t, err)
	// defer conn.Close()
	// cli := todopb.NewTodoServiceClient(conn)
	// rsp, err := cli.HelloArk(context.Background(),
	//	&todopb.HelloArkRequest{
	//		Message: "API Test",
	//	})
	// require.Nil(t, err)
	// require.Equal(t, "API Test", rsp.GetReply())
}
