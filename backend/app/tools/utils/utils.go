package toolsutils

import (
	"context"
	"net"
	"os"
	"strings"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"google.golang.org/grpc/metadata"
)

func getGrpcMetadata(ctx context.Context) metadata.MD {
	// 从上下文中获取 grpc metadata
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		return md
	}

	return metadata.New(nil)
}

func newDeviceID() string {
	return uuid.New().String()
}

func GetDeviceID(ctx context.Context) string {
	md := getGrpcMetadata(ctx)

	id := md.Get("x-moe-device-id")
	if len(id) > 0 && len(id[0]) > 0 {
		return id[0]
	}

	return newDeviceID()
}

// GetIP get current IP address
func GetIP(ctx context.Context) string {
	// 先尝试从 grpc metadata 中获取
	md := getGrpcMetadata(ctx)
	ip := md.Get("x-forwarded-for")
	if len(ip) > 0 && len(ip[0]) > 0 {
		return ip[0]
	}

	// 获取本机IP地址
	addresses, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}
	for _, address := range addresses {
		ipNet, ok := address.(*net.IPNet)
		// 跳过回环地址
		if !ok || ipNet.IP.IsLoopback() {
			continue
		}
		// 只返回IPv4地址
		if ipNet.IP.To4() != nil {
			return ipNet.IP.String()
		}
	}

	return ""
}

func GetUserAgent(ctx context.Context) string {
	md := getGrpcMetadata(ctx)
	ua := md.Get("user-agent")
	if len(ua) > 0 && len(ua[0]) > 0 {
		return ua[0]
	}

	return "moego-tools"
}

func GetProjectToolsDir() string {
	dir, _ := os.Getwd()
	dirSplit := strings.Split(dir, "/")
	index := lo.IndexOf(dirSplit, "tools")
	dirSplit = dirSplit[:index+1]

	return strings.Join(dirSplit, "/")
}
