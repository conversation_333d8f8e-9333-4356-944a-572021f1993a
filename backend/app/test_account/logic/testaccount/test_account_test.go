package testaccount_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/test_account/logic/testaccount"
	logicentity "github.com/MoeGolibrary/moego/backend/app/test_account/logic/testaccount/entity"
	accountentity "github.com/MoeGolibrary/moego/backend/app/test_account/repo/account/entity"
	accountmock "github.com/MoeGolibrary/moego/backend/app/test_account/repo/mock/account"
	organizationmock "github.com/MoeGolibrary/moego/backend/app/test_account/repo/mock/organization"
	testaccountmock "github.com/MoeGolibrary/moego/backend/app/test_account/repo/mock/testaccount"
	repo "github.com/MoeGolibrary/moego/backend/app/test_account/repo/testaccount"
	repoentity "github.com/MoeGolibrary/moego/backend/app/test_account/repo/testaccount/entity"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
)

func TestBorrowTestAccount(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTestAccount := testaccountmock.NewMockReadWriter(ctrl)
	mockAccount := accountmock.NewMockReadWriter(ctrl)
	mockOrganization := organizationmock.NewMockReadWriter(ctrl)

	logic := testaccount.NewLogicWithParams(mockTestAccount, mockAccount, mockOrganization, nil)

	t.Run("Borrow a test account by email", func(t *testing.T) {
		params := &logicentity.BorrowParams{
			Borrower: "test-borrower",
			Email:    "<EMAIL>",
		}

		testAccount := &repoentity.TestAccount{
			ID:       1,
			Email:    "<EMAIL>",
			Password: "password",
		}

		contract := &repoentity.LeaseContract{
			ID: 123,
		}

		mockTestAccount.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, fn func(repo.ReadWriter) error) error {
			return fn(mockTestAccount)
		})
		mockTestAccount.EXPECT().GetForUpdateByEmail(gomock.Any(), params.Email).Return(testAccount, nil)
		mockTestAccount.EXPECT().Occupy(gomock.Any(), testAccount.ID, params.Borrower, false).Return(contract, nil)

		result, err := logic.BorrowTestAccount(context.Background(), params)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, testAccount.ID, result.ID)
		assert.Equal(t, testAccount.Email, result.Email)
		assert.Equal(t, testAccount.Password, result.Password)
		assert.Equal(t, contract.ID, result.ContractID)
	})

	t.Run("Borrow test account with GetForUpdateByEmail error", func(t *testing.T) {
		params := &logicentity.BorrowParams{
			Borrower: "test-borrower",
			Email:    "<EMAIL>",
		}

		mockTestAccount.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, fn func(repo.ReadWriter) error) error {
			return fn(mockTestAccount)
		})
		mockTestAccount.EXPECT().GetForUpdateByEmail(gomock.Any(), params.Email).Return(nil, assert.AnError)

		result, err := logic.BorrowTestAccount(context.Background(), params)
		assert.Error(t, err)
		assert.Equal(t, assert.AnError, err)
		assert.Nil(t, result)
	})

	t.Run("Borrow a test account by ID", func(t *testing.T) {
		params := &logicentity.BorrowParams{
			Borrower: "test-borrower",
			ID:       1,
		}

		testAccount := &repoentity.TestAccount{
			ID:       1,
			Email:    "<EMAIL>",
			Password: "password",
		}

		contract := &repoentity.LeaseContract{
			ID: 123,
		}

		mockTestAccount.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, fn func(repo.ReadWriter) error) error {
			return fn(mockTestAccount)
		})
		mockTestAccount.EXPECT().GetForUpdateByID(gomock.Any(), params.ID).Return(testAccount, nil)
		mockTestAccount.EXPECT().Occupy(gomock.Any(), testAccount.ID, params.Borrower, false).Return(contract, nil)

		result, err := logic.BorrowTestAccount(context.Background(), params)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, testAccount.ID, result.ID)
		assert.Equal(t, testAccount.Email, result.Email)
		assert.Equal(t, testAccount.Password, result.Password)
		assert.Equal(t, contract.ID, result.ContractID)
	})

	t.Run("Borrow a test account with wrong owner", func(t *testing.T) {
		params := &logicentity.BorrowParams{
			Borrower: "test-borrower",
			Email:    "<EMAIL>",
		}

		testAccount := &repoentity.TestAccount{
			ID:       1,
			Email:    "<EMAIL>",
			Password: "password",
			Owner:    "other-owner",
		}

		mockTestAccount.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, fn func(repo.ReadWriter) error) error {
			return fn(mockTestAccount)
		})
		mockTestAccount.EXPECT().GetForUpdateByEmail(gomock.Any(), params.Email).Return(testAccount, nil)

		result, err := logic.BorrowTestAccount(context.Background(), params)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, "the account does not belong to the borrower", err.Error())
	})

	t.Run("Borrow a test account by attributes with error", func(t *testing.T) {
		params := &logicentity.BorrowParams{
			Borrower: "test-borrower",
			Attributes: &repoentity.Attributes{
				RegionCode: pointer.Get("US"),
			},
		}

		mockTestAccount.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, fn func(repo.ReadWriter) error) error {
			return fn(mockTestAccount)
		})
		mockTestAccount.EXPECT().GetForUpdate(gomock.Any(), params.Attributes).Return(nil, assert.AnError)

		result, err := logic.BorrowTestAccount(context.Background(), params)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, assert.AnError, err)
	})
}

func TestCreateTestAccount(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTestAccount := testaccountmock.NewMockReadWriter(ctrl)
	mockAccount := accountmock.NewMockReadWriter(ctrl)
	mockOrganization := organizationmock.NewMockReadWriter(ctrl)

	logic := testaccount.NewLogicWithParams(mockTestAccount, mockAccount, mockOrganization, nil)

	t.Run("Create a standard test account", func(t *testing.T) {
		params := &logicentity.CreateParams{
			Email:    "<EMAIL>",
			Password: "password",
			Attributes: &repoentity.Attributes{
				RegionCode: pointer.Get("US"),
			},
		}

		mockAccount.EXPECT().CreateAccount(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, account *accountentity.Account) error {
				account.ID = 1
				return nil
			})
		mockOrganization.EXPECT().InitCompany(gomock.Any(), gomock.Any(), "US").Return(int64(1), int64(1), nil)
		mockAccount.EXPECT().Login(gomock.Any(), gomock.Any()).Return(nil, nil)
		mockTestAccount.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)

		result, err := logic.CreateTestAccount(context.Background(), params)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.ID)
		assert.Equal(t, params.Email, result.Email)
	})

	t.Run("Create a disposable test account", func(t *testing.T) {
		params := &logicentity.CreateParams{
			Disposable: true,
			Attributes: &repoentity.Attributes{},
		}

		mockAccount.EXPECT().CreateAccount(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, account *accountentity.Account) error {
				assert.Contains(t, account.Email, "auto-test-")
				assert.Equal(t, "AutoTest!123", account.Password)
				account.ID = 2
				return nil
			})
		mockOrganization.EXPECT().InitCompany(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), int64(1), nil)
		mockAccount.EXPECT().Login(gomock.Any(), gomock.Any()).Return(nil, nil)

		result, err := logic.CreateTestAccount(context.Background(), params)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Contains(t, result.Email, "auto-test-")
		assert.Equal(t, "AutoTest!123", result.Password)
		assert.Equal(t, int64(2), result.ID)
	})

	t.Run("Create a test account with invalid attributes", func(t *testing.T) {
		params := &logicentity.CreateParams{
			Attributes: &repoentity.Attributes{
				RegionCode:   pointer.Get("INVALID"),
				HasSmsCredit: pointer.Get(true),
			},
		}

		result, err := logic.CreateTestAccount(context.Background(), params)
		assert.Error(t, err)
		assert.Nil(t, result)
	})

	t.Run("Create a test account with account creation failure", func(t *testing.T) {
		params := &logicentity.CreateParams{
			Email:    "<EMAIL>",
			Password: "password",
			Attributes: &repoentity.Attributes{
				RegionCode: pointer.Get("US"),
			},
		}

		mockAccount.EXPECT().CreateAccount(gomock.Any(), gomock.Any()).Return(assert.AnError)

		result, err := logic.CreateTestAccount(context.Background(), params)
		assert.Error(t, err)
		assert.Equal(t, assert.AnError, err)
		assert.Nil(t, result)
	})

	t.Run("Create a non-disposable test account", func(t *testing.T) {
		params := &logicentity.CreateParams{
			Email:      "<EMAIL>",
			Password:   "password",
			Owner:      "test-owner",
			Disposable: false,
			Attributes: &repoentity.Attributes{
				RegionCode: pointer.Get("US"),
			},
		}

		mockAccount.EXPECT().CreateAccount(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, account *accountentity.Account) error {
				account.ID = 3
				return nil
			})
		mockOrganization.EXPECT().InitCompany(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), int64(1), nil)
		mockAccount.EXPECT().Login(gomock.Any(), gomock.Any()).Return(nil, nil)
		mockTestAccount.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)

		result, err := logic.CreateTestAccount(context.Background(), params)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "<EMAIL>", result.Email)
		assert.Equal(t, "password", result.Password)
		assert.Equal(t, "test-owner", result.Owner)
		assert.Equal(t, int64(3), result.ID)
		assert.Equal(t, false, result.Occupied)
	})

	t.Run("Create test account with init company failure", func(t *testing.T) {
		params := &logicentity.CreateParams{
			Disposable: true,
			Attributes: &repoentity.Attributes{
				RegionCode: pointer.Get("US"),
			},
		}

		mockAccount.EXPECT().CreateAccount(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, account *accountentity.Account) error {
				account.ID = 4
				return nil
			})
		mockOrganization.EXPECT().InitCompany(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), int64(0), assert.AnError)

		result, err := logic.CreateTestAccount(context.Background(), params)
		assert.Error(t, err)
		assert.Equal(t, assert.AnError, err)
		assert.Nil(t, result)
	})

	t.Run("Create test account with login failure", func(t *testing.T) {
		params := &logicentity.CreateParams{
			Disposable: true,
			Attributes: &repoentity.Attributes{
				RegionCode: pointer.Get("US"),
			},
		}

		mockAccount.EXPECT().CreateAccount(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, account *accountentity.Account) error {
				account.ID = 5
				return nil
			})
		mockOrganization.EXPECT().InitCompany(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), int64(1), nil)
		mockAccount.EXPECT().Login(gomock.Any(), gomock.Any()).Return(nil, assert.AnError)

		result, err := logic.CreateTestAccount(context.Background(), params)
		assert.Error(t, err)
		assert.Equal(t, assert.AnError, err)
		assert.Nil(t, result)
	})

	t.Run("Create test account with save failure", func(t *testing.T) {
		params := &logicentity.CreateParams{
			Email:      "<EMAIL>",
			Password:   "password",
			Owner:      "test-owner",
			Disposable: false,
			Attributes: &repoentity.Attributes{
				RegionCode: pointer.Get("US"),
			},
		}

		mockAccount.EXPECT().CreateAccount(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, account *accountentity.Account) error {
				account.ID = 6
				return nil
			})
		mockOrganization.EXPECT().InitCompany(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), int64(1), nil)
		mockAccount.EXPECT().Login(gomock.Any(), gomock.Any()).Return(nil, nil)
		mockTestAccount.EXPECT().Save(gomock.Any(), gomock.Any()).Return(assert.AnError)

		result, err := logic.CreateTestAccount(context.Background(), params)
		assert.Error(t, err)
		assert.Equal(t, assert.AnError, err)
		assert.Nil(t, result)
	})

	t.Run("Create test account with different region", func(t *testing.T) {
		params := &logicentity.CreateParams{
			Disposable: true,
			Attributes: &repoentity.Attributes{
				RegionCode: pointer.Get("CA"),
			},
		}

		mockAccount.EXPECT().CreateAccount(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, account *accountentity.Account) error {
				account.ID = 7
				return nil
			})
		mockOrganization.EXPECT().InitCompany(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), int64(1), nil)
		mockAccount.EXPECT().Login(gomock.Any(), gomock.Any()).Return(nil, nil)

		result, err := logic.CreateTestAccount(context.Background(), params)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(7), result.ID)
		assert.Equal(t, "CA", *result.Attributes.RegionCode)
	})
}

func TestReturnTestAccount(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTestAccount := testaccountmock.NewMockReadWriter(ctrl)
	mockAccount := accountmock.NewMockReadWriter(ctrl)
	mockOrganization := organizationmock.NewMockReadWriter(ctrl)

	logic := testaccount.NewLogicWithParams(mockTestAccount, mockAccount, mockOrganization, nil)

	t.Run("Return a test account", func(t *testing.T) {
		contractID := int64(123)

		mockTestAccount.EXPECT().Return(gomock.Any(), contractID).Return(nil)

		err := logic.ReturnTestAccount(context.Background(), contractID)
		assert.NoError(t, err)
	})

	t.Run("Return test account with error", func(t *testing.T) {
		contractID := int64(456)

		mockTestAccount.EXPECT().Return(gomock.Any(), contractID).Return(assert.AnError)

		err := logic.ReturnTestAccount(context.Background(), contractID)
		assert.Error(t, err)
		assert.Equal(t, assert.AnError, err)
	})
}

func TestReleaseOverdueTestAccounts(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTestAccount := testaccountmock.NewMockReadWriter(ctrl)
	mockAccount := accountmock.NewMockReadWriter(ctrl)
	mockOrganization := organizationmock.NewMockReadWriter(ctrl)

	logic := testaccount.NewLogicWithParams(mockTestAccount, mockAccount, mockOrganization, nil)

	t.Run("Release overdue test accounts", func(t *testing.T) {
		mockTestAccount.EXPECT().ReleaseOverdueTestAccounts(gomock.Any()).Return(nil)

		err := logic.ReleaseOverdueTestAccounts(context.Background())
		assert.NoError(t, err)
	})

	t.Run("Release overdue test accounts with error", func(t *testing.T) {
		mockTestAccount.EXPECT().ReleaseOverdueTestAccounts(gomock.Any()).Return(assert.AnError)

		err := logic.ReleaseOverdueTestAccounts(context.Background())
		assert.Error(t, err)
		assert.Equal(t, assert.AnError, err)
	})
}

func TestListTestAccounts(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTestAccount := testaccountmock.NewMockReadWriter(ctrl)
	mockAccount := accountmock.NewMockReadWriter(ctrl)
	mockOrganization := organizationmock.NewMockReadWriter(ctrl)

	logic := testaccount.NewLogicWithParams(mockTestAccount, mockAccount, mockOrganization, nil)

	t.Run("List test accounts", func(t *testing.T) {
		accounts := []*repoentity.TestAccount{
			{ID: 1, Email: "<EMAIL>"},
			{ID: 2, Email: "<EMAIL>"},
		}

		nextPageToken := "2"
		total := int64(20)

		filter := &repo.ListFilter{
			PageSize:  10,
			PageToken: "1",
		}

		mockTestAccount.EXPECT().ListTestAccounts(gomock.Any(), filter).Return(accounts, &nextPageToken, total, nil)

		result, nextToken, totalResult, err := logic.ListTestAccounts(context.Background(), filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)
		assert.Equal(t, &nextPageToken, nextToken)
		assert.Equal(t, total, totalResult)
	})
}

func TestListTestAccountsWithFilter(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockTestAccount := testaccountmock.NewMockReadWriter(ctrl)
	mockAccount := accountmock.NewMockReadWriter(ctrl)
	mockOrganization := organizationmock.NewMockReadWriter(ctrl)

	logic := testaccount.NewLogicWithParams(mockTestAccount, mockAccount, mockOrganization, nil)

	t.Run("List test accounts with owner filter", func(t *testing.T) {
		filter := &repo.ListFilter{
			PageSize:  10,
			PageToken: "1",
			Owner:     pointer.Get("test-owner"),
		}

		accounts := []*repoentity.TestAccount{
			{ID: 1, Email: "<EMAIL>", Owner: "test-owner"},
		}

		mockTestAccount.EXPECT().ListTestAccounts(gomock.Any(), filter).Return(accounts, nil, int64(1), nil)

		result, nextToken, totalResult, err := logic.ListTestAccounts(context.Background(), filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 1)
		assert.Equal(t, "test-owner", result[0].Owner)
		assert.Nil(t, nextToken)
		assert.Equal(t, int64(1), totalResult)
	})

	t.Run("List test accounts with occupied filter", func(t *testing.T) {
		filter := &repo.ListFilter{
			PageSize:  10,
			PageToken: "1",
			Occupied:  pointer.Get(true),
		}

		accounts := []*repoentity.TestAccount{
			{ID: 1, Email: "<EMAIL>", Occupied: true},
		}

		mockTestAccount.EXPECT().ListTestAccounts(gomock.Any(), filter).Return(accounts, nil, int64(1), nil)

		result, nextToken, totalResult, err := logic.ListTestAccounts(context.Background(), filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 1)
		assert.Equal(t, true, result[0].Occupied)
		assert.Nil(t, nextToken)
		assert.Equal(t, int64(1), totalResult)
	})

	t.Run("List test accounts with attributes filter", func(t *testing.T) {
		filter := &repo.ListFilter{
			PageSize:  10,
			PageToken: "1",
			Attributes: &repoentity.Attributes{
				RegionCode: pointer.Get("US"),
			},
		}

		accounts := []*repoentity.TestAccount{
			{ID: 1, Email: "<EMAIL>"},
		}

		mockTestAccount.EXPECT().ListTestAccounts(gomock.Any(), filter).Return(accounts, nil, int64(1), nil)

		result, nextToken, totalResult, err := logic.ListTestAccounts(context.Background(), filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 1)
		assert.Nil(t, nextToken)
		assert.Equal(t, int64(1), totalResult)
	})

	t.Run("List test accounts with multiple filters", func(t *testing.T) {
		filter := &repo.ListFilter{
			PageSize:  5,
			PageToken: "2",
			Owner:     pointer.Get("test-owner"),
			Occupied:  pointer.Get(false),
			Attributes: &repoentity.Attributes{
				RegionCode: pointer.Get("CA"),
			},
		}

		accounts := []*repoentity.TestAccount{
			{ID: 3, Email: "<EMAIL>", Owner: "test-owner", Occupied: false},
		}

		mockTestAccount.EXPECT().ListTestAccounts(gomock.Any(), filter).Return(accounts, nil, int64(1), nil)

		result, nextToken, totalResult, err := logic.ListTestAccounts(context.Background(), filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 1)
		assert.Equal(t, "test-owner", result[0].Owner)
		assert.Equal(t, false, result[0].Occupied)
		assert.Nil(t, nextToken)
		assert.Equal(t, int64(1), totalResult)
	})

	t.Run("List test accounts with invalid page token", func(t *testing.T) {
		filter := &repo.ListFilter{
			PageSize:  10,
			PageToken: "invalid",
		}

		mockTestAccount.EXPECT().ListTestAccounts(gomock.Any(), filter).Return(nil, nil, int64(0), assert.AnError)

		result, nextToken, totalResult, err := logic.ListTestAccounts(context.Background(), filter)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Nil(t, nextToken)
		assert.Equal(t, int64(0), totalResult)
	})

	t.Run("List test accounts with default page size", func(t *testing.T) {
		accounts := []*repoentity.TestAccount{
			{ID: 1, Email: "<EMAIL>"},
		}

		filter := &repo.ListFilter{
			PageSize:  0, // Should default to 20
			PageToken: "",
		}

		mockTestAccount.EXPECT().ListTestAccounts(gomock.Any(), filter).Return(accounts, nil, int64(1), nil)

		result, nextToken, totalResult, err := logic.ListTestAccounts(context.Background(), filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 1)
		assert.Nil(t, nextToken)
		assert.Equal(t, int64(1), totalResult)
	})

	t.Run("List test accounts with error", func(t *testing.T) {
		filter := &repo.ListFilter{
			PageSize:  10,
			PageToken: "1",
		}

		mockTestAccount.EXPECT().ListTestAccounts(gomock.Any(), filter).Return(nil, nil, int64(0), assert.AnError)

		result, nextToken, totalResult, err := logic.ListTestAccounts(context.Background(), filter)
		assert.Error(t, err)
		assert.Equal(t, assert.AnError, err)
		assert.Nil(t, result)
		assert.Nil(t, nextToken)
		assert.Equal(t, int64(0), totalResult)
	})
}
