// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/test_account/v1/test_account_service.proto

package testaccountpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TestAccount resource message
type TestAccount struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// email
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// password
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	// onwer
	Owner string `protobuf:"bytes,4,opt,name=owner,proto3" json:"owner,omitempty"`
	// disposable
	Disposable bool `protobuf:"varint,5,opt,name=disposable,proto3" json:"disposable,omitempty"`
	// attributes
	Attributes *Attributes `protobuf:"bytes,7,opt,name=attributes,proto3" json:"attributes,omitempty"`
	// occupied
	Occupied      bool `protobuf:"varint,8,opt,name=occupied,proto3" json:"occupied,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestAccount) Reset() {
	*x = TestAccount{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAccount) ProtoMessage() {}

func (x *TestAccount) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAccount.ProtoReflect.Descriptor instead.
func (*TestAccount) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{0}
}

func (x *TestAccount) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TestAccount) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *TestAccount) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *TestAccount) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *TestAccount) GetDisposable() bool {
	if x != nil {
		return x.Disposable
	}
	return false
}

func (x *TestAccount) GetAttributes() *Attributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *TestAccount) GetOccupied() bool {
	if x != nil {
		return x.Occupied
	}
	return false
}

// attributes of test account
type Attributes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地区代码, 暂时只支持这些地区: US, CA, GB, AU, CN, 后续再添加其他地区
	RegionCode *string `protobuf:"bytes,1,opt,name=region_code,json=regionCode,proto3,oneof" json:"region_code,omitempty"`
	// 是否启用 boarding daycare
	EnableBoardingDaycare *bool `protobuf:"varint,2,opt,name=enable_boarding_daycare,json=enableBoardingDaycare,proto3,oneof" json:"enable_boarding_daycare,omitempty"`
	// 是否启用 online booking
	EnableOnlineBooking *bool `protobuf:"varint,3,opt,name=enable_online_booking,json=enableOnlineBooking,proto3,oneof" json:"enable_online_booking,omitempty"`
	// 是否启用 stripe
	EnableStripe *bool `protobuf:"varint,6,opt,name=enable_stripe,json=enableStripe,proto3,oneof" json:"enable_stripe,omitempty"`
	// 是否有 SMS credit
	HasSmsCredit *bool `protobuf:"varint,7,opt,name=has_sms_credit,json=hasSmsCredit,proto3,oneof" json:"has_sms_credit,omitempty"`
	// 是否有 email credit
	HasEmailCredit *bool `protobuf:"varint,8,opt,name=has_email_credit,json=hasEmailCredit,proto3,oneof" json:"has_email_credit,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Attributes) Reset() {
	*x = Attributes{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Attributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attributes) ProtoMessage() {}

func (x *Attributes) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attributes.ProtoReflect.Descriptor instead.
func (*Attributes) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{1}
}

func (x *Attributes) GetRegionCode() string {
	if x != nil && x.RegionCode != nil {
		return *x.RegionCode
	}
	return ""
}

func (x *Attributes) GetEnableBoardingDaycare() bool {
	if x != nil && x.EnableBoardingDaycare != nil {
		return *x.EnableBoardingDaycare
	}
	return false
}

func (x *Attributes) GetEnableOnlineBooking() bool {
	if x != nil && x.EnableOnlineBooking != nil {
		return *x.EnableOnlineBooking
	}
	return false
}

func (x *Attributes) GetEnableStripe() bool {
	if x != nil && x.EnableStripe != nil {
		return *x.EnableStripe
	}
	return false
}

func (x *Attributes) GetHasSmsCredit() bool {
	if x != nil && x.HasSmsCredit != nil {
		return *x.HasSmsCredit
	}
	return false
}

func (x *Attributes) GetHasEmailCredit() bool {
	if x != nil && x.HasEmailCredit != nil {
		return *x.HasEmailCredit
	}
	return false
}

// BorrowTestAccountRequest
type BorrowTestAccountRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// borrower (who are you)
	// suggest to use test case/suite name
	Borrower string `protobuf:"bytes,1,opt,name=borrower,proto3" json:"borrower,omitempty"`
	// 指定 identifier 获取账号
	// 可以指定一个 id 或者 email
	// 如果都不指定，则随机获取一个账号
	//
	// Types that are valid to be assigned to Identifier:
	//
	//	*BorrowTestAccountRequest_Id
	//	*BorrowTestAccountRequest_Email
	Identifier isBorrowTestAccountRequest_Identifier `protobuf_oneof:"identifier"`
	// attributes
	Attributes *Attributes `protobuf:"bytes,4,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	// 在借用期间是否允许共享测试账号，即不占用测试账号，建议只读场景的 test case 开启
	Shared        *bool `protobuf:"varint,5,opt,name=shared,proto3,oneof" json:"shared,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BorrowTestAccountRequest) Reset() {
	*x = BorrowTestAccountRequest{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BorrowTestAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BorrowTestAccountRequest) ProtoMessage() {}

func (x *BorrowTestAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BorrowTestAccountRequest.ProtoReflect.Descriptor instead.
func (*BorrowTestAccountRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{2}
}

func (x *BorrowTestAccountRequest) GetBorrower() string {
	if x != nil {
		return x.Borrower
	}
	return ""
}

func (x *BorrowTestAccountRequest) GetIdentifier() isBorrowTestAccountRequest_Identifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *BorrowTestAccountRequest) GetId() int64 {
	if x != nil {
		if x, ok := x.Identifier.(*BorrowTestAccountRequest_Id); ok {
			return x.Id
		}
	}
	return 0
}

func (x *BorrowTestAccountRequest) GetEmail() string {
	if x != nil {
		if x, ok := x.Identifier.(*BorrowTestAccountRequest_Email); ok {
			return x.Email
		}
	}
	return ""
}

func (x *BorrowTestAccountRequest) GetAttributes() *Attributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *BorrowTestAccountRequest) GetShared() bool {
	if x != nil && x.Shared != nil {
		return *x.Shared
	}
	return false
}

type isBorrowTestAccountRequest_Identifier interface {
	isBorrowTestAccountRequest_Identifier()
}

type BorrowTestAccountRequest_Id struct {
	// id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3,oneof"`
}

type BorrowTestAccountRequest_Email struct {
	// email
	Email string `protobuf:"bytes,3,opt,name=email,proto3,oneof"`
}

func (*BorrowTestAccountRequest_Id) isBorrowTestAccountRequest_Identifier() {}

func (*BorrowTestAccountRequest_Email) isBorrowTestAccountRequest_Identifier() {}

// BorrowTestAccountResponse
type BorrowTestAccountResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// email
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// password
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	// contract id
	ContractId    int64 `protobuf:"varint,4,opt,name=contract_id,json=contractId,proto3" json:"contract_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BorrowTestAccountResponse) Reset() {
	*x = BorrowTestAccountResponse{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BorrowTestAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BorrowTestAccountResponse) ProtoMessage() {}

func (x *BorrowTestAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BorrowTestAccountResponse.ProtoReflect.Descriptor instead.
func (*BorrowTestAccountResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{3}
}

func (x *BorrowTestAccountResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BorrowTestAccountResponse) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BorrowTestAccountResponse) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *BorrowTestAccountResponse) GetContractId() int64 {
	if x != nil {
		return x.ContractId
	}
	return 0
}

// ReturnTestAccountRequest
type ReturnTestAccountRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// contract id
	ContractId    int64 `protobuf:"varint,3,opt,name=contract_id,json=contractId,proto3" json:"contract_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReturnTestAccountRequest) Reset() {
	*x = ReturnTestAccountRequest{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReturnTestAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReturnTestAccountRequest) ProtoMessage() {}

func (x *ReturnTestAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReturnTestAccountRequest.ProtoReflect.Descriptor instead.
func (*ReturnTestAccountRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{4}
}

func (x *ReturnTestAccountRequest) GetContractId() int64 {
	if x != nil {
		return x.ContractId
	}
	return 0
}

// ReturnTestAccountResponse
type ReturnTestAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReturnTestAccountResponse) Reset() {
	*x = ReturnTestAccountResponse{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReturnTestAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReturnTestAccountResponse) ProtoMessage() {}

func (x *ReturnTestAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReturnTestAccountResponse.ProtoReflect.Descriptor instead.
func (*ReturnTestAccountResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{5}
}

// CreateTestAccountRequest
type CreateTestAccountRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. The test account to create
	TestAccount *TestAccount `protobuf:"bytes,1,opt,name=test_account,json=testAccount,proto3" json:"test_account,omitempty"`
	// Optional. The test account ID to use for this request
	TestAccountId *string `protobuf:"bytes,2,opt,name=test_account_id,json=testAccountId,proto3,oneof" json:"test_account_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTestAccountRequest) Reset() {
	*x = CreateTestAccountRequest{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTestAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTestAccountRequest) ProtoMessage() {}

func (x *CreateTestAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTestAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateTestAccountRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{6}
}

func (x *CreateTestAccountRequest) GetTestAccount() *TestAccount {
	if x != nil {
		return x.TestAccount
	}
	return nil
}

func (x *CreateTestAccountRequest) GetTestAccountId() string {
	if x != nil && x.TestAccountId != nil {
		return *x.TestAccountId
	}
	return ""
}

// ReleaseTestAccountsRequest
type ReleaseTestAccountsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// overdue
	Overdue       bool `protobuf:"varint,1,opt,name=overdue,proto3" json:"overdue,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseTestAccountsRequest) Reset() {
	*x = ReleaseTestAccountsRequest{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseTestAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseTestAccountsRequest) ProtoMessage() {}

func (x *ReleaseTestAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseTestAccountsRequest.ProtoReflect.Descriptor instead.
func (*ReleaseTestAccountsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{7}
}

func (x *ReleaseTestAccountsRequest) GetOverdue() bool {
	if x != nil {
		return x.Overdue
	}
	return false
}

// ReleaseTestAccountsResponse
type ReleaseTestAccountsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseTestAccountsResponse) Reset() {
	*x = ReleaseTestAccountsResponse{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseTestAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseTestAccountsResponse) ProtoMessage() {}

func (x *ReleaseTestAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseTestAccountsResponse.ProtoReflect.Descriptor instead.
func (*ReleaseTestAccountsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{8}
}

// ListTestAccountsRequest
type ListTestAccountsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Optional. The maximum number of accounts to return.
	// If empty, fetch 20 accounts by default.
	PageSize *int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// Optional. A token to retrieve the next page of results.
	// Currently use as page number.
	// If empty, fetch the first page by default.
	PageToken *string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3,oneof" json:"page_token,omitempty"`
	// Optional. Filters to apply to the list of test accounts.
	Filter        *ListTestAccountsFilter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestAccountsRequest) Reset() {
	*x = ListTestAccountsRequest{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestAccountsRequest) ProtoMessage() {}

func (x *ListTestAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestAccountsRequest.ProtoReflect.Descriptor instead.
func (*ListTestAccountsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListTestAccountsRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *ListTestAccountsRequest) GetPageToken() string {
	if x != nil && x.PageToken != nil {
		return *x.PageToken
	}
	return ""
}

func (x *ListTestAccountsRequest) GetFilter() *ListTestAccountsFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListTestAccountsResponse
type ListTestAccountsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of test accounts.
	TestAccounts []*TestAccount `protobuf:"bytes,1,rep,name=test_accounts,json=testAccounts,proto3" json:"test_accounts,omitempty"`
	// A token to retrieve the next page of results.
	// Currently use as page number.
	// If empty, there are no more pages.
	NextPageToken *string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3,oneof" json:"next_page_token,omitempty"`
	// total size
	TotalSize     int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestAccountsResponse) Reset() {
	*x = ListTestAccountsResponse{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestAccountsResponse) ProtoMessage() {}

func (x *ListTestAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestAccountsResponse.ProtoReflect.Descriptor instead.
func (*ListTestAccountsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListTestAccountsResponse) GetTestAccounts() []*TestAccount {
	if x != nil {
		return x.TestAccounts
	}
	return nil
}

func (x *ListTestAccountsResponse) GetNextPageToken() string {
	if x != nil && x.NextPageToken != nil {
		return *x.NextPageToken
	}
	return ""
}

func (x *ListTestAccountsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// ListTestAccountsFilter
type ListTestAccountsFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Optional. Filter by owner
	Owner *string `protobuf:"bytes,1,opt,name=owner,proto3,oneof" json:"owner,omitempty"`
	// Optional. Filter by occupied status
	Occupied *bool `protobuf:"varint,2,opt,name=occupied,proto3,oneof" json:"occupied,omitempty"`
	// Optional. Filter by attributes
	Attributes    *Attributes `protobuf:"bytes,3,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestAccountsFilter) Reset() {
	*x = ListTestAccountsFilter{}
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestAccountsFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestAccountsFilter) ProtoMessage() {}

func (x *ListTestAccountsFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestAccountsFilter.ProtoReflect.Descriptor instead.
func (*ListTestAccountsFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListTestAccountsFilter) GetOwner() string {
	if x != nil && x.Owner != nil {
		return *x.Owner
	}
	return ""
}

func (x *ListTestAccountsFilter) GetOccupied() bool {
	if x != nil && x.Occupied != nil {
		return *x.Occupied
	}
	return false
}

func (x *ListTestAccountsFilter) GetAttributes() *Attributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

var File_backend_proto_test_account_v1_test_account_service_proto protoreflect.FileDescriptor

const file_backend_proto_test_account_v1_test_account_service_proto_rawDesc = "" +
	"\n" +
	"8backend/proto/test_account/v1/test_account_service.proto\x12\x1dbackend.proto.test_account.v1\x1a\x1bbuf/validate/validate.proto\"\xec\x01\n" +
	"\vTestAccount\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12\x14\n" +
	"\x05owner\x18\x04 \x01(\tR\x05owner\x12\x1e\n" +
	"\n" +
	"disposable\x18\x05 \x01(\bR\n" +
	"disposable\x12I\n" +
	"\n" +
	"attributes\x18\a \x01(\v2).backend.proto.test_account.v1.AttributesR\n" +
	"attributes\x12\x1a\n" +
	"\boccupied\x18\b \x01(\bR\boccupied\"\xc7\x03\n" +
	"\n" +
	"Attributes\x12?\n" +
	"\vregion_code\x18\x01 \x01(\tB\x19\xbaH\x16r\x14R\x02USR\x02CAR\x02GBR\x02AUR\x02CNH\x00R\n" +
	"regionCode\x88\x01\x01\x12;\n" +
	"\x17enable_boarding_daycare\x18\x02 \x01(\bH\x01R\x15enableBoardingDaycare\x88\x01\x01\x127\n" +
	"\x15enable_online_booking\x18\x03 \x01(\bH\x02R\x13enableOnlineBooking\x88\x01\x01\x12(\n" +
	"\renable_stripe\x18\x06 \x01(\bH\x03R\fenableStripe\x88\x01\x01\x12)\n" +
	"\x0ehas_sms_credit\x18\a \x01(\bH\x04R\fhasSmsCredit\x88\x01\x01\x12-\n" +
	"\x10has_email_credit\x18\b \x01(\bH\x05R\x0ehasEmailCredit\x88\x01\x01B\x0e\n" +
	"\f_region_codeB\x1a\n" +
	"\x18_enable_boarding_daycareB\x18\n" +
	"\x16_enable_online_bookingB\x10\n" +
	"\x0e_enable_stripeB\x11\n" +
	"\x0f_has_sms_creditB\x13\n" +
	"\x11_has_email_credit\"\x93\x02\n" +
	"\x18BorrowTestAccountRequest\x12&\n" +
	"\bborrower\x18\x01 \x01(\tB\n" +
	"\xbaH\ar\x05\x10\x01\x18\xff\x01R\bborrower\x12\x19\n" +
	"\x02id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\x02id\x12\x1f\n" +
	"\x05email\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01H\x00R\x05email\x12N\n" +
	"\n" +
	"attributes\x18\x04 \x01(\v2).backend.proto.test_account.v1.AttributesH\x01R\n" +
	"attributes\x88\x01\x01\x12\x1b\n" +
	"\x06shared\x18\x05 \x01(\bH\x02R\x06shared\x88\x01\x01B\f\n" +
	"\n" +
	"identifierB\r\n" +
	"\v_attributesB\t\n" +
	"\a_shared\"~\n" +
	"\x19BorrowTestAccountResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12\x1f\n" +
	"\vcontract_id\x18\x04 \x01(\x03R\n" +
	"contractId\"D\n" +
	"\x18ReturnTestAccountRequest\x12(\n" +
	"\vcontract_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"contractId\"\x1b\n" +
	"\x19ReturnTestAccountResponse\"\xb2\x01\n" +
	"\x18CreateTestAccountRequest\x12U\n" +
	"\ftest_account\x18\x01 \x01(\v2*.backend.proto.test_account.v1.TestAccountB\x06\xbaH\x03\xc8\x01\x01R\vtestAccount\x12+\n" +
	"\x0ftest_account_id\x18\x02 \x01(\tH\x00R\rtestAccountId\x88\x01\x01B\x12\n" +
	"\x10_test_account_id\"6\n" +
	"\x1aReleaseTestAccountsRequest\x12\x18\n" +
	"\aoverdue\x18\x01 \x01(\bR\aoverdue\"\x1d\n" +
	"\x1bReleaseTestAccountsResponse\"\xed\x01\n" +
	"\x17ListTestAccountsRequest\x12)\n" +
	"\tpage_size\x18\x01 \x01(\x05B\a\xbaH\x04\x1a\x02 \x00H\x00R\bpageSize\x88\x01\x01\x12+\n" +
	"\n" +
	"page_token\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01H\x01R\tpageToken\x88\x01\x01\x12R\n" +
	"\x06filter\x18\x03 \x01(\v25.backend.proto.test_account.v1.ListTestAccountsFilterH\x02R\x06filter\x88\x01\x01B\f\n" +
	"\n" +
	"_page_sizeB\r\n" +
	"\v_page_tokenB\t\n" +
	"\a_filter\"\xcb\x01\n" +
	"\x18ListTestAccountsResponse\x12O\n" +
	"\rtest_accounts\x18\x01 \x03(\v2*.backend.proto.test_account.v1.TestAccountR\ftestAccounts\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tH\x00R\rnextPageToken\x88\x01\x01\x12\x1d\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x05R\ttotalSizeB\x12\n" +
	"\x10_next_page_token\"\xca\x01\n" +
	"\x16ListTestAccountsFilter\x12\x19\n" +
	"\x05owner\x18\x01 \x01(\tH\x00R\x05owner\x88\x01\x01\x12\x1f\n" +
	"\boccupied\x18\x02 \x01(\bH\x01R\boccupied\x88\x01\x01\x12N\n" +
	"\n" +
	"attributes\x18\x03 \x01(\v2).backend.proto.test_account.v1.AttributesH\x02R\n" +
	"attributes\x88\x01\x01B\b\n" +
	"\x06_ownerB\v\n" +
	"\t_occupiedB\r\n" +
	"\v_attributes2\xb5\x05\n" +
	"\x12TestAccountService\x12\x86\x01\n" +
	"\x11BorrowTestAccount\x127.backend.proto.test_account.v1.BorrowTestAccountRequest\x1a8.backend.proto.test_account.v1.BorrowTestAccountResponse\x12\x86\x01\n" +
	"\x11ReturnTestAccount\x127.backend.proto.test_account.v1.ReturnTestAccountRequest\x1a8.backend.proto.test_account.v1.ReturnTestAccountResponse\x12x\n" +
	"\x11CreateTestAccount\x127.backend.proto.test_account.v1.CreateTestAccountRequest\x1a*.backend.proto.test_account.v1.TestAccount\x12\x8c\x01\n" +
	"\x13ReleaseTestAccounts\x129.backend.proto.test_account.v1.ReleaseTestAccountsRequest\x1a:.backend.proto.test_account.v1.ReleaseTestAccountsResponse\x12\x83\x01\n" +
	"\x10ListTestAccounts\x126.backend.proto.test_account.v1.ListTestAccountsRequest\x1a7.backend.proto.test_account.v1.ListTestAccountsResponseBv\n" +
	"'com.moego.backend.proto.test_account.v1P\x01ZIgithub.com/MoeGolibrary/moego/backend/proto/test_account/v1;testaccountpbb\x06proto3"

var (
	file_backend_proto_test_account_v1_test_account_service_proto_rawDescOnce sync.Once
	file_backend_proto_test_account_v1_test_account_service_proto_rawDescData []byte
)

func file_backend_proto_test_account_v1_test_account_service_proto_rawDescGZIP() []byte {
	file_backend_proto_test_account_v1_test_account_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_test_account_v1_test_account_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_test_account_v1_test_account_service_proto_rawDesc), len(file_backend_proto_test_account_v1_test_account_service_proto_rawDesc)))
	})
	return file_backend_proto_test_account_v1_test_account_service_proto_rawDescData
}

var file_backend_proto_test_account_v1_test_account_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_backend_proto_test_account_v1_test_account_service_proto_goTypes = []any{
	(*TestAccount)(nil),                 // 0: backend.proto.test_account.v1.TestAccount
	(*Attributes)(nil),                  // 1: backend.proto.test_account.v1.Attributes
	(*BorrowTestAccountRequest)(nil),    // 2: backend.proto.test_account.v1.BorrowTestAccountRequest
	(*BorrowTestAccountResponse)(nil),   // 3: backend.proto.test_account.v1.BorrowTestAccountResponse
	(*ReturnTestAccountRequest)(nil),    // 4: backend.proto.test_account.v1.ReturnTestAccountRequest
	(*ReturnTestAccountResponse)(nil),   // 5: backend.proto.test_account.v1.ReturnTestAccountResponse
	(*CreateTestAccountRequest)(nil),    // 6: backend.proto.test_account.v1.CreateTestAccountRequest
	(*ReleaseTestAccountsRequest)(nil),  // 7: backend.proto.test_account.v1.ReleaseTestAccountsRequest
	(*ReleaseTestAccountsResponse)(nil), // 8: backend.proto.test_account.v1.ReleaseTestAccountsResponse
	(*ListTestAccountsRequest)(nil),     // 9: backend.proto.test_account.v1.ListTestAccountsRequest
	(*ListTestAccountsResponse)(nil),    // 10: backend.proto.test_account.v1.ListTestAccountsResponse
	(*ListTestAccountsFilter)(nil),      // 11: backend.proto.test_account.v1.ListTestAccountsFilter
}
var file_backend_proto_test_account_v1_test_account_service_proto_depIdxs = []int32{
	1,  // 0: backend.proto.test_account.v1.TestAccount.attributes:type_name -> backend.proto.test_account.v1.Attributes
	1,  // 1: backend.proto.test_account.v1.BorrowTestAccountRequest.attributes:type_name -> backend.proto.test_account.v1.Attributes
	0,  // 2: backend.proto.test_account.v1.CreateTestAccountRequest.test_account:type_name -> backend.proto.test_account.v1.TestAccount
	11, // 3: backend.proto.test_account.v1.ListTestAccountsRequest.filter:type_name -> backend.proto.test_account.v1.ListTestAccountsFilter
	0,  // 4: backend.proto.test_account.v1.ListTestAccountsResponse.test_accounts:type_name -> backend.proto.test_account.v1.TestAccount
	1,  // 5: backend.proto.test_account.v1.ListTestAccountsFilter.attributes:type_name -> backend.proto.test_account.v1.Attributes
	2,  // 6: backend.proto.test_account.v1.TestAccountService.BorrowTestAccount:input_type -> backend.proto.test_account.v1.BorrowTestAccountRequest
	4,  // 7: backend.proto.test_account.v1.TestAccountService.ReturnTestAccount:input_type -> backend.proto.test_account.v1.ReturnTestAccountRequest
	6,  // 8: backend.proto.test_account.v1.TestAccountService.CreateTestAccount:input_type -> backend.proto.test_account.v1.CreateTestAccountRequest
	7,  // 9: backend.proto.test_account.v1.TestAccountService.ReleaseTestAccounts:input_type -> backend.proto.test_account.v1.ReleaseTestAccountsRequest
	9,  // 10: backend.proto.test_account.v1.TestAccountService.ListTestAccounts:input_type -> backend.proto.test_account.v1.ListTestAccountsRequest
	3,  // 11: backend.proto.test_account.v1.TestAccountService.BorrowTestAccount:output_type -> backend.proto.test_account.v1.BorrowTestAccountResponse
	5,  // 12: backend.proto.test_account.v1.TestAccountService.ReturnTestAccount:output_type -> backend.proto.test_account.v1.ReturnTestAccountResponse
	0,  // 13: backend.proto.test_account.v1.TestAccountService.CreateTestAccount:output_type -> backend.proto.test_account.v1.TestAccount
	8,  // 14: backend.proto.test_account.v1.TestAccountService.ReleaseTestAccounts:output_type -> backend.proto.test_account.v1.ReleaseTestAccountsResponse
	10, // 15: backend.proto.test_account.v1.TestAccountService.ListTestAccounts:output_type -> backend.proto.test_account.v1.ListTestAccountsResponse
	11, // [11:16] is the sub-list for method output_type
	6,  // [6:11] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_backend_proto_test_account_v1_test_account_service_proto_init() }
func file_backend_proto_test_account_v1_test_account_service_proto_init() {
	if File_backend_proto_test_account_v1_test_account_service_proto != nil {
		return
	}
	file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[1].OneofWrappers = []any{}
	file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[2].OneofWrappers = []any{
		(*BorrowTestAccountRequest_Id)(nil),
		(*BorrowTestAccountRequest_Email)(nil),
	}
	file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[6].OneofWrappers = []any{}
	file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[9].OneofWrappers = []any{}
	file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_test_account_v1_test_account_service_proto_msgTypes[11].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_test_account_v1_test_account_service_proto_rawDesc), len(file_backend_proto_test_account_v1_test_account_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_test_account_v1_test_account_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_test_account_v1_test_account_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_test_account_v1_test_account_service_proto_msgTypes,
	}.Build()
	File_backend_proto_test_account_v1_test_account_service_proto = out.File
	file_backend_proto_test_account_v1_test_account_service_proto_goTypes = nil
	file_backend_proto_test_account_v1_test_account_service_proto_depIdxs = nil
}
