load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "b",
    srcs = ["b.go"],
    importpath = "golang.org/x/tools/internal/gcimporter/testdata/issue58296/b",
    visibility = [
        "//:__subpackages__",
        "@org_golang_x_tools_go_vcs//:__subpackages__",
    ],
    deps = ["@org_golang_x_tools//internal/gcimporter/testdata/issue58296/b/a"],
)

alias(
    name = "go_default_library",
    actual = ":b",
    visibility = [
        "//:__subpackages__",
        "@org_golang_x_tools_go_vcs//:__subpackages__",
    ],
)
