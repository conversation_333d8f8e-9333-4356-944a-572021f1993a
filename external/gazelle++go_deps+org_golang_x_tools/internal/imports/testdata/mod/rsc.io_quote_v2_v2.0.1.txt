rsc.io/quote/v2@v2.0.1

-- .mod --
module rsc.io/quote/v2

require rsc.io/sampler v1.3.0
-- .info --
{"Version":"v2.0.1","Name":"754f68430672776c84704e2d10209a6ec700cd64","Short":"754f68430672","Time":"2018-07-09T16:25:34Z"}
-- go.mod --
module rsc.io/quote/v2

require rsc.io/sampler v1.3.0
-- quote.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Package quote collects pithy sayings.
package quote // import "rsc.io/quote"

import "rsc.io/sampler"

// Hello returns a greeting.
func HelloV2() string {
	return sampler.Hello()
}

// Glass returns a useful phrase for world travelers.
func GlassV2() string {
	// See http://www.oocities.org/nodotus/hbglass.html.
	return "I can eat glass and it doesn't hurt me."
}

// Go returns a Go proverb.
func GoV2() string {
	return "Don't communicate by sharing memory, share memory by communicating."
}

// Opt returns an optimization truth.
func OptV2() string {
	// Wisdom from ken.
	return "If a program is too slow, it must have a loop."
}
-- quote_test.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package quote

import (
	"os"
	"testing"
)

func init() {
	os.Setenv("LC_ALL", "en")
}

func TestHello(t *testing.T) {
	hello := "Hello, world."
	if out := Hello(); out != hello {
		t.Errorf("Hello() = %q, want %q", out, hello)
	}
}

func TestGlass(t *testing.T) {
	glass := "I can eat glass and it doesn't hurt me."
	if out := Glass(); out != glass {
		t.Errorf("Glass() = %q, want %q", out, glass)
	}
}

func TestGo(t *testing.T) {
	go1 := "Don't communicate by sharing memory, share memory by communicating."
	if out := Go(); out != go1 {
		t.Errorf("Go() = %q, want %q", out, go1)
	}
}

func TestOpt(t *testing.T) {
	opt := "If a program is too slow, it must have a loop."
	if out := Opt(); out != opt {
		t.Errorf("Opt() = %q, want %q", out, opt)
	}
}
