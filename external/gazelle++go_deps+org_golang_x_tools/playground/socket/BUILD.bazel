load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "socket",
    srcs = ["socket.go"],
    importpath = "golang.org/x/tools/playground/socket",
    visibility = ["//visibility:public"],
    deps = [
        "//txtar",
        "@org_golang_x_net//websocket",
    ],
)

alias(
    name = "go_default_library",
    actual = ":socket",
    visibility = ["//visibility:public"],
)

go_test(
    name = "socket_test",
    srcs = ["socket_test.go"],
    embed = [":socket"],
)
