load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "nilness",
    srcs = [
        "doc.go",
        "nilness.go",
    ],
    embedsrcs = ["doc.go"],
    importpath = "golang.org/x/tools/go/analysis/passes/nilness",
    visibility = ["//visibility:public"],
    deps = [
        "//go/analysis",
        "//go/analysis/passes/buildssa",
        "//go/analysis/passes/internal/analysisutil",
        "//go/ssa",
        "//internal/typeparams",
    ],
)

alias(
    name = "go_default_library",
    actual = ":nilness",
    visibility = ["//visibility:public"],
)

go_test(
    name = "nilness_test",
    srcs = ["nilness_test.go"],
    deps = [
        ":nilness",
        "//go/analysis/analysistest",
    ],
)
