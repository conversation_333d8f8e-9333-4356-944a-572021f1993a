load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "stringintconv_lib",
    srcs = ["main.go"],
    importpath = "golang.org/x/tools/go/analysis/passes/stringintconv/cmd/stringintconv",
    visibility = ["//visibility:private"],
    deps = [
        "//go/analysis/passes/stringintconv",
        "//go/analysis/singlechecker",
    ],
)

go_binary(
    name = "stringintconv",
    embed = [":stringintconv_lib"],
    visibility = ["//visibility:public"],
)
