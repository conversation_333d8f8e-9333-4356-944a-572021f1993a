load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "cgo",
    srcs = [
        "cgo.go",
        "cgo_pkgconfig.go",
    ],
    importpath = "golang.org/x/tools/go/internal/cgo",
    visibility = [
        "//go:__subpackages__",
        "@org_golang_x_tools_go_vcs//:__subpackages__",
    ],
)

alias(
    name = "go_default_library",
    actual = ":cgo",
    visibility = [
        "//go:__subpackages__",
        "@org_golang_x_tools_go_vcs//:__subpackages__",
    ],
)
