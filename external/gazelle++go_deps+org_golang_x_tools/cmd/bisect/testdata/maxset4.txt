{"Fail": "amber || apricot && peach || red && green && blue || cyan && magenta && yellow && black", "Bisect": {"MaxSet": 4}}
-- stdout --
--- change set #1 (enabling changes causes failure)
amber
---
--- change set #2 (enabling changes causes failure)
blue
green
red
---
--- change set #3 (enabling changes causes failure)
black
cyan
magenta
yellow
---
--- change set #4 (enabling changes causes failure)
apricot
peach
---
-- stderr --
bisect: checking target with all changes disabled
bisect: run: test n... ok (90 matches)
bisect: checking target with all changes enabled
bisect: run: test y... FAIL (90 matches)
bisect: target succeeds with no changes, fails with all changes
bisect: searching for minimal set of enabled changes causing failure
bisect: run: test +0... FAIL (45 matches)
bisect: run: test +00... ok (23 matches)
bisect: run: test +10... FAIL (22 matches)
bisect: run: test +010... FAIL (11 matches)
bisect: run: test +0010... FAIL (6 matches)
bisect: run: test +00010... FAIL (3 matches)
bisect: run: test +000010... FAIL (2 matches)
bisect: run: test +0000010... FAIL (1 matches)
bisect: confirming failing change set
bisect: run: test v+x002... FAIL (1 matches)
bisect: FOUND failing change set
bisect: checking for more failures
bisect: run: test -x002... FAIL (89 matches)
bisect: target still fails; searching for more bad changes
bisect: run: test +0-x002... ok (44 matches)
bisect: run: test +1-x002... FAIL (45 matches)
bisect: run: test +01-x002... ok (23 matches)
bisect: run: test +11-x002... ok (22 matches)
bisect: run: test +01+11-x002... FAIL (23 matches)
bisect: run: test +001+11-x002... ok (12 matches)
bisect: run: test +101+11-x002... FAIL (11 matches)
bisect: run: test +0101+11-x002... ok (6 matches)
bisect: run: test +1101+11-x002... ok (5 matches)
bisect: run: test +0101+11+1101-x002... FAIL (6 matches)
bisect: run: test +00101+11+1101-x002... FAIL (3 matches)
bisect: run: test +000101+11+1101-x002... FAIL (2 matches)
bisect: run: test +0000101+11+1101-x002... ok (1 matches)
bisect: run: test +1000101+11+1101-x002... FAIL (1 matches)
bisect: run: test +1101+11+x045-x002... FAIL (5 matches)
bisect: run: test +01101+11+x045-x002... FAIL (3 matches)
bisect: run: test +001101+11+x045-x002... FAIL (2 matches)
bisect: run: test +0001101+11+x045-x002... FAIL (1 matches)
bisect: run: test +11+x045+x00d-x002... FAIL (22 matches)
bisect: run: test +011+x045+x00d-x002... ok (11 matches)
bisect: run: test +111+x045+x00d-x002... FAIL (11 matches)
bisect: run: test +0111+x045+x00d-x002... FAIL (6 matches)
bisect: run: test +00111+x045+x00d-x002... FAIL (3 matches)
bisect: run: test +000111+x045+x00d-x002... ok (2 matches)
bisect: run: test +100111+x045+x00d-x002... FAIL (1 matches)
bisect: confirming failing change set
bisect: run: test v+x045+x00d+x027-x002... FAIL (3 matches)
bisect: FOUND failing change set
bisect: checking for more failures
bisect: run: test -x045-x00d-x027-x002... FAIL (86 matches)
bisect: target still fails; searching for more bad changes
bisect: run: test +0-x045-x00d-x027-x002... ok (44 matches)
bisect: run: test +1-x045-x00d-x027-x002... ok (42 matches)
bisect: run: test +0+1-x045-x00d-x027-x002... FAIL (44 matches)
bisect: run: test +00+1-x045-x00d-x027-x002... FAIL (23 matches)
bisect: run: test +000+1-x045-x00d-x027-x002... ok (12 matches)
bisect: run: test +100+1-x045-x00d-x027-x002... ok (11 matches)
bisect: run: test +000+1+100-x045-x00d-x027-x002... FAIL (12 matches)
bisect: run: test +0000+1+100-x045-x00d-x027-x002... FAIL (6 matches)
bisect: run: test +00000+1+100-x045-x00d-x027-x002... FAIL (3 matches)
bisect: run: test +000000+1+100-x045-x00d-x027-x002... ok (2 matches)
bisect: run: test +100000+1+100-x045-x00d-x027-x002... FAIL (1 matches)
bisect: run: test +100+1+x020-x045-x00d-x027-x002... FAIL (11 matches)
bisect: run: test +0100+1+x020-x045-x00d-x027-x002... ok (6 matches)
bisect: run: test +1100+1+x020-x045-x00d-x027-x002... FAIL (5 matches)
bisect: run: test +01100+1+x020-x045-x00d-x027-x002... FAIL (3 matches)
bisect: run: test +001100+1+x020-x045-x00d-x027-x002... FAIL (2 matches)
bisect: run: test +0001100+1+x020-x045-x00d-x027-x002... FAIL (1 matches)
bisect: run: test +1+x020+x00c-x045-x00d-x027-x002... FAIL (42 matches)
bisect: run: test +01+x020+x00c-x045-x00d-x027-x002... FAIL (21 matches)
bisect: run: test +001+x020+x00c-x045-x00d-x027-x002... FAIL (12 matches)
bisect: run: test +0001+x020+x00c-x045-x00d-x027-x002... ok (6 matches)
bisect: run: test +1001+x020+x00c-x045-x00d-x027-x002... ok (6 matches)
bisect: run: test +0001+x020+x00c+1001-x045-x00d-x027-x002... FAIL (6 matches)
bisect: run: test +00001+x020+x00c+1001-x045-x00d-x027-x002... ok (3 matches)
bisect: run: test +10001+x020+x00c+1001-x045-x00d-x027-x002... FAIL (3 matches)
bisect: run: test +010001+x020+x00c+1001-x045-x00d-x027-x002... ok (2 matches)
bisect: run: test +110001+x020+x00c+1001-x045-x00d-x027-x002... FAIL (1 matches)
bisect: run: test +1001+x020+x00c+x031-x045-x00d-x027-x002... FAIL (6 matches)
bisect: run: test +01001+x020+x00c+x031-x045-x00d-x027-x002... ok (3 matches)
bisect: run: test +11001+x020+x00c+x031-x045-x00d-x027-x002... FAIL (3 matches)
bisect: run: test +011001+x020+x00c+x031-x045-x00d-x027-x002... FAIL (2 matches)
bisect: run: test +0011001+x020+x00c+x031-x045-x00d-x027-x002... ok (1 matches)
bisect: run: test +1011001+x020+x00c+x031-x045-x00d-x027-x002... FAIL (1 matches)
bisect: confirming failing change set
bisect: run: test v+x020+x00c+x031+x059-x045-x00d-x027-x002... FAIL (4 matches)
bisect: FOUND failing change set
bisect: checking for more failures
bisect: run: test -x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (82 matches)
bisect: target still fails; searching for more bad changes
bisect: run: test +0-x020-x00c-x031-x059-x045-x00d-x027-x002... ok (42 matches)
bisect: run: test +1-x020-x00c-x031-x059-x045-x00d-x027-x002... ok (40 matches)
bisect: run: test +0+1-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (42 matches)
bisect: run: test +00+1-x020-x00c-x031-x059-x045-x00d-x027-x002... ok (21 matches)
bisect: run: test +10+1-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (21 matches)
bisect: run: test +010+1-x020-x00c-x031-x059-x045-x00d-x027-x002... ok (10 matches)
bisect: run: test +110+1-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (11 matches)
bisect: run: test +0110+1-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (6 matches)
bisect: run: test +00110+1-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (3 matches)
bisect: run: test +000110+1-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (2 matches)
bisect: run: test +0000110+1-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (1 matches)
bisect: run: test +1+x006-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (40 matches)
bisect: run: test +01+x006-x020-x00c-x031-x059-x045-x00d-x027-x002... ok (19 matches)
bisect: run: test +11+x006-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (21 matches)
bisect: run: test +011+x006-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (11 matches)
bisect: run: test +0011+x006-x020-x00c-x031-x059-x045-x00d-x027-x002... ok (6 matches)
bisect: run: test +1011+x006-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (5 matches)
bisect: run: test +01011+x006-x020-x00c-x031-x059-x045-x00d-x027-x002... ok (3 matches)
bisect: run: test +11011+x006-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (2 matches)
bisect: run: test +011011+x006-x020-x00c-x031-x059-x045-x00d-x027-x002... ok (1 matches)
bisect: run: test +111011+x006-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (1 matches)
bisect: confirming failing change set
bisect: run: test v+x006+x03b-x020-x00c-x031-x059-x045-x00d-x027-x002... FAIL (2 matches)
bisect: FOUND failing change set
bisect: checking for more failures
bisect: run: test -x006-x03b-x020-x00c-x031-x059-x045-x00d-x027-x002... ok (80 matches)
bisect: target succeeds with all remaining changes enabled
