# Test of -test flag.

deadcode -test -filter=example.com example.com/p

 want "Dead"
!want "Live1"
!want "Live2"

 want "ExampleDead"
!want "ExampleLive"

-- go.mod --
module example.com
go 1.18

-- p/p.go --
package p

func Live1() {}
func Live2() {}
func Dead() {}

-- p/p_test.go --
package p_test

import "example.com/p"

import "testing"

func Test(t *testing.T) {
	p.Live1()
}

func ExampleLive() {
	p.Live2()
	// Output:
}

// A test Example function without an "Output:" comment is never executed.
func ExampleDead() {
	p.Dead()
}