load("@io_bazel_rules_go//go/tools/bazel_testing:def.bzl", "go_bazel_test")

go_bazel_test(
    name = "coverage_test",
    srcs = ["coverage_test.go"],
)

go_bazel_test(
    name = "binary_coverage_test",
    srcs = ["binary_coverage_test.go"],
)

go_bazel_test(
    name = "lcov_coverage_test",
    srcs = ["lcov_coverage_test.go"],
    target_compatible_with = select({
        "@platforms//os:windows": ["@platforms//:incompatible"],
        "//conditions:default": [],
    }),
)

go_bazel_test(
    name = "lcov_test_main_coverage_test",
    srcs = ["lcov_test_main_coverage_test.go"],
    target_compatible_with = select({
        "@platforms//os:windows": ["@platforms//:incompatible"],
        "//conditions:default": [],
    }),
)

go_bazel_test(
    name = "issue3017_test",
    srcs = ["issue3017_test.go"],
)
