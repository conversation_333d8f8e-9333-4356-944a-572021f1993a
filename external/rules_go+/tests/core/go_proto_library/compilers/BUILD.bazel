load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")
load(
    "//proto:compiler.bzl",
    "go_proto_compiler",
)
load(
    "//proto/wkt:well_known_types.bzl",
    "GOGO_WELL_KNOWN_TYPE_REMAPS",
)

go_library(
    name = "protoc_gen_dbenum_lib",
    srcs = [
        "dbenums.go",
        "main.go",
    ],
    importpath = "github.com/bazelbuild/rules_go/tests/core/go_proto_library/compilers",
    visibility = ["//visibility:private"],
    deps = [
        "@com_github_gogo_protobuf//proto",
        "@com_github_gogo_protobuf//protoc-gen-gogo/descriptor",
        "@com_github_gogo_protobuf//protoc-gen-gogo/generator",
        "@com_github_gogo_protobuf//protoc-gen-gogo/plugin",
        "@com_github_gogo_protobuf//vanity",
        "@com_github_gogo_protobuf//vanity/command",
    ],
)

go_binary(
    name = "protoc-gen-dbenum-compiler",
    embed = [":protoc_gen_dbenum_lib"],
    visibility = ["//visibility:private"],
)

go_proto_compiler(
    name = "dbenum_compiler",
    options = GOGO_WELL_KNOWN_TYPE_REMAPS,
    plugin = "//tests/core/go_proto_library/compilers:protoc-gen-dbenum-compiler",
    suffixes = [
        "_dbenum.pb.go",
        ".pb.go",
    ],
    visibility = ["//visibility:public"],
)
