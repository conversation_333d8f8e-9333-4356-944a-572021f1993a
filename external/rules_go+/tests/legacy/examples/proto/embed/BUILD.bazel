load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "embed_proto",
    srcs = ["embed.proto"],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "embed_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_proto",
    ],
    # Note that if you forget the importpath everything will break horribly.
    importpath = "github.com/bazelbuild/rules_go/examples/proto/embed",
    proto = ":embed_proto",
)

go_library(
    name = "go_default_library",
    srcs = ["embed.go"],
    embed = [":embed_go_proto"],
    importpath = "github.com/bazelbuild/rules_go/examples/proto/embed",
    visibility = ["//visibility:public"],
)
