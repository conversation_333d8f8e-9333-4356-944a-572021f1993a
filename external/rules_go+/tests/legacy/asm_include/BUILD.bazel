load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

config_setting(
    name = "linux_amd64",
    constraint_values = [
        "@platforms//cpu:x86_64",
        "@platforms//os:linux",
    ],
)

config_setting(
    name = "darwin_amd64",
    constraint_values = [
        "@platforms//cpu:x86_64",
        "@platforms//os:macos",
    ],
)

LIB_AMD64_SRCS = [
    "foo_amd64.go",
    "foo_amd64.s",
    "foo_amd64.h",
]

LIB_OTHER_SRCS = ["foo_other.go"]

go_library(
    name = "go_default_library",
    srcs = select({
        ":linux_amd64": LIB_AMD64_SRCS,
        ":darwin_amd64": LIB_AMD64_SRCS,
        "//conditions:default": LIB_OTHER_SRCS,
    }),
    importpath = "github.com/bazelbuild/rules_go/tests/asm_include",
)

go_test(
    name = "go_default_test",
    size = "small",
    srcs = ["foo_test.go"],
    embed = [":go_default_library"],
)
