/* Copyright 2016 The Bazel Authors. All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"go/build"
	"testing"

	"github.com/bazelbuild/rules_go/go/runfiles"
)

func TestReadFileInfoEmbeds(t *testing.T) {
	rf, err := runfiles.New()
	if err != nil {
		t.Fatalf("Error creating runfiles: %v", err)
	}

	f, err := rf.Rlocation("io_bazel_rules_go/go/tools/builders/read_test_fixture.go")
	if err != nil {
		t.Fatalf("Unable to get test file: %v", err)
	}

	fileInfo, err := readFileInfo(build.Default, f)
	if err != nil {
		t.Fatalf("readFileInfo: %v", err)
	}

	numExpectedEmbeds := 2
	if len(fileInfo.embeds) != numExpectedEmbeds {
		t.Fatalf("did not find expected number of file embeds! found %d got %d", len(fileInfo.embeds), numExpectedEmbeds)
	}
}
