load("//go:def.bzl", "go_binary", "go_library", "go_test")

go_binary(
    name = "fetch_repo",
    embed = [":go_default_library"],
    visibility = ["//visibility:public"],
)

go_library(
    name = "go_default_library",
    srcs = ["main.go"],
    importpath = "github.com/bazelbuild/rules_go/go/tools/fetch_repo",
    visibility = ["//visibility:private"],
    deps = ["@org_golang_x_tools_go_vcs//:go_default_library"],
)

go_test(
    name = "go_default_test",
    size = "small",
    srcs = ["fetch_repo_test.go"],
    embed = [":go_default_library"],
    deps = ["@org_golang_x_tools_go_vcs//:go_default_library"],
)
