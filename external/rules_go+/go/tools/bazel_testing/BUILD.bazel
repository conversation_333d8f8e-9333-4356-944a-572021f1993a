load("@bazel_skylib//:bzl_library.bzl", "bzl_library")
load("@go_host_compatible_sdk_label//:defs.bzl", "HOST_COMPATIBLE_SDK")
load("//go:def.bzl", "go_library")

HOST_COMPATIBLE_SDK_FILES = HOST_COMPATIBLE_SDK.same_package_label("files")

go_library(
    name = "bazel_testing",
    srcs = ["bazel_testing.go"],
    data = [
        HOST_COMPATIBLE_SDK,
        HOST_COMPATIBLE_SDK_FILES,
    ],
    importpath = "github.com/bazelbuild/rules_go/go/tools/bazel_testing",
    visibility = ["//visibility:public"],
    x_defs = {
        "goRootFile": "$(rlocationpath {})".format(HOST_COMPATIBLE_SDK),
    },
    deps = [
        "//go/runfiles",
        "//go/tools/bazel",
        "//go/tools/internal/txtar",
    ],
)

filegroup(
    name = "all_files",
    testonly = True,
    srcs = glob(["**"]),
    visibility = ["//visibility:public"],
)

alias(
    name = "go_default_library",
    actual = ":bazel_testing",
    visibility = ["//visibility:public"],
)

bzl_library(
    name = "def",
    srcs = ["def.bzl"],
    visibility = ["//visibility:public"],
    deps = ["//go:def"],
)
