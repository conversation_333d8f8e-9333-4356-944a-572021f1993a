load("@bazel_skylib//:bzl_library.bzl", "bzl_library")
load("@io_bazel_rules_go//go/tools/bazel_testing:def.bzl", "go_bazel_test")
load(":gazelle_binary_test.bzl", "gazelle_binary_test_suite")

# gazelle:exclude *_test.go
go_bazel_test(
    name = "bazel_test",
    srcs = [
        "go_repository_test.go",
        "runner_test.go",
    ],
    rule_files = [
        "//:all_files",
        "@io_bazel_rules_go//:all_files",
    ],
    deps = ["//testtools"],
)

gazelle_binary_test_suite()

# TODO(jayconrod): test fetch_repo error cases.

exports_files(
    [
        "gazelle.bash.in",
        "extend_docs.bzl",
        "repository_docs.bzl",
        "list_repository_tools_srcs.go",
        "repository_rules_test_errors.patch",
    ],
    visibility = ["//visibility:public"],
)

filegroup(
    name = "all_files",
    testonly = True,
    srcs = [
        "BUILD.bazel",
        "common.bzl",
        "extend_docs.bzl",
        "gazelle.bash.in",
        "gazelle_binary.bzl",
        "gazelle_binary_test.bzl",
        "go_repository.bzl",
        "go_repository_cache.bzl",
        "go_repository_config.bzl",
        "go_repository_tools.bzl",
        "go_repository_tools_srcs.bzl",
        "is_bazel_module.bzl",
        "list_repository_tools_srcs.go",
        "overlay_repository.bzl",
        "repository_docs.bzl",
        "repository_rules_test_errors.patch",
        "//internal/bzlmod:all_files",
        "//internal/gazellebinarytest:all_files",
        "//internal/generationtest:all_files",
        "//internal/language:all_files",
        "//internal/module:all_files",
        "//internal/version:all_files",
        "//internal/wspace:all_files",
    ],
    visibility = ["//visibility:public"],
)

bzl_library(
    name = "gazelle_binary",
    srcs = ["gazelle_binary.bzl"],
    visibility = ["//:__subpackages__"],
    deps = ["@io_bazel_rules_go//go:def"],
)

bzl_library(
    name = "go_repository",
    srcs = ["go_repository.bzl"],
    visibility = ["//:__subpackages__"],
    deps = [
        ":common",
        "//internal:go_repository_cache",
        "@bazel_tools//tools/build_defs/repo:utils.bzl",
    ],
)

bzl_library(
    name = "go_repository_cache",
    srcs = ["go_repository_cache.bzl"],
    visibility = ["//:__subpackages__"],
    deps = [":common"],
)

bzl_library(
    name = "go_repository_config",
    srcs = ["go_repository_config.bzl"],
    visibility = ["//:__subpackages__"],
    deps = [
        ":common",
        "//internal:go_repository_cache",
    ],
)

bzl_library(
    name = "go_repository_tools",
    srcs = ["go_repository_tools.bzl"],
    visibility = ["//:__subpackages__"],
    deps = [
        ":common",
        "//internal:go_repository_cache",
        "//internal:go_repository_tools_srcs",
    ],
)

bzl_library(
    name = "repository_docs",
    srcs = ["repository_docs.bzl"],
    visibility = ["//:__subpackages__"],
    deps = [
        ":go_repository",
        ":overlay_repository",
    ],
)

bzl_library(
    name = "extend_docs",
    srcs = ["extend_docs.bzl"],
    visibility = ["//:__subpackages__"],
    deps = [
        "gazelle_binary",
        "//internal/generationtest",
    ],
)

bzl_library(
    name = "common",
    srcs = ["common.bzl"],
    visibility = ["//:__subpackages__"],
)

bzl_library(
    name = "go_repository_tools_srcs",
    srcs = ["go_repository_tools_srcs.bzl"],
    visibility = ["//:__subpackages__"],
)

bzl_library(
    name = "is_bazel_module",
    srcs = ["is_bazel_module.bzl"],
    visibility = ["//:__subpackages__"],
)

bzl_library(
    name = "overlay_repository",
    srcs = ["overlay_repository.bzl"],
    visibility = ["//:__subpackages__"],
)
