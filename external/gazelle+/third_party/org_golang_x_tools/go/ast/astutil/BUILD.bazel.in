load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "enclosing.go",
        "imports.go",
        "util.go",
    ],
    importpath = "golang.org/x/tools/go/ast/astutil",
    visibility = ["//visibility:public"],
)

go_test(
    name = "go_default_test",
    srcs = ["imports_test.go"],
    embed = [":go_default_library"],
)

go_test(
    name = "go_default_xtest",
    srcs = ["enclosing_test.go"],
    deps = [":go_default_library"],
)
