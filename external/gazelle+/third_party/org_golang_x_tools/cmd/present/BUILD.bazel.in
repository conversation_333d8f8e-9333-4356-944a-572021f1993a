load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "go_default_library",
    srcs = [
        "dir.go",
        "doc.go",
        "local.go",
        "play.go",
        "play_socket.go",
    ],
    importpath = "golang.org/x/tools/cmd/present",
    visibility = ["//visibility:private"],
    deps = [
        "//godoc/static:go_default_library",
        "//playground/socket:go_default_library",
        "//present:go_default_library",
    ],
)

go_binary(
    name = "present",
    embed = [":go_default_library"],
    visibility = ["//visibility:public"],
)
