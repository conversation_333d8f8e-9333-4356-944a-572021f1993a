load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "go_default_library",
    srcs = ["main.go"],
    importpath = "golang.org/x/tools/cmd/gomvpkg",
    visibility = ["//visibility:private"],
    deps = [
        "//go/buildutil:go_default_library",
        "//refactor/rename:go_default_library",
    ],
)

go_binary(
    name = "gomvpkg",
    embed = [":go_default_library"],
    visibility = ["//visibility:public"],
)
