load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "bar_proto",
    srcs = [
        "bar1.proto",
        "bar2.proto",
    ],
    _gazelle_imports = [],
    visibility = ["//visibility:public"],
)

proto_library(
    name = "foo_proto",
    srcs = [
        "foo1.proto",
        "foo2.proto",
    ],
    _gazelle_imports = [],
    visibility = ["//visibility:public"],
)
