load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "errgroup",
    srcs = ["errgroup.go"],
    importpath = "golang.org/x/sync/errgroup",
    visibility = ["//visibility:public"],
)

alias(
    name = "go_default_library",
    actual = ":errgroup",
    visibility = ["//visibility:public"],
)

go_test(
    name = "errgroup_test",
    srcs = [
        "errgroup_example_md5all_test.go",
        "errgroup_test.go",
    ],
    deps = [":errgroup"],
)
