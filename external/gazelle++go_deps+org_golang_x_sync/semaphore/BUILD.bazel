load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "semaphore",
    srcs = ["semaphore.go"],
    importpath = "golang.org/x/sync/semaphore",
    visibility = ["//visibility:public"],
)

alias(
    name = "go_default_library",
    actual = ":semaphore",
    visibility = ["//visibility:public"],
)

go_test(
    name = "semaphore_test",
    srcs = [
        "semaphore_bench_test.go",
        "semaphore_example_test.go",
        "semaphore_test.go",
    ],
    deps = [
        ":semaphore",
        "//errgroup",
    ],
)
