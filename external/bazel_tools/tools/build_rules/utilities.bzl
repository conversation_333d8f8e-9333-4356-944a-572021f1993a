# Copyright 2017 Google Inc. All Rights Reserved.
# Copyright 2017 The Bazel Authors. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""This rule exposes the source jar of a java_*_library rule as a label."""

load("@rules_java//java/common:java_info.bzl", "JavaInfo")

def _java_library_srcs_impl(ctx):
    if len(ctx.attr.deps) != 1:
        fail("Only one deps value supported", "deps")
    dep = ctx.attr.deps[0]
    return [DefaultInfo(files = depset(dep[JavaInfo].source_jars))]

_java_library_srcs = rule(
    implementation = _java_library_srcs_impl,
    attrs = {
        "deps": attr.label_list(
            mandatory = True,
            allow_empty = False,
            providers = [JavaInfo],
        ),
    },
)

def java_library_srcs(name, deps, visibility = None, **kwargs):
    """Provides the source jars generated by a java_*_library rule."""
    _java_library_srcs(name = name, deps = deps, visibility = visibility, **kwargs)
